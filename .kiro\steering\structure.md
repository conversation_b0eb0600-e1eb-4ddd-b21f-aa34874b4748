# 项目结构

## 根目录
- `src/` - 主要源代码
- `prisma/` - 数据库模式和迁移文件
- `scripts/` - 实用脚本
- `docs/` - 文档文件
- `.kiro/` - Kiro AI 助手配置

## 源码组织 (`src/`)

### App Router (`src/app/`)
- Next.js 14 App Router 结构
- 路由组：`(auth)/` 用于身份认证页面
- API 路由位于 `api/` 子目录
- 页面特定的布局和组件

### 组件 (`src/components/`)
- `auth/` - 身份认证相关组件
- `dashboard/` - 仪表板和导航组件
- `editor/` - TipTap 编辑器组件和扩展
- `hierarchy/` - 文件夹/文档树组件
- `storage/` - 存储和同步组件
- `ui/` - 可重用UI组件（按钮、模态框等）

### 库文件 (`src/lib/`)
- `auth/` - 身份认证工具和配置
- `db/` - 数据库连接和 Prisma 客户端
- `services/` - 外部服务集成
- `storage/` - 本地和远程存储服务
- `utils/` - 通用工具函数

### 数据层
- `hooks/` - 用于数据获取的自定义 React hooks
- `types/` - TypeScript 类型定义
- `stores/` - Zustand 状态管理（如果存在）

## 数据库 (`prisma/`)
- `schema.prisma` - 主数据库模式
- `schema.production.prisma` - 生产环境特定模式
- `migrations/` - 数据库迁移文件
- `seed.ts` - 数据库种子脚本

## 关键模式

### 导入别名
- `@/*` - 根 src 目录
- `@/components/*` - 组件目录
- `@/lib/*` - 库目录
- `@/types/*` - 类型定义
- `@/hooks/*` - 自定义 hooks

### 组件组织
- 在基于功能的文件夹中分组相关组件
- 将UI组件与业务逻辑组件分离
- 使用索引文件进行清洁导入

### 数据库模型
- 以用户为中心的设计，具有适当的关系
- 具有自引用的层级文件夹结构
- 文档版本控制和历史跟踪
- AI交互日志记录和配置

### 文件命名
- 文件和文件夹使用 kebab-case
- 组件文件使用 PascalCase
- Hook 文件以 `use` 为前缀
- 类型文件以 `.types.ts` 为后缀

## 配置文件
- `next.config.js` - Next.js 配置
- `tsconfig.json` - TypeScript 配置，包含路径映射
- `tailwind.config.js` - Tailwind CSS 配置
- `.eslintrc.json` - ESLint 规则和 TypeScript 集成
- `.prettierrc` - 代码格式化，配合 Tailwind 插件