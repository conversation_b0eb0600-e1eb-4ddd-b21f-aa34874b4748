'use client';

import React, { useState } from 'react';
import { Editor } from '@/components/editor/Editor';

/**
 * 文本选择功能测试页面
 */
export default function SelectionTestPage() {
  const [content, setContent] = useState(`
    <h1>文本选择测试</h1>
    <p>请选择这段文本来测试选择菜单功能。</p>
    <p>菜单应该在选择文本后自动显示。</p>
    <p>这是一段较长的文本，用于测试菜单位置计算和边界检测功能。选择不同位置的文本来验证菜单是否能正确显示在合适的位置。</p>
  `);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">文本选择菜单测试</h1>
        
        <div className="bg-white rounded-lg shadow p-6">
          <Editor
            content={content}
            onChange={setContent}
            placeholder="开始编辑..."
            enableAI={true}
            className="min-h-[400px]"
          />
        </div>
        
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold mb-2">测试说明：</h3>
          <ul className="text-sm space-y-1">
            <li>• 选择编辑器中的任意文本（至少2个字符）</li>
            <li>• 菜单应该自动显示在选择文本附近</li>
            <li>• 菜单包含 Chat、Edit、格式化和实用工具选项</li>
            <li>• 点击菜单外部或按 ESC 键可隐藏菜单</li>
            <li>• 菜单会自动调整位置以避免超出视窗边界</li>
          </ul>
        </div>
      </div>
    </div>
  );
}