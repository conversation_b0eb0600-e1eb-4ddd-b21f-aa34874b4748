'use client';

import { useState } from 'react';
import { User } from 'next-auth';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';

interface UserProfileProps {
  user: User;
  onClose: () => void;
}

export function UserProfile({ user, onClose }: UserProfileProps) {
  const { logout } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'settings' | 'ai'>('profile');

  const handleLogout = async () => {
    try {
      await logout();
      onClose();
    } catch (error) {
      console.error('退出登录失败:', error);
    }
  };

  const tabs = [
    { id: 'profile', label: '个人资料', icon: '👤' },
    { id: 'settings', label: '应用设置', icon: '⚙️' },
    { id: 'ai', label: 'AI 配置', icon: '🤖' }
  ];

  return (
    <>
      {/* 遮罩层 */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* 侧边栏 */}
      <div className="fixed right-0 top-0 h-full w-96 bg-white shadow-xl z-50 overflow-y-auto">
        {/* 头部 */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">用户中心</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            ✕
          </button>
        </div>

        {/* 标签页 */}
        <div className="flex border-b border-gray-200">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        {/* 内容区域 */}
        <div className="p-6">
          {activeTab === 'profile' && (
            <ProfileTab user={user} onLogout={handleLogout} />
          )}
          {activeTab === 'settings' && (
            <SettingsTab />
          )}
          {activeTab === 'ai' && (
            <AIConfigTab />
          )}
        </div>
      </div>
    </>
  );
}

function ProfileTab({ user, onLogout }: { user: User; onLogout: () => void }) {
  return (
    <div className="space-y-6">
      {/* 用户头像和基本信息 */}
      <div className="text-center">
        <div className="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
          {user.image ? (
            <img
              src={user.image}
              alt="用户头像"
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            user.name?.charAt(0) || user.email?.charAt(0) || 'U'
          )}
        </div>
        <h3 className="text-lg font-medium text-gray-900">
          {user.name || '未设置姓名'}
        </h3>
        <p className="text-gray-600">{user.email}</p>
      </div>

      {/* 账户信息 */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            用户名
          </label>
          <div className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
            {user.name || '未设置'}
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            邮箱地址
          </label>
          <div className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
            {user.email}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            账户类型
          </label>
          <div className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
            免费版
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="space-y-3 pt-4 border-t border-gray-200">
        <Button variant="outline" className="w-full">
          编辑个人资料
        </Button>
        <Button variant="outline" className="w-full">
          更改密码
        </Button>
        <Button 
          variant="destructive" 
          className="w-full"
          onClick={onLogout}
        >
          退出登录
        </Button>
      </div>
    </div>
  );
}

function SettingsTab() {
  const [settings, setSettings] = useState({
    theme: 'light',
    language: 'zh-CN',
    autoSave: true,
    notifications: true
  });

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">应用设置</h3>

      <div className="space-y-4">
        {/* 主题设置 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            主题
          </label>
          <select
            value={settings.theme}
            onChange={(e) => setSettings({ ...settings, theme: e.target.value })}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="light">浅色</option>
            <option value="dark">深色</option>
            <option value="auto">跟随系统</option>
          </select>
        </div>

        {/* 语言设置 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            语言
          </label>
          <select
            value={settings.language}
            onChange={(e) => setSettings({ ...settings, language: e.target.value })}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="zh-CN">简体中文</option>
            <option value="en-US">English</option>
          </select>
        </div>

        {/* 自动保存 */}
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700">
            自动保存
          </label>
          <input
            type="checkbox"
            checked={settings.autoSave}
            onChange={(e) => setSettings({ ...settings, autoSave: e.target.checked })}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
        </div>

        {/* 通知设置 */}
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700">
            桌面通知
          </label>
          <input
            type="checkbox"
            checked={settings.notifications}
            onChange={(e) => setSettings({ ...settings, notifications: e.target.checked })}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
        </div>
      </div>

      <Button className="w-full">
        保存设置
      </Button>
    </div>
  );
}

function AIConfigTab() {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">AI 配置</h3>
      
      <div className="text-center py-8">
        <div className="text-4xl mb-4">🤖</div>
        <p className="text-gray-600 mb-4">
          AI 配置功能即将推出
        </p>
        <p className="text-sm text-gray-500">
          您将能够配置 OpenAI、Ollama、Gemini 等 AI 服务
        </p>
      </div>

      <div className="space-y-4">
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">OpenAI</h4>
          <p className="text-sm text-blue-700">
            配置 OpenAI API 密钥和模型选择
          </p>
        </div>

        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <h4 className="font-medium text-green-900 mb-2">Ollama</h4>
          <p className="text-sm text-green-700">
            连接本地 Ollama 服务器
          </p>
        </div>

        <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
          <h4 className="font-medium text-purple-900 mb-2">Gemini</h4>
          <p className="text-sm text-purple-700">
            配置 Google Gemini API
          </p>
        </div>
      </div>
    </div>
  );
}