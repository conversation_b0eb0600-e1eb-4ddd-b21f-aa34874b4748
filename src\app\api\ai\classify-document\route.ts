/**
 * AI 文档分类 API 路由
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { aiDocumentClassifier } from '@/lib/services/ai-document-classifier';
import { z } from 'zod';

// 请求参数验证模式
const classifyRequestSchema = z.object({
  documentId: z.string().min(1, '文档ID不能为空'),
  options: z.object({
    enableAutoClassification: z.boolean().optional(),
    enableFolderSuggestions: z.boolean().optional(),
    enableRelatedDocuments: z.boolean().optional(),
    minConfidenceThreshold: z.number().min(0).max(1).optional(),
    maxSuggestions: z.number().min(1).max(50).optional(),
    analysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).optional()
  }).optional()
});

const batchClassifyRequestSchema = z.object({
  documentIds: z.array(z.string()).min(1, '至少需要一个文档ID').max(20, '最多支持20个文档'),
  options: z.object({
    enableAutoClassification: z.boolean().optional(),
    enableFolderSuggestions: z.boolean().optional(),
    enableRelatedDocuments: z.boolean().optional(),
    minConfidenceThreshold: z.number().min(0).max(1).optional(),
    maxSuggestions: z.number().min(1).max(50).optional(),
    analysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).optional()
  }).optional()
});

/**
 * POST /api/ai/classify-document
 * 分类单个文档
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { documentId, options } = classifyRequestSchema.parse(body);

    // 执行文档分类
    const classification = await aiDocumentClassifier.classifyDocument(
      documentId,
      session.user.id,
      options
    );

    return NextResponse.json({
      success: true,
      data: classification
    });

  } catch (error) {
    console.error('文档分类失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '分类失败' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/ai/classify-document
 * 批量分类文档
 */
export async function PUT(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { documentIds, options } = batchClassifyRequestSchema.parse(body);

    // 执行批量分类
    const result = await aiDocumentClassifier.batchClassifyDocuments({
      documentIds,
      options: options || {},
      userId: session.user.id
    });

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('批量文档分类失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '批量分类失败' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/ai/classify-document/categories
 * 获取建议的分类列表
 */
export async function GET() {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取建议分类
    const categories = await aiDocumentClassifier.getSuggestedCategories(session.user.id);

    return NextResponse.json({
      success: true,
      data: categories
    });

  } catch (error) {
    console.error('获取建议分类失败:', error);
    
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '获取分类失败' },
      { status: 500 }
    );
  }
}
