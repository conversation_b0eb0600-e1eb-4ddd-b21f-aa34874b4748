// 测试文件夹创建的脚本
const { PrismaClient } = require('@prisma/client');

async function testFolderCreation() {
  const prisma = new PrismaClient();

  try {
    console.log('测试文件夹创建...');

    // 获取现有用户
    const users = await prisma.user.findMany();
    console.log('现有用户:', users);

    if (users.length === 0) {
      console.log('没有用户，无法测试');
      return;
    }

    const testUser = users[0];
    console.log('使用用户:', testUser.email);

    // 尝试创建文件夹
    const folder = await prisma.folder.create({
      data: {
        name: 'Test Folder',
        userId: testUser.id,
      }
    });

    console.log('✅ 文件夹创建成功:', folder);

    // 清理测试数据
    await prisma.folder.delete({
      where: { id: folder.id }
    });

    console.log('✅ 测试完成');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testFolderCreation();
