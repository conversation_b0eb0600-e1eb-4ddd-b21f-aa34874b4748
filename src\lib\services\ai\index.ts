/**
 * AI 服务模块统一入口
 * 导出所有 AI 服务相关的类和接口
 */

// 基础接口和类型
export type {
  IAIService
} from './base-ai-service';

export {
  BaseAIService
} from './base-ai-service';

// 具体服务实现
export { OpenAIService } from './openai-service';
export { OllamaService } from './ollama-service';
export { GeminiService } from './gemini-service';

// 服务工厂和管理器
export {
  AIServiceFactory,
  AIServiceManager,
  aiServiceManager
} from './ai-service-factory';

// 类型定义
export type {
  AIProvider,
  AIServiceConfig,
  AIRequest,
  AIResponse,
  RewriteOptions,
  TextAnalysis,
  RetryConfig
} from '@/types/ai.types';

export {
  AIErrorType,
  AIServiceError
} from '@/types/ai.types';

// 文本生成和改写服务
export {
  TextGenerationService,
  createTextGenerationService
} from './text-generation-service';

export {
  TextRewriteService,
  createTextRewriteService
} from './text-rewrite-service';

export {
  DocumentAnalysisService,
  createDocumentAnalysisService
} from './document-analysis-service';

export {
  TranslationExplanationService,
  createTranslationExplanationService
} from './translation-explanation-service';

export type {
  TextRewriteRequest,
  TextRewriteResult,
  TextChange,
  RewriteStyle,
  RewriteType
} from './text-rewrite-service';

export type {
  TextGenerationRequest,
  TextGenerationResult
} from './text-generation-service';

export type {
  DocumentAnalysisRequest,
  DocumentAnalysisResult,
  DocumentSummary,
  KeywordExtraction,
  DocumentOutline,
  ContentAnalysis,
  DocumentAnalysisType,
  SummaryLength
} from './document-analysis-service';

export type {
  TranslationRequest,
  TranslationResult,
  ExplanationRequest,
  ExplanationResult,
  CustomInstructionRequest,
  CustomInstructionResult,
  CreativeWritingRequest,
  CreativeWritingResult,
  SupportedLanguage,
  TranslationQuality,
  ExplanationType,
  ContentFormat,
  CreativeWritingType
} from './translation-explanation-service';

export {
  SUPPORTED_LANGUAGES
} from './translation-explanation-service';

// 便捷函数
export {
  createAIService,
  getDefaultAIService,
  validateAIConfig,
  testAIConnection,
  formatAIError,
  getRecommendedModels,
  estimateTokens
} from './ai-utils';