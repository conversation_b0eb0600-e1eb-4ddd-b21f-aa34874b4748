/**
 * AI 文档分析服务
 * 专门处理文档摘要、关键词提取、大纲生成和内容分析功能
 */

import { IAIService } from './base-ai-service';
import { aiServiceManager } from './ai-service-factory';
import { AIRequest, AIResponse, AIServiceError, AIErrorType } from '@/types/ai.types';

/**
 * 文档分析类型
 */
export type DocumentAnalysisType = 
  | 'summary'     // 摘要生成
  | 'keywords'    // 关键词提取
  | 'outline'     // 大纲生成
  | 'analysis'    // 内容分析
  | 'topics';     // 主题分析

/**
 * 摘要长度选项
 */
export type SummaryLength = 'short' | 'medium' | 'long';

/**
 * 文档分析请求参数
 */
export interface DocumentAnalysisRequest {
  /** 文档内容 */
  content: string;
  /** 分析类型 */
  type: DocumentAnalysisType;
  /** 摘要长度（仅用于摘要生成） */
  summaryLength?: SummaryLength;
  /** 关键词数量（仅用于关键词提取） */
  keywordCount?: number;
  /** 大纲层级深度（仅用于大纲生成） */
  outlineDepth?: number;
  /** 文档标题（可选，用于更好的分析） */
  title?: string;
  /** 特殊要求 */
  instructions?: string;
}

/**
 * 文档摘要结果
 */
export interface DocumentSummary {
  /** 摘要内容 */
  summary: string;
  /** 摘要长度 */
  length: SummaryLength;
  /** 原文字数 */
  originalWordCount: number;
  /** 摘要字数 */
  summaryWordCount: number;
  /** 压缩比例 */
  compressionRatio: number;
}

/**
 * 关键词提取结果
 */
export interface KeywordExtraction {
  /** 关键词列表 */
  keywords: string[];
  /** 主题标签 */
  topics: string[];
  /** 重要概念 */
  concepts: string[];
  /** 专业术语 */
  terms: string[];
}

/**
 * 文档大纲结果
 */
export interface DocumentOutline {
  /** 大纲项目 */
  items: OutlineItem[];
  /** 大纲层级深度 */
  depth: number;
  /** 总章节数 */
  totalSections: number;
}

/**
 * 大纲项目
 */
export interface OutlineItem {
  /** 标题 */
  title: string;
  /** 层级（1-6） */
  level: number;
  /** 内容摘要 */
  summary?: string;
  /** 子项目 */
  children?: OutlineItem[];
  /** 在原文中的位置 */
  position?: number;
}

/**
 * 内容分析结果
 */
export interface ContentAnalysis {
  /** 主要主题 */
  mainTopics: string[];
  /** 语调分析 */
  tone: {
    /** 整体语调 */
    overall: 'formal' | 'informal' | 'neutral' | 'academic' | 'conversational';
    /** 情感倾向 */
    sentiment: 'positive' | 'negative' | 'neutral';
    /** 专业程度 */
    professionalism: 'high' | 'medium' | 'low';
  };
  /** 结构分析 */
  structure: {
    /** 文档类型 */
    type: 'article' | 'report' | 'essay' | 'documentation' | 'other';
    /** 组织方式 */
    organization: 'chronological' | 'logical' | 'comparative' | 'descriptive' | 'mixed';
    /** 段落数量 */
    paragraphCount: number;
    /** 平均段落长度 */
    avgParagraphLength: number;
  };
  /** 可读性分析 */
  readability: {
    /** 可读性评分（0-100） */
    score: number;
    /** 难度等级 */
    level: 'elementary' | 'middle' | 'high' | 'college' | 'graduate';
    /** 建议改进点 */
    suggestions: string[];
  };
  /** 内容质量评估 */
  quality: {
    /** 整体评分（0-100） */
    score: number;
    /** 优点 */
    strengths: string[];
    /** 改进建议 */
    improvements: string[];
  };
}

/**
 * 文档分析结果
 */
export interface DocumentAnalysisResult {
  /** 分析类型 */
  type: DocumentAnalysisType;
  /** 摘要结果（如果是摘要分析） */
  summary?: DocumentSummary;
  /** 关键词结果（如果是关键词分析） */
  keywords?: KeywordExtraction;
  /** 大纲结果（如果是大纲分析） */
  outline?: DocumentOutline;
  /** 内容分析结果（如果是内容分析） */
  analysis?: ContentAnalysis;
  /** 使用的令牌数 */
  tokensUsed: number;
  /** 响应时间 */
  responseTime: number;
  /** 生成的唯一ID */
  id: string;
  /** 分析时间 */
  timestamp: Date;
}

/**
 * AI 文档分析服务类
 */
export class DocumentAnalysisService {
  private aiService: IAIService;
  
  constructor(aiService?: IAIService) {
    this.aiService = aiService || aiServiceManager.getDefaultService();
  }
  
  /**
   * 执行文档分析
   * @param request 分析请求参数
   * @returns 分析结果
   */
  async analyzeDocument(request: DocumentAnalysisRequest): Promise<DocumentAnalysisResult> {
    try {
      const prompt = this.buildAnalysisPrompt(request);
      const aiRequest: AIRequest = {
        prompt,
        maxTokens: this.getMaxTokensForType(request.type),
        temperature: this.getTemperatureForType(request.type)
      };
      
      const startTime = Date.now();
      const response = await this.aiService.generateText(aiRequest);
      const responseTime = Date.now() - startTime;
      
      // 解析分析结果
      const parsedResult = await this.parseAnalysisResponse(response.content, request);
      
      return {
        type: request.type,
        ...parsedResult,
        tokensUsed: response.tokensUsed,
        responseTime,
        id: this.generateId(),
        timestamp: new Date()
      };
    } catch (error) {
      if (error instanceof AIServiceError) {
        throw error;
      }
      throw new AIServiceError(
        AIErrorType.UNKNOWN_ERROR,
        `文档分析失败: ${error instanceof Error ? error.message : '未知错误'}`,
        this.aiService.provider,
        error instanceof Error ? error : undefined
      );
    }
  }
  
  /**
   * 生成文档摘要
   * @param content 文档内容
   * @param length 摘要长度
   * @param title 文档标题
   * @returns 摘要结果
   */
  async generateSummary(
    content: string, 
    length: SummaryLength = 'medium',
    title?: string
  ): Promise<DocumentAnalysisResult> {
    return this.analyzeDocument({
      content,
      type: 'summary',
      summaryLength: length,
      title
    });
  }
  
  /**
   * 提取关键词
   * @param content 文档内容
   * @param count 关键词数量
   * @param title 文档标题
   * @returns 关键词提取结果
   */
  async extractKeywords(
    content: string, 
    count: number = 10,
    title?: string
  ): Promise<DocumentAnalysisResult> {
    return this.analyzeDocument({
      content,
      type: 'keywords',
      keywordCount: count,
      title
    });
  }
  
  /**
   * 生成文档大纲
   * @param content 文档内容
   * @param depth 大纲深度
   * @param title 文档标题
   * @returns 大纲生成结果
   */
  async generateOutline(
    content: string, 
    depth: number = 3,
    title?: string
  ): Promise<DocumentAnalysisResult> {
    return this.analyzeDocument({
      content,
      type: 'outline',
      outlineDepth: depth,
      title
    });
  }
  
  /**
   * 分析文档内容
   * @param content 文档内容
   * @param title 文档标题
   * @returns 内容分析结果
   */
  async analyzeContent(
    content: string,
    title?: string
  ): Promise<DocumentAnalysisResult> {
    return this.analyzeDocument({
      content,
      type: 'analysis',
      title
    });
  }
  
  /**
   * 构建分析提示
   */
  private buildAnalysisPrompt(request: DocumentAnalysisRequest): string {
    const { content, type, title } = request;
    
    let prompt = '';
    const titleInfo = title ? `文档标题：${title}\n\n` : '';
    
    switch (type) {
      case 'summary':
        prompt = this.buildSummaryPrompt(content, request.summaryLength, titleInfo);
        break;
      case 'keywords':
        prompt = this.buildKeywordsPrompt(content, request.keywordCount, titleInfo);
        break;
      case 'outline':
        prompt = this.buildOutlinePrompt(content, request.outlineDepth, titleInfo);
        break;
      case 'analysis':
        prompt = this.buildAnalysisPrompt2(content, titleInfo);
        break;
      case 'topics':
        prompt = this.buildTopicsPrompt(content, titleInfo);
        break;
    }
    
    // 添加特殊要求
    if (request.instructions) {
      prompt += `\n\n特殊要求：${request.instructions}`;
    }
    
    return prompt;
  }
  
  /**
   * 构建摘要提示
   */
  private buildSummaryPrompt(content: string, length?: SummaryLength, titleInfo?: string): string {
    const lengthMap = {
      short: '100-150字',
      medium: '200-300字',
      long: '400-500字'
    };
    
    const targetLength = lengthMap[length || 'medium'];
    
    return `${titleInfo}请为以下文档生成摘要，要求：
- 摘要长度：${targetLength}
- 保留核心观点和重要信息
- 语言简洁明了，逻辑清晰
- 突出文档的主要价值和结论

文档内容：
${content}

请按以下格式输出：
摘要：[摘要内容]
原文字数：[数字]
摘要字数：[数字]
压缩比例：[百分比]`;
  }
  
  /**
   * 构建关键词提示
   */
  private buildKeywordsPrompt(content: string, count?: number, titleInfo?: string): string {
    const keywordCount = count || 10;
    
    return `${titleInfo}请分析以下文档并提取关键信息：
- 提取${keywordCount}个最重要的关键词
- 识别3-5个主要主题
- 找出重要概念和专业术语
- 按重要性排序

文档内容：
${content}

请按以下格式输出：
关键词：[关键词1, 关键词2, ...]
主题标签：[主题1, 主题2, ...]
重要概念：[概念1, 概念2, ...]
专业术语：[术语1, 术语2, ...]`;
  }
  
  /**
   * 构建大纲提示
   */
  private buildOutlinePrompt(content: string, depth?: number, titleInfo?: string): string {
    const outlineDepth = depth || 3;
    
    return `${titleInfo}请为以下文档生成结构化大纲：
- 大纲层级深度：${outlineDepth}级
- 每个章节包含简要说明
- 保持逻辑层次清晰
- 体现文档的整体结构

文档内容：
${content}

请按以下格式输出：
大纲：
1. [一级标题]
   - [简要说明]
   1.1 [二级标题]
       - [简要说明]
       1.1.1 [三级标题]
             - [简要说明]
总章节数：[数字]`;
  }
  
  /**
   * 构建内容分析提示
   */
  private buildAnalysisPrompt2(content: string, titleInfo?: string): string {
    return `${titleInfo}请对以下文档进行全面分析：

文档内容：
${content}

请按以下格式输出详细分析：

主要主题：[主题1, 主题2, 主题3]

语调分析：
- 整体语调：[formal/informal/neutral/academic/conversational]
- 情感倾向：[positive/negative/neutral]
- 专业程度：[high/medium/low]

结构分析：
- 文档类型：[article/report/essay/documentation/other]
- 组织方式：[chronological/logical/comparative/descriptive/mixed]
- 段落数量：[数字]
- 平均段落长度：[数字]字

可读性分析：
- 可读性评分：[0-100]
- 难度等级：[elementary/middle/high/college/graduate]
- 改进建议：[建议1, 建议2, ...]

内容质量：
- 整体评分：[0-100]
- 优点：[优点1, 优点2, ...]
- 改进建议：[建议1, 建议2, ...]`;
  }
  
  /**
   * 构建主题分析提示
   */
  private buildTopicsPrompt(content: string, titleInfo?: string): string {
    return `${titleInfo}请分析以下文档的主题分布：

文档内容：
${content}

请按以下格式输出：
主要主题：[主题1, 主题2, 主题3]
次要主题：[主题4, 主题5, ...]
主题关联：[主题间的关系描述]`;
  }
  
  /**
   * 解析分析响应
   */
  private async parseAnalysisResponse(
    content: string, 
    request: DocumentAnalysisRequest
  ): Promise<Partial<DocumentAnalysisResult>> {
    switch (request.type) {
      case 'summary':
        return { summary: this.parseSummaryResponse(content, request) };
      case 'keywords':
        return { keywords: this.parseKeywordsResponse(content) };
      case 'outline':
        return { outline: this.parseOutlineResponse(content) };
      case 'analysis':
        return { analysis: this.parseContentAnalysisResponse(content) };
      default:
        return {};
    }
  }
  
  /**
   * 解析摘要响应
   */
  private parseSummaryResponse(content: string, request: DocumentAnalysisRequest): DocumentSummary {
    const summaryMatch = content.match(/摘要：\s*(.+?)(?=\n|$)/);
    const originalWordMatch = content.match(/原文字数：\s*(\d+)/);
    const summaryWordMatch = content.match(/摘要字数：\s*(\d+)/);
    const compressionMatch = content.match(/压缩比例：\s*(\d+(?:\.\d+)?)%/);
    
    const summary = summaryMatch ? summaryMatch[1].trim() : content.trim();
    const originalWordCount = originalWordMatch ? parseInt(originalWordMatch[1]) : request.content.length;
    const summaryWordCount = summaryWordMatch ? parseInt(summaryWordMatch[1]) : summary.length;
    const compressionRatio = compressionMatch ? parseFloat(compressionMatch[1]) : 
      Math.round((summaryWordCount / originalWordCount) * 100);
    
    return {
      summary,
      length: request.summaryLength || 'medium',
      originalWordCount,
      summaryWordCount,
      compressionRatio
    };
  }
  
  /**
   * 解析关键词响应
   */
  private parseKeywordsResponse(content: string): KeywordExtraction {
    const keywordsMatch = content.match(/关键词：\s*(.+?)(?=\n|$)/);
    const topicsMatch = content.match(/主题标签：\s*(.+?)(?=\n|$)/);
    const conceptsMatch = content.match(/重要概念：\s*(.+?)(?=\n|$)/);
    const termsMatch = content.match(/专业术语：\s*(.+?)(?=\n|$)/);
    
    const parseList = (text: string | undefined): string[] => {
      if (!text) return [];
      return text.split(/[,，、]/).map(item => item.trim()).filter(item => item.length > 0);
    };
    
    return {
      keywords: parseList(keywordsMatch?.[1]),
      topics: parseList(topicsMatch?.[1]),
      concepts: parseList(conceptsMatch?.[1]),
      terms: parseList(termsMatch?.[1])
    };
  }
  
  /**
   * 解析大纲响应
   */
  private parseOutlineResponse(content: string): DocumentOutline {
    const outlineMatch = content.match(/大纲：\s*([\s\S]+?)(?=总章节数：|$)/);
    const totalMatch = content.match(/总章节数：\s*(\d+)/);
    
    const outlineText = outlineMatch ? outlineMatch[1].trim() : content;
    const items = this.parseOutlineItems(outlineText);
    
    return {
      items,
      depth: this.calculateOutlineDepth(items),
      totalSections: totalMatch ? parseInt(totalMatch[1]) : items.length
    };
  }
  
  /**
   * 解析大纲项目
   */
  private parseOutlineItems(text: string): OutlineItem[] {
    const lines = text.split('\n').filter(line => line.trim());
    const items: OutlineItem[] = [];
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed) continue;
      
      // 匹配不同层级的标题
      const match = trimmed.match(/^(\d+(?:\.\d+)*)\s+(.+?)(?:\s*-\s*(.+))?$/);
      if (match) {
        const [, number, title, summary] = match;
        const level = number.split('.').length;
        
        items.push({
          title: title.trim(),
          level,
          summary: summary?.trim(),
          position: 0 // 暂时设为0，实际应用中可以计算真实位置
        });
      }
    }
    
    return items;
  }
  
  /**
   * 计算大纲深度
   */
  private calculateOutlineDepth(items: OutlineItem[]): number {
    return Math.max(...items.map(item => item.level), 0);
  }
  
  /**
   * 解析内容分析响应
   */
  private parseContentAnalysisResponse(content: string): ContentAnalysis {
    // 解析主要主题
    const topicsMatch = content.match(/主要主题：\s*(.+?)(?=\n|$)/);
    const mainTopics = topicsMatch ? 
      topicsMatch[1].split(/[,，、]/).map(t => t.trim()).filter(t => t.length > 0) : [];
    
    // 解析语调分析
    const overallToneMatch = content.match(/整体语调：\s*(\w+)/);
    const sentimentMatch = content.match(/情感倾向：\s*(\w+)/);
    const professionalismMatch = content.match(/专业程度：\s*(\w+)/);
    
    // 解析结构分析
    const docTypeMatch = content.match(/文档类型：\s*(\w+)/);
    const organizationMatch = content.match(/组织方式：\s*(\w+)/);
    const paragraphCountMatch = content.match(/段落数量：\s*(\d+)/);
    const avgLengthMatch = content.match(/平均段落长度：\s*(\d+)/);
    
    // 解析可读性分析
    const readabilityScoreMatch = content.match(/可读性评分：\s*(\d+)/);
    const difficultyMatch = content.match(/难度等级：\s*(\w+)/);
    const readabilitySuggestionsMatch = content.match(/改进建议：\s*(.+?)(?=\n内容质量：|$)/);
    
    // 解析内容质量
    const qualityScoreMatch = content.match(/整体评分：\s*(\d+)/);
    const strengthsMatch = content.match(/优点：\s*(.+?)(?=\n.*改进建议：|$)/);
    const improvementsMatch = content.match(/改进建议：\s*(.+?)$/);
    
    const parseList = (text: string | undefined): string[] => {
      if (!text) return [];
      return text.split(/[,，、]/).map(item => item.trim()).filter(item => item.length > 0);
    };
    
    return {
      mainTopics,
      tone: {
        overall: (overallToneMatch?.[1] as any) || 'neutral',
        sentiment: (sentimentMatch?.[1] as any) || 'neutral',
        professionalism: (professionalismMatch?.[1] as any) || 'medium'
      },
      structure: {
        type: (docTypeMatch?.[1] as any) || 'other',
        organization: (organizationMatch?.[1] as any) || 'mixed',
        paragraphCount: paragraphCountMatch ? parseInt(paragraphCountMatch[1]) : 0,
        avgParagraphLength: avgLengthMatch ? parseInt(avgLengthMatch[1]) : 0
      },
      readability: {
        score: readabilityScoreMatch ? parseInt(readabilityScoreMatch[1]) : 50,
        level: (difficultyMatch?.[1] as any) || 'middle',
        suggestions: parseList(readabilitySuggestionsMatch?.[1])
      },
      quality: {
        score: qualityScoreMatch ? parseInt(qualityScoreMatch[1]) : 50,
        strengths: parseList(strengthsMatch?.[1]),
        improvements: parseList(improvementsMatch?.[1])
      }
    };
  }
  
  /**
   * 根据分析类型获取最大令牌数
   */
  private getMaxTokensForType(type: DocumentAnalysisType): number {
    switch (type) {
      case 'summary':
        return 800;
      case 'keywords':
        return 400;
      case 'outline':
        return 1000;
      case 'analysis':
        return 1500;
      case 'topics':
        return 600;
      default:
        return 800;
    }
  }
  
  /**
   * 根据分析类型获取温度参数
   */
  private getTemperatureForType(type: DocumentAnalysisType): number {
    switch (type) {
      case 'summary':
        return 0.3;
      case 'keywords':
        return 0.2;
      case 'outline':
        return 0.4;
      case 'analysis':
        return 0.5;
      case 'topics':
        return 0.4;
      default:
        return 0.3;
    }
  }
  
  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 创建文档分析服务实例
 */
export function createDocumentAnalysisService(aiService?: IAIService): DocumentAnalysisService {
  return new DocumentAnalysisService(aiService);
}