'use client';

import { useState } from 'react';
import { Editor } from '@/components/editor/Editor';
import { MenuBar } from '@/components/editor/MenuBar';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import { SlashCommandExtension } from '@/lib/editor/slash-command-extension';
import { SLASH_COMMANDS } from '@/lib/editor/slash-commands';

/**
 * 斜杠命令演示页面
 * 用于测试和展示斜杠命令功能
 */
export default function SlashCommandDemoPage() {
  const [content, setContent] = useState('<p>欢迎使用斜杠命令演示！</p><p>输入 "/" 来查看可用的命令。</p>');

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: { levels: [1, 2, 3] },
        bulletList: { keepMarks: true, keepAttributes: false },
        orderedList: { keepMarks: true, keepAttributes: false },
        codeBlock: { HTMLAttributes: { class: 'code-block' } },
      }),
      Placeholder.configure({
        placeholder: '开始写作... (输入 "/" 查看命令)',
        emptyEditorClass: 'is-editor-empty',
      }),
      CharacterCount.configure({ limit: null }),
      SlashCommandExtension.configure({
        commands: SLASH_COMMANDS,
        trigger: '/',
        allowSpaces: false,
        startOfLine: false,
        char: '/',
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      setContent(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
      },
    },
  });

  return (
    <div className="min-h-screen bg-background">
      {/* 页面标题 */}
      <div className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-6">
          <h1 className="text-3xl font-bold text-foreground">斜杠命令演示</h1>
          <p className="text-muted-foreground mt-2">
            测试和体验斜杠命令功能。输入 "/" 来查看可用的命令。
          </p>
        </div>
      </div>

      {/* 编辑器区域 */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* 菜单栏 */}
          <MenuBar editor={editor} />
          
          {/* 编辑器 */}
          <div className="border border-border rounded-lg bg-card">
            <Editor
              content={content}
              onChange={setContent}
              placeholder="开始写作... (输入 &quot;/&quot; 查看命令)"
              className="min-h-[500px]"
            />
          </div>
          
          {/* 使用说明 */}
          <div className="mt-8 p-6 bg-muted/50 rounded-lg">
            <h2 className="text-lg font-semibold mb-4">使用说明</h2>
            <div className="space-y-3 text-sm text-muted-foreground">
              <div className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded text-xs flex items-center justify-center font-medium">1</span>
                <div>
                  <strong>触发斜杠命令：</strong>在编辑器中输入 "/" 字符
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded text-xs flex items-center justify-center font-medium">2</span>
                <div>
                  <strong>搜索命令：</strong>继续输入文字来过滤命令列表
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded text-xs flex items-center justify-center font-medium">3</span>
                <div>
                  <strong>选择命令：</strong>使用上下箭头键或鼠标选择命令
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded text-xs flex items-center justify-center font-medium">4</span>
                <div>
                  <strong>执行命令：</strong>按 Enter 键或点击命令来执行
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded text-xs flex items-center justify-center font-medium">5</span>
                <div>
                  <strong>表格操作：</strong>选中表格时会显示表格工具栏，可以添加/删除行列
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded text-xs flex items-center justify-center font-medium">6</span>
                <div>
                  <strong>取消命令：</strong>按 Escape 键或点击其他地方来关闭菜单
                </div>
              </div>
            </div>
          </div>

          {/* 新功能展示 */}
          <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
            <h2 className="text-lg font-semibold mb-4 text-blue-900">🆕 最新功能</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="space-y-2">
                <h3 className="font-medium text-blue-800">基础格式化</h3>
                <ul className="space-y-1 text-blue-700">
                  <li>• 行内代码格式 (`)</li>
                  <li>• 粗体格式 (B)</li>
                  <li>• 斜体格式 (I)</li>
                  <li>• 删除线格式 (S)</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium text-purple-800">高级功能</h3>
                <ul className="space-y-1 text-purple-700">
                  <li>• 表格插入和编辑 (📊)</li>
                  <li>• 水平分割线 (—)</li>
                  <li>• 任务列表 (☑️)</li>
                  <li>• 多种提示框 (ℹ️⚠️✅❌)</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium text-green-800">媒体插入</h3>
                <ul className="space-y-1 text-green-700">
                  <li>• 图片上传和插入 (🖼️)</li>
                  <li>• 链接插入和编辑 (🔗)</li>
                  <li>• PDF 文档插入 (📄)</li>
                  <li>• 视频插入 (🎥) 即将推出</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 媒体功能说明 */}
          <div className="mt-8 p-6 bg-gradient-to-r from-green-50 to-teal-50 rounded-lg border border-green-200">
            <h2 className="text-lg font-semibold mb-4 text-green-900">📁 媒体插入功能</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-green-800 mb-2">🖼️ 图片插入</h4>
                  <ul className="space-y-1 text-green-700 text-xs">
                    <li>• 支持文件上传和 URL 输入</li>
                    <li>• 拖拽上传支持</li>
                    <li>• 图片预览和 Alt 文本设置</li>
                    <li>• 支持 JPG、PNG、GIF 格式</li>
                    <li>• 文件大小限制 5MB</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-green-800 mb-2">🔗 链接插入</h4>
                  <ul className="space-y-1 text-green-700 text-xs">
                    <li>• 链接 URL 和显示文本设置</li>
                    <li>• 链接标题和打开方式配置</li>
                    <li>• 现有链接编辑和移除</li>
                    <li>• URL 验证和预览</li>
                    <li>• 快捷键 Ctrl+K</li>
                  </ul>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-green-800 mb-2">📄 PDF 插入</h4>
                  <ul className="space-y-1 text-green-700 text-xs">
                    <li>• PDF 文件上传和 URL 输入</li>
                    <li>• 链接卡片和嵌入预览两种模式</li>
                    <li>• PDF 标题和描述设置</li>
                    <li>• 文件大小限制 10MB</li>
                    <li>• 支持主流 PDF 服务链接</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-green-800 mb-2">🎥 视频插入</h4>
                  <ul className="space-y-1 text-green-700 text-xs">
                    <li>• 即将推出</li>
                    <li>• 将支持视频文件上传</li>
                    <li>• 将支持 YouTube、Vimeo 等</li>
                    <li>• 将支持视频预览和播放</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* 可用命令列表 */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            {SLASH_COMMANDS.map((category) => (
              <div key={category.name} className="p-6 bg-card border border-border rounded-lg">
                <h3 className="text-lg font-semibold mb-4">{category.name}</h3>
                <div className="space-y-3">
                  {category.commands.map((command) => (
                    <div key={command.id} className="flex items-center gap-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-muted rounded flex items-center justify-center text-sm">
                        {command.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm">{command.label}</div>
                        <div className="text-xs text-muted-foreground truncate">
                          {command.description}
                        </div>
                      </div>
                      {command.shortcut && (
                        <div className="flex-shrink-0 text-xs text-muted-foreground font-mono bg-muted px-2 py-1 rounded">
                          {command.shortcut}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* 内容预览 */}
          <div className="mt-8 p-6 bg-muted/30 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">内容预览 (HTML)</h3>
            <pre className="text-xs bg-background p-4 rounded border overflow-x-auto">
              <code>{content}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}