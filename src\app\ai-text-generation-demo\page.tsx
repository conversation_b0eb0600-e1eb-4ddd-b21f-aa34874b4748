'use client';

import React, { useState } from 'react';
import { Editor } from '@/components/editor/Editor';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Sparkles, Zap, FileText, Settings } from 'lucide-react';

/**
 * AI 文本生成功能演示页面
 */
export default function AITextGenerationDemoPage() {
  const [content, setContent] = useState(`# AI 文本生成功能演示

欢迎使用 AI 文本生成功能！这个编辑器集成了强大的 AI 写作辅助功能。

## 如何使用

### 1. 续写功能
将光标放在任意段落末尾，然后：
- 输入 \`/ai-continue\` 或 \`/AI 续写\`
- 使用快捷键 \`Ctrl+Shift+G\`
- AI 将基于上下文继续写作

### 2. 扩展功能
选中一段文本，然后：
- 输入 \`/ai-expand\` 或 \`/AI 扩展\`
- 使用快捷键 \`Ctrl+Shift+E\`
- AI 将扩展和丰富选中的内容

### 3. 改写功能
选中需要改写的文本，然后：
- 输入 \`/ai-rewrite\` 或 \`/AI 改写\`
- AI 将提供多个改写版本供选择

## 示例内容

这是一个简单的段落，你可以尝试在这里使用 AI 续写功能。

人工智能正在改变我们的工作方式。`);

  const [aiEnabled, setAiEnabled] = useState(true);

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="w-8 h-8 text-blue-500" />
            <h1 className="text-3xl font-bold text-gray-900">
              AI 文本生成功能演示
            </h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            体验强大的 AI 写作辅助功能，包括智能续写、内容扩展和文本改写
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 功能说明卡片 */}
          <div className="lg:col-span-1 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5 text-yellow-500" />
                  快捷操作
                </CardTitle>
                <CardDescription>
                  快速访问 AI 功能
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <Badge variant="outline" className="w-full justify-start">
                    <kbd className="mr-2">Ctrl+Shift+G</kbd>
                    AI 续写
                  </Badge>
                  <Badge variant="outline" className="w-full justify-start">
                    <kbd className="mr-2">Ctrl+Shift+E</kbd>
                    AI 扩展
                  </Badge>
                  <Badge variant="outline" className="w-full justify-start">
                    <kbd className="mr-2">/ai-continue</kbd>
                    斜杠命令
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5 text-green-500" />
                  使用技巧
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="p-2 bg-blue-50 rounded">
                  <strong>续写：</strong>将光标放在段落末尾，AI 会基于上下文继续写作
                </div>
                <div className="p-2 bg-green-50 rounded">
                  <strong>扩展：</strong>选中文本后使用，AI 会添加更多细节和说明
                </div>
                <div className="p-2 bg-purple-50 rounded">
                  <strong>改写：</strong>选中文本后使用，AI 会提供多个改写版本
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5 text-gray-500" />
                  设置
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-sm">启用 AI 功能</span>
                  <Button
                    variant={aiEnabled ? "default" : "outline"}
                    size="sm"
                    onClick={() => setAiEnabled(!aiEnabled)}
                  >
                    {aiEnabled ? "已启用" : "已禁用"}
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  {aiEnabled 
                    ? "AI 功能已启用，可以使用所有 AI 写作辅助功能" 
                    : "AI 功能已禁用，相关功能将不可用"
                  }
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 编辑器区域 */}
          <div className="lg:col-span-3">
            <Card className="h-full">
              <CardHeader>
                <CardTitle>智能编辑器</CardTitle>
                <CardDescription>
                  在下方编辑器中尝试 AI 文本生成功能
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg overflow-hidden">
                  <Editor
                    content={content}
                    onChange={handleContentChange}
                    placeholder="开始写作，或使用 AI 功能辅助创作..."
                    enableAI={aiEnabled}
                    className="min-h-[600px]"
                  />
                </div>
                
                {/* 状态栏 */}
                <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
                  <div className="flex items-center gap-4">
                    <span>字数: {content.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length}</span>
                    <span>字符: {content.replace(/<[^>]*>/g, '').length}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${aiEnabled ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                    <span>AI {aiEnabled ? '已启用' : '已禁用'}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 功能演示说明 */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>功能演示说明</CardTitle>
              <CardDescription>
                了解如何充分利用 AI 文本生成功能
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-semibold">1</span>
                    </div>
                    <h3 className="font-semibold">AI 续写</h3>
                  </div>
                  <p className="text-sm text-gray-600">
                    将光标放在任意段落末尾，使用 <code>/ai-continue</code> 命令或快捷键 <code>Ctrl+Shift+G</code>，
                    AI 会基于当前内容的上下文智能续写。
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 font-semibold">2</span>
                    </div>
                    <h3 className="font-semibold">内容扩展</h3>
                  </div>
                  <p className="text-sm text-gray-600">
                    选中需要扩展的文本，使用 <code>/ai-expand</code> 命令或快捷键 <code>Ctrl+Shift+E</code>，
                    AI 会为选中内容添加更多细节和说明。
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 font-semibold">3</span>
                    </div>
                    <h3 className="font-semibold">文本改写</h3>
                  </div>
                  <p className="text-sm text-gray-600">
                    选中需要改写的文本，使用 <code>/ai-rewrite</code> 命令，
                    AI 会提供多个不同风格的改写版本供您选择。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}