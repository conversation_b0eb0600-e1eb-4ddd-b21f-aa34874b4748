/**
 * AI 批处理器
 * 优化 AI 服务调用的批处理和队列管理
 */

import { AIRequest, AIResponse, AIProvider, AIServiceError, AIErrorType } from '@/types/ai.types';
import { aiCacheManager } from './ai-cache-manager';

interface BatchJob {
  id: string;
  requests: AIRequest[];
  provider: AIProvider;
  priority: 'high' | 'medium' | 'low';
  createdAt: number;
  timeout: number;
  resolve: (responses: AIResponse[]) => void;
  reject: (error: Error) => void;
}

interface ProcessorConfig {
  /** 最大并发批次数 */
  maxConcurrentBatches: number;
  /** 批次大小 */
  batchSize: number;
  /** 批次超时时间（毫秒） */
  batchTimeout: number;
  /** 队列最大长度 */
  maxQueueLength: number;
  /** 重试配置 */
  retryConfig: {
    maxRetries: number;
    initialDelay: number;
    backoffMultiplier: number;
    maxDelay: number;
  };
}

interface ProcessorStats {
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  averageProcessingTime: number;
  queueLength: number;
  activeBatches: number;
  throughput: number; // 每秒处理的请求数
}

/**
 * AI 批处理器
 */
export class AIBatchProcessor {
  private jobQueue: BatchJob[] = [];
  private activeBatches = new Map<string, BatchJob>();
  private processingTimer?: NodeJS.Timeout;
  private stats: ProcessorStats = {
    totalJobs: 0,
    completedJobs: 0,
    failedJobs: 0,
    averageProcessingTime: 0,
    queueLength: 0,
    activeBatches: 0,
    throughput: 0,
  };
  private processingTimes: number[] = [];
  private throughputWindow: number[] = [];
  private lastThroughputUpdate = Date.now();

  constructor(
    private config: ProcessorConfig = {
      maxConcurrentBatches: 3,
      batchSize: 5,
      batchTimeout: 100,
      maxQueueLength: 100,
      retryConfig: {
        maxRetries: 3,
        initialDelay: 1000,
        backoffMultiplier: 2,
        maxDelay: 10000,
      },
    }
  ) {
    this.startProcessing();
    this.startStatsUpdater();
  }

  /**
   * 添加批处理任务
   */
  async addBatch(
    requests: AIRequest[],
    provider: AIProvider,
    executor: (requests: AIRequest[]) => Promise<AIResponse[]>,
    priority: 'high' | 'medium' | 'low' = 'medium',
    timeout: number = 30000
  ): Promise<AIResponse[]> {
    // 检查队列长度
    if (this.jobQueue.length >= this.config.maxQueueLength) {
      throw new AIServiceError(
        AIErrorType.QUOTA_EXCEEDED,
        '批处理队列已满，请稍后重试',
        provider
      );
    }

    return new Promise<AIResponse[]>((resolve, reject) => {
      const job: BatchJob = {
        id: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        requests,
        provider,
        priority,
        createdAt: Date.now(),
        timeout,
        resolve,
        reject,
      };

      // 按优先级插入队列
      this.insertByPriority(job);
      this.stats.totalJobs++;
      this.updateQueueStats();

      // 绑定执行器到任务
      (job as any).executor = executor;
    });
  }

  /**
   * 按优先级插入任务
   */
  private insertByPriority(job: BatchJob) {
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    const jobPriority = priorityOrder[job.priority];

    let insertIndex = this.jobQueue.length;
    for (let i = 0; i < this.jobQueue.length; i++) {
      const queuePriority = priorityOrder[this.jobQueue[i].priority];
      if (jobPriority < queuePriority) {
        insertIndex = i;
        break;
      }
    }

    this.jobQueue.splice(insertIndex, 0, job);
  }

  /**
   * 开始处理队列
   */
  private startProcessing() {
    this.processingTimer = setInterval(() => {
      this.processQueue();
    }, 10); // 高频检查
  }

  /**
   * 处理队列
   */
  private async processQueue() {
    // 检查是否可以处理新批次
    if (this.activeBatches.size >= this.config.maxConcurrentBatches || 
        this.jobQueue.length === 0) {
      return;
    }

    // 获取下一个任务
    const job = this.jobQueue.shift();
    if (!job) {
      return;
    }

    // 检查任务是否超时
    if (Date.now() - job.createdAt > job.timeout) {
      job.reject(new AIServiceError(
        AIErrorType.TIMEOUT,
        '批处理任务超时',
        job.provider
      ));
      this.stats.failedJobs++;
      return;
    }

    // 添加到活跃批次
    this.activeBatches.set(job.id, job);
    this.updateQueueStats();

    // 处理任务
    this.processBatch(job);
  }

  /**
   * 处理单个批次
   */
  private async processBatch(job: BatchJob, retryCount = 0) {
    const startTime = Date.now();
    const executor = (job as any).executor;

    try {
      // 检查缓存
      const cachedResponses: (AIResponse | null)[] = await Promise.all(
        job.requests.map(request => aiCacheManager.get(request, job.provider))
      );

      const uncachedRequests: AIRequest[] = [];
      const uncachedIndices: number[] = [];

      // 分离缓存命中和未命中的请求
      cachedResponses.forEach((response, index) => {
        if (!response) {
          uncachedRequests.push(job.requests[index]);
          uncachedIndices.push(index);
        }
      });

      let responses: AIResponse[] = [...cachedResponses] as AIResponse[];

      // 处理未缓存的请求
      if (uncachedRequests.length > 0) {
        const newResponses = await executor(uncachedRequests);
        
        // 缓存新响应
        await Promise.all(
          newResponses.map((response, index) => 
            aiCacheManager.set(uncachedRequests[index], response, job.provider)
          )
        );

        // 合并响应
        uncachedIndices.forEach((originalIndex, newIndex) => {
          responses[originalIndex] = newResponses[newIndex];
        });
      }

      // 记录处理时间
      const processingTime = Date.now() - startTime;
      this.recordProcessingTime(processingTime);

      // 完成任务
      job.resolve(responses);
      this.stats.completedJobs++;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.recordProcessingTime(processingTime);

      // 重试逻辑
      if (retryCount < this.config.retryConfig.maxRetries && 
          this.shouldRetry(error)) {
        
        const delay = Math.min(
          this.config.retryConfig.initialDelay * 
          Math.pow(this.config.retryConfig.backoffMultiplier, retryCount),
          this.config.retryConfig.maxDelay
        );

        setTimeout(() => {
          this.processBatch(job, retryCount + 1);
        }, delay);

        return;
      }

      // 失败处理
      const aiError = error instanceof AIServiceError 
        ? error 
        : new AIServiceError(
            AIErrorType.UNKNOWN_ERROR,
            error instanceof Error ? error.message : '批处理失败',
            job.provider,
            error
          );

      job.reject(aiError);
      this.stats.failedJobs++;
    } finally {
      // 从活跃批次中移除
      this.activeBatches.delete(job.id);
      this.updateQueueStats();
    }
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: any): boolean {
    if (error instanceof AIServiceError) {
      return [
        AIErrorType.NETWORK_ERROR,
        AIErrorType.TIMEOUT,
        AIErrorType.SERVICE_UNAVAILABLE,
      ].includes(error.type);
    }
    return false;
  }

  /**
   * 记录处理时间
   */
  private recordProcessingTime(time: number) {
    this.processingTimes.push(time);
    
    // 保持最近100次的记录
    if (this.processingTimes.length > 100) {
      this.processingTimes.shift();
    }

    // 更新平均处理时间
    this.stats.averageProcessingTime = 
      this.processingTimes.reduce((sum, time) => sum + time, 0) / 
      this.processingTimes.length;
  }

  /**
   * 更新队列统计
   */
  private updateQueueStats() {
    this.stats.queueLength = this.jobQueue.length;
    this.stats.activeBatches = this.activeBatches.size;
  }

  /**
   * 启动统计更新器
   */
  private startStatsUpdater() {
    setInterval(() => {
      this.updateThroughput();
    }, 1000); // 每秒更新一次吞吐量
  }

  /**
   * 更新吞吐量统计
   */
  private updateThroughput() {
    const now = Date.now();
    const timeDiff = now - this.lastThroughputUpdate;
    
    if (timeDiff >= 1000) {
      const completedInWindow = this.stats.completedJobs;
      this.throughputWindow.push(completedInWindow);
      
      // 保持最近60秒的记录
      if (this.throughputWindow.length > 60) {
        this.throughputWindow.shift();
      }
      
      // 计算平均吞吐量
      this.stats.throughput = this.throughputWindow.length > 0
        ? this.throughputWindow.reduce((sum, count) => sum + count, 0) / this.throughputWindow.length
        : 0;
      
      this.lastThroughputUpdate = now;
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): ProcessorStats {
    return { ...this.stats };
  }

  /**
   * 获取详细状态
   */
  getDetailedStatus() {
    return {
      stats: this.getStats(),
      queueJobs: this.jobQueue.map(job => ({
        id: job.id,
        provider: job.provider,
        priority: job.priority,
        requestCount: job.requests.length,
        waitTime: Date.now() - job.createdAt,
      })),
      activeBatches: Array.from(this.activeBatches.values()).map(job => ({
        id: job.id,
        provider: job.provider,
        priority: job.priority,
        requestCount: job.requests.length,
        processingTime: Date.now() - job.createdAt,
      })),
      config: this.config,
    };
  }

  /**
   * 暂停处理
   */
  pause() {
    if (this.processingTimer) {
      clearInterval(this.processingTimer);
      this.processingTimer = undefined;
    }
  }

  /**
   * 恢复处理
   */
  resume() {
    if (!this.processingTimer) {
      this.startProcessing();
    }
  }

  /**
   * 清空队列
   */
  clearQueue() {
    // 拒绝所有待处理的任务
    this.jobQueue.forEach(job => {
      job.reject(new AIServiceError(
        AIErrorType.UNKNOWN_ERROR,
        '队列已清空',
        job.provider
      ));
    });

    this.jobQueue.length = 0;
    this.updateQueueStats();
  }

  /**
   * 销毁处理器
   */
  destroy() {
    this.pause();
    this.clearQueue();
    
    // 取消所有活跃批次
    this.activeBatches.forEach(job => {
      job.reject(new AIServiceError(
        AIErrorType.UNKNOWN_ERROR,
        '处理器已销毁',
        job.provider
      ));
    });
    
    this.activeBatches.clear();
  }
}

/**
 * 全局批处理器实例
 */
export const aiBatchProcessor = new AIBatchProcessor();