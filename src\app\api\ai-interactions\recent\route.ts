/**
 * 最近 AI 交互记录 API 路由
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { AIInteractionHistoryService } from '@/lib/services/ai-interaction-history';

/**
 * 获取最近的 AI 交互记录
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') 
      ? parseInt(searchParams.get('limit')!) 
      : 5;

    const interactions = await AIInteractionHistoryService.getRecentInteractions(
      session.user.id,
      limit
    );
    
    return NextResponse.json(interactions);
  } catch (error) {
    console.error('获取最近AI交互记录失败:', error);
    return NextResponse.json(
      { error: '获取最近记录失败' },
      { status: 500 }
    );
  }
}
