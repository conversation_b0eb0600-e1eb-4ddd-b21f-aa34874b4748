'use client';

import React, { useState, useEffect } from 'react';
import { DocumentVersion, type VersionComparison } from '@/lib/services/version-history';
import { 
  History, 
  Clock, 
  User, 
  GitBranch,
  Eye,
  RotateCcw,
  Search,
  Filter,
  ChevronDown,
  ChevronRight,
  FileText,
  Zap,
  Users,
  GitMerge,
  Download
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface VersionHistoryProps {
  documentId: string;
  versions: DocumentVersion[];
  currentVersion?: DocumentVersion;
  onRestoreVersion?: (version: DocumentVersion) => Promise<void>;
  onViewVersion?: (version: DocumentVersion) => void;
  onCompareVersions?: (version1: DocumentVersion, version2: DocumentVersion) => void;
  className?: string;
}

/**
 * 版本历史组件
 * 显示文档的版本历史记录和操作
 */
export function VersionHistory({ 
  documentId,
  versions,
  currentVersion,
  onRestoreVersion,
  onViewVersion,
  onCompareVersions,
  className 
}: VersionHistoryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'user' | 'ai' | 'sync' | 'merge'>('all');
  const [expandedVersions, setExpandedVersions] = useState<Set<string>>(new Set());
  const [selectedVersions, setSelectedVersions] = useState<Set<string>>(new Set());
  const [isRestoring, setIsRestoring] = useState<string | null>(null);

  // 过滤版本
  const filteredVersions = versions.filter(version => {
    const matchesSearch = !searchQuery || 
      version.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      version.changeDescription?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      version.metadata.changesSummary?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesFilter = filterType === 'all' || version.changeType === filterType;

    return matchesSearch && matchesFilter;
  });

  const toggleVersionExpansion = (versionId: string) => {
    const newExpanded = new Set(expandedVersions);
    if (newExpanded.has(versionId)) {
      newExpanded.delete(versionId);
    } else {
      newExpanded.add(versionId);
    }
    setExpandedVersions(newExpanded);
  };

  const toggleVersionSelection = (versionId: string) => {
    const newSelected = new Set(selectedVersions);
    if (newSelected.has(versionId)) {
      newSelected.delete(versionId);
    } else {
      if (newSelected.size >= 2) {
        newSelected.clear();
      }
      newSelected.add(versionId);
    }
    setSelectedVersions(newSelected);
  };

  const handleRestoreVersion = async (version: DocumentVersion) => {
    if (!onRestoreVersion) return;

    setIsRestoring(version.id);
    try {
      await onRestoreVersion(version);
    } catch (error) {
      console.error('恢复版本失败:', error);
    } finally {
      setIsRestoring(null);
    }
  };

  const handleCompareSelected = () => {
    if (selectedVersions.size === 2 && onCompareVersions) {
      const selectedArray = Array.from(selectedVersions);
      const version1 = versions.find(v => v.id === selectedArray[0]);
      const version2 = versions.find(v => v.id === selectedArray[1]);
      if (version1 && version2) {
        onCompareVersions(version1, version2);
      }
    }
  };

  const getChangeTypeInfo = (changeType: string) => {
    switch (changeType) {
      case 'user':
        return {
          icon: User,
          color: 'text-blue-500',
          bgColor: 'bg-blue-100',
          text: '用户编辑'
        };
      case 'ai':
        return {
          icon: Zap,
          color: 'text-purple-500',
          bgColor: 'bg-purple-100',
          text: 'AI 辅助'
        };
      case 'sync':
        return {
          icon: Download,
          color: 'text-green-500',
          bgColor: 'bg-green-100',
          text: '同步更新'
        };
      case 'merge':
        return {
          icon: GitMerge,
          color: 'text-orange-500',
          bgColor: 'bg-orange-100',
          text: '合并操作'
        };
      default:
        return {
          icon: FileText,
          color: 'text-gray-500',
          bgColor: 'bg-gray-100',
          text: '其他'
        };
    }
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={cn('bg-white border rounded-lg shadow-sm', className)}>
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <History className="h-5 w-5 text-gray-500" />
          <h3 className="font-medium text-gray-900">版本历史</h3>
          <span className="text-sm text-gray-500">
            ({filteredVersions.length} 个版本)
          </span>
        </div>
        
        {selectedVersions.size === 2 && (
          <button
            onClick={handleCompareSelected}
            className="flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <GitBranch className="h-4 w-4" />
            比较版本
          </button>
        )}
      </div>

      {/* 搜索和过滤 */}
      <div className="p-4 border-b space-y-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索版本..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
            className="px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">所有类型</option>
            <option value="user">用户编辑</option>
            <option value="ai">AI 辅助</option>
            <option value="sync">同步更新</option>
            <option value="merge">合并操作</option>
          </select>
        </div>
      </div>

      {/* 版本列表 */}
      <div className="max-h-96 overflow-y-auto">
        {filteredVersions.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <History className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>没有找到匹配的版本</p>
          </div>
        ) : (
          <div className="space-y-1">
            {filteredVersions.map((version, index) => {
              const changeTypeInfo = getChangeTypeInfo(version.changeType);
              const ChangeTypeIcon = changeTypeInfo.icon;
              const isExpanded = expandedVersions.has(version.id);
              const isSelected = selectedVersions.has(version.id);
              const isCurrent = currentVersion?.id === version.id;

              return (
                <div
                  key={version.id}
                  className={cn(
                    'border-l-4 hover:bg-gray-50',
                    isCurrent ? 'border-l-blue-500 bg-blue-50' : 'border-l-transparent',
                    isSelected ? 'bg-blue-50' : ''
                  )}
                >
                  <div className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 flex-1">
                        <button
                          onClick={() => toggleVersionExpansion(version.id)}
                          className="p-1 hover:bg-gray-200 rounded"
                        >
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4 text-gray-500" />
                          ) : (
                            <ChevronRight className="h-4 w-4 text-gray-500" />
                          )}
                        </button>

                        <div
                          className="flex items-center gap-2 cursor-pointer flex-1"
                          onClick={() => toggleVersionSelection(version.id)}
                        >
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => {}}
                            className="rounded border-gray-300"
                          />
                          
                          <div className={cn(
                            'flex items-center gap-1 px-2 py-1 rounded text-xs',
                            changeTypeInfo.bgColor,
                            changeTypeInfo.color
                          )}>
                            <ChangeTypeIcon className="h-3 w-3" />
                            {changeTypeInfo.text}
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-gray-900">
                                版本 {version.version}
                              </span>
                              {isCurrent && (
                                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                                  当前版本
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-gray-600">
                              {version.title}
                            </div>
                          </div>
                        </div>

                        <div className="text-xs text-gray-500">
                          {formatDate(version.createdAt)}
                        </div>
                      </div>

                      <div className="flex items-center gap-1 ml-4">
                        {onViewVersion && (
                          <button
                            onClick={() => onViewVersion(version)}
                            className="p-1 text-gray-400 hover:text-gray-600 rounded"
                            title="查看版本"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        )}
                        
                        {onRestoreVersion && !isCurrent && (
                          <button
                            onClick={() => handleRestoreVersion(version)}
                            disabled={isRestoring === version.id}
                            className="p-1 text-gray-400 hover:text-gray-600 rounded disabled:opacity-50"
                            title="恢复到此版本"
                          >
                            <RotateCcw className={cn(
                              'h-4 w-4',
                              isRestoring === version.id && 'animate-spin'
                            )} />
                          </button>
                        )}
                      </div>
                    </div>

                    {/* 展开的详细信息 */}
                    {isExpanded && (
                      <div className="mt-3 ml-8 space-y-2">
                        {version.changeDescription && (
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">变更描述: </span>
                            {version.changeDescription}
                          </div>
                        )}

                        {version.metadata.changesSummary && (
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">变更摘要: </span>
                            {version.metadata.changesSummary}
                          </div>
                        )}

                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">字数: </span>
                            <span className="text-gray-600">{version.metadata.wordCount}</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">字符数: </span>
                            <span className="text-gray-600">{version.metadata.characterCount}</span>
                          </div>
                          {version.metadata.conflictResolution && (
                            <div>
                              <span className="font-medium text-gray-700">冲突解决: </span>
                              <span className="text-gray-600">
                                {version.metadata.conflictResolution === 'local' ? '本地版本' :
                                 version.metadata.conflictResolution === 'remote' ? '远程版本' : '合并版本'}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 底部信息 */}
      {filteredVersions.length > 0 && (
        <div className="p-4 border-t bg-gray-50 text-sm text-gray-600">
          <div className="flex items-center justify-between">
            <span>
              显示 {filteredVersions.length} 个版本，共 {versions.length} 个
            </span>
            {selectedVersions.size > 0 && (
              <span>
                已选择 {selectedVersions.size} 个版本
                {selectedVersions.size === 2 && ' (可以比较)'}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * 版本比较组件
 */
interface VersionComparisonProps {
  version1: DocumentVersion;
  version2: DocumentVersion;
  comparison?: VersionComparison;
  onClose?: () => void;
  className?: string;
}

export function VersionComparison({ 
  version1, 
  version2, 
  comparison,
  onClose,
  className 
}: VersionComparisonProps) {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={cn('bg-white border rounded-lg shadow-lg', className)}>
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <GitBranch className="h-5 w-5 text-gray-500" />
          <h3 className="font-medium text-gray-900">版本比较</h3>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 rounded"
          >
            ×
          </button>
        )}
      </div>

      {/* 版本信息 */}
      <div className="p-4 border-b">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">版本 {version1.version}</h4>
            <div className="text-sm text-gray-600">
              <div>{version1.title}</div>
              <div>{formatDate(version1.createdAt)}</div>
              <div>{version1.metadata.wordCount} 词，{version1.metadata.characterCount} 字符</div>
            </div>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">版本 {version2.version}</h4>
            <div className="text-sm text-gray-600">
              <div>{version2.title}</div>
              <div>{formatDate(version2.createdAt)}</div>
              <div>{version2.metadata.wordCount} 词，{version2.metadata.characterCount} 字符</div>
            </div>
          </div>
        </div>
      </div>

      {/* 比较结果 */}
      {comparison && (
        <div className="p-4">
          <div className="space-y-4">
            <div className="flex items-center gap-4 text-sm">
              <span className="font-medium text-gray-700">相似度:</span>
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full"
                  style={{ width: `${comparison.similarity * 100}%` }}
                />
              </div>
              <span className="text-gray-600">
                {Math.round(comparison.similarity * 100)}%
              </span>
            </div>

            <div className="text-sm">
              <span className="font-medium text-gray-700">变更摘要: </span>
              <span className="text-gray-600">{comparison.summary}</span>
            </div>

            {comparison.additions.length > 0 && (
              <div>
                <h5 className="font-medium text-green-700 mb-2">
                  新增内容 ({comparison.additions.length} 项)
                </h5>
                <div className="space-y-1">
                  {comparison.additions.slice(0, 5).map((addition, index) => (
                    <div key={index} className="text-sm bg-green-50 p-2 rounded border-l-4 border-green-400">
                      + {addition}
                    </div>
                  ))}
                  {comparison.additions.length > 5 && (
                    <div className="text-xs text-gray-500">
                      还有 {comparison.additions.length - 5} 项...
                    </div>
                  )}
                </div>
              </div>
            )}

            {comparison.deletions.length > 0 && (
              <div>
                <h5 className="font-medium text-red-700 mb-2">
                  删除内容 ({comparison.deletions.length} 项)
                </h5>
                <div className="space-y-1">
                  {comparison.deletions.slice(0, 5).map((deletion, index) => (
                    <div key={index} className="text-sm bg-red-50 p-2 rounded border-l-4 border-red-400">
                      - {deletion}
                    </div>
                  ))}
                  {comparison.deletions.length > 5 && (
                    <div className="text-xs text-gray-500">
                      还有 {comparison.deletions.length - 5} 项...
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}