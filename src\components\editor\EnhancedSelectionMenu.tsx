'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/Button';
import { 
  MessageCircleIcon, 
  EditIcon, 
  BoldIcon,
  ItalicIcon,
  CopyIcon,
  LinkIcon,
  MoreHorizontalIcon
} from 'lucide-react';
import { ChatDialog } from './ChatDialog';
import { EditDialog } from './EditDialog';

/**
 * 增强选择菜单属性
 */
interface EnhancedSelectionMenuProps {
  /** TipTap 编辑器实例 */
  editor: Editor;
  /** 是否启用 */
  enabled?: boolean;
  /** 最小选择文本长度 */
  minSelectionLength?: number;
  /** 是否启用 AI 功能 */
  enableAI?: boolean;
}

/**
 * 菜单状态
 */
interface MenuState {
  visible: boolean;
  position: { x: number; y: number };
  selectedText: string;
  selectionRange: { from: number; to: number };
}

/**
 * 对话框状态
 */
interface DialogState {
  chatVisible: boolean;
  editVisible: boolean;
  chatAction: string;
  editAction: string;
}

/**
 * 增强版文本选择菜单组件
 * 集成 Chat 和 Edit 功能，提供完整的文本交互体验
 */
export function EnhancedSelectionMenu({
  editor,
  enabled = true,
  minSelectionLength = 2,
  enableAI = true
}: EnhancedSelectionMenuProps) {
  const [menuState, setMenuState] = useState<MenuState>({
    visible: false,
    position: { x: 0, y: 0 },
    selectedText: '',
    selectionRange: { from: 0, to: 0 }
  });

  const [dialogState, setDialogState] = useState<DialogState>({
    chatVisible: false,
    editVisible: false,
    chatAction: 'explain',
    editAction: 'improve'
  });

  const [showMoreActions, setShowMoreActions] = useState(false);

  const menuRef = useRef<HTMLDivElement>(null);
  const showTimeoutRef = useRef<NodeJS.Timeout>();
  const hideTimeoutRef = useRef<NodeJS.Timeout>();

  /**
   * 计算菜单位置
   */
  const calculateMenuPosition = useCallback((coords: { left: number; top: number; bottom: number }) => {
    const editorElement = editor.view.dom;
    const editorRect = editorElement.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const isMobile = viewportWidth < 640;
    
    let x = coords.left - editorRect.left;
    let y = coords.bottom - editorRect.top + 8;
    
    // 估算菜单尺寸 - 移动端更小
    const menuWidth = isMobile ? 240 : (showMoreActions ? 320 : 280);
    const menuHeight = isMobile ? 100 : (showMoreActions ? 200 : 120);
    
    // 防止超出边界
    if (x + menuWidth > viewportWidth - 20) {
      x = Math.max(10, viewportWidth - menuWidth - 20);
    }
    
    if (coords.bottom + menuHeight > viewportHeight - 20) {
      y = coords.top - editorRect.top - menuHeight - 8;
    }
    
    x = Math.max(10, x);
    y = Math.max(10, y);
    
    return { x, y };
  }, [editor, showMoreActions]);

  /**
   * 显示菜单
   */
  const showMenu = useCallback((selectedText: string, range: { from: number; to: number }) => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = undefined;
    }

    try {
      const coords = editor.view.coordsAtPos(range.to);
      const position = calculateMenuPosition(coords);

      setMenuState({
        visible: true,
        position,
        selectedText,
        selectionRange: range
      });
    } catch (error) {
      console.warn('无法计算菜单位置:', error);
    }
  }, [editor, calculateMenuPosition]);

  /**
   * 隐藏菜单
   */
  const hideMenu = useCallback(() => {
    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current);
      showTimeoutRef.current = undefined;
    }
    setMenuState(prev => ({ ...prev, visible: false }));
    setShowMoreActions(false);
  }, []);

  /**
   * 延迟隐藏菜单
   */
  const delayedHideMenu = useCallback(() => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }
    hideTimeoutRef.current = setTimeout(hideMenu, 300);
  }, [hideMenu]);

  /**
   * 处理选择变化
   */
  const handleSelectionChange = useCallback(() => {
    if (!enabled) return;

    const { from, to } = editor.state.selection;
    const selectedText = editor.state.doc.textBetween(from, to);

    if (!selectedText.trim() || selectedText.length < minSelectionLength) {
      delayedHideMenu();
      return;
    }

    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current);
    }

    showTimeoutRef.current = setTimeout(() => {
      showMenu(selectedText, { from, to });
    }, 150);
  }, [editor, enabled, minSelectionLength, showMenu, delayedHideMenu]);

  /**
   * 打开 Chat 对话框
   */
  const openChatDialog = (action: string = 'explain') => {
    setDialogState(prev => ({
      ...prev,
      chatVisible: true,
      chatAction: action
    }));
    hideMenu();
  };

  /**
   * 打开 Edit 对话框
   */
  const openEditDialog = (action: string = 'improve') => {
    setDialogState(prev => ({
      ...prev,
      editVisible: true,
      editAction: action
    }));
    hideMenu();
  };

  /**
   * 关闭对话框
   */
  const closeDialogs = () => {
    setDialogState({
      chatVisible: false,
      editVisible: false,
      chatAction: 'explain',
      editAction: 'improve'
    });
  };

  /**
   * 执行格式化操作
   */
  const executeFormat = useCallback((format: string) => {
    const { from, to } = menuState.selectionRange;
    editor.chain().focus().setTextSelection({ from, to }).run();
    
    switch (format) {
      case 'bold':
        if (editor.can().toggleBold()) {
          editor.chain().focus().toggleBold().run();
        }
        break;
      case 'italic':
        if (editor.can().toggleItalic()) {
          editor.chain().focus().toggleItalic().run();
        }
        break;
      case 'copy':
        navigator.clipboard.writeText(menuState.selectedText);
        break;
      case 'link':
        const url = window.prompt('请输入链接地址:');
        if (url && editor.can().setLink({ href: url })) {
          editor.chain().focus().setLink({ href: url }).run();
        }
        break;
    }
    
    hideMenu();
  }, [editor, menuState, hideMenu]);

  // 监听编辑器选择变化
  useEffect(() => {
    if (!editor) return;

    const handleUpdate = () => {
      setTimeout(handleSelectionChange, 10);
    };

    editor.on('selectionUpdate', handleUpdate);
    return () => {
      editor.off('selectionUpdate', handleUpdate);
    };
  }, [editor, handleSelectionChange]);

  // 监听鼠标和键盘事件
  useEffect(() => {
    const handleMouseDown = (event: MouseEvent) => {
      const target = event.target as Element;
      
      if (menuRef.current?.contains(target)) {
        return;
      }
      
      if (editor.view.dom.contains(target)) {
        delayedHideMenu();
        return;
      }
      
      hideMenu();
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && menuState.visible) {
        event.preventDefault();
        hideMenu();
      }
    };

    if (menuState.visible) {
      document.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('keydown', handleKeyDown);
      
      return () => {
        document.removeEventListener('mousedown', handleMouseDown);
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [menuState.visible, editor, delayedHideMenu, hideMenu]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (showTimeoutRef.current) clearTimeout(showTimeoutRef.current);
      if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);
    };
  }, []);

  if (!enabled || !menuState.visible) {
    return (
      <>
        {/* Chat 对话框 */}
        <ChatDialog
          visible={dialogState.chatVisible}
          onClose={closeDialogs}
          selectedText={menuState.selectedText}
          editor={editor}
          initialAction={dialogState.chatAction}
        />
        
        {/* Edit 对话框 */}
        <EditDialog
          visible={dialogState.editVisible}
          onClose={closeDialogs}
          selectedText={menuState.selectedText}
          editor={editor}
          selectionRange={menuState.selectionRange}
          initialAction={dialogState.editAction}
        />
      </>
    );
  }

  return (
    <>
      <div
        ref={menuRef}
        className="
          absolute bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-50
          animate-in slide-in-from-bottom-2 fade-in duration-200
          min-w-[200px] sm:min-w-[240px] max-w-[280px] sm:max-w-none
          touch-manipulation
        "
        style={{
          left: menuState.position.x,
          top: menuState.position.y,
        }}
        onMouseEnter={() => {
          if (hideTimeoutRef.current) {
            clearTimeout(hideTimeoutRef.current);
            hideTimeoutRef.current = undefined;
          }
        }}
        onMouseLeave={delayedHideMenu}
        onTouchStart={() => {
          if (hideTimeoutRef.current) {
            clearTimeout(hideTimeoutRef.current);
            hideTimeoutRef.current = undefined;
          }
        }}
      >
        {/* 头部 - 移动端简化 */}
        <div className="flex items-center gap-2 px-2 py-1 mb-2 border-b">
          <span className="text-sm font-medium text-gray-700 hidden sm:inline">文本操作</span>
          <span className="text-sm font-medium text-gray-700 sm:hidden">操作</span>
          <span className="text-xs text-gray-500 ml-auto">
            {menuState.selectedText.length}字符
          </span>
        </div>

        {/* 主要操作 - 移动端优化 */}
        {enableAI && (
          <div className="space-y-1 mb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => openChatDialog('explain')}
              className="w-full justify-start h-9 sm:h-8 p-2 hover:bg-blue-50 hover:text-blue-700 touch-manipulation active:bg-blue-100"
            >
              <MessageCircleIcon className="h-4 w-4 mr-2 text-blue-600" />
              <span className="text-sm">Chat</span>
              <span className="text-xs text-gray-500 ml-auto hidden sm:inline">与 AI 对话</span>
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => openEditDialog('improve')}
              className="w-full justify-start h-9 sm:h-8 p-2 hover:bg-green-50 hover:text-green-700 touch-manipulation active:bg-green-100"
            >
              <EditIcon className="h-4 w-4 mr-2 text-green-600" />
              <span className="text-sm">Edit</span>
              <span className="text-xs text-gray-500 ml-auto hidden sm:inline">AI 编辑</span>
            </Button>
          </div>
        )}

        {/* 格式化操作 */}
        <div className="border-t pt-2">
          <div className="flex items-center justify-between mb-1">
            <div className="text-xs text-gray-500 px-2">快速操作</div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowMoreActions(!showMoreActions)}
              className="h-6 w-6 p-0"
            >
              <MoreHorizontalIcon className="h-3 w-3" />
            </Button>
          </div>
          
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => executeFormat('bold')}
              className="flex-1 h-9 sm:h-8 p-1 hover:bg-gray-100 touch-manipulation active:bg-gray-200"
              title="加粗"
            >
              <BoldIcon className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => executeFormat('italic')}
              className="flex-1 h-9 sm:h-8 p-1 hover:bg-gray-100 touch-manipulation active:bg-gray-200"
              title="斜体"
            >
              <ItalicIcon className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => executeFormat('copy')}
              className="flex-1 h-9 sm:h-8 p-1 hover:bg-gray-100 touch-manipulation active:bg-gray-200"
              title="复制"
            >
              <CopyIcon className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => executeFormat('link')}
              className="flex-1 h-9 sm:h-8 p-1 hover:bg-gray-100 touch-manipulation active:bg-gray-200"
              title="添加链接"
            >
              <LinkIcon className="h-4 w-4" />
            </Button>
          </div>

          {/* 更多操作 */}
          {showMoreActions && (
            <div className="mt-2 pt-2 border-t space-y-1">
              <div className="text-xs text-gray-500 px-2 mb-1">AI 快捷操作</div>
              <div className="grid grid-cols-2 gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => openChatDialog('translate')}
                  className="h-8 text-xs hover:bg-blue-50"
                >
                  翻译
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => openEditDialog('formal')}
                  className="h-8 text-xs hover:bg-green-50"
                >
                  正式化
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => openChatDialog('summarize')}
                  className="h-8 text-xs hover:bg-blue-50"
                >
                  总结
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => openEditDialog('simplify')}
                  className="h-8 text-xs hover:bg-green-50"
                >
                  简化
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* 提示 */}
        <div className="mt-2 pt-2 border-t">
          <p className="text-xs text-gray-400 text-center">
            选择操作或按 ESC 取消
          </p>
        </div>
      </div>

      {/* Chat 对话框 */}
      <ChatDialog
        visible={dialogState.chatVisible}
        onClose={closeDialogs}
        selectedText={menuState.selectedText}
        editor={editor}
        initialAction={dialogState.chatAction}
      />
      
      {/* Edit 对话框 */}
      <EditDialog
        visible={dialogState.editVisible}
        onClose={closeDialogs}
        selectedText={menuState.selectedText}
        editor={editor}
        selectionRange={menuState.selectionRange}
        initialAction={dialogState.editAction}
      />
    </>
  );
}
export default EnhancedSelectionMenu;
