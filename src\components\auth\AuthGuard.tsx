"use client";

import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

/**
 * 认证守卫组件属性接口
 */
interface AuthGuardProps {
  children: React.ReactNode; // 子组件
  requireAuth?: boolean;     // 是否需要认证，默认为 true
  redirectTo?: string;       // 未认证时重定向的路径，默认为登录页面
}

/**
 * 认证守卫组件
 * 用于保护需要认证的页面或组件
 * 如果用户未登录且需要认证，会自动重定向到登录页面
 * 
 * @param children 需要保护的子组件
 * @param requireAuth 是否需要认证，默认为 true
 * @param redirectTo 未认证时重定向的路径，默认为 "/auth/signin"
 */
export function AuthGuard({ 
  children, 
  requireAuth = true, 
  redirectTo = "/auth/signin" 
}: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // 如果需要认证但用户未登录，重定向到登录页面
    if (requireAuth && !isLoading && !isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, requireAuth, redirectTo, router]);

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // 如果需要认证但用户未登录，显示空白页面（等待重定向）
  if (requireAuth && !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">正在跳转到登录页面...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}