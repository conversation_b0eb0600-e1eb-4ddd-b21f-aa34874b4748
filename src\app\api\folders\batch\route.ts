import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';
import { z } from 'zod';

// 批量操作的验证模式
const batchOperationSchema = z.object({
  operation: z.enum(['delete', 'move', 'duplicate']),
  folderIds: z.array(z.string()).min(1).max(50), // 限制最多50个文件夹
  targetParentId: z.string().nullable().optional(), // 移动操作时的目标父文件夹
  force: z.boolean().optional().default(false), // 强制删除（包括非空文件夹）
});

/**
 * POST /api/folders/batch - 批量操作文件夹
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = batchOperationSchema.parse(body);
    const { operation, folderIds, targetParentId, force } = validatedData;

    // 验证所有文件夹都属于当前用户
    const folders = await prisma.folder.findMany({
      where: {
        id: { in: folderIds },
        userId: session.user.id,
      },
      include: {
        children: true,
        documents: true,
      },
    });

    if (folders.length !== folderIds.length) {
      return NextResponse.json(
        { error: '部分文件夹未找到或无权限访问' },
        { status: 404 }
      );
    }

    let result;

    switch (operation) {
      case 'delete':
        // 批量删除文件夹
        const deleteResults = [];
        
        for (const folder of folders) {
          // 检查是否为空文件夹或强制删除
          if (!force && (folder.children.length > 0 || folder.documents.length > 0)) {
            deleteResults.push({
              id: folder.id,
              name: folder.name,
              success: false,
              error: '文件夹不为空',
            });
            continue;
          }

          try {
            if (force) {
              // 强制删除，包括所有内容
              await prisma.$transaction(async (tx) => {
                await deleteFolderRecursively(tx, folder.id);
              });
            } else {
              // 普通删除（空文件夹）
              await prisma.folder.delete({
                where: { id: folder.id },
              });
            }

            deleteResults.push({
              id: folder.id,
              name: folder.name,
              success: true,
            });
          } catch (error) {
            deleteResults.push({
              id: folder.id,
              name: folder.name,
              success: false,
              error: '删除失败',
            });
          }
        }

        result = { deleteResults };
        break;

      case 'move':
        // 验证目标父文件夹（如果指定）
        if (targetParentId) {
          const targetFolder = await prisma.folder.findFirst({
            where: {
              id: targetParentId,
              userId: session.user.id,
            },
          });

          if (!targetFolder) {
            return NextResponse.json(
              { error: '目标文件夹未找到' },
              { status: 404 }
            );
          }

          // 检查循环引用
          for (const folderId of folderIds) {
            const isCircular = await checkCircularReference(folderId, targetParentId);
            if (isCircular) {
              return NextResponse.json(
                { error: '不能将文件夹移动到自身或其子文件夹中' },
                { status: 400 }
              );
            }
          }
        }

        // 批量移动文件夹
        const moveResults = [];
        
        for (const folder of folders) {
          try {
            // 检查目标位置是否已存在同名文件夹
            const existingFolder = await prisma.folder.findFirst({
              where: {
                name: folder.name,
                parentId: targetParentId,
                userId: session.user.id,
                id: { not: folder.id },
              },
            });

            if (existingFolder) {
              moveResults.push({
                id: folder.id,
                name: folder.name,
                success: false,
                error: '目标位置已存在同名文件夹',
              });
              continue;
            }

            await prisma.folder.update({
              where: { id: folder.id },
              data: { parentId: targetParentId },
            });

            moveResults.push({
              id: folder.id,
              name: folder.name,
              success: true,
            });
          } catch (error) {
            moveResults.push({
              id: folder.id,
              name: folder.name,
              success: false,
              error: '移动失败',
            });
          }
        }

        result = { moveResults };
        break;

      case 'duplicate':
        // 批量复制文件夹
        const duplicateResults = [];
        
        for (const folder of folders) {
          try {
            const duplicatedFolder = await duplicateFolderRecursively(
              folder.id,
              folder.parentId,
              session.user.id,
              `${folder.name} (副本)`
            );

            duplicateResults.push({
              originalId: folder.id,
              originalName: folder.name,
              duplicatedId: duplicatedFolder.id,
              duplicatedName: duplicatedFolder.name,
              success: true,
            });
          } catch (error) {
            duplicateResults.push({
              originalId: folder.id,
              originalName: folder.name,
              success: false,
              error: '复制失败',
            });
          }
        }

        result = { duplicateResults };
        break;

      default:
        return NextResponse.json(
          { error: '不支持的操作类型' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      message: `批量${operation === 'delete' ? '删除' : operation === 'move' ? '移动' : '复制'}操作完成`,
      result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '输入参数无效', details: error.issues },
        { status: 400 }
      );
    }

    console.error('批量操作文件夹失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 递归删除文件夹及其所有内容
 */
async function deleteFolderRecursively(tx: any, folderId: string): Promise<void> {
  // 获取所有子文件夹
  const children = await tx.folder.findMany({
    where: { parentId: folderId },
    select: { id: true },
  });

  // 递归删除所有子文件夹
  for (const child of children) {
    await deleteFolderRecursively(tx, child.id);
  }

  // 删除文件夹中的所有文档的相关数据
  const documents = await tx.document.findMany({
    where: { folderId },
    select: { id: true },
  });

  for (const document of documents) {
    // 删除文档历史记录
    await tx.documentHistory.deleteMany({
      where: { documentId: document.id },
    });

    // 删除AI交互记录
    await tx.aIInteraction.deleteMany({
      where: { documentId: document.id },
    });
  }

  // 删除文件夹中的所有文档
  await tx.document.deleteMany({
    where: { folderId },
  });

  // 最后删除文件夹本身
  await tx.folder.delete({
    where: { id: folderId },
  });
}

/**
 * 递归复制文件夹及其所有内容
 */
async function duplicateFolderRecursively(
  originalFolderId: string,
  parentId: string | null,
  userId: string,
  newName: string
): Promise<any> {
  return await prisma.$transaction(async (tx) => {
    // 获取原始文件夹信息
    const originalFolder = await tx.folder.findUnique({
      where: { id: originalFolderId },
      include: {
        children: true,
        documents: true,
      },
    });

    if (!originalFolder) {
      throw new Error('原始文件夹未找到');
    }

    // 创建新文件夹
    const newFolder = await tx.folder.create({
      data: {
        name: newName,
        parentId,
        userId,
      },
    });

    // 复制所有文档
    for (const document of originalFolder.documents) {
      await tx.document.create({
        data: {
          title: `${document.title} (副本)`,
          content: document.content,
          folderId: newFolder.id,
          userId,
          wordCount: document.wordCount,
          charCount: document.charCount,
        },
      });
    }

    // 递归复制所有子文件夹
    for (const child of originalFolder.children) {
      await duplicateFolderRecursively(
        child.id,
        newFolder.id,
        userId,
        child.name
      );
    }

    return newFolder;
  });
}

/**
 * 检查循环引用
 */
async function checkCircularReference(
  folderId: string,
  targetParentId: string
): Promise<boolean> {
  if (folderId === targetParentId) {
    return true;
  }

  const descendants = await getDescendantIds(folderId);
  return descendants.includes(targetParentId);
}

/**
 * 获取所有子文件夹ID
 */
async function getDescendantIds(folderId: string): Promise<string[]> {
  const descendants: string[] = [];
  
  const children = await prisma.folder.findMany({
    where: { parentId: folderId },
    select: { id: true },
  });

  for (const child of children) {
    descendants.push(child.id);
    const childDescendants = await getDescendantIds(child.id);
    descendants.push(...childDescendants);
  }

  return descendants;
}