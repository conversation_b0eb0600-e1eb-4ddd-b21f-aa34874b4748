'use client';

import React, { useState } from 'react';
import { Editor } from '@/components/editor/Editor';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

/**
 * AI 文本改写功能演示页面
 */
export default function AIRewriteDemoPage() {
  const [content, setContent] = useState(`
    <h1>AI 文本改写功能演示</h1>
    
    <p>欢迎使用 AI 文本改写功能！这个功能可以帮助您：</p>
    
    <ul>
      <li>改进和优化文本质量</li>
      <li>检查和修正语法错误</li>
      <li>调整文本风格（正式、非正式、学术等）</li>
      <li>简化或扩展文本内容</li>
    </ul>
    
    <h2>使用方法</h2>
    
    <p>要使用 AI 改写功能，请按照以下步骤操作：</p>
    
    <ol>
      <li>选择您想要改写的文本</li>
      <li>选择文本后会自动显示改写菜单</li>
      <li>选择合适的改写选项</li>
      <li>查看改写结果并选择是否应用</li>
    </ol>
    
    <h2>示例文本</h2>
    
    <p>下面是一些可以用来测试改写功能的示例文本：</p>
    
    <blockquote>
      <p>这个产品真的很好用，我觉得大家都应该试试看。它的功能很强大，而且操作也很简单。</p>
    </blockquote>
    
    <blockquote>
      <p>人工智能技术在近年来得到了快速发展，它在各个领域都有广泛的应用前景。</p>
    </blockquote>
    
    <blockquote>
      <p>我们公司的业绩在上个季度有了显著的提升，这主要归功于团队的努力和市场策略的调整。</p>
    </blockquote>
    
    <h2>快捷键</h2>
    
    <p>您也可以使用以下快捷键快速访问改写功能：</p>
    
    <ul>
      <li><strong>Ctrl+Shift+R</strong>: 改进优化</li>
      <li><strong>Ctrl+Shift+G</strong>: 语法检查</li>
      <li><strong>Ctrl+Shift+F</strong>: 正式化</li>
    </ul>
    
    <p>选择任意文本段落，然后尝试使用这些功能吧！</p>
  `);

  const [selectedDemo, setSelectedDemo] = useState('basic');

  const demoTexts = {
    basic: '这个产品真的很好用，我觉得大家都应该试试看。它的功能很强大，而且操作也很简单。',
    academic: '人工智能技术在近年来得到了快速发展，它在各个领域都有广泛的应用前景。',
    business: '我们公司的业绩在上个季度有了显著的提升，这主要归功于团队的努力和市场策略的调整。',
    casual: '昨天去了那家新开的餐厅，味道还不错，就是价格有点贵，不过环境挺好的。',
    technical: '该系统采用微服务架构，通过容器化部署实现高可用性和可扩展性，同时集成了监控和日志系统。'
  };

  const insertDemoText = (text: string) => {
    const newContent = content + `<p>${text}</p>`;
    setContent(newContent);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AI 文本改写功能演示
          </h1>
          <p className="text-gray-600">
            体验强大的 AI 文本改写功能，提升您的写作效率和质量
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 侧边栏 - 功能说明和示例文本 */}
          <div className="lg:col-span-1 space-y-4">
            <Card className="p-4">
              <h3 className="font-semibold mb-3">功能特点</h3>
              <ul className="text-sm space-y-2 text-gray-600">
                <li>• 智能改进文本质量</li>
                <li>• 语法错误检查修正</li>
                <li>• 多种风格转换</li>
                <li>• 文本简化和扩展</li>
                <li>• 实时对比显示</li>
                <li>• 多版本选择</li>
              </ul>
            </Card>

            <Card className="p-4">
              <h3 className="font-semibold mb-3">使用说明</h3>
              <ol className="text-sm space-y-2 text-gray-600">
                <li>1. 选择要改写的文本</li>
                <li>2. 选择改写选项</li>
                <li>3. 查看对比结果</li>
                <li>4. 应用满意的版本</li>
              </ol>
            </Card>

            <Card className="p-4">
              <h3 className="font-semibold mb-3">示例文本</h3>
              <Tabs value={selectedDemo} onValueChange={setSelectedDemo}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="basic">基础</TabsTrigger>
                  <TabsTrigger value="academic">学术</TabsTrigger>
                </TabsList>
                <TabsContent value="basic" className="space-y-2">
                  <div className="text-xs text-gray-500 mb-2">点击插入到编辑器：</div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText(demoTexts.basic)}
                  >
                    <div className="text-xs leading-relaxed">
                      {demoTexts.basic}
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText(demoTexts.casual)}
                  >
                    <div className="text-xs leading-relaxed">
                      {demoTexts.casual}
                    </div>
                  </Button>
                </TabsContent>
                <TabsContent value="academic" className="space-y-2">
                  <div className="text-xs text-gray-500 mb-2">点击插入到编辑器：</div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText(demoTexts.academic)}
                  >
                    <div className="text-xs leading-relaxed">
                      {demoTexts.academic}
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText(demoTexts.business)}
                  >
                    <div className="text-xs leading-relaxed">
                      {demoTexts.business}
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText(demoTexts.technical)}
                  >
                    <div className="text-xs leading-relaxed">
                      {demoTexts.technical}
                    </div>
                  </Button>
                </TabsContent>
              </Tabs>
            </Card>

            <Card className="p-4">
              <h3 className="font-semibold mb-3">快捷键</h3>
              <div className="text-sm space-y-1 text-gray-600">
                <div><kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">⌘⇧R</kbd> 改进优化</div>
                <div><kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">⌘⇧G</kbd> 语法检查</div>
                <div><kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">⌘⇧F</kbd> 正式化</div>
              </div>
            </Card>
          </div>

          {/* 主编辑区域 */}
          <div className="lg:col-span-3">
            <Card className="p-6">
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-xl font-semibold">编辑器</h2>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <span>选择文本后会显示改写选项</span>
                </div>
              </div>
              
              <div className="border rounded-lg overflow-hidden">
                <Editor
                  content={content}
                  onChange={setContent}
                  placeholder="开始输入或插入示例文本..."
                  enableAI={true}
                  className="min-h-[600px]"
                />
              </div>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">💡 使用提示</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 选择任意文本段落，会自动显示改写菜单</li>
                  <li>• 使用斜杠命令 "/" 可以快速访问 AI 功能</li>
                  <li>• 改写结果支持多版本对比和选择</li>
                  <li>• 支持键盘快捷键快速操作</li>
                </ul>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}