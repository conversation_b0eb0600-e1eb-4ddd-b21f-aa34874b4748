/**
 * Test script to verify folder and document hierarchy functionality
 * This is a simple test that can be run to verify the API endpoints work correctly
 */

interface TestResult {
  success: boolean;
  message: string;
  data?: any;
}

class HierarchyTester {
  private baseUrl: string;
  private authHeaders: HeadersInit;

  constructor(baseUrl: string = 'http://localhost:3000', authToken?: string) {
    this.baseUrl = baseUrl;
    this.authHeaders = authToken 
      ? { 'Authorization': `Bearer ${authToken}` }
      : {};
  }

  async runTests(): Promise<void> {
    console.log('🧪 Starting Hierarchy API Tests...\n');

    const tests = [
      () => this.testCreateFolder(),
      () => this.testCreateSubfolder(),
      () => this.testCreateDocument(),
      () => this.testCreateDocumentInFolder(),
      () => this.testGetFolders(),
      () => this.testGetDocuments(),
      () => this.testUpdateFolder(),
      () => this.testMoveDocument(),
      () => this.testSearchDocuments(),
      () => this.testDeleteDocument(),
      () => this.testDeleteFolder(),
    ];

    let passed = 0;
    let failed = 0;

    for (const test of tests) {
      try {
        const result = await test();
        if (result.success) {
          console.log(`✅ ${result.message}`);
          passed++;
        } else {
          console.log(`❌ ${result.message}`);
          failed++;
        }
      } catch (error) {
        console.log(`❌ Test failed with error: ${error}`);
        failed++;
      }
    }

    console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
  }

  private async makeRequest(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<Response> {
    const url = `${this.baseUrl}/api${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...this.authHeaders,
      ...options.headers,
    };

    return fetch(url, {
      ...options,
      headers,
    });
  }

  private async testCreateFolder(): Promise<TestResult> {
    const response = await this.makeRequest('/folders', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Test Folder',
      }),
    });

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: 'Create folder test passed',
        data: data.folder,
      };
    } else {
      const error = await response.text();
      return {
        success: false,
        message: `Create folder test failed: ${error}`,
      };
    }
  }

  private async testCreateSubfolder(): Promise<TestResult> {
    // First create a parent folder
    const parentResponse = await this.makeRequest('/folders', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Parent Folder',
      }),
    });

    if (!parentResponse.ok) {
      return {
        success: false,
        message: 'Failed to create parent folder for subfolder test',
      };
    }

    const parentData = await parentResponse.json();
    const parentId = parentData.folder.id;

    // Create subfolder
    const response = await this.makeRequest('/folders', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Sub Folder',
        parentId,
      }),
    });

    if (response.ok) {
      return {
        success: true,
        message: 'Create subfolder test passed',
      };
    } else {
      const error = await response.text();
      return {
        success: false,
        message: `Create subfolder test failed: ${error}`,
      };
    }
  }

  private async testCreateDocument(): Promise<TestResult> {
    const response = await this.makeRequest('/documents', {
      method: 'POST',
      body: JSON.stringify({
        title: 'Test Document',
        content: 'This is a test document content.',
      }),
    });

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: 'Create document test passed',
        data: data.document,
      };
    } else {
      const error = await response.text();
      return {
        success: false,
        message: `Create document test failed: ${error}`,
      };
    }
  }

  private async testCreateDocumentInFolder(): Promise<TestResult> {
    // First create a folder
    const folderResponse = await this.makeRequest('/folders', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Document Folder',
      }),
    });

    if (!folderResponse.ok) {
      return {
        success: false,
        message: 'Failed to create folder for document test',
      };
    }

    const folderData = await folderResponse.json();
    const folderId = folderData.folder.id;

    // Create document in folder
    const response = await this.makeRequest('/documents', {
      method: 'POST',
      body: JSON.stringify({
        title: 'Document in Folder',
        content: 'This document is in a folder.',
        folderId,
      }),
    });

    if (response.ok) {
      return {
        success: true,
        message: 'Create document in folder test passed',
      };
    } else {
      const error = await response.text();
      return {
        success: false,
        message: `Create document in folder test failed: ${error}`,
      };
    }
  }

  private async testGetFolders(): Promise<TestResult> {
    const response = await this.makeRequest('/folders');

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: `Get folders test passed (found ${data.folders.length} folders)`,
      };
    } else {
      const error = await response.text();
      return {
        success: false,
        message: `Get folders test failed: ${error}`,
      };
    }
  }

  private async testGetDocuments(): Promise<TestResult> {
    const response = await this.makeRequest('/documents');

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: `Get documents test passed (found ${data.documents.length} documents)`,
      };
    } else {
      const error = await response.text();
      return {
        success: false,
        message: `Get documents test failed: ${error}`,
      };
    }
  }

  private async testUpdateFolder(): Promise<TestResult> {
    // First create a folder to update
    const createResponse = await this.makeRequest('/folders', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Folder to Update',
      }),
    });

    if (!createResponse.ok) {
      return {
        success: false,
        message: 'Failed to create folder for update test',
      };
    }

    const createData = await createResponse.json();
    const folderId = createData.folder.id;

    // Update the folder
    const response = await this.makeRequest(`/folders/${folderId}`, {
      method: 'PUT',
      body: JSON.stringify({
        name: 'Updated Folder Name',
      }),
    });

    if (response.ok) {
      return {
        success: true,
        message: 'Update folder test passed',
      };
    } else {
      const error = await response.text();
      return {
        success: false,
        message: `Update folder test failed: ${error}`,
      };
    }
  }

  private async testMoveDocument(): Promise<TestResult> {
    // Create a folder and document first
    const folderResponse = await this.makeRequest('/folders', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Target Folder',
      }),
    });

    const docResponse = await this.makeRequest('/documents', {
      method: 'POST',
      body: JSON.stringify({
        title: 'Document to Move',
        content: 'This document will be moved.',
      }),
    });

    if (!folderResponse.ok || !docResponse.ok) {
      return {
        success: false,
        message: 'Failed to create folder or document for move test',
      };
    }

    const folderData = await folderResponse.json();
    const docData = await docResponse.json();

    // Move document to folder
    const response = await this.makeRequest(`/documents/${docData.document.id}`, {
      method: 'PUT',
      body: JSON.stringify({
        folderId: folderData.folder.id,
      }),
    });

    if (response.ok) {
      return {
        success: true,
        message: 'Move document test passed',
      };
    } else {
      const error = await response.text();
      return {
        success: false,
        message: `Move document test failed: ${error}`,
      };
    }
  }

  private async testSearchDocuments(): Promise<TestResult> {
    const response = await this.makeRequest('/documents/search?q=test');

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: `Search documents test passed (found ${data.documents.length} results)`,
      };
    } else {
      const error = await response.text();
      return {
        success: false,
        message: `Search documents test failed: ${error}`,
      };
    }
  }

  private async testDeleteDocument(): Promise<TestResult> {
    // Create a document to delete
    const createResponse = await this.makeRequest('/documents', {
      method: 'POST',
      body: JSON.stringify({
        title: 'Document to Delete',
        content: 'This document will be deleted.',
      }),
    });

    if (!createResponse.ok) {
      return {
        success: false,
        message: 'Failed to create document for delete test',
      };
    }

    const createData = await createResponse.json();
    const docId = createData.document.id;

    // Delete the document
    const response = await this.makeRequest(`/documents/${docId}`, {
      method: 'DELETE',
    });

    if (response.ok) {
      return {
        success: true,
        message: 'Delete document test passed',
      };
    } else {
      const error = await response.text();
      return {
        success: false,
        message: `Delete document test failed: ${error}`,
      };
    }
  }

  private async testDeleteFolder(): Promise<TestResult> {
    // Create an empty folder to delete
    const createResponse = await this.makeRequest('/folders', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Folder to Delete',
      }),
    });

    if (!createResponse.ok) {
      return {
        success: false,
        message: 'Failed to create folder for delete test',
      };
    }

    const createData = await createResponse.json();
    const folderId = createData.folder.id;

    // Delete the folder
    const response = await this.makeRequest(`/folders/${folderId}`, {
      method: 'DELETE',
    });

    if (response.ok) {
      return {
        success: true,
        message: 'Delete folder test passed',
      };
    } else {
      const error = await response.text();
      return {
        success: false,
        message: `Delete folder test failed: ${error}`,
      };
    }
  }
}

// Export for use in other files
export { HierarchyTester };

// If running directly (for manual testing)
if (typeof window === 'undefined' && require.main === module) {
  const tester = new HierarchyTester();
  tester.runTests().catch(console.error);
}