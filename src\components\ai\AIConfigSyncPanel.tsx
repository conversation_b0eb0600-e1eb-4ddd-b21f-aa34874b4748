'use client';

/**
 * AI 配置同步面板组件
 * 提供配置同步的管理界面
 */

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  RefreshCw as Sync, 
  Smartphone, 
  Monitor, 
  Tablet, 
  Globe,
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  Trash2,
  Plus,
  RefreshCw,
  History,
  AlertCircle
} from 'lucide-react';

import { useAIConfigSync } from '@/hooks/useAIConfigSync';
import type { DeviceInfo, SyncRecord, ConfigConflict } from '@/hooks/useAIConfigSync';

export function AIConfigSyncPanel() {
  const {
    devices,
    syncHistory,
    conflicts,
    loading,
    syncing,
    error,
    registerDevice,
    syncAllConfigs,
    resolveConflict,
    cleanupSyncHistory,
    getCurrentDeviceInfo,
    clearError
  } = useAIConfigSync();

  const [showAddDevice, setShowAddDevice] = useState(false);
  const [newDeviceName, setNewDeviceName] = useState('');
  const [selectedDeviceType, setSelectedDeviceType] = useState<'desktop' | 'mobile' | 'tablet' | 'web'>('desktop');
  const [selectedPlatform, setSelectedPlatform] = useState('');

  /**
   * 获取设备图标
   */
  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'desktop':
        return <Monitor className="w-4 h-4" />;
      case 'mobile':
        return <Smartphone className="w-4 h-4" />;
      case 'tablet':
        return <Tablet className="w-4 h-4" />;
      case 'web':
        return <Globe className="w-4 h-4" />;
      default:
        return <Monitor className="w-4 h-4" />;
    }
  };

  /**
   * 获取同步状态图标
   */
  const getSyncStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'syncing':
        return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  /**
   * 处理添加设备
   */
  const handleAddDevice = async () => {
    if (!newDeviceName.trim() || !selectedPlatform) {
      return;
    }

    const deviceId = await registerDevice({
      name: newDeviceName,
      type: selectedDeviceType,
      platform: selectedPlatform
    });

    if (deviceId) {
      setShowAddDevice(false);
      setNewDeviceName('');
      setSelectedPlatform('');
    }
  };

  /**
   * 处理同步所有配置
   */
  const handleSyncAll = async () => {
    const currentDevice = devices.find(d => d.isActive);
    if (currentDevice) {
      await syncAllConfigs(currentDevice.id);
    }
  };

  /**
   * 处理解决冲突
   */
  const handleResolveConflict = async (configId: string, resolution: 'local' | 'remote') => {
    await resolveConflict(configId, resolution);
  };

  /**
   * 处理清理历史
   */
  const handleCleanupHistory = async () => {
    if (confirm('确定要清理30天前的同步历史吗？')) {
      await cleanupSyncHistory(30);
    }
  };

  /**
   * 自动检测当前设备
   */
  const handleAutoDetectDevice = () => {
    const deviceInfo = getCurrentDeviceInfo();
    setNewDeviceName(deviceInfo.name);
    setSelectedDeviceType(deviceInfo.type);
    setSelectedPlatform(deviceInfo.platform);
  };

  return (
    <div className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2"
            >
              关闭
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* 同步概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Sync className="w-5 h-5 mr-2" />
            配置同步
          </CardTitle>
          <CardDescription>
            管理您的 AI 配置在不同设备间的同步
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-sm text-muted-foreground">
                已连接设备: {devices.length}
              </div>
              {conflicts.length > 0 && (
                <div className="flex items-center text-sm text-yellow-600">
                  <AlertTriangle className="w-4 h-4 mr-1" />
                  {conflicts.length} 个冲突
                </div>
              )}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAddDevice(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                添加设备
              </Button>
              <Button
                onClick={handleSyncAll}
                disabled={syncing || devices.length === 0}
                size="sm"
              >
                {syncing ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    同步中...
                  </>
                ) : (
                  <>
                    <Sync className="w-4 h-4 mr-2" />
                    同步所有
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 设备列表 */}
      <Card>
        <CardHeader>
          <CardTitle>已连接设备</CardTitle>
          <CardDescription>
            管理您的设备和查看同步状态
          </CardDescription>
        </CardHeader>
        <CardContent>
          {devices.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Monitor className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>暂无已连接的设备</p>
              <Button
                variant="outline"
                onClick={() => setShowAddDevice(true)}
                className="mt-4"
              >
                添加第一个设备
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {devices.map((device) => (
                <div
                  key={device.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    {getDeviceIcon(device.type)}
                    <div>
                      <div className="font-medium">{device.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {device.platform} • 最后同步: {device.lastSyncAt.toLocaleString()}
                      </div>
                    </div>
                    {device.isActive && (
                      <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                        当前设备
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-muted-foreground">已同步</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 配置冲突 */}
      {conflicts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-yellow-600">
              <AlertTriangle className="w-5 h-5 mr-2" />
              配置冲突
            </CardTitle>
            <CardDescription>
              解决配置在不同设备间的冲突
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {conflicts.map((conflict) => (
                <div key={conflict.configId} className="border rounded-lg p-4">
                  <div className="font-medium mb-2">配置 ID: {conflict.configId}</div>
                  <div className="space-y-2">
                    {conflict.conflicts.map((item, index) => (
                      <div key={index} className="text-sm">
                        <div className="font-medium">{item.field} 字段冲突:</div>
                        <div className="ml-4 space-y-1">
                          <div>本地值: {JSON.stringify(item.localValue)}</div>
                          <div>远程值: {JSON.stringify(item.remoteValue)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="flex space-x-2 mt-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleResolveConflict(conflict.configId, 'local')}
                    >
                      使用本地版本
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleResolveConflict(conflict.configId, 'remote')}
                    >
                      使用远程版本
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 同步历史 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <History className="w-5 h-5 mr-2" />
              同步历史
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCleanupHistory}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              清理历史
            </Button>
          </CardTitle>
          <CardDescription>
            查看最近的同步操作记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          {syncHistory.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <History className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>暂无同步历史</p>
            </div>
          ) : (
            <div className="space-y-3">
              {syncHistory.slice(0, 10).map((record) => (
                <div
                  key={record.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    {getSyncStatusIcon(record.status)}
                    <div>
                      <div className="font-medium">
                        {record.action === 'create' ? '创建' : 
                         record.action === 'update' ? '更新' : '删除'} 配置
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {record.createdAt.toLocaleString()}
                        {record.error && ` • 错误: ${record.error}`}
                      </div>
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    设备: {record.deviceId}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 添加设备对话框 */}
      {showAddDevice && (
        <Card className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle>添加新设备</CardTitle>
              <CardDescription>
                注册一个新设备以同步配置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="deviceName">设备名称</Label>
                <Input
                  id="deviceName"
                  value={newDeviceName}
                  onChange={(e) => setNewDeviceName(e.target.value)}
                  placeholder="输入设备名称"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="deviceType">设备类型</Label>
                <Select
                  value={selectedDeviceType}
                  onValueChange={(value: any) => setSelectedDeviceType(value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desktop">桌面设备</SelectItem>
                    <SelectItem value="mobile">手机</SelectItem>
                    <SelectItem value="tablet">平板</SelectItem>
                    <SelectItem value="web">网页端</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="platform">平台</Label>
                <Input
                  id="platform"
                  value={selectedPlatform}
                  onChange={(e) => setSelectedPlatform(e.target.value)}
                  placeholder="如: Windows, macOS, iOS, Android"
                />
              </div>

              <Button
                variant="outline"
                onClick={handleAutoDetectDevice}
                className="w-full"
              >
                自动检测当前设备
              </Button>
            </CardContent>
            <div className="flex justify-end space-x-2 p-6 pt-0">
              <Button
                variant="outline"
                onClick={() => setShowAddDevice(false)}
              >
                取消
              </Button>
              <Button
                onClick={handleAddDevice}
                disabled={!newDeviceName.trim() || !selectedPlatform}
              >
                添加设备
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}