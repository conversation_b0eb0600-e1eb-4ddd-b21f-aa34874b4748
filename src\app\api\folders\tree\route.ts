import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';

/**
 * GET /api/folders/tree - 获取完整的文件夹树结构
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const includeDocuments = searchParams.get('includeDocuments') === 'true';
    const includeEmpty = searchParams.get('includeEmpty') !== 'false';

    // 构建递归查询的包含选项
    const buildIncludeOptions = (depth: number = 5): any => {
      const includeOptions: any = {
        _count: {
          select: {
            documents: true,
            children: true,
          },
        },
      };

      if (includeDocuments) {
        includeOptions.documents = {
          select: {
            id: true,
            title: true,
            createdAt: true,
            updatedAt: true,
            wordCount: true,
            charCount: true,
          },
          orderBy: {
            updatedAt: 'desc',
          },
        };
      }

      if (depth > 0) {
        includeOptions.children = {
          include: buildIncludeOptions(depth - 1),
          orderBy: {
            name: 'asc',
          },
        };
      }

      return includeOptions;
    };

    // 获取根级文件夹
    const rootFolders = await prisma.folder.findMany({
      where: {
        userId: session.user.id,
        parentId: null,
      },
      include: buildIncludeOptions(),
      orderBy: {
        name: 'asc',
      },
    });

    // 如果需要，过滤掉空文件夹
    const filterEmptyFolders = (folders: any[]): any[] => {
      if (includeEmpty) return folders;

      return folders.filter(folder => {
        // 递归过滤子文件夹
        if (folder.children) {
          folder.children = filterEmptyFolders(folder.children);
        }

        // 保留有文档或有子文件夹的文件夹
        return folder._count.documents > 0 ||
               (folder.children && folder.children.length > 0);
      });
    };

    const filteredFolders = filterEmptyFolders(rootFolders);

    // 获取根目录下的文档（如果需要）
    let rootDocuments = [];
    if (includeDocuments) {
      rootDocuments = await prisma.document.findMany({
        where: {
          userId: session.user.id,
          folderId: null,
          isDeleted: false, // 只包含未删除的文档
        },
        select: {
          id: true,
          title: true,
          createdAt: true,
          updatedAt: true,
          wordCount: true,
          charCount: true,
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });
    }

    // 计算统计信息
    const stats = await prisma.$transaction([
      prisma.folder.count({
        where: {
          userId: session.user.id,
          isDeleted: false, // 只统计未删除的文件夹
        },
      }),
      prisma.document.count({
        where: {
          userId: session.user.id,
          isDeleted: false, // 只统计未删除的文档
        },
      }),
    ]);

    return NextResponse.json({
      tree: {
        folders: filteredFolders,
        documents: rootDocuments,
      },
      stats: {
        totalFolders: stats[0],
        totalDocuments: stats[1],
      },
    });
  } catch (error) {
    console.error('获取文件夹树失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}