/**
 * AI 处理状态管理 Hook
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { aiProcessingManager } from '@/lib/services/ai-processing-manager';
import type {
  AIProcessingProgress,
  AIProcessingResult,
  AIProcessingContext,
  AIProcessingOptions,
  AIProcessingStats,
} from '@/types/ai-status.types';

/**
 * AI 处理状态 Hook 的返回类型
 */
interface UseAIProcessingReturn {
  /** 当前处理进度 */
  progress: AIProcessingProgress | null;
  /** 是否正在处理 */
  isProcessing: boolean;
  /** 处理结果 */
  result: AIProcessingResult | null;
  /** 错误信息 */
  error: string | null;
  /** 开始处理 */
  startProcessing: (
    type: string,
    input: string,
    options?: Partial<AIProcessingOptions>
  ) => Promise<string>;
  /** 取消处理 */
  cancelProcessing: () => void;
  /** 清除状态 */
  clearState: () => void;
  /** 重试处理 */
  retryProcessing: () => Promise<void>;
}

/**
 * 多个 AI 处理状态管理 Hook 的返回类型
 */
interface UseMultipleAIProcessingReturn {
  /** 所有处理进度 */
  allProgress: Map<string, AIProcessingProgress>;
  /** 活动处理数量 */
  activeCount: number;
  /** 统计信息 */
  stats: AIProcessingStats;
  /** 开始新处理 */
  startProcessing: (
    type: string,
    input: string,
    options?: Partial<AIProcessingOptions>
  ) => Promise<string>;
  /** 取消指定处理 */
  cancelProcessing: (id: string) => void;
  /** 取消所有处理 */
  cancelAllProcessing: () => void;
  /** 清除完成的处理 */
  clearCompleted: () => void;
}

/**
 * 单个 AI 处理状态管理 Hook
 */
export function useAIProcessing(
  documentId?: string,
  defaultOptions?: Partial<AIProcessingOptions>
): UseAIProcessingReturn {
  const [progress, setProgress] = useState<AIProcessingProgress | null>(null);
  const [result, setResult] = useState<AIProcessingResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [currentId, setCurrentId] = useState<string | null>(null);
  const [lastContext, setLastContext] = useState<AIProcessingContext | null>(null);

  const unsubscribeRef = useRef<(() => void) | null>(null);

  /**
   * 清理订阅
   */
  const cleanup = useCallback(() => {
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }
  }, []);

  /**
   * 开始处理
   */
  const startProcessing = useCallback(async (
    type: string,
    input: string,
    options: Partial<AIProcessingOptions> = {}
  ): Promise<string> => {
    // 清理之前的状态
    cleanup();
    setProgress(null);
    setResult(null);
    setError(null);

    // 生成处理 ID
    const id = `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setCurrentId(id);

    // 合并选项
    const mergedOptions: AIProcessingOptions = {
      showDetailedProgress: true,
      showTokenCount: true,
      showTimeEstimate: true,
      allowCancel: true,
      autoHideDelay: 5000,
      progressUpdateInterval: 100,
      ...defaultOptions,
      ...options,
    };

    // 创建处理上下文
    const context: AIProcessingContext = {
      id,
      documentId,
      userId: 'current-user', // 实际应用中应该从认证状态获取
      type,
      input,
      options: mergedOptions,
      startTime: new Date(),
      cancelToken: new AbortController(),
    };

    setLastContext(context);

    // 订阅状态变化
    unsubscribeRef.current = aiProcessingManager.subscribe(id, (newProgress) => {
      setProgress(newProgress);
      
      if (newProgress.error) {
        setError(newProgress.error);
      }
    });

    // 监听完成事件
    const completeHandler = (event: any) => {
      if (event.data.result) {
        setResult(event.data.result);
      }
    };

    aiProcessingManager.once(`complete:${id}`, completeHandler);
    aiProcessingManager.once(`error:${id}`, completeHandler);

    try {
      // 开始处理
      await aiProcessingManager.startProcessing(context);
      return id;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('处理失败');
      aiProcessingManager.handleError(id, error);
      throw error;
    }
  }, [documentId, defaultOptions, cleanup]);

  /**
   * 取消处理
   */
  const cancelProcessing = useCallback(() => {
    if (currentId) {
      aiProcessingManager.cancelProcessing(currentId);
    }
  }, [currentId]);

  /**
   * 清除状态
   */
  const clearState = useCallback(() => {
    cleanup();
    setProgress(null);
    setResult(null);
    setError(null);
    setCurrentId(null);
    setLastContext(null);
  }, [cleanup]);

  /**
   * 重试处理
   */
  const retryProcessing = useCallback(async (): Promise<void> => {
    if (!lastContext) {
      throw new Error('没有可重试的处理上下文');
    }

    await startProcessing(
      lastContext.type,
      lastContext.input,
      lastContext.options
    );
  }, [lastContext, startProcessing]);

  // 计算是否正在处理
  const isProcessing = progress?.status === 'preparing' || 
                      progress?.status === 'connecting' || 
                      progress?.status === 'processing' || 
                      progress?.status === 'generating';

  // 清理副作用
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    progress,
    isProcessing,
    result,
    error,
    startProcessing,
    cancelProcessing,
    clearState,
    retryProcessing,
  };
}

/**
 * 多个 AI 处理状态管理 Hook
 */
export function useMultipleAIProcessing(
  documentId?: string,
  defaultOptions?: Partial<AIProcessingOptions>
): UseMultipleAIProcessingReturn {
  const [allProgress, setAllProgress] = useState<Map<string, AIProcessingProgress>>(new Map());
  const [stats, setStats] = useState<AIProcessingStats>({
    totalProcessed: 0,
    successCount: 0,
    errorCount: 0,
    cancelledCount: 0,
    averageProcessingTime: 0,
    totalTokensUsed: 0,
    byType: {},
    byProvider: {},
  });

  const activeProcessingIds = useRef<Set<string>>(new Set());

  /**
   * 更新所有进度状态
   */
  const updateAllProgress = useCallback(() => {
    const currentProgress = aiProcessingManager.getAllActiveProcessing();
    setAllProgress(new Map(currentProgress));
    setStats(aiProcessingManager.getStats());
  }, []);

  /**
   * 开始新处理
   */
  const startProcessing = useCallback(async (
    type: string,
    input: string,
    options: Partial<AIProcessingOptions> = {}
  ): Promise<string> => {
    // 生成处理 ID
    const id = `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 合并选项
    const mergedOptions: AIProcessingOptions = {
      showDetailedProgress: true,
      showTokenCount: true,
      showTimeEstimate: true,
      allowCancel: true,
      autoHideDelay: 5000,
      progressUpdateInterval: 100,
      ...defaultOptions,
      ...options,
    };

    // 创建处理上下文
    const context: AIProcessingContext = {
      id,
      documentId,
      userId: 'current-user', // 实际应用中应该从认证状态获取
      type,
      input,
      options: mergedOptions,
      startTime: new Date(),
      cancelToken: new AbortController(),
    };

    // 添加到活动处理集合
    activeProcessingIds.current.add(id);

    // 订阅状态变化
    const unsubscribe = aiProcessingManager.subscribe(id, () => {
      updateAllProgress();
    });

    // 监听完成或错误事件
    const cleanupHandler = () => {
      activeProcessingIds.current.delete(id);
      unsubscribe();
      updateAllProgress();
    };

    aiProcessingManager.once(`complete:${id}`, cleanupHandler);
    aiProcessingManager.once(`error:${id}`, cleanupHandler);
    aiProcessingManager.once(`cancel:${id}`, cleanupHandler);

    try {
      // 开始处理
      await aiProcessingManager.startProcessing(context);
      updateAllProgress();
      return id;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('处理失败');
      aiProcessingManager.handleError(id, error);
      throw error;
    }
  }, [documentId, defaultOptions, updateAllProgress]);

  /**
   * 取消指定处理
   */
  const cancelProcessing = useCallback((id: string) => {
    aiProcessingManager.cancelProcessing(id);
  }, []);

  /**
   * 取消所有处理
   */
  const cancelAllProcessing = useCallback(() => {
    activeProcessingIds.current.forEach(id => {
      aiProcessingManager.cancelProcessing(id);
    });
  }, []);

  /**
   * 清除完成的处理
   */
  const clearCompleted = useCallback(() => {
    // 这里可以实现清除已完成处理的逻辑
    updateAllProgress();
  }, [updateAllProgress]);

  // 计算活动处理数量
  const activeCount = Array.from(allProgress.values()).filter(
    progress => progress.status === 'preparing' || 
               progress.status === 'connecting' || 
               progress.status === 'processing' || 
               progress.status === 'generating'
  ).length;

  // 初始化时更新状态
  useEffect(() => {
    updateAllProgress();
  }, [updateAllProgress]);

  return {
    allProgress,
    activeCount,
    stats,
    startProcessing,
    cancelProcessing,
    cancelAllProcessing,
    clearCompleted,
  };
}