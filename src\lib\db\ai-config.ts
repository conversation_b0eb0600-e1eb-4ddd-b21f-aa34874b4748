import { prisma } from './prisma'
import type { AIConfiguration } from '@prisma/client'

export async function createAIConfiguration(data: {
  userId: string
  provider: string
  apiKey?: string
  endpoint?: string
  model: string
  maxTokens?: number
  temperature?: number
  isDefault?: boolean
}): Promise<AIConfiguration> {
  // If this is set as default, unset all other defaults for this user
  if (data.isDefault) {
    await prisma.aIConfiguration.updateMany({
      where: { userId: data.userId },
      data: { isDefault: false },
    })
  }

  return prisma.aIConfiguration.create({
    data: {
      userId: data.userId,
      provider: data.provider,
      apiKey: data.apiKey,
      endpoint: data.endpoint,
      model: data.model,
      maxTokens: data.maxTokens ?? 2000,
      temperature: data.temperature ?? 0.7,
      isDefault: data.isDefault ?? false,
    },
  })
}

export async function getAIConfigurationsByUserId(
  userId: string
): Promise<AIConfiguration[]> {
  return prisma.aIConfiguration.findMany({
    where: { userId },
    orderBy: [{ isDefault: 'desc' }, { createdAt: 'asc' }],
  })
}

export async function getDefaultAIConfiguration(
  userId: string
): Promise<AIConfiguration | null> {
  return prisma.aIConfiguration.findFirst({
    where: { userId, isDefault: true },
  })
}

export async function getAIConfigurationById(
  id: string,
  userId: string
): Promise<AIConfiguration | null> {
  return prisma.aIConfiguration.findFirst({
    where: { id, userId },
  })
}

export async function updateAIConfiguration(
  id: string,
  userId: string,
  data: Partial<{
    provider: string
    apiKey: string
    endpoint: string
    model: string
    maxTokens: number
    temperature: number
    isDefault: boolean
  }>
): Promise<AIConfiguration> {
  // If this is being set as default, unset all other defaults for this user
  if (data.isDefault) {
    await prisma.aIConfiguration.updateMany({
      where: { userId, id: { not: id } },
      data: { isDefault: false },
    })
  }

  return prisma.aIConfiguration.update({
    where: { id, userId },
    data,
  })
}

export async function deleteAIConfiguration(
  id: string,
  userId: string
): Promise<AIConfiguration> {
  return prisma.aIConfiguration.delete({
    where: { id, userId },
  })
}

export async function createAIInteraction(data: {
  documentId: string
  userId: string
  type: string
  input: string
  output: string
  provider: string
  model: string
  tokens?: number
}): Promise<void> {
  await prisma.aIInteraction.create({
    data: {
      documentId: data.documentId,
      userId: data.userId,
      type: data.type,
      input: data.input,
      output: data.output,
      provider: data.provider,
      model: data.model,
      tokens: data.tokens ?? 0,
    },
  })
}

export async function getAIInteractionsByDocumentId(
  documentId: string,
  userId: string
): Promise<any[]> {
  return prisma.aIInteraction.findMany({
    where: { documentId, userId },
    orderBy: { createdAt: 'desc' },
  })
}

export async function getAIInteractionsByUserId(
  userId: string,
  limit?: number
): Promise<any[]> {
  return prisma.aIInteraction.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' },
    take: limit,
  })
}
