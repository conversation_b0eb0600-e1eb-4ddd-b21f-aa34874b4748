/**
 * AI 文档推荐 API 路由
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { aiDocumentRecommender } from '@/lib/services/ai-document-recommender';
import { z } from 'zod';

// 请求参数验证模式
const relatedDocumentsSchema = z.object({
  documentId: z.string().min(1, '文档ID不能为空'),
  limit: z.number().min(1).max(50).optional().default(10)
});

const topicDocumentsSchema = z.object({
  topic: z.string().min(1, '主题不能为空'),
  excludeDocumentId: z.string().optional(),
  limit: z.number().min(1).max(50).optional().default(10)
});

const recentDocumentsSchema = z.object({
  days: z.number().min(1).max(30).optional().default(7),
  limit: z.number().min(1).max(50).optional().default(10)
});

const smartRecommendationsSchema = z.object({
  contextDocumentId: z.string().optional(),
  limit: z.number().min(1).max(50).optional().default(10)
});

/**
 * POST /api/ai/document-recommendations/related
 * 获取相关文档推荐
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { documentId, limit } = relatedDocumentsSchema.parse(body);

    // 获取相关文档推荐
    const recommendations = await aiDocumentRecommender.getRelatedDocuments(
      documentId,
      session.user.id,
      limit
    );

    return NextResponse.json({
      success: true,
      data: recommendations
    });

  } catch (error) {
    console.error('获取相关文档推荐失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '获取推荐失败' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/ai/document-recommendations/by-topic
 * 基于主题获取相关文档
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url);
    const topic = searchParams.get('topic');
    const excludeDocumentId = searchParams.get('excludeDocumentId') || undefined;
    const limit = parseInt(searchParams.get('limit') || '10');

    const { topic: validatedTopic, excludeDocumentId: validatedExclude, limit: validatedLimit } = 
      topicDocumentsSchema.parse({ topic, excludeDocumentId, limit });

    // 获取主题相关文档
    const recommendations = await aiDocumentRecommender.getDocumentsByTopic(
      validatedTopic,
      session.user.id,
      validatedExclude,
      validatedLimit
    );

    return NextResponse.json({
      success: true,
      data: recommendations
    });

  } catch (error) {
    console.error('获取主题相关文档失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '获取推荐失败' },
      { status: 500 }
    );
  }
}
/**
 * PU
T /api/ai/document-recommendations/recent
 * 获取最近相关的文档
 */
export async function PUT(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { days, limit } = recentDocumentsSchema.parse(body);

    // 获取最近相关文档
    const recommendations = await aiDocumentRecommender.getRecentlyRelatedDocuments(
      session.user.id,
      days,
      limit
    );

    return NextResponse.json({
      success: true,
      data: recommendations
    });

  } catch (error) {
    console.error('获取最近相关文档失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '获取推荐失败' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/ai/document-recommendations/smart
 * 获取智能推荐
 */
export async function PATCH(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { contextDocumentId, limit } = smartRecommendationsSchema.parse(body);

    // 获取智能推荐
    const recommendations = await aiDocumentRecommender.getSmartRecommendations(
      session.user.id,
      contextDocumentId,
      limit
    );

    return NextResponse.json({
      success: true,
      data: recommendations
    });

  } catch (error) {
    console.error('获取智能推荐失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '获取推荐失败' },
      { status: 500 }
    );
  }
}
