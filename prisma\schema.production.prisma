// Production Prisma schema for PostgreSQL
// To use this schema for production:
// 1. Copy this file to schema.prisma
// 2. Set DATABASE_URL to your PostgreSQL connection string
// 3. Run: npx prisma migrate deploy

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String   @id @default(cuid())
  email        String   @unique
  name         String
  avatar       String?
  subscription String   @default("free") // free, pro, enterprise
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  folders        Folder[]
  documents      Document[]
  aiConfigs      AIConfiguration[]
  aiInteractions AIInteraction[]

  @@map("users")
}

model Folder {
  id        String   @id @default(cuid())
  name      String
  parentId  String?
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent    Folder?    @relation("FolderHierarchy", fields: [parentId], references: [id])
  children  Folder[]   @relation("FolderHierarchy")
  documents Document[]

  @@map("folders")
}

model Document {
  id          String    @id @default(cuid())
  title       String
  content     String    // JSON string of TipTap content
  folderId    String?
  userId      String
  wordCount   Int       @default(0)
  charCount   Int       @default(0)
  isPublic    Boolean   @default(false)
  shareToken  String?   @unique
  lastSyncAt  DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  user           User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  folder         Folder?           @relation(fields: [folderId], references: [id])
  history        DocumentHistory[]
  aiInteractions AIInteraction[]

  @@map("documents")
}

model DocumentHistory {
  id         String   @id @default(cuid())
  documentId String
  version    Int
  content    String   // JSON string
  changeType String   // user, ai
  createdAt  DateTime @default(now())

  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@map("document_history")
}

model AIConfiguration {
  id          String   @id @default(cuid())
  userId      String
  provider    String   // openai, ollama, gemini
  apiKey      String?
  endpoint    String?
  model       String
  maxTokens   Int      @default(2000)
  temperature Float    @default(0.7)
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ai_configurations")
}

model AIInteraction {
  id         String   @id @default(cuid())
  documentId String
  userId     String
  type       String   // generate, rewrite, summarize, analyze, translate, explain
  input      String
  output     String
  provider   String
  model      String
  tokens     Int      @default(0)
  createdAt  DateTime @default(now())

  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ai_interactions")
}