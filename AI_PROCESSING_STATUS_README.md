# AI 处理状态和进度显示功能

## 功能概述

AI 处理状态和进度显示功能为用户提供了完整的 AI 处理过程可视化，包括实时进度跟踪、状态管理、错误处理和结果展示等功能。

## 主要特性

### 🔄 实时进度跟踪
- **多阶段进度显示**：准备中 → 连接中 → 处理中 → 生成中 → 完成
- **详细进度信息**：每个阶段的具体进度百分比和描述
- **总体进度条**：显示整个处理过程的总体进度
- **时间估算**：显示已用时间和预计剩余时间

### 📊 状态管理
- **多种状态支持**：准备、连接、处理、生成、完成、错误、取消
- **状态转换**：自动管理状态之间的转换逻辑
- **并发处理**：支持多个 AI 任务同时进行
- **状态持久化**：处理状态在页面刷新后保持

### 🎛️ 用户交互
- **取消功能**：用户可以随时取消正在进行的处理
- **重试机制**：处理失败后可以一键重试
- **暂停/恢复**：支持暂停和恢复处理过程
- **批量操作**：支持批量取消、清理等操作

### 📈 统计分析
- **处理统计**：总处理数、成功数、失败数、取消数
- **性能指标**：平均处理时间、令牌消耗统计
- **分类统计**：按功能类型和 AI 提供商分组统计
- **趋势分析**：处理历史和使用趋势

## 技术架构

### 核心组件

#### 1. 类型定义 (`src/types/ai-status.types.ts`)
```typescript
// 处理状态枚举
export enum AIProcessingStatus {
  IDLE = 'idle',
  PREPARING = 'preparing',
  CONNECTING = 'connecting',
  PROCESSING = 'processing',
  GENERATING = 'generating',
  COMPLETED = 'completed',
  ERROR = 'error',
  CANCELLED = 'cancelled',
}

// 处理进度接口
export interface AIProcessingProgress {
  status: AIProcessingStatus;
  overallProgress: number;
  currentStage?: string;
  stages: AIProcessingStage[];
  startTime: Date;
  estimatedTimeRemaining?: number;
  tokensProcessed?: number;
  error?: string;
  cancellable: boolean;
}
```

#### 2. 状态管理器 (`src/lib/services/ai-processing-manager.ts`)
```typescript
export class AIProcessingManagerImpl {
  // 开始处理
  async startProcessing(context: AIProcessingContext): Promise<void>
  
  // 更新进度
  updateProgress(id: string, progress: Partial<AIProcessingProgress>): void
  
  // 完成处理
  completeProcessing(id: string, result: AIProcessingResult): void
  
  // 处理错误
  handleError(id: string, error: Error): void
  
  // 取消处理
  cancelProcessing(id: string): void
  
  // 订阅状态变化
  subscribe(id: string, callback: (progress: AIProcessingProgress) => void): () => void
}
```

#### 3. React Hooks (`src/hooks/use-ai-processing.ts`)
```typescript
// 单个处理状态管理
export function useAIProcessing(documentId?: string): {
  progress: AIProcessingProgress | null;
  isProcessing: boolean;
  result: AIProcessingResult | null;
  error: string | null;
  startProcessing: (type: string, input: string, options?: Partial<AIProcessingOptions>) => Promise<string>;
  cancelProcessing: () => void;
  clearState: () => void;
  retryProcessing: () => Promise<void>;
}

// 多个处理状态管理
export function useMultipleAIProcessing(documentId?: string): {
  allProgress: Map<string, AIProcessingProgress>;
  activeCount: number;
  stats: AIProcessingStats;
  startProcessing: (type: string, input: string, options?: Partial<AIProcessingOptions>) => Promise<string>;
  cancelProcessing: (id: string) => void;
  cancelAllProcessing: () => void;
  clearCompleted: () => void;
}
```

### UI 组件

#### 1. 进度指示器 (`AIProcessingIndicator`)
- **详细模式**：显示完整的处理阶段和进度信息
- **紧凑模式**：简化显示，适用于工具栏或状态栏
- **交互功能**：取消、重试、暂停/恢复按钮
- **自定义选项**：可配置显示内容和行为

```tsx
<AIProcessingIndicator
  progress={progress}
  options={{
    showDetailedProgress: true,
    showTokenCount: true,
    showTimeEstimate: true,
    allowCancel: true,
  }}
  onCancel={handleCancel}
  onRetry={handleRetry}
  compact={false}
/>
```

#### 2. 状态栏 (`AIProcessingStatusBar`)
- **底部固定显示**：在页面底部显示当前处理状态
- **可展开详情**：点击展开查看详细处理信息
- **批量操作**：支持取消所有处理任务
- **自动隐藏**：完成后自动隐藏

```tsx
<AIProcessingStatusBar
  documentId="doc-123"
  showDetails={true}
/>
```

#### 3. 处理管理器 (`AIProcessingManager`)
- **完整管理界面**：提供所有处理任务的管理功能
- **过滤和搜索**：按状态、类型、时间范围过滤
- **批量选择**：支持多选和批量操作
- **统计面板**：显示详细的处理统计信息
- **导出功能**：导出处理记录为 JSON 文件

```tsx
<AIProcessingManager
  documentId="doc-123"
  showStats={true}
  showHistory={true}
/>
```

## 使用方法

### 1. 基本使用

```tsx
import { useAIProcessing } from '@/hooks/use-ai-processing';
import { AIProcessingIndicator } from '@/components/ai/AIProcessingIndicator';

function MyComponent() {
  const {
    progress,
    isProcessing,
    result,
    error,
    startProcessing,
    cancelProcessing,
    retryProcessing,
  } = useAIProcessing('my-document');

  const handleStartProcessing = async () => {
    try {
      await startProcessing('generate', '请帮我写一段文章', {
        showDetailedProgress: true,
        showTokenCount: true,
        allowCancel: true,
      });
    } catch (error) {
      console.error('处理失败:', error);
    }
  };

  return (
    <div>
      <button onClick={handleStartProcessing} disabled={isProcessing}>
        开始处理
      </button>
      
      {progress && (
        <AIProcessingIndicator
          progress={progress}
          onCancel={cancelProcessing}
          onRetry={retryProcessing}
        />
      )}
      
      {result && (
        <div>
          <h3>处理结果</h3>
          <p>{result.output}</p>
        </div>
      )}
      
      {error && (
        <div className="error">
          错误: {error}
        </div>
      )}
    </div>
  );
}
```

### 2. 多任务管理

```tsx
import { useMultipleAIProcessing } from '@/hooks/use-ai-processing';
import { AIProcessingManager } from '@/components/ai/AIProcessingManager';

function MultiTaskComponent() {
  const {
    allProgress,
    activeCount,
    stats,
    startProcessing,
    cancelAllProcessing,
  } = useMultipleAIProcessing('my-document');

  const startBatchProcessing = async () => {
    const tasks = [
      { type: 'generate', input: '生成内容1' },
      { type: 'rewrite', input: '改写内容2' },
      { type: 'summarize', input: '总结内容3' },
    ];

    for (const task of tasks) {
      await startProcessing(task.type, task.input);
    }
  };

  return (
    <div>
      <div>
        <button onClick={startBatchProcessing}>开始批量处理</button>
        <button onClick={cancelAllProcessing} disabled={activeCount === 0}>
          取消所有
        </button>
        <span>活动任务: {activeCount}</span>
      </div>
      
      <AIProcessingManager
        documentId="my-document"
        showStats={true}
      />
    </div>
  );
}
```

### 3. 状态栏集成

```tsx
import { AIProcessingStatusBar } from '@/components/ai/AIProcessingStatusBar';

function App() {
  return (
    <div>
      {/* 你的应用内容 */}
      <main>
        {/* 页面内容 */}
      </main>
      
      {/* 状态栏会在有处理任务时自动显示 */}
      <AIProcessingStatusBar
        documentId="current-document"
        showDetails={true}
      />
    </div>
  );
}
```

## 配置选项

### AIProcessingOptions
```typescript
interface AIProcessingOptions {
  /** 是否显示详细进度 */
  showDetailedProgress?: boolean;
  /** 是否显示令牌计数 */
  showTokenCount?: boolean;
  /** 是否显示时间估算 */
  showTimeEstimate?: boolean;
  /** 是否允许取消 */
  allowCancel?: boolean;
  /** 自动隐藏延迟（毫秒） */
  autoHideDelay?: number;
  /** 进度更新间隔（毫秒） */
  progressUpdateInterval?: number;
}
```

### 默认配置
```typescript
const defaultOptions: AIProcessingOptions = {
  showDetailedProgress: true,
  showTokenCount: true,
  showTimeEstimate: true,
  allowCancel: true,
  autoHideDelay: 5000,
  progressUpdateInterval: 100,
};
```

## 样式定制

### CSS 类名
组件使用 Tailwind CSS 类名，可以通过 `className` 属性进行自定义：

```tsx
<AIProcessingIndicator
  progress={progress}
  className="custom-indicator-style"
  compact={false}
/>
```

### 主题定制
可以通过 CSS 变量定制主题颜色：

```css
:root {
  --ai-processing-primary: #3b82f6;
  --ai-processing-success: #10b981;
  --ai-processing-error: #ef4444;
  --ai-processing-warning: #f59e0b;
}
```

## 性能优化

### 1. 事件驱动更新
- 使用 EventEmitter 模式，只在状态变化时更新 UI
- 避免不必要的重渲染

### 2. 内存管理
- 自动清理完成的处理任务
- 取消订阅防止内存泄漏

### 3. 批量操作优化
- 支持批量取消和清理操作
- 优化大量并发处理的性能

## 错误处理

### 1. 网络错误
```typescript
try {
  await startProcessing('generate', 'input');
} catch (error) {
  if (error instanceof AIServiceError) {
    switch (error.type) {
      case AIErrorType.NETWORK_ERROR:
        // 处理网络错误
        break;
      case AIErrorType.QUOTA_EXCEEDED:
        // 处理配额超限
        break;
      // 其他错误类型...
    }
  }
}
```

### 2. 处理超时
```typescript
const options: AIProcessingOptions = {
  timeout: 30000, // 30秒超时
  allowCancel: true,
};

await startProcessing('generate', 'input', options);
```

### 3. 重试机制
```typescript
const { retryProcessing } = useAIProcessing();

// 处理失败后重试
if (error) {
  await retryProcessing();
}
```

## 测试

### 运行测试
```bash
# 运行功能测试
npx tsx scripts/test-ai-processing-status.ts

# 启动演示页面
npm run dev
# 访问 http://localhost:3000/ai-processing-demo
```

### 测试覆盖
- ✅ 单个处理流程测试
- ✅ 多个并发处理测试
- ✅ 处理取消功能测试
- ✅ 错误处理测试
- ✅ 统计信息测试

## 最佳实践

### 1. 用户体验
- 始终提供清晰的进度指示
- 允许用户取消长时间运行的任务
- 在处理失败时提供重试选项
- 显示有意义的错误消息

### 2. 性能考虑
- 避免频繁的状态更新
- 使用防抖处理用户交互
- 及时清理完成的任务
- 限制并发处理数量

### 3. 可访问性
- 提供键盘导航支持
- 使用语义化的 HTML 结构
- 添加适当的 ARIA 标签
- 支持屏幕阅读器

## 故障排除

### 常见问题

1. **进度不更新**
   - 检查是否正确订阅了状态变化
   - 确认处理管理器正常工作
   - 验证组件是否正确接收 props

2. **取消功能不工作**
   - 确认 `cancellable` 属性为 true
   - 检查 AbortController 是否正确设置
   - 验证取消回调函数是否正确绑定

3. **内存泄漏**
   - 确保在组件卸载时取消订阅
   - 清理定时器和事件监听器
   - 及时清理完成的处理任务

### 调试技巧
- 使用浏览器开发者工具查看状态变化
- 启用详细日志记录
- 使用 React DevTools 检查组件状态
- 监控网络请求和响应

## 扩展功能

### 计划中的功能
- **处理队列管理**：支持处理任务队列和优先级
- **离线支持**：在网络断开时缓存处理请求
- **通知系统**：处理完成时发送桌面通知
- **历史记录**：保存和查看处理历史
- **性能分析**：详细的性能指标和分析

### 自定义扩展
可以通过继承基础类或实现接口来扩展功能：

```typescript
class CustomProcessingManager extends AIProcessingManagerImpl {
  // 自定义实现
}
```

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个功能！

### 开发环境设置
1. 克隆项目并安装依赖
2. 运行测试确保功能正常
3. 启动开发服务器进行测试
4. 提交前运行完整的测试套件

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 和 Prettier 规则
- 添加适当的注释和文档
- 编写单元测试覆盖新功能