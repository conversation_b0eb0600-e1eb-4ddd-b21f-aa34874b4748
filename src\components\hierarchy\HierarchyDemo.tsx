'use client';

import React, { useState, useEffect } from 'react';
import { FolderTreeFixed } from './FolderTreeFixed';
import { useServerDocument } from '@/hooks/useServerDocuments';

export const HierarchyDemo: React.FC = () => {
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const { document, loading: documentLoading } = useServerDocument(selectedDocumentId);

  // 添加调试事件监听
  useEffect(() => {
    const handleDocumentMoved = (event: CustomEvent) => {
      setDebugInfo(prev => [...prev, `文档移动: ${event.detail.documentId} -> ${event.detail.targetFolderId || '根目录'}`]);
    };

    const handleFolderMoved = (event: CustomEvent) => {
      setDebugInfo(prev => [...prev, `文件夹移动: ${event.detail.folderId} -> ${event.detail.targetFolderId || '根目录'}`]);
    };

    window.addEventListener('document-moved', handleDocumentMoved as EventListener);
    window.addEventListener('folder-moved', handleFolderMoved as EventListener);

    return () => {
      window.removeEventListener('document-moved', handleDocumentMoved as EventListener);
      window.removeEventListener('folder-moved', handleFolderMoved as EventListener);
    };
  }, []);

  const handleSelectFolder = (folderId: string | null) => {
    setSelectedFolderId(folderId);
    setSelectedDocumentId(null); // Clear document selection when folder is selected
  };

  const handleSelectDocument = (documentId: string) => {
    setSelectedDocumentId(documentId);
    setSelectedFolderId(null); // Clear folder selection when document is selected
  };

  const clearDebugInfo = () => {
    setDebugInfo([]);
  };

  return (
    <div className="h-screen flex">
      {/* Sidebar with folder tree */}
      <div className="w-80 border-r bg-gray-50">
        <FolderTreeFixed
          onSelectFolder={handleSelectFolder}
          onSelectDocument={handleSelectDocument}
          selectedFolderId={selectedFolderId}
          selectedDocumentId={selectedDocumentId}
        />
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b p-4 bg-white">
          <div className="flex items-center justify-between">
            <div>
              {selectedDocumentId && document ? (
                <div>
                  <h1 className="text-xl font-semibold">{document.title}</h1>
                  <p className="text-sm text-gray-500">
                    {document.wordCount} words • {document.charCount} characters
                  </p>
                </div>
              ) : selectedFolderId ? (
                <div>
                  <h1 className="text-xl font-semibold">已选择文件夹</h1>
                  <p className="text-sm text-gray-500">文件夹 ID: {selectedFolderId}</p>
                </div>
              ) : (
                <div>
                  <h1 className="text-xl font-semibold">文档编辑器</h1>
                  <p className="text-sm text-gray-500">从侧边栏选择文档或文件夹</p>
                </div>
              )}
            </div>

            <div className="flex gap-2">
              <button className="px-3 py-1 text-sm border rounded hover:bg-gray-50">
                保存
              </button>
              <button className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600">
                分享
              </button>
            </div>
          </div>
        </div>

        {/* Content area */}
        <div className="flex-1 p-4 overflow-auto">
          {documentLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-gray-500">正在加载文档...</div>
            </div>
          ) : selectedDocumentId && document ? (
            <div className="h-full">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  文档标题
                </label>
                <input
                  type="text"
                  value={document.title}
                  className="w-full px-3 py-2 border rounded-md"
                  readOnly
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  内容
                </label>
                <textarea
                  value={document.content}
                  className="w-full h-96 px-3 py-2 border rounded-md resize-none"
                  readOnly
                />
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                  <strong>创建时间:</strong> {new Date(document.createdAt).toLocaleString()}
                </div>
                <div>
                  <strong>更新时间:</strong> {new Date(document.updatedAt).toLocaleString()}
                </div>
                <div>
                  <strong>词数:</strong> {document.wordCount}
                </div>
                <div>
                  <strong>字符数:</strong> {document.charCount}
                </div>
              </div>
            </div>
          ) : selectedFolderId ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <div className="text-gray-500 mb-4">文件夹视图</div>
                <p className="text-sm text-gray-400">
                  这里将显示文件夹内容并允许进行文件夹管理操作。
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="text-center">
                <div className="text-gray-500 mb-4">欢迎使用文档编辑器</div>
                <p className="text-sm text-gray-400 mb-6">
                  从侧边栏选择一个文档开始编辑，或创建一个新文档。
                </p>
              </div>

              {/* 调试信息面板 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-700">操作日志</h3>
                  <button
                    onClick={clearDebugInfo}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    清除
                  </button>
                </div>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {debugInfo.length === 0 ? (
                    <p className="text-sm text-gray-500">暂无操作记录</p>
                  ) : (
                    debugInfo.map((info, index) => (
                      <div key={index} className="text-sm text-gray-600 bg-white px-2 py-1 rounded">
                        {info}
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* 使用指南 */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-semibold text-blue-800 mb-3">测试指南</h3>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• 点击文件夹展开/折叠其内容</li>
                  <li>• 右键点击文件夹或文档查看菜单</li>
                  <li>• 拖拽文件夹和文档进行移动操作</li>
                  <li>• 使用悬浮的 + 按钮创建新项目</li>
                  <li>• 移动操作会在操作日志中显示</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};