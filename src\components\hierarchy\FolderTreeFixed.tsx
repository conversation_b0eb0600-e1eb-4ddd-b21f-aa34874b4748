'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { ChevronRight, ChevronDown, Folder, FolderOpen, FileText, Plus, MoreHorizontal, Edit2, Trash2, Copy, Move, RefreshCw, Expand, Minimize2 } from 'lucide-react';
import { useFolders } from '@/hooks/useFolders';
import { FolderWithRelations } from '@/lib/services/folder-service';

interface FolderTreeFixedProps {
  onSelectFolder?: (folderId: string | null) => void;
  onSelectDocument?: (documentId: string) => void;
  selectedFolderId?: string | null;
  selectedDocumentId?: string | null;
}

interface InlineEditProps {
  value: string;
  onSave: (value: string) => void;
  onCancel: () => void;
  className?: string;
  error?: string | null;
}

const InlineEdit: React.FC<InlineEditProps> = ({ value, onSave, onCancel, className = '', error }) => {
  const [editValue, setEditValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, []);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onSave(editValue.trim());
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  const handleBlur = () => {
    onSave(editValue.trim());
  };

  return (
    <div className={`relative ${className}`}>
      <input
        ref={inputRef}
        value={editValue}
        onChange={(e) => setEditValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        className={`bg-white border px-1 rounded text-sm w-full ${
          error ? 'border-red-500' : 'border-blue-500'
        }`}
      />
      {error && (
        <div className="absolute top-full left-0 mt-1 p-1 bg-red-100 border border-red-300 rounded text-xs text-red-700 whitespace-nowrap z-50">
          {error}
        </div>
      )}
    </div>
  );
};

interface ContextMenuProps {
  x: number;
  y: number;
  type: 'folder' | 'document';
  itemId: string;
  itemName: string;
  onClose: () => void;
  onRename: () => void;
  onDelete: () => void;
  onDuplicate?: () => void;
  onRefresh?: () => void;
  onExpandAll?: () => void;
  onCollapseAll?: () => void;
}

const ContextMenu: React.FC<ContextMenuProps> = ({
  x,
  y,
  type,
  onClose,
  onRename,
  onDelete,
  onDuplicate,
  onRefresh,
  onExpandAll,
  onCollapseAll,
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  return (
    <div
      ref={menuRef}
      className="fixed bg-white border border-gray-200 rounded-md shadow-lg py-1 z-50 min-w-32"
      style={{ left: x, top: y }}
    >
      <button
        className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
        onClick={() => {
          onRename();
          onClose();
        }}
      >
        <Edit2 size={14} />
        重命名
      </button>
      {type === 'document' && onDuplicate && (
        <button
          className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
          onClick={() => {
            onDuplicate();
            onClose();
          }}
        >
          <Copy size={14} />
          复制
        </button>
      )}

      {type === 'folder' && onRefresh && (
        <button
          className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
          onClick={() => {
            onRefresh();
            onClose();
          }}
        >
          <RefreshCw size={14} />
          刷新
        </button>
      )}

      {/* 全部展开和收起选项 */}
      {onExpandAll && (
        <button
          className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
          onClick={() => {
            onExpandAll();
            onClose();
          }}
        >
          <Expand size={14} />
          全部展开
        </button>
      )}

      {onCollapseAll && (
        <button
          className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
          onClick={() => {
            onCollapseAll();
            onClose();
          }}
        >
          <Minimize2 size={14} />
          收起全部
        </button>
      )}

      <hr className="my-1" />

      <button
        className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 text-red-600 flex items-center gap-2"
        onClick={() => {
          onDelete();
          onClose();
        }}
      >
        <Trash2 size={14} />
        删除
      </button>
    </div>
  );
};

export const FolderTreeFixed: React.FC<FolderTreeFixedProps> = ({
  onSelectFolder,
  onSelectDocument,
  selectedFolderId,
  selectedDocumentId,
}) => {
  const { folders, loading, error, createFolder, updateFolder, deleteFolder, refreshFolders } = useFolders();

  // 状态管理
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [lastSelectedItem, setLastSelectedItem] = useState<string | null>(null);
  const [editingItem, setEditingItem] = useState<{ id: string; type: 'folder' | 'document'; name: string } | null>(null);
  const [rootDocuments, setRootDocuments] = useState<any[]>([]);
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number; type: 'folder' | 'document'; id: string; name: string } | null>(null);

  // 拖拽状态
  const [dragOverTarget, setDragOverTarget] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  // 模态框状态
  const [showCreateFolder, setShowCreateFolder] = useState(false);
  const [showCreateDocument, setShowCreateDocument] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [newDocumentTitle, setNewDocumentTitle] = useState('');
  const [parentFolderId, setParentFolderId] = useState<string | null>(null);

  // 错误状态
  const [folderError, setFolderError] = useState<string | null>(null);
  const [documentError, setDocumentError] = useState<string | null>(null);
  const [renameError, setRenameError] = useState<string | null>(null);

  // 展开/收起状态
  const [isAllExpanded, setIsAllExpanded] = useState(false);

  // 刷新时保持展开状态
  const refreshWithStatePreservation = useCallback(async () => {
    const currentExpandedFolders = new Set(expandedFolders);
    await refreshFolders();
    setExpandedFolders(currentExpandedFolders);
  }, [refreshFolders, expandedFolders]);

  // 获取根目录文档
  const fetchRootDocuments = useCallback(async () => {
    try {
      // 使用稳定的排序方式，避免因操作导致顺序变化
      const response = await fetch('/api/documents?rootOnly=true&sortBy=title&sortOrder=asc');
      if (response.ok) {
        const data = await response.json();
        setRootDocuments(data.documents || []);
      }
    } catch (error) {
      console.error('Error fetching root documents:', error);
    }
  }, []);

  // 初始化和键盘事件
  useEffect(() => {
    fetchRootDocuments();
  }, []); // 只在组件挂载时执行一次

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F5') {
        event.preventDefault();
        refreshWithStatePreservation();
      } else if (event.key === 'F2' && selectedItems.size === 1) {
        event.preventDefault();
        const selectedId = Array.from(selectedItems)[0];
        handleStartEdit(selectedId);
      }
    };

    const handleRefreshFolder = async () => {
      await refreshWithStatePreservation();
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('refresh-folder', handleRefreshFolder);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('refresh-folder', handleRefreshFolder);
    };
  }, [refreshWithStatePreservation, selectedItems]); // 键盘事件监听器的依赖

  // 处理文件夹展开/收缩
  const toggleFolder = useCallback((folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  }, []);

  // 获取所有项目的扁平化列表（用于Shift选择）
  const getAllItems = useCallback(() => {
    const items: Array<{ id: string; type: 'folder' | 'document'; name: string }> = [];

    // 添加根文档
    rootDocuments.forEach(doc => {
      items.push({ id: doc.id, type: 'document' as const, name: doc.title });
    });

    // 递归添加文件夹和文档
    const addFolderItems = (folder: any, level: number) => {
      items.push({ id: folder.id, type: 'folder' as const, name: folder.name });

      // 添加子文档
      if (folder.documents) {
        folder.documents.forEach((doc: any) => {
          items.push({ id: doc.id, type: 'document' as const, name: doc.title });
        });
      }

      // 递归添加子文件夹
      if (folder.children) {
        folder.children.forEach((child: any) => addFolderItems(child, level + 1));
      }
    };

    folders.forEach(folder => addFolderItems(folder, 0));
    return items;
  }, [folders, rootDocuments]);

  // 处理项目选择
  const handleItemSelect = useCallback((itemId: string, itemType: 'folder' | 'document', event: React.MouseEvent) => {
    if (event.shiftKey && lastSelectedItem) {
      // Shift + 点击：范围选择
      const allItems = getAllItems();
      const lastIndex = allItems.findIndex(item => item.id === lastSelectedItem);
      const currentIndex = allItems.findIndex(item => item.id === itemId);

      if (lastIndex !== -1 && currentIndex !== -1) {
        const startIndex = Math.min(lastIndex, currentIndex);
        const endIndex = Math.max(lastIndex, currentIndex);

        const rangeItems = allItems.slice(startIndex, endIndex + 1).map(item => item.id);
        setSelectedItems(new Set(rangeItems));
      }
    } else if (event.ctrlKey || event.metaKey) {
      // Ctrl/Cmd + 点击：多选
      setSelectedItems(prev => {
        const newSet = new Set(prev);
        if (newSet.has(itemId)) {
          newSet.delete(itemId);
        } else {
          newSet.add(itemId);
        }
        return newSet;
      });
      setLastSelectedItem(itemId);
    } else {
      // 普通点击：单选
      setSelectedItems(new Set([itemId]));
      setLastSelectedItem(itemId);

      if (itemType === 'folder') {
        onSelectFolder?.(itemId);
      } else {
        onSelectDocument?.(itemId);
      }
    }
  }, [onSelectFolder, onSelectDocument]);

  // 开始编辑
  const handleStartEdit = useCallback((itemId: string) => {
    const findItemName = (id: string): { name: string; type: 'folder' | 'document' } | null => {
      // 在根文档中查找
      const rootDoc = rootDocuments.find(doc => doc.id === id);
      if (rootDoc) return { name: rootDoc.title, type: 'document' };

      // 递归查找文件夹和文档
      const searchInFolder = (folder: any): { name: string; type: 'folder' | 'document' } | null => {
        if (folder.id === id) return { name: folder.name, type: 'folder' };

        if (folder.documents) {
          const doc = folder.documents.find((d: any) => d.id === id);
          if (doc) return { name: doc.title, type: 'document' };
        }

        if (folder.children) {
          for (const child of folder.children) {
            const result = searchInFolder(child);
            if (result) return result;
          }
        }

        return null;
      };

      for (const folder of folders) {
        const result = searchInFolder(folder);
        if (result) return result;
      }

      return null;
    };

    const item = findItemName(itemId);
    if (item) {
      setEditingItem({ id: itemId, type: item.type, name: item.name });
    }
  }, [folders, rootDocuments]);

  // 保存编辑
  const handleSaveEdit = useCallback(async (newName: string) => {
    if (!editingItem || !newName.trim()) {
      setEditingItem(null);
      setRenameError(null);
      return;
    }

    // 清除之前的错误
    setRenameError(null);

    try {
      if (editingItem.type === 'folder') {
        await updateFolder(editingItem.id, { name: newName.trim() });
      } else {
        const response = await fetch(`/api/documents/${editingItem.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ title: newName.trim() }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '重命名文档失败');
        }

        await fetchRootDocuments();
      }

      await refreshWithStatePreservation();
      // 重命名成功，退出编辑状态
      setEditingItem(null);
    } catch (error) {
      console.error('重命名失败:', error);
      // 重命名失败，显示错误信息，保持编辑状态
      const errorMessage = error instanceof Error ? error.message : '重命名失败，请稍后重试';
      setRenameError(errorMessage);
      // 不退出编辑状态，让用户可以重试
    }
  }, [editingItem, updateFolder, fetchRootDocuments, refreshWithStatePreservation]);

  // 取消编辑
  const handleCancelEdit = useCallback(() => {
    setEditingItem(null);
    setRenameError(null); // 清除重命名错误
  }, []);

  // 全部展开
  const handleExpandAll = useCallback(() => {
    const getAllFolderIds = (folders: FolderWithRelations[]): string[] => {
      const ids: string[] = [];
      folders.forEach(folder => {
        ids.push(folder.id);
        if (folder.children && folder.children.length > 0) {
          ids.push(...getAllFolderIds(folder.children));
        }
      });
      return ids;
    };

    const allFolderIds = getAllFolderIds(folders);
    setExpandedFolders(new Set(allFolderIds));
    setIsAllExpanded(true);
  }, [folders]);

  // 收起全部
  const handleCollapseAll = useCallback(() => {
    setExpandedFolders(new Set());
    setIsAllExpanded(false);
  }, []);

  // 删除文件夹
  const handleDeleteFolder = useCallback(async (folderId: string) => {
    if (confirm('您确定要删除这个文件夹吗？')) {
      try {
        await deleteFolder(folderId);
        await refreshWithStatePreservation();
      } catch (error: any) {
        if (error.response?.status === 400) {
          const errorData = error.response.data;
          if (errorData.hasChildren || errorData.hasDocuments) {
            let confirmMessage = '文件夹不为空：\n';
            if (errorData.hasChildren) {
              confirmMessage += `• 包含 ${errorData.childrenCount} 个子文件夹\n`;
            }
            if (errorData.hasDocuments) {
              confirmMessage += `• 包含 ${errorData.documentsCount} 个文档\n`;
            }
            confirmMessage += '\n是否强制删除？这将删除文件夹及其所有内容。';

            if (confirm(confirmMessage)) {
              try {
                const response = await fetch(`/api/folders/${folderId}?force=true`, {
                  method: 'DELETE',
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || '强制删除失败');
                }

                await refreshWithStatePreservation();
              } catch (forceError: any) {
                alert('强制删除失败：' + (forceError.message || '未知错误'));
              }
            }
            return;
          }
        }

        alert('删除失败：' + (error.message || '未知错误'));
      }
    }
  }, [deleteFolder, refreshWithStatePreservation]);

  // 移动文件夹
  const handleMoveFolder = useCallback(async (folderId: string, targetFolderId: string | null) => {
    try {
      await updateFolder(folderId, { parentId: targetFolderId });
      await refreshWithStatePreservation();

      // 触发移动事件
      window.dispatchEvent(new CustomEvent('folder-moved', {
        detail: { folderId, targetFolderId }
      }));
    } catch (error) {
      console.error('移动文件夹失败:', error);
      alert('移动文件夹失败');
    }
  }, [updateFolder, refreshWithStatePreservation]);

  // 移动文档
  const handleMoveDocument = useCallback(async (documentId: string, targetFolderId: string | null) => {
    try {
      const response = await fetch(`/api/documents/${documentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ folderId: targetFolderId }),
      });

      if (response.ok) {
        await refreshWithStatePreservation();
        await fetchRootDocuments();

        // 触发移动事件
        window.dispatchEvent(new CustomEvent('document-moved', {
          detail: { documentId, targetFolderId }
        }));
      } else {
        throw new Error('移动文档失败');
      }
    } catch (error) {
      console.error('移动文档失败:', error);
      alert('移动文档失败');
    }
  }, [refreshWithStatePreservation, fetchRootDocuments]);

  // 删除文档
  const handleDeleteDocument = useCallback(async (documentId: string) => {
    if (confirm('您确定要删除这个文档吗？')) {
      try {
        const response = await fetch(`/api/documents/${documentId}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          await refreshWithStatePreservation();
          await fetchRootDocuments();
        }
      } catch (error) {
        console.error('删除文档失败:', error);
        alert('删除文档失败');
      }
    }
  }, [refreshWithStatePreservation, fetchRootDocuments]);

  // 复制文档
  const handleDuplicateDocument = useCallback(async (documentId: string) => {
    try {
      const response = await fetch(`/api/documents/${documentId}`);
      if (response.ok) {
        const data = await response.json();
        const originalDocument = data.document;

        const createResponse = await fetch('/api/documents', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            title: `${originalDocument.title} (副本)`,
            content: originalDocument.content,
            folderId: originalDocument.folderId
          }),
        });

        if (createResponse.ok) {
          await refreshWithStatePreservation();
          await fetchRootDocuments();
        }
      }
    } catch (error) {
      console.error('复制文档失败:', error);
      alert('复制文档失败');
    }
  }, [refreshWithStatePreservation, fetchRootDocuments]);

  // 批量移动
  const handleBatchMove = useCallback(async (items: Array<{ type: 'folder' | 'document'; id: string }>, targetFolderId: string | null) => {
    try {
      const movePromises = items.map(async (item) => {
        if (item.type === 'folder') {
          return updateFolder(item.id, { parentId: targetFolderId });
        } else if (item.type === 'document') {
          const response = await fetch(`/api/documents/${item.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ folderId: targetFolderId }),
          });
          return response.ok;
        }
      });

      await Promise.all(movePromises);
      await refreshWithStatePreservation();
      await fetchRootDocuments();

      // 清空选择
      setSelectedItems(new Set());

      // 触发移动事件
      items.forEach(item => {
        window.dispatchEvent(new CustomEvent(item.type === 'folder' ? 'folder-moved' : 'document-moved', {
          detail: {
            [item.type === 'folder' ? 'folderId' : 'documentId']: item.id,
            targetFolderId
          }
        }));
      });
    } catch (error) {
      console.error('批量移动失败:', error);
      alert('批量移动失败');
    }
  }, [updateFolder, refreshWithStatePreservation, fetchRootDocuments]);

  // 创建文件夹
  const handleCreateFolder = useCallback(async (parentId?: string) => {
    setParentFolderId(parentId || null);
    setFolderError(null); // 清除之前的错误
    setShowCreateFolder(true);
  }, []);

  const handleSubmitFolder = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (newFolderName.trim()) {
      // 清除之前的错误
      setFolderError(null);

      try {
        const result = await createFolder(newFolderName.trim(), parentFolderId || undefined);
        if (result) {
          // 创建成功，关闭弹框
          setNewFolderName('');
          setShowCreateFolder(false);
          setParentFolderId(null);
          await refreshWithStatePreservation();
        }
      } catch (err) {
        // 创建失败，显示错误信息，保持弹框打开
        const errorMessage = err instanceof Error ? err.message : '创建文件夹失败，请稍后重试';
        setFolderError(errorMessage);
      }
    }
  }, [newFolderName, parentFolderId, createFolder, refreshWithStatePreservation]);

  // 创建文档
  const handleCreateDocument = useCallback(async (folderId?: string) => {
    setParentFolderId(folderId || null);
    setDocumentError(null); // 清除之前的错误
    setShowCreateDocument(true);
  }, []);

  const handleSubmitDocument = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (newDocumentTitle.trim()) {
      // 清除之前的错误
      setDocumentError(null);

      try {
        const response = await fetch('/api/documents', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            title: newDocumentTitle.trim(),
            content: '',
            folderId: parentFolderId || undefined
          }),
        });

        if (response.ok) {
          // 创建成功，关闭弹框
          setNewDocumentTitle('');
          setShowCreateDocument(false);
          setParentFolderId(null);
          await refreshWithStatePreservation();
          await fetchRootDocuments();
        } else {
          // 创建失败，显示错误信息
          const errorData = await response.json();
          setDocumentError(errorData.error || '创建文档失败，请稍后重试');
        }
      } catch (error) {
        console.error('创建文档失败:', error);
        setDocumentError('网络错误，请检查网络连接后重试');
      }
    }
  }, [newDocumentTitle, parentFolderId, refreshWithStatePreservation, fetchRootDocuments]);

  // 渲染文档节点
  const renderDocument = (document: any, level: number) => {
    const isSelected = selectedItems.has(document.id);
    const isEditing = editingItem?.id === document.id;

    return (
      <div
        key={document.id}
        className={`flex items-center gap-1 px-2 py-1 hover:bg-gray-100 cursor-pointer rounded group ${
          isSelected ? 'bg-blue-100' : ''
        }`}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={(e) => handleItemSelect(document.id, 'document', e)}
        onContextMenu={(e) => {
          e.preventDefault();
          setContextMenu({ x: e.clientX, y: e.clientY, type: 'document', id: document.id, name: document.title });
        }}
        draggable
        onDragStart={(e) => {
          setIsDragging(true);
          const isSelected = selectedItems.has(document.id);
          const itemsToMove = isSelected ? Array.from(selectedItems) : [document.id];

          if (itemsToMove.length === 1) {
            // 单个项目：使用原来的格式以保持兼容性
            const dragData = {
              type: 'document',
              id: document.id,
              title: document.title
            };
            e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
          } else {
            // 多个项目：使用批量格式
            const dragData = {
              type: 'batch',
              items: itemsToMove.map(id => {
                const allItems = getAllItems();
                const item = allItems.find(item => item.id === id);
                return {
                  type: item?.type || 'document',
                  id: id,
                  title: item?.name || document.title
                };
              })
            };
            e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
          }
          e.dataTransfer.effectAllowed = 'move';
        }}
        onDragEnd={() => {
          setIsDragging(false);
          setDragOverTarget(null);
        }}
      >
        <div className="w-4" />
        <FileText size={16} className="text-gray-500" />
        {isEditing && editingItem ? (
          <InlineEdit
            value={editingItem.name}
            onSave={handleSaveEdit}
            onCancel={handleCancelEdit}
            className="flex-1"
            error={renameError}
          />
        ) : (
          <span className="flex-1 text-sm truncate">{document.title}</span>
        )}
        <span className="text-xs text-gray-400">{document.wordCount}w</span>

        <button
          onClick={(e) => {
            e.stopPropagation();
            setContextMenu({ x: e.clientX, y: e.clientY, type: 'document', id: document.id, name: document.title });
          }}
          className="p-1 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100"
          title="更多选项"
        >
          <MoreHorizontal size={12} />
        </button>
      </div>
    );
  };

  // 渲染文件夹节点
  const renderFolder = (folder: FolderWithRelations, level: number) => {
    const isExpanded = expandedFolders.has(folder.id);
    const isSelected = selectedItems.has(folder.id);
    const isEditing = editingItem?.id === folder.id;
    const isDragOver = dragOverTarget === folder.id;
    const hasChildren = (folder.children && folder.children.length > 0) ||
      (folder.documents && folder.documents.length > 0);

    return (
      <div key={folder.id} className="select-none">
        <div
          className={`flex items-center gap-1 px-2 py-1 hover:bg-gray-100 cursor-pointer rounded group ${
            isSelected ? 'bg-blue-100' : ''
          } ${isDragOver ? 'bg-green-100 ring-2 ring-green-300' : ''}`}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
          onClick={(e) => {
            if (hasChildren) toggleFolder(folder.id);
            handleItemSelect(folder.id, 'folder', e);
          }}

          onContextMenu={(e) => {
            e.preventDefault();
            setContextMenu({ x: e.clientX, y: e.clientY, type: 'folder', id: folder.id, name: folder.name });
          }}
          draggable
          onDragStart={(e) => {
            setIsDragging(true);
            const isSelected = selectedItems.has(folder.id);
            const itemsToMove = isSelected ? Array.from(selectedItems) : [folder.id];

            if (itemsToMove.length === 1) {
              // 单个项目：使用原来的格式以保持兼容性
              const dragData = {
                type: 'folder',
                id: folder.id,
                name: folder.name
              };
              e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
            } else {
              // 多个项目：使用批量格式
              const dragData = {
                type: 'batch',
                items: itemsToMove.map(id => {
                  const allItems = getAllItems();
                  const item = allItems.find(item => item.id === id);
                  return {
                    type: item?.type || 'folder',
                    id: id,
                    name: item?.name || folder.name
                  };
                })
              };
              e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
            }
            e.dataTransfer.effectAllowed = 'move';
          }}
          onDragEnd={() => {
            setIsDragging(false);
            setDragOverTarget(null);
          }}
          onDragOver={(e) => {
            e.preventDefault();
            e.stopPropagation(); // 阻止事件冒泡
            e.dataTransfer.dropEffect = 'move';
            setDragOverTarget(folder.id);
          }}
          onDragLeave={(e) => {
            // 只有当鼠标真正离开元素时才清除高亮
            const rect = e.currentTarget.getBoundingClientRect();
            const x = e.clientX;
            const y = e.clientY;

            if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
              setDragOverTarget(null);
            }
          }}
          onDrop={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setDragOverTarget(null);

            try {
              const dragData = JSON.parse(e.dataTransfer.getData('text/plain'));

              if (dragData.type === 'batch') {
                // 批量移动
                handleBatchMove(dragData.items, folder.id);
              } else if (dragData.type === 'folder' && dragData.id !== folder.id) {
                // 单个文件夹移动（向后兼容）
                handleMoveFolder(dragData.id, folder.id);
              } else if (dragData.type === 'document') {
                // 单个文档移动（向后兼容）
                handleMoveDocument(dragData.id, folder.id);
              }
            } catch (error) {
              console.error('Error parsing drag data:', error);
            }
          }}
        >
          {hasChildren ? (
            isExpanded ? (
              <ChevronDown size={16} className="text-gray-500" />
            ) : (
              <ChevronRight size={16} className="text-gray-500" />
            )
          ) : (
            <div className="w-4" />
          )}

          {isExpanded ? (
            <FolderOpen size={16} className="text-blue-500" />
          ) : (
            <Folder size={16} className="text-blue-500" />
          )}

          {isEditing && editingItem ? (
            <InlineEdit
              value={editingItem.name}
              onSave={handleSaveEdit}
              onCancel={handleCancelEdit}
              className="flex-1"
              error={renameError}
            />
          ) : (
            <span className="flex-1 text-sm truncate">{folder.name}</span>
          )}

          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleCreateDocument(folder.id);
              }}
              className="p-1 hover:bg-gray-200 rounded"
              title="创建文档"
            >
              <Plus size={12} />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleCreateFolder(folder.id);
              }}
              className="p-1 hover:bg-gray-200 rounded"
              title="创建文件夹"
            >
              <Folder size={12} />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setContextMenu({ x: e.clientX, y: e.clientY, type: 'folder', id: folder.id, name: folder.name });
              }}
              className="p-1 hover:bg-gray-200 rounded"
              title="更多选项"
            >
              <MoreHorizontal size={12} />
            </button>
          </div>
        </div>

        {isExpanded && (
          <div>
            {folder.children?.map((childFolder) =>
              renderFolder(childFolder as FolderWithRelations, level + 1)
            )}
            {folder.documents?.map((document) =>
              renderDocument(document, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  if (loading) return <div className="p-4 text-sm text-gray-500">Loading...</div>;
  // 注意：不再在这里显示全局错误，错误应该在具体操作的弹框中显示

  const rootFolders = folders.filter(folder => !folder.parentId);

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex items-center justify-between p-3 border-b">
        <h2 className="text-sm font-medium">文件夹</h2>
        <div className="flex gap-1">
          <button
            onClick={() => handleCreateDocument()}
            className="p-1 hover:bg-gray-100 rounded"
            title="创建文档"
          >
            <Plus size={14} />
          </button>
          <button
            onClick={() => handleCreateFolder()}
            className="p-1 hover:bg-gray-100 rounded"
            title="创建文件夹"
          >
            <Folder size={14} />
          </button>
          <button
            onClick={refreshWithStatePreservation}
            className="p-1 hover:bg-gray-100 rounded"
            title="刷新"
          >
            <RefreshCw size={14} />
          </button>
        </div>
      </div>

      <div className={`flex-1 overflow-auto ${dragOverTarget === 'root' ? 'bg-blue-50' : ''}`}
        onContextMenu={(e) => {
          // 只有在空白区域才显示根目录右键菜单
          if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('text-gray-500')) {
            e.preventDefault();
            setContextMenu({
              x: e.clientX,
              y: e.clientY,
              type: 'folder',
              id: 'root',
              name: '根目录'
            });
          }
        }}
        onDragOver={(e) => {
          // 只有在空白区域才设置根目录高亮
          if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('text-gray-500')) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            setDragOverTarget('root');
          } else {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
          }
        }}
        onDragLeave={(e) => {
          // 检查是否真的离开了容器区域
          const rect = e.currentTarget.getBoundingClientRect();
          const x = e.clientX;
          const y = e.clientY;

          if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
            setDragOverTarget(null);
          }
        }}
        onDrop={(e) => {
          e.preventDefault();
          setDragOverTarget(null); // 重置高亮状态

          try {
            const dragData = JSON.parse(e.dataTransfer.getData('text/plain'));

            // 拖拽到根目录
            if (dragData.type === 'batch') {
              // 批量移动到根目录
              handleBatchMove(dragData.items, null);
            } else if (dragData.type === 'folder') {
              // 单个文件夹移动（向后兼容）
              handleMoveFolder(dragData.id, null);
            } else if (dragData.type === 'document') {
              // 单个文档移动（向后兼容）
              handleMoveDocument(dragData.id, null);
            }
          } catch (error) {
            console.error('Error parsing drag data:', error);
          }
        }}
      >
        <div className="text-xs text-gray-500 p-2">
          提示：Ctrl+点击多选，Shift+点击范围选择，F2重命名，拖拽移动
        </div>

        {/* 根目录文档 */}
        {rootDocuments.map(document => renderDocument(document, 0))}

        {/* 根目录文件夹 */}
        {rootFolders.map(folder => renderFolder(folder, 0))}
      </div>

      {/* 右键菜单 */}
      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          type={contextMenu.type}
          itemId={contextMenu.id}
          itemName={contextMenu.name}
          onClose={() => setContextMenu(null)}
          onRename={() => handleStartEdit(contextMenu.id)}
          onDelete={() => {
            if (contextMenu.type === 'folder') {
              handleDeleteFolder(contextMenu.id);
            } else {
              handleDeleteDocument(contextMenu.id);
            }
          }}
          onDuplicate={contextMenu.type === 'document' ? () => handleDuplicateDocument(contextMenu.id) : undefined}
          onRefresh={contextMenu.type === 'folder' ? () => {
            window.dispatchEvent(new CustomEvent('refresh-folder'));
          } : undefined}
          onExpandAll={handleExpandAll}
          onCollapseAll={handleCollapseAll}
        />
      )}

      {/* 创建文件夹模态框 */}
      {showCreateFolder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-80">
            <h3 className="text-lg font-medium mb-4">创建文件夹</h3>
            <form onSubmit={handleSubmitFolder}>
              <input
                type="text"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                placeholder="文件夹名称"
                className={`w-full px-3 py-2 border rounded-md mb-2 ${folderError ? 'border-red-500' : ''}`}
                autoFocus
              />
              {folderError && (
                <div className="text-red-500 text-sm mb-4 p-2 bg-red-50 rounded-md">
                  {folderError}
                </div>
              )}
              <div className="flex gap-2 justify-end">
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateFolder(false);
                    setFolderError(null);
                    setNewFolderName('');
                  }}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md"
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  创建
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 创建文档模态框 */}
      {showCreateDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-80">
            <h3 className="text-lg font-medium mb-4">创建文档</h3>
            <form onSubmit={handleSubmitDocument}>
              <input
                type="text"
                value={newDocumentTitle}
                onChange={(e) => setNewDocumentTitle(e.target.value)}
                placeholder="文档标题"
                className={`w-full px-3 py-2 border rounded-md mb-2 ${documentError ? 'border-red-500' : ''}`}
                autoFocus
              />
              {documentError && (
                <div className="text-red-500 text-sm mb-4 p-2 bg-red-50 rounded-md">
                  {documentError}
                </div>
              )}
              <div className="flex gap-2 justify-end">
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateDocument(false);
                    setDocumentError(null);
                    setNewDocumentTitle('');
                  }}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md"
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  创建
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default FolderTreeFixed;