/**
 * AI 配置同步管理 Hook
 * 提供配置同步的状态管理和操作功能
 */

import { useState, useEffect, useCallback } from 'react';

/**
 * 设备信息接口
 */
export interface DeviceInfo {
  id: string;
  name: string;
  type: 'desktop' | 'mobile' | 'tablet' | 'web';
  platform: string;
  lastSyncAt: Date;
  isActive: boolean;
}

/**
 * 同步记录接口
 */
export interface SyncRecord {
  id: string;
  userId: string;
  deviceId: string;
  configId: string;
  action: 'create' | 'update' | 'delete';
  status: 'pending' | 'syncing' | 'completed' | 'failed';
  data?: any;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
}

/**
 * 配置冲突接口
 */
export interface ConfigConflict {
  configId: string;
  conflicts: Array<{
    deviceId: string;
    field: string;
    localValue: any;
    remoteValue: any;
  }>;
}

/**
 * 同步结果接口
 */
export interface SyncResult {
  success: boolean;
  syncedCount: number;
  failedCount: number;
  conflicts: number;
}

/**
 * 配置同步状态接口
 */
export interface ConfigSyncStatus {
  lastSyncAt?: Date;
  syncedDevices: string[];
  pendingDevices: string[];
  conflicts: boolean;
}

/**
 * AI 配置同步管理 Hook
 */
export function useAIConfigSync() {
  const [devices, setDevices] = useState<DeviceInfo[]>([]);
  const [syncHistory, setSyncHistory] = useState<SyncRecord[]>([]);
  const [conflicts, setConflicts] = useState<ConfigConflict[]>([]);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 获取用户设备列表
   */
  const fetchDevices = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/ai-config/sync?action=devices');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '获取设备列表失败');
      }

      setDevices(data.devices.map((device: any) => ({
        ...device,
        lastSyncAt: new Date(device.lastSyncAt)
      })));
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取设备列表失败');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 获取同步历史
   */
  const fetchSyncHistory = useCallback(async (limit: number = 50) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/ai-config/sync?action=history&limit=${limit}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '获取同步历史失败');
      }

      setSyncHistory(data.history.map((record: any) => ({
        ...record,
        createdAt: new Date(record.createdAt),
        completedAt: record.completedAt ? new Date(record.completedAt) : undefined
      })));
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取同步历史失败');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 获取配置冲突
   */
  const fetchConflicts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/ai-config/sync?action=conflicts');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '获取配置冲突失败');
      }

      setConflicts(data.conflicts);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取配置冲突失败');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 获取配置同步状态
   */
  const getConfigSyncStatus = useCallback(async (configId: string): Promise<ConfigSyncStatus | null> => {
    try {
      const response = await fetch(`/api/ai-config/sync?action=status&configId=${configId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '获取同步状态失败');
      }

      return {
        ...data.status,
        lastSyncAt: data.status.lastSyncAt ? new Date(data.status.lastSyncAt) : undefined
      };
    } catch (err) {
      console.error('获取配置同步状态失败:', err);
      return null;
    }
  }, []);

  /**
   * 注册新设备
   */
  const registerDevice = useCallback(async (deviceInfo: {
    name: string;
    type: 'desktop' | 'mobile' | 'tablet' | 'web';
    platform: string;
  }): Promise<string | null> => {
    try {
      setSyncing(true);
      setError(null);

      const response = await fetch('/api/ai-config/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'register-device',
          data: deviceInfo
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '注册设备失败');
      }

      // 刷新设备列表
      await fetchDevices();
      
      return data.deviceId;
    } catch (err) {
      setError(err instanceof Error ? err.message : '注册设备失败');
      return null;
    } finally {
      setSyncing(false);
    }
  }, [fetchDevices]);

  /**
   * 同步所有配置
   */
  const syncAllConfigs = useCallback(async (deviceId: string): Promise<SyncResult | null> => {
    try {
      setSyncing(true);
      setError(null);

      const response = await fetch('/api/ai-config/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'sync-all',
          deviceId
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '同步配置失败');
      }

      // 刷新同步历史
      await fetchSyncHistory();
      
      return data.result;
    } catch (err) {
      setError(err instanceof Error ? err.message : '同步配置失败');
      return null;
    } finally {
      setSyncing(false);
    }
  }, [fetchSyncHistory]);

  /**
   * 同步单个配置
   */
  const syncConfig = useCallback(async (configId: string, deviceId: string): Promise<any | null> => {
    try {
      setSyncing(true);
      setError(null);

      const response = await fetch('/api/ai-config/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'sync-config',
          configId,
          deviceId
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '同步配置失败');
      }

      return data.config;
    } catch (err) {
      setError(err instanceof Error ? err.message : '同步配置失败');
      return null;
    } finally {
      setSyncing(false);
    }
  }, []);

  /**
   * 应用同步的配置
   */
  const applySyncedConfig = useCallback(async (configData: any, deviceId: string): Promise<boolean> => {
    try {
      setSyncing(true);
      setError(null);

      const response = await fetch('/api/ai-config/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'apply-sync',
          deviceId,
          data: configData
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '应用配置失败');
      }

      return data.success;
    } catch (err) {
      setError(err instanceof Error ? err.message : '应用配置失败');
      return false;
    } finally {
      setSyncing(false);
    }
  }, []);

  /**
   * 解决配置冲突
   */
  const resolveConflict = useCallback(async (
    configId: string,
    resolution: 'local' | 'remote' | 'merge',
    mergeData?: any
  ): Promise<boolean> => {
    try {
      setSyncing(true);
      setError(null);

      const response = await fetch('/api/ai-config/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'resolve-conflict',
          configId,
          data: {
            resolution,
            mergeData
          }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '解决冲突失败');
      }

      // 刷新冲突列表
      await fetchConflicts();
      
      return data.success;
    } catch (err) {
      setError(err instanceof Error ? err.message : '解决冲突失败');
      return false;
    } finally {
      setSyncing(false);
    }
  }, [fetchConflicts]);

  /**
   * 清理同步历史
   */
  const cleanupSyncHistory = useCallback(async (days: number = 30): Promise<number> => {
    try {
      setSyncing(true);
      setError(null);

      const response = await fetch('/api/ai-config/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'cleanup',
          data: { days }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '清理历史失败');
      }

      // 刷新同步历史
      await fetchSyncHistory();
      
      return data.cleanedCount;
    } catch (err) {
      setError(err instanceof Error ? err.message : '清理历史失败');
      return 0;
    } finally {
      setSyncing(false);
    }
  }, [fetchSyncHistory]);

  /**
   * 获取当前设备信息
   */
  const getCurrentDeviceInfo = useCallback((): {
    name: string;
    type: 'desktop' | 'mobile' | 'tablet' | 'web';
    platform: string;
  } => {
    // 检测设备类型和平台
    const userAgent = navigator.userAgent;
    let type: 'desktop' | 'mobile' | 'tablet' | 'web' = 'web';
    let platform = 'Unknown';

    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      type = /iPad/.test(userAgent) ? 'tablet' : 'mobile';
    } else {
      type = 'desktop';
    }

    if (/Windows/.test(userAgent)) {
      platform = 'Windows';
    } else if (/Mac/.test(userAgent)) {
      platform = 'macOS';
    } else if (/Linux/.test(userAgent)) {
      platform = 'Linux';
    } else if (/Android/.test(userAgent)) {
      platform = 'Android';
    } else if (/iPhone|iPad/.test(userAgent)) {
      platform = 'iOS';
    }

    return {
      name: `${platform} ${type}`,
      type,
      platform
    };
  }, []);

  // 初始加载
  useEffect(() => {
    fetchDevices();
    fetchSyncHistory();
    fetchConflicts();
  }, [fetchDevices, fetchSyncHistory, fetchConflicts]);

  return {
    devices,
    syncHistory,
    conflicts,
    loading,
    syncing,
    error,
    fetchDevices,
    fetchSyncHistory,
    fetchConflicts,
    getConfigSyncStatus,
    registerDevice,
    syncAllConfigs,
    syncConfig,
    applySyncedConfig,
    resolveConflict,
    cleanupSyncHistory,
    getCurrentDeviceInfo,
    clearError: () => setError(null)
  };
}