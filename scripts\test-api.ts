/**
 * API 测试脚本
 * 测试文档管理 API 的各种功能
 */

const API_BASE = 'http://localhost:3001/api';

interface TestResult {
  name: string;
  success: boolean;
  error?: string;
  data?: any;
}

const results: TestResult[] = [];

/**
 * 执行测试并记录结果
 */
async function runTest(name: string, testFn: () => Promise<any>): Promise<void> {
  try {
    console.log(`🧪 Testing: ${name}`);
    const data = await testFn();
    results.push({ name, success: true, data });
    console.log(`✅ ${name} - 成功`);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    results.push({ name, success: false, error: errorMessage });
    console.log(`❌ ${name} - 失败: ${errorMessage}`);
  }
}

/**
 * 测试文档统计 API
 */
async function testDocumentStats() {
  const response = await fetch(`${API_BASE}/documents/stats`);
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  const data = await response.json();
  
  if (!data.stats) {
    throw new Error('响应中缺少 stats 字段');
  }
  
  return data.stats;
}

/**
 * 测试文档搜索 API
 */
async function testDocumentSearch() {
  const response = await fetch(`${API_BASE}/documents/search?q=test`);
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  const data = await response.json();
  
  if (!Array.isArray(data.documents)) {
    throw new Error('响应中缺少 documents 数组');
  }
  
  return data.documents;
}

/**
 * 测试公共分享文档 API
 */
async function testSharedDocument() {
  // 使用一个不存在的令牌测试错误处理
  const response = await fetch(`${API_BASE}/shared/nonexistent-token`);
  
  if (response.status !== 404) {
    throw new Error(`期望状态码 404，实际得到 ${response.status}`);
  }
  
  const data = await response.json();
  
  if (!data.error) {
    throw new Error('响应中缺少错误信息');
  }
  
  return { message: '正确处理了不存在的分享令牌' };
}

/**
 * 测试批量操作 API
 */
async function testBatchOperations() {
  const response = await fetch(`${API_BASE}/documents/batch`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      operation: 'delete',
      documentIds: ['nonexistent-id'],
    }),
  });
  
  // 应该返回 401 未授权，因为没有提供认证
  if (response.status !== 401) {
    throw new Error(`期望状态码 401，实际得到 ${response.status}`);
  }
  
  return { message: '正确处理了未授权的批量操作请求' };
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🚀 开始测试文档管理 API...\n');
  
  // 注意：这些测试不需要认证，主要测试 API 结构和错误处理
  await runTest('文档统计 API', testDocumentStats);
  await runTest('文档搜索 API', testDocumentSearch);
  await runTest('公共分享文档 API', testSharedDocument);
  await runTest('批量操作 API', testBatchOperations);
  
  console.log('\n📊 测试结果汇总:');
  console.log('================');
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
    if (!result.success && result.error) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  console.log(`\n总计: ${successCount}/${totalCount} 测试通过`);
  
  if (successCount === totalCount) {
    console.log('🎉 所有测试都通过了！');
  } else {
    console.log('⚠️  部分测试失败，请检查上述错误信息');
  }
}

// 运行测试
main().catch(console.error);