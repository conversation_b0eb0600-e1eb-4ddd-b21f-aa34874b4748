'use client';

import { useState } from 'react';
import { LocalDocument } from '@/lib/storage/database';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';

interface DocumentCardProps {
  document: LocalDocument;
}

export function DocumentCard({ document }: DocumentCardProps) {
  const [showActions, setShowActions] = useState(false);

  // 获取文档预览文本
  const getPreviewText = (content: any): string => {
    if (!content || !content.content) return '空文档';
    
    const extractText = (node: any): string => {
      if (node.type === 'text') {
        return node.text || '';
      }
      if (node.content && Array.isArray(node.content)) {
        return node.content.map(extractText).join('');
      }
      return '';
    };

    const text = content.content.map(extractText).join(' ').trim();
    return text.length > 120 ? text.substring(0, 120) + '...' : text || '空文档';
  };

  // 格式化日期
  const formatDate = (date: Date): string => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '今天';
    if (diffDays === 2) return '昨天';
    if (diffDays <= 7) return `${diffDays - 1} 天前`;
    
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  };

  // 获取文档状态颜色
  const getStatusColor = () => {
    if (document.isDirty) return 'text-orange-600';
    if (document.lastSyncAt) return 'text-green-600';
    return 'text-gray-600';
  };

  // 获取文档状态文本
  const getStatusText = () => {
    if (document.isDirty) return '未同步';
    if (document.lastSyncAt) return '已同步';
    return '本地';
  };

  const previewText = getPreviewText(document.content);

  return (
    <div
      className="relative border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200 group cursor-pointer"
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* 文档标题 */}
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-medium text-gray-900 truncate flex-1 group-hover:text-blue-600 transition-colors">
          {document.title}
        </h3>
        
        {/* 操作按钮 */}
        <div className={`flex space-x-1 transition-opacity duration-200 ${
          showActions ? 'opacity-100' : 'opacity-0'
        }`}>
          <Link href={`/editor?id=${document.id}`}>
            <button
              className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
              title="编辑"
            >
              ✏️
            </button>
          </Link>
          <button
            className="p-1 text-gray-400 hover:text-green-600 transition-colors"
            title="复制"
            onClick={(e) => {
              e.stopPropagation();
              // TODO: 实现复制功能
            }}
          >
            📋
          </button>
          <button
            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
            title="删除"
            onClick={(e) => {
              e.stopPropagation();
              // TODO: 实现删除功能
            }}
          >
            🗑️
          </button>
        </div>
      </div>

      {/* 文档预览 */}
      <p className="text-sm text-gray-600 mb-3 line-clamp-3 leading-relaxed">
        {previewText}
      </p>

      {/* 标签 */}
      {document.metadata.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-3">
          {document.metadata.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
            >
              {tag}
            </span>
          ))}
          {document.metadata.tags.length > 3 && (
            <span className="text-xs text-gray-500">
              +{document.metadata.tags.length - 3}
            </span>
          )}
        </div>
      )}

      {/* 文档信息 */}
      <div className="flex justify-between items-center text-xs text-gray-500">
        <div className="flex space-x-3">
          <span>{document.metadata.wordCount} 词</span>
          <span>{document.metadata.characterCount} 字符</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className={getStatusColor()}>
            {getStatusText()}
          </span>
          <span>•</span>
          <span>{formatDate(document.updatedAt)}</span>
        </div>
      </div>

      {/* 点击区域 */}
      <Link href={`/editor?id=${document.id}`} className="absolute inset-0" />
    </div>
  );
}