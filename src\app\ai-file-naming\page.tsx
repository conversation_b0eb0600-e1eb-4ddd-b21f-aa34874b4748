/**
 * AI 文件命名管理页面
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  FileText, 
  Sparkles, 
  Edit3, 
  Eye,
  CheckCircle,
  Lightbulb,
  BarChart3,
  Settings
} from 'lucide-react';
import { AIFileNaming } from '@/components/ai/AIFileNaming';
import { useAIFileNaming } from '@/hooks/use-ai-file-naming';
import { useToast } from '@/hooks/use-toast';

/**
 * AI 文件命名管理页面
 */
export default function AIFileNamingPage() {
  const { toast } = useToast();
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>('');
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);
  const [testContent, setTestContent] = useState<string>('');

  const {
    namingSuggestion,
    documentSummary,
    smartSuggestions,
    fileNameValidation,
    batchResult,
    generateNamingSuggestions,
    generateDocumentSummary,
    loadSmartRenameSuggestions,
    validateFileName,
    isLoading,
    isGenerating
  } = useAIFileNaming({
    autoLoad: true
  });

  /**
   * 处理命名完成
   */
  const handleNamingComplete = (suggestion: any) => {
    console.log('命名完成:', suggestion);
    toast({
      title: '命名建议已生成',
      description: `推荐名称: ${suggestion.bestSuggestion.suggestedName}`
    });
  };

  /**
   * 处理重命名应用
   */
  const handleRenameApplied = (oldName: string, newName: string) => {
    console.log('重命名应用:', { oldName, newName });
    toast({
      title: '重命名已应用',
      description: `文件已重命名为: ${newName}`
    });
  };

  /**
   * 测试命名建议
   */
  const handleTestNaming = async () => {
    if (!testContent.trim()) {
      toast({
        title: '错误',
        description: '请输入测试内容',
        variant: 'destructive'
      });
      return;
    }

    // 这里可以创建一个临时文档进行测试
    // 实际实现中需要调用相应的API
    toast({
      title: '测试功能',
      description: '此功能需要实际的文档ID进行测试'
    });
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      {/* 页面头部 */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-purple-100 rounded-lg">
            <Sparkles className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">AI 文件命名助手</h1>
            <p className="text-gray-600">智能分析文档内容，生成最适合的文件名建议和摘要</p>
          </div>
        </div>

        {/* 功能概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Edit3 className="h-8 w-8 text-blue-500" />
                <div>
                  <h3 className="font-medium">智能命名</h3>
                  <p className="text-sm text-gray-600">基于内容生成文件名</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Eye className="h-8 w-8 text-green-500" />
                <div>
                  <h3 className="font-medium">文档摘要</h3>
                  <p className="text-sm text-gray-600">自动生成内容摘要</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Lightbulb className="h-8 w-8 text-yellow-500" />
                <div>
                  <h3 className="font-medium">重命名建议</h3>
                  <p className="text-sm text-gray-600">智能优化现有文件名</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-8 w-8 text-purple-500" />
                <div>
                  <h3 className="font-medium">名称验证</h3>
                  <p className="text-sm text-gray-600">评估文件名质量</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 主要功能区域 */}
      <Tabs defaultValue="naming" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="naming" className="flex items-center gap-2">
            <Edit3 className="h-4 w-4" />
            文件命名
          </TabsTrigger>
          <TabsTrigger value="test" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            功能测试
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            使用统计
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            设置
          </TabsTrigger>
        </TabsList>

        {/* 文件命名 */}
        <TabsContent value="naming" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>文档选择</CardTitle>
              <CardDescription>选择要处理的文档</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">单个文档ID</label>
                  <Input
                    placeholder="输入文档ID"
                    value={selectedDocumentId}
                    onChange={(e) => setSelectedDocumentId(e.target.value)}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">批量文档ID（每行一个）</label>
                  <Textarea
                    placeholder="输入文档ID列表，每行一个"
                    value={selectedDocumentIds.join('\n')}
                    onChange={(e) => setSelectedDocumentIds(
                      e.target.value.split('\n').filter(id => id.trim())
                    )}
                    rows={4}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <AIFileNaming
            documentId={selectedDocumentId || undefined}
            documentIds={selectedDocumentIds.length > 0 ? selectedDocumentIds : undefined}
            onNamingComplete={handleNamingComplete}
            onRenameApplied={handleRenameApplied}
            showAdvancedOptions={true}
          />
        </TabsContent>

        {/* 功能测试 */}
        <TabsContent value="test" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>功能测试</CardTitle>
              <CardDescription>测试AI文件命名功能</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">测试内容</label>
                <Textarea
                  placeholder="输入要测试的文档内容..."
                  value={testContent}
                  onChange={(e) => setTestContent(e.target.value)}
                  rows={6}
                />
              </div>
              
              <div className="flex items-center gap-4">
                <Button
                  onClick={handleTestNaming}
                  disabled={isGenerating || !testContent.trim()}
                >
                  测试命名建议
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => validateFileName('测试文档', testContent)}
                  disabled={isLoading || !testContent.trim()}
                >
                  测试名称验证
                </Button>
              </div>

              {/* 测试结果展示 */}
              {fileNameValidation && (
                <Card className="mt-4">
                  <CardHeader>
                    <CardTitle className="text-lg">验证结果</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">质量评分:</span>
                        <span className={`font-bold ${
                          fileNameValidation.qualityScore >= 80 ? 'text-green-600' :
                          fileNameValidation.qualityScore >= 60 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {fileNameValidation.qualityScore}/100
                        </span>
                      </div>
                      
                      {fileNameValidation.errors.length > 0 && (
                        <div className="text-sm text-red-600">
                          <strong>错误:</strong> {fileNameValidation.errors.join(', ')}
                        </div>
                      )}
                      
                      {fileNameValidation.warnings.length > 0 && (
                        <div className="text-sm text-yellow-600">
                          <strong>警告:</strong> {fileNameValidation.warnings.join(', ')}
                        </div>
                      )}
                      
                      {fileNameValidation.suggestions.length > 0 && (
                        <div className="text-sm text-blue-600">
                          <strong>建议:</strong> {fileNameValidation.suggestions.join(', ')}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 使用统计 */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">命名建议统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">生成次数</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">平均置信度</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">应用率</span>
                    <span className="font-medium">--</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">摘要生成统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">生成次数</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">平均字数</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">质量评分</span>
                    <span className="font-medium">--</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">重命名建议统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">建议数量</span>
                    <span className="font-medium">{smartSuggestions.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">高优先级</span>
                    <span className="font-medium">
                      {smartSuggestions.filter(s => s.priority === 'high').length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">已应用</span>
                    <span className="font-medium">
                      {smartSuggestions.filter(s => s.applied).length}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>使用提示</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm text-gray-600">
                <p>• <strong>智能命名:</strong> AI会分析文档内容，提取关键信息生成合适的文件名</p>
                <p>• <strong>文档摘要:</strong> 自动生成文档的核心内容摘要，包含关键词和主题</p>
                <p>• <strong>重命名建议:</strong> 分析现有文件名质量，提供改进建议</p>
                <p>• <strong>名称验证:</strong> 评估文件名的有效性、可读性和相关性</p>
                <p>• <strong>批量处理:</strong> 支持同时处理多个文档，提高工作效率</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 设置 */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>功能设置</CardTitle>
              <CardDescription>配置AI文件命名的行为和参数</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">默认命名风格</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="descriptive">描述性</option>
                      <option value="concise">简洁</option>
                      <option value="formal">正式</option>
                      <option value="technical">技术性</option>
                      <option value="creative">创意</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">默认语言</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="zh">中文</option>
                      <option value="en">英文</option>
                      <option value="auto">自动检测</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">最大文件名长度</label>
                    <input
                      type="number"
                      min="10"
                      max="100"
                      defaultValue="50"
                      className="w-full p-2 border rounded-md"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">建议数量</label>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      defaultValue="5"
                      className="w-full p-2 border rounded-md"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">自动包含日期</span>
                  </label>

                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">包含类型前缀</span>
                  </label>

                  <label className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">启用智能重命名提醒</span>
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">排除词汇（用逗号分隔）</label>
                  <Input
                    placeholder="文档, 新建, 未命名"
                    defaultValue="文档, 新建, 未命名"
                  />
                </div>

                <Button className="w-full">
                  保存设置
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}