# AI 代理配置指南

## 概述

本应用支持通过 HTTP 代理访问 OpenAI API，配置方式有两种：
1. **环境变量配置**（全局默认）
2. **页面配置**（用户自定义，优先级更高）

## 配置优先级

```
页面配置 > 环境变量 (.env) > 默认值
```

## 环境变量配置

### 1. 编辑 `.env` 文件

```bash
# HTTP Proxy Configuration
# Default HTTP proxy for OpenAI API (optional)
HTTP_PROXY="http://127.0.0.1:57800"
HTTPS_PROXY="http://127.0.0.1:57800"

# AI Configuration
# OpenAI API Keys (optional)
OPENAI_API_KEY="your-openai-api-key"
GEMINI_API_KEY="your-gemini-api-key"

# AI Service Endpoints (optional)
OPENAI_ENDPOINT=""
OLLAMA_ENDPOINT="http://localhost:11434"
GEMINI_ENDPOINT=""
```

### 2. 环境变量说明

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `HTTP_PROXY` | HTTP 代理地址 | `http://127.0.0.1:57800` |
| `HTTPS_PROXY` | HTTPS 代理地址 | `http://127.0.0.1:57800` |
| `OPENAI_API_KEY` | OpenAI API 密钥 | `sk-...` |
| `GEMINI_API_KEY` | Gemini API 密钥 | `AIza...` |
| `OPENAI_ENDPOINT` | OpenAI 自定义端点 | 留空使用默认 |
| `OLLAMA_ENDPOINT` | Ollama 服务端点 | `http://localhost:11434` |
| `GEMINI_ENDPOINT` | Gemini 自定义端点 | 留空使用默认 |

## 页面配置

### 1. 访问 AI 配置页面

```
http://localhost:4501/ai-config
```

### 2. 添加 OpenAI 配置

- **服务提供商**: OpenAI
- **API 密钥**: `***************************************************`
- **HTTP 代理地址**: `http://127.0.0.1:57800` （可选，留空使用环境变量）
- **模型**: `gpt-3.5-turbo`

### 3. 配置说明

- **HTTP 代理地址**字段是可选的
- 如果填写，会覆盖环境变量中的代理配置
- 如果留空，会使用环境变量 `HTTP_PROXY` 或 `HTTPS_PROXY`
- 如果环境变量也未设置，则直连 OpenAI API

## 使用场景

### 场景1: 全局代理配置

适用于所有用户都使用相同代理的情况：

1. 在 `.env` 文件中配置 `HTTP_PROXY`
2. 用户在页面中不填写代理地址
3. 系统自动使用环境变量中的代理

### 场景2: 用户自定义代理

适用于不同用户使用不同代理的情况：

1. 用户在 AI 配置页面中填写自己的代理地址
2. 系统优先使用用户配置的代理
3. 如果用户未配置，回退到环境变量

### 场景3: 混合配置

1. 在 `.env` 中设置默认代理
2. 部分用户可以在页面中覆盖为自己的代理
3. 其他用户使用默认代理

## 测试配置

### 1. 查看环境变量状态

在 AI 配置页面中可以看到"环境配置状态"卡片，显示：
- OpenAI API Key 是否已配置
- Gemini API Key 是否已配置
- HTTP 代理是否已配置

### 2. 测试连接

1. 在 AI 配置页面添加配置
2. 点击"测试连接"按钮
3. 查看连接结果

### 3. 命令行测试

```bash
# 测试代理连接
node test-openai-proxy.js
```

## 故障排除

### 1. 代理连接失败

- 检查代理服务器是否运行
- 确认代理地址格式正确
- 查看控制台日志

### 2. 环境变量未生效

- 确认 `.env` 文件在项目根目录
- 重启开发服务器
- 检查环境变量格式

### 3. 页面配置未保存

- 确认用户已登录
- 检查表单验证错误
- 查看网络请求状态

## 安全注意事项

1. **不要在代码中硬编码 API 密钥**
2. **使用环境变量存储敏感信息**
3. **定期更换 API 密钥**
4. **确保代理服务器安全**
5. **在生产环境中使用 HTTPS**

## 开发建议

1. **开发环境**：使用 `.env` 文件配置
2. **生产环境**：使用系统环境变量
3. **测试环境**：可以使用不同的代理配置
4. **CI/CD**：通过环境变量注入配置