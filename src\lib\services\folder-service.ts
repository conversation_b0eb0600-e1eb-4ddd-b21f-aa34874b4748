import { prisma } from '@/lib/db/prisma';
import { Folder, Document } from '@prisma/client';

/**
 * 文件夹及其关联数据的类型
 */
export type FolderWithRelations = Folder & {
  parent?: {
    id: string;
    name: string;
  } | null;
  children?: FolderWithRelations[];
  documents?: Document[];
  _count?: {
    documents: number;
    children: number;
  };
};

/**
 * 文件夹服务类
 * 提供文件夹相关的业务逻辑操作
 */
export class FolderService {
  /**
   * 验证用户是否有权限访问文件夹
   * @param folderId 文件夹ID
   * @param userId 用户ID
   * @returns 文件夹信息（如果有权限）
   */
  static async validateFolderAccess(
    folderId: string,
    userId: string
  ): Promise<Folder | null> {
    return await prisma.folder.findFirst({
      where: {
        id: folderId,
        userId: userId,
        isDeleted: false, // 只返回未删除的文件夹
      },
    });
  }

  /**
   * 检查文件夹名称是否在指定位置重复
   * @param name 文件夹名称
   * @param parentId 父文件夹ID（可选）
   * @param userId 用户ID
   * @param excludeFolderId 排除的文件夹ID（用于更新时检查）
   * @returns 是否重复
   */
  static async isFolderNameDuplicate(
    name: string,
    userId: string,
    parentId?: string | null,
    excludeFolderId?: string
  ): Promise<boolean> {
    const whereClause: any = {
      name,
      userId,
      parentId: parentId || null,
      isDeleted: false, // 只检查活跃的文件夹
    };

    if (excludeFolderId) {
      whereClause.id = { not: excludeFolderId };
    }

    const existingFolder = await prisma.folder.findFirst({
      where: whereClause,
    });

    return !!existingFolder;
  }

  /**
   * 获取文件夹的完整路径
   * @param folderId 文件夹ID
   * @returns 文件夹路径数组
   */
  static async getFolderPath(folderId: string): Promise<string[]> {
    const folder = await prisma.folder.findUnique({
      where: { id: folderId },
      include: {
        parent: true,
      },
    });

    if (!folder) {
      return [];
    }

    const path = [folder.name];

    if (folder.parent) {
      const parentPath = await this.getFolderPath(folder.parent.id);
      path.unshift(...parentPath);
    }

    return path;
  }

  /**
   * 获取文件夹的所有子文件夹ID（递归）
   * @param folderId 文件夹ID
   * @returns 所有子文件夹ID数组
   */
  static async getDescendantIds(folderId: string): Promise<string[]> {
    const descendants: string[] = [];

    const children = await prisma.folder.findMany({
      where: { parentId: folderId },
      select: { id: true },
    });

    for (const child of children) {
      descendants.push(child.id);
      const childDescendants = await this.getDescendantIds(child.id);
      descendants.push(...childDescendants);
    }

    return descendants;
  }

  /**
   * 检查循环引用
   * @param folderId 要移动的文件夹ID
   * @param targetParentId 目标父文件夹ID
   * @returns 是否存在循环引用
   */
  static async checkCircularReference(
    folderId: string,
    targetParentId: string
  ): Promise<boolean> {
    if (folderId === targetParentId) {
      return true;
    }

    const descendants = await this.getDescendantIds(folderId);
    return descendants.includes(targetParentId);
  }

  /**
   * 获取文件夹的统计信息
   * @param folderId 文件夹ID
   * @param userId 用户ID
   * @returns 统计信息
   */
  static async getFolderStats(folderId: string, userId: string) {
    const folder = await prisma.folder.findFirst({
      where: {
        id: folderId,
        userId,
      },
      include: {
        _count: {
          select: {
            documents: true,
            children: true,
          },
        },
      },
    });

    if (!folder) {
      return null;
    }

    // 递归计算所有子文件夹的统计信息
    const descendants = await this.getDescendantIds(folderId);

    const [totalDocuments, totalSubfolders] = await Promise.all([
      prisma.document.count({
        where: {
          folderId: { in: [folderId, ...descendants] },
          userId,
        },
      }),
      descendants.length,
    ]);

    return {
      directDocuments: folder._count.documents,
      directSubfolders: folder._count.children,
      totalDocuments,
      totalSubfolders,
      depth: await this.getFolderDepth(folderId),
    };
  }

  /**
   * 获取文件夹的深度
   * @param folderId 文件夹ID
   * @returns 文件夹深度（根级为0）
   */
  static async getFolderDepth(folderId: string): Promise<number> {
    const folder = await prisma.folder.findUnique({
      where: { id: folderId },
      select: { parentId: true },
    });

    if (!folder || !folder.parentId) {
      return 0;
    }

    return 1 + await this.getFolderDepth(folder.parentId);
  }

  /**
   * 构建文件夹树结构
   * @param userId 用户ID
   * @param includeDocuments 是否包含文档
   * @returns 文件夹树
   */
  static async buildFolderTree(
    userId: string,
    includeDocuments: boolean = false
  ): Promise<FolderWithRelations[]> {
    // 获取所有文件夹
    const allFolders = await prisma.folder.findMany({
      where: { userId },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            documents: true,
            children: true,
          },
        },
        ...(includeDocuments && {
          documents: {
            select: {
              id: true,
              title: true,
              createdAt: true,
              updatedAt: true,
              wordCount: true,
              charCount: true,
            },
            orderBy: {
              updatedAt: 'desc',
            },
          },
        }),
      },
      orderBy: {
        name: 'asc',
      },
    });

    // 构建树结构
    const folderMap = new Map<string, FolderWithRelations>();
    const rootFolders: FolderWithRelations[] = [];

    // 第一遍：创建所有文件夹的映射
    allFolders.forEach(folder => {
      folderMap.set(folder.id, { ...folder, children: [] });
    });

    // 第二遍：建立父子关系
    allFolders.forEach(folder => {
      const folderNode = folderMap.get(folder.id)!;

      if (folder.parentId) {
        const parent = folderMap.get(folder.parentId);
        if (parent) {
          parent.children!.push(folderNode);
        }
      } else {
        rootFolders.push(folderNode);
      }
    });

    return rootFolders;
  }

  /**
   * 搜索文件夹
   * @param userId 用户ID
   * @param query 搜索关键词
   * @param options 搜索选项
   * @returns 搜索结果
   */
  static async searchFolders(
    userId: string,
    query: string,
    options: {
      parentId?: string;
      limit?: number;
      includeDocuments?: boolean;
    } = {}
  ) {
    const { parentId, limit = 50, includeDocuments = false } = options;

    const whereClause: any = {
      userId,
      name: {
        contains: query,
        mode: 'insensitive',
      },
    };

    if (parentId) {
      whereClause.parentId = parentId;
    }

    const includeOptions: any = {
      parent: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          documents: true,
          children: true,
        },
      },
    };

    if (includeDocuments) {
      includeOptions.documents = {
        select: {
          id: true,
          title: true,
          createdAt: true,
          updatedAt: true,
          wordCount: true,
          charCount: true,
        },
        orderBy: {
          updatedAt: 'desc',
        },
        take: 10,
      };
    }

    return await prisma.folder.findMany({
      where: whereClause,
      include: includeOptions,
      orderBy: [
        { name: 'asc' },
        { createdAt: 'desc' },
      ],
      take: limit,
    });
  }

  /**
   * 获取用户的文件夹统计信息
   * @param userId 用户ID
   * @returns 统计信息
   */
  static async getUserFolderStats(userId: string) {
    const [
      totalFolders,
      rootFolders,
      totalDocuments,
      documentsInFolders,
      documentsInRoot,
      recentFolders,
    ] = await Promise.all([
      prisma.folder.count({
        where: { userId },
      }),
      prisma.folder.count({
        where: {
          userId,
          parentId: null,
        },
      }),
      prisma.document.count({
        where: { userId },
      }),
      prisma.document.count({
        where: {
          userId,
          folderId: { not: null },
        },
      }),
      prisma.document.count({
        where: {
          userId,
          folderId: null,
        },
      }),
      prisma.folder.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          id: true,
          name: true,
          createdAt: true,
          _count: {
            select: {
              documents: true,
              children: true,
            },
          },
        },
      }),
    ]);

    const folderUsageRate = totalDocuments > 0
      ? Math.round((documentsInFolders / totalDocuments) * 100)
      : 0;

    const avgDocumentsPerFolder = totalFolders > 0
      ? Math.round(documentsInFolders / totalFolders)
      : 0;

    return {
      totalFolders,
      rootFolders,
      totalDocuments,
      documentsInFolders,
      documentsInRoot,
      folderUsageRate,
      avgDocumentsPerFolder,
      recentFolders,
    };
  }

  /**
   * 清理空文件夹
   * @param userId 用户ID
   * @param dryRun 是否只是预览（不实际删除）
   * @returns 清理结果
   */
  static async cleanupEmptyFolders(userId: string, dryRun: boolean = true) {
    const emptyFolders = await prisma.folder.findMany({
      where: {
        userId,
        documents: { none: {} },
        children: { none: {} },
      },
      select: {
        id: true,
        name: true,
        createdAt: true,
      },
    });

    if (!dryRun && emptyFolders.length > 0) {
      await prisma.folder.deleteMany({
        where: {
          id: { in: emptyFolders.map(f => f.id) },
        },
      });
    }

    return {
      emptyFolders,
      count: emptyFolders.length,
      deleted: !dryRun,
    };
  }
}