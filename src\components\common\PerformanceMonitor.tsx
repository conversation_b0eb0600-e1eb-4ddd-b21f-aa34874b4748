'use client';

import { useState, useEffect, useCallback } from 'react';
import { aiCacheManager } from '@/lib/services/ai/ai-cache-manager';
import { aiBatchProcessor } from '@/lib/services/ai/ai-batch-processor';
import { CodeSplittingManager } from '@/hooks/useCodeSplitting';

interface PerformanceMetrics {
  // 内存使用情况
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  
  // AI 服务性能
  aiPerformance: {
    cacheHitRate: number;
    averageResponseTime: number;
    queueLength: number;
    activeBatches: number;
    throughput: number;
  };
  
  // 代码分割统计
  codeSplitting: {
    loadedModules: number;
    pendingModules: number;
    registeredModules: number;
  };
  
  // 页面性能
  pagePerformance: {
    loadTime: number;
    domContentLoaded: number;
    firstContentfulPaint: number;
    largestContentfulPaint: number;
  };
}

interface PerformanceMonitorProps {
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 更新间隔（毫秒） */
  updateInterval?: number;
  /** 是否在开发环境中显示 */
  showInDevelopment?: boolean;
  /** 性能阈值配置 */
  thresholds?: {
    memoryUsage: number;
    cacheHitRate: number;
    responseTime: number;
  };
}

/**
 * 性能监控组件
 * 实时监控应用性能指标
 */
export function PerformanceMonitor({
  showDetails = false,
  updateInterval = 5000,
  showInDevelopment = true,
  thresholds = {
    memoryUsage: 80, // 80%
    cacheHitRate: 70, // 70%
    responseTime: 2000, // 2秒
  },
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [alerts, setAlerts] = useState<string[]>([]);

  // 检查是否应该显示监控器
  const shouldShow = showInDevelopment && process.env.NODE_ENV === 'development';

  // 收集性能指标
  const collectMetrics = useCallback(async (): Promise<PerformanceMetrics> => {
    // 内存使用情况
    const memoryInfo = (performance as any).memory;
    const memoryUsage = memoryInfo ? {
      used: memoryInfo.usedJSHeapSize,
      total: memoryInfo.totalJSHeapSize,
      percentage: (memoryInfo.usedJSHeapSize / memoryInfo.totalJSHeapSize) * 100,
    } : { used: 0, total: 0, percentage: 0 };

    // AI 服务性能
    const aiStats = aiCacheManager.getStats();
    const batchStats = aiBatchProcessor.getStats();
    const aiPerformance = {
      cacheHitRate: aiStats.hitRate * 100,
      averageResponseTime: batchStats.averageProcessingTime,
      queueLength: batchStats.queueLength,
      activeBatches: batchStats.activeBatches,
      throughput: batchStats.throughput,
    };

    // 代码分割统计
    const codeSplittingManager = CodeSplittingManager.getInstance();
    const codeSplitting = codeSplittingManager.getStats();

    // 页面性能
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paintEntries = performance.getEntriesByType('paint');
    const lcpEntries = performance.getEntriesByType('largest-contentful-paint');

    const pagePerformance = {
      loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
      domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
      firstContentfulPaint: paintEntries.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0,
      largestContentfulPaint: lcpEntries[lcpEntries.length - 1]?.startTime || 0,
    };

    return {
      memoryUsage,
      aiPerformance,
      codeSplitting,
      pagePerformance,
    };
  }, []);

  // 检查性能警告
  const checkAlerts = useCallback((metrics: PerformanceMetrics) => {
    const newAlerts: string[] = [];

    if (metrics.memoryUsage.percentage > thresholds.memoryUsage) {
      newAlerts.push(`内存使用率过高: ${metrics.memoryUsage.percentage.toFixed(1)}%`);
    }

    if (metrics.aiPerformance.cacheHitRate < thresholds.cacheHitRate) {
      newAlerts.push(`AI缓存命中率过低: ${metrics.aiPerformance.cacheHitRate.toFixed(1)}%`);
    }

    if (metrics.aiPerformance.averageResponseTime > thresholds.responseTime) {
      newAlerts.push(`AI响应时间过长: ${metrics.aiPerformance.averageResponseTime.toFixed(0)}ms`);
    }

    if (metrics.aiPerformance.queueLength > 20) {
      newAlerts.push(`AI请求队列过长: ${metrics.aiPerformance.queueLength}`);
    }

    setAlerts(newAlerts);
  }, [thresholds]);

  // 定期更新指标
  useEffect(() => {
    if (!shouldShow) return;

    const updateMetrics = async () => {
      try {
        const newMetrics = await collectMetrics();
        setMetrics(newMetrics);
        checkAlerts(newMetrics);
      } catch (error) {
        console.error('收集性能指标失败:', error);
      }
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, updateInterval);

    return () => clearInterval(interval);
  }, [shouldShow, collectMetrics, checkAlerts, updateInterval]);

  if (!shouldShow || !metrics) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* 性能指示器 */}
      <div
        className={`
          w-12 h-12 rounded-full cursor-pointer transition-all duration-200
          ${alerts.length > 0 
            ? 'bg-red-500 animate-pulse' 
            : metrics.aiPerformance.cacheHitRate > thresholds.cacheHitRate
              ? 'bg-green-500'
              : 'bg-yellow-500'
          }
          hover:scale-110 shadow-lg
        `}
        onClick={() => setIsVisible(!isVisible)}
        title={`性能监控 - ${alerts.length > 0 ? '有警告' : '正常'}`}
      >
        <div className="flex items-center justify-center h-full text-white font-bold">
          ⚡
        </div>
      </div>

      {/* 详细面板 */}
      {isVisible && (
        <div className="absolute bottom-16 right-0 w-80 bg-white rounded-lg shadow-xl border border-gray-200 p-4 max-h-96 overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">性能监控</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          {/* 警告信息 */}
          {alerts.length > 0 && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <h4 className="text-sm font-medium text-red-800 mb-2">⚠️ 性能警告</h4>
              <ul className="text-xs text-red-700 space-y-1">
                {alerts.map((alert, index) => (
                  <li key={index}>• {alert}</li>
                ))}
              </ul>
            </div>
          )}

          {/* 内存使用情况 */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">内存使用</h4>
            <div className="flex items-center space-x-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    metrics.memoryUsage.percentage > thresholds.memoryUsage
                      ? 'bg-red-500'
                      : metrics.memoryUsage.percentage > 60
                        ? 'bg-yellow-500'
                        : 'bg-green-500'
                  }`}
                  style={{ width: `${Math.min(metrics.memoryUsage.percentage, 100)}%` }}
                />
              </div>
              <span className="text-xs text-gray-600">
                {metrics.memoryUsage.percentage.toFixed(1)}%
              </span>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {(metrics.memoryUsage.used / 1024 / 1024).toFixed(1)}MB / 
              {(metrics.memoryUsage.total / 1024 / 1024).toFixed(1)}MB
            </div>
          </div>

          {/* AI 性能 */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">AI 服务</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-gray-500">缓存命中率:</span>
                <span className={`ml-1 ${
                  metrics.aiPerformance.cacheHitRate > thresholds.cacheHitRate
                    ? 'text-green-600'
                    : 'text-red-600'
                }`}>
                  {metrics.aiPerformance.cacheHitRate.toFixed(1)}%
                </span>
              </div>
              <div>
                <span className="text-gray-500">响应时间:</span>
                <span className={`ml-1 ${
                  metrics.aiPerformance.averageResponseTime < thresholds.responseTime
                    ? 'text-green-600'
                    : 'text-red-600'
                }`}>
                  {metrics.aiPerformance.averageResponseTime.toFixed(0)}ms
                </span>
              </div>
              <div>
                <span className="text-gray-500">队列长度:</span>
                <span className="ml-1 text-gray-700">
                  {metrics.aiPerformance.queueLength}
                </span>
              </div>
              <div>
                <span className="text-gray-500">活跃批次:</span>
                <span className="ml-1 text-gray-700">
                  {metrics.aiPerformance.activeBatches}
                </span>
              </div>
            </div>
          </div>

          {/* 代码分割 */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">代码分割</h4>
            <div className="text-xs text-gray-600">
              已加载: {metrics.codeSplitting.loadedModules} / 
              注册: {metrics.codeSplitting.registeredModules}
              {metrics.codeSplitting.pendingModules > 0 && (
                <span className="text-yellow-600 ml-1">
                  (待加载: {metrics.codeSplitting.pendingModules})
                </span>
              )}
            </div>
          </div>

          {/* 页面性能 */}
          {showDetails && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">页面性能</h4>
              <div className="text-xs text-gray-600 space-y-1">
                <div>加载时间: {metrics.pagePerformance.loadTime.toFixed(0)}ms</div>
                <div>DOM就绪: {metrics.pagePerformance.domContentLoaded.toFixed(0)}ms</div>
                <div>首次内容绘制: {metrics.pagePerformance.firstContentfulPaint.toFixed(0)}ms</div>
                {metrics.pagePerformance.largestContentfulPaint > 0 && (
                  <div>最大内容绘制: {metrics.pagePerformance.largestContentfulPaint.toFixed(0)}ms</div>
                )}
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="mt-4 pt-3 border-t border-gray-200 flex space-x-2">
            <button
              onClick={() => {
                aiCacheManager.clear();
                aiBatchProcessor.clearQueue();
              }}
              className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
            >
              清理缓存
            </button>
            <button
              onClick={() => setIsVisible(false)}
              className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
            >
              关闭
            </button>
          </div>
        </div>
      )}
    </div>
  );
}