'use client';

/**
 * AI 配置列表组件
 * 显示和管理用户的 AI 配置
 */

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Settings, 
  Trash2, 
  Star, 
  StarOff, 
  Plus, 
  Edit, 
  Zap,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

import type { AIConfigData } from '@/hooks/useAIConfig';

interface AIConfigListProps {
  /** 配置列表 */
  configs: AIConfigData[];
  /** 加载状态 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 添加配置回调 */
  onAdd: () => void;
  /** 编辑配置回调 */
  onEdit: (config: AIConfigData) => void;
  /** 删除配置回调 */
  onDelete: (id: string) => Promise<boolean>;
  /** 设置默认配置回调 */
  onSetDefault: (id: string) => Promise<boolean>;
  /** 测试连接回调 */
  onTestConnection?: (config: AIConfigData) => void;
}

export function AIConfigList({
  configs,
  loading,
  error,
  onAdd,
  onEdit,
  onDelete,
  onSetDefault,
  onTestConnection
}: AIConfigListProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [settingDefaultId, setSettingDefaultId] = useState<string | null>(null);

  /**
   * 处理删除配置
   */
  const handleDelete = async (id: string, name: string) => {
    if (!confirm(`确定要删除配置 "${name}" 吗？此操作不可撤销。`)) {
      return;
    }

    setDeletingId(id);
    try {
      await onDelete(id);
    } finally {
      setDeletingId(null);
    }
  };

  /**
   * 处理设置默认配置
   */
  const handleSetDefault = async (id: string) => {
    setSettingDefaultId(id);
    try {
      await onSetDefault(id);
    } finally {
      setSettingDefaultId(null);
    }
  };

  /**
   * 获取提供商显示名称
   */
  const getProviderDisplayName = (provider: string): string => {
    const names: Record<string, string> = {
      openai: 'OpenAI',
      ollama: 'Ollama',
      gemini: 'Gemini'
    };
    return names[provider] || provider;
  };

  /**
   * 获取提供商颜色
   */
  const getProviderColor = (provider: string): string => {
    const colors: Record<string, string> = {
      openai: 'bg-green-100 text-green-800',
      ollama: 'bg-blue-100 text-blue-800',
      gemini: 'bg-purple-100 text-purple-800'
    };
    return colors[provider] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* 头部操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">AI 配置管理</h2>
          <p className="text-muted-foreground">
            管理您的 AI 服务配置和连接设置
          </p>
        </div>
        <Button onClick={onAdd}>
          <Plus className="w-4 h-4 mr-2" />
          添加配置
        </Button>
      </div>

      {/* 配置列表 */}
      {configs.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Settings className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">暂无 AI 配置</h3>
            <p className="text-muted-foreground text-center mb-4">
              添加您的第一个 AI 服务配置来开始使用 AI 功能
            </p>
            <Button onClick={onAdd}>
              <Plus className="w-4 h-4 mr-2" />
              添加配置
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {configs.map((config) => (
            <Card key={config.id} className={config.isDefault ? 'ring-2 ring-blue-500' : ''}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getProviderColor(config.provider)}`}>
                      {getProviderDisplayName(config.provider)}
                    </span>
                    {config.isDefault && (
                      <div className="flex items-center text-blue-600">
                        <Star className="w-4 h-4 mr-1 fill-current" />
                        <span className="text-sm font-medium">默认</span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {onTestConnection && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onTestConnection(config)}
                        title="测试连接"
                      >
                        <Zap className="w-4 h-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(config)}
                      title="编辑配置"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    {!config.isDefault && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSetDefault(config.id)}
                        disabled={settingDefaultId === config.id}
                        title="设为默认"
                      >
                        <StarOff className="w-4 h-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(config.id, `${getProviderDisplayName(config.provider)} - ${config.model}`)}
                      disabled={deletingId === config.id}
                      title="删除配置"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <CardTitle className="text-lg">
                  {config.model}
                </CardTitle>
                <CardDescription>
                  创建于 {new Date(config.createdAt).toLocaleDateString()}
                  {config.updatedAt !== config.createdAt && (
                    <span> • 更新于 {new Date(config.updatedAt).toLocaleDateString()}</span>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-muted-foreground">最大令牌:</span>
                    <p>{config.maxTokens.toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="font-medium text-muted-foreground">温度:</span>
                    <p>{config.temperature}</p>
                  </div>
                  <div>
                    <span className="font-medium text-muted-foreground">API 密钥:</span>
                    <p className="flex items-center">
                      {config.hasApiKey ? (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1 text-green-600" />
                          已配置
                        </>
                      ) : (
                        <>
                          <AlertCircle className="w-3 h-3 mr-1 text-yellow-600" />
                          未配置
                        </>
                      )}
                    </p>
                  </div>
                  {config.endpoint && (
                    <div>
                      <span className="font-medium text-muted-foreground">端点:</span>
                      <p className="truncate" title={config.endpoint}>
                        {config.endpoint}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}