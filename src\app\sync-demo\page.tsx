'use client';

import React, { useState } from 'react';
import { useSync } from '@/hooks/useSync';
import { 
  SyncControlPanel,
  SyncStatusIndicator,
  SyncProgress,
  ConflictList
} from '@/components/sync';
import { 
  RefreshCw, 
  Settings, 
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

/**
 * 同步功能演示页面
 * 展示各种同步组件和功能
 */
export default function SyncDemoPage() {
  const [activeTab, setActiveTab] = useState<'control' | 'status' | 'conflicts'>('control');
  
  const {
    syncState,
    isOnline,
    isSyncing,
    lastSyncAt,
    syncProgress,
    conflicts,
    hasConflicts,
    manualSync,
    resolveConflict,
    syncStats
  } = useSync({
    onSyncComplete: (results) => {
      console.log('同步完成:', results);
    },
    onSyncError: (error) => {
      console.error('同步错误:', error);
    },
    onConflictDetected: (conflict) => {
      console.log('检测到冲突:', conflict);
    }
  });

  const handleManualSync = async () => {
    try {
      await manualSync({
        force: false,
        includeDeleted: false,
        conflictResolution: 'manual'
      });
    } catch (error) {
      console.error('手动同步失败:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                文档同步功能演示
              </h1>
              <p className="text-gray-600 mt-1">
                展示文档同步服务的各种功能和组件
              </p>
            </div>
            <SyncStatusIndicator />
          </div>
        </div>

        {/* 快速操作 */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            快速操作
          </h2>
          <div className="flex items-center gap-4">
            <button
              onClick={handleManualSync}
              disabled={isSyncing || !isOnline}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              <RefreshCw className={`h-4 w-4 ${isSyncing ? 'animate-spin' : ''}`} />
              {isSyncing ? '同步中...' : '立即同步'}
            </button>
            
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>状态:</span>
              {isOnline ? (
                <span className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  在线
                </span>
              ) : (
                <span className="flex items-center gap-1 text-red-600">
                  <AlertTriangle className="h-4 w-4" />
                  离线
                </span>
              )}
            </div>

            {lastSyncAt && (
              <div className="text-sm text-gray-600">
                上次同步: {lastSyncAt.toLocaleTimeString()}
              </div>
            )}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="text-2xl font-bold text-green-600">
              {syncStats.totalSynced}
            </div>
            <div className="text-sm text-gray-600">已同步文档</div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {syncStats.totalConflicts}
            </div>
            <div className="text-sm text-gray-600">同步冲突</div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="text-2xl font-bold text-red-600">
              {syncStats.totalErrors}
            </div>
            <div className="text-sm text-gray-600">同步错误</div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="text-2xl font-bold text-blue-600">
              {syncState.pendingChanges.length}
            </div>
            <div className="text-sm text-gray-600">待同步项目</div>
          </div>
        </div>

        {/* 同步进度 */}
        {syncProgress && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              同步进度
            </h2>
            <SyncProgress progress={syncProgress as any} />
          </div>
        )}

        {/* 选项卡 */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('control')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'control'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                同步控制
              </button>
              <button
                onClick={() => setActiveTab('status')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'status'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                状态详情
              </button>
              <button
                onClick={() => setActiveTab('conflicts')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'conflicts'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                冲突管理
                {hasConflicts && (
                  <span className="ml-2 bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">
                    {conflicts.length}
                  </span>
                )}
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'control' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  同步控制面板
                </h3>
                <SyncControlPanel />
              </div>
            )}

            {activeTab === 'status' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  详细状态信息
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">同步状态</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">网络连接:</span>
                        <span className={isOnline ? 'text-green-600' : 'text-red-600'}>
                          {isOnline ? '在线' : '离线'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">同步状态:</span>
                        <span className={isSyncing ? 'text-blue-600' : 'text-green-600'}>
                          {isSyncing ? '同步中' : '已同步'}
                        </span>
                      </div>
                      {lastSyncAt && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">上次同步:</span>
                          <span className="text-gray-500">
                            {lastSyncAt.toLocaleString()}
                          </span>
                        </div>
                      )}
                      {syncStats.lastSyncDuration && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">同步耗时:</span>
                          <span className="text-gray-500">
                            {(syncStats.lastSyncDuration / 1000).toFixed(1)}s
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">待处理项目</h4>
                    {syncState.pendingChanges.length > 0 ? (
                      <div className="space-y-2">
                        {syncState.pendingChanges.slice(0, 5).map((change) => (
                          <div key={change.id} className="flex items-center gap-2 text-sm">
                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                            <span className="text-gray-600">
                              {change.type} - {change.entityType}
                            </span>
                            <span className="text-gray-400 text-xs">
                              {change.timestamp.toLocaleTimeString()}
                            </span>
                          </div>
                        ))}
                        {syncState.pendingChanges.length > 5 && (
                          <div className="text-xs text-gray-400">
                            还有 {syncState.pendingChanges.length - 5} 项...
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500">
                        没有待处理的项目
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'conflicts' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  冲突管理
                </h3>
                
                {hasConflicts ? (
                  <ConflictList
                    conflicts={conflicts}
                    onResolveConflict={resolveConflict}
                  />
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                    <p>没有同步冲突</p>
                    <p className="text-sm">所有文档都已成功同步</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 调试信息 */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            调试信息
          </h2>
          <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-auto">
            {JSON.stringify(
              {
                syncState: {
                  ...syncState,
                  pendingChanges: syncState.pendingChanges.length,
                  conflicts: syncState.conflicts.length
                },
                syncStats
              },
              null,
              2
            )}
          </pre>
        </div>
      </div>
    </div>
  );
}