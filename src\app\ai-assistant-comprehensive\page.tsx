'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { AIAssistantContainer } from '@/components/ai/AIAssistantPanel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  SparklesIcon,
  SmartphoneIcon,
  TabletIcon,
  MonitorIcon,
  CheckCircleIcon,
  InfoIcon,
  SettingsIcon,
  LayersIcon,
  ZapIcon,
  PaletteIcon,
  KeyboardIcon,
  TouchpadIcon as TouchIcon,
  EyeIcon,
  ArrowRightIcon
} from 'lucide-react';

/**
 * AI 助手面板综合演示页面
 * 展示所有功能特性和响应式设计
 */
export default function AIAssistantComprehensivePage() {
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 });
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const [selectedText, setSelectedText] = useState('');
  const [panelOpen, setPanelOpen] = useState(false);
  const [interactionCount, setInteractionCount] = useState(0);
  const [aiActionHistory, setAiActionHistory] = useState<Array<{
    action: string;
    timestamp: Date;
    success: boolean;
  }>>([]);

  // 检测屏幕尺寸和设备类型
  useEffect(() => {
    const updateScreenInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({ width, height });
      
      if (width < 768) {
        setDeviceType('mobile');
      } else if (width < 1024) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    updateScreenInfo();
    window.addEventListener('resize', updateScreenInfo);
    
    return () => window.removeEventListener('resize', updateScreenInfo);
  }, []);

  /**
   * 处理 AI 操作
   */
  const handleAIAction = useCallback(async (actionId: string, data?: any) => {
    console.log('AI Action:', actionId, data);
    
    setInteractionCount(prev => prev + 1);
    
    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // 记录操作历史
    setAiActionHistory(prev => [{
      action: actionId,
      timestamp: new Date(),
      success: Math.random() > 0.1 // 90% 成功率
    }, ...prev.slice(0, 9)]);
    
    return Promise.resolve();
  }, []);

  /**
   * 处理面板状态变化
   */
  const handlePanelStateChange = useCallback((isOpen: boolean) => {
    setPanelOpen(isOpen);
  }, []);

  /**
   * 获取设备图标和颜色
   */
  const getDeviceInfo = () => {
    switch (deviceType) {
      case 'mobile':
        return { 
          icon: SmartphoneIcon, 
          color: 'text-green-600', 
          bg: 'bg-green-50',
          name: '移动端'
        };
      case 'tablet':
        return { 
          icon: TabletIcon, 
          color: 'text-blue-600', 
          bg: 'bg-blue-50',
          name: '平板端'
        };
      default:
        return { 
          icon: MonitorIcon, 
          color: 'text-purple-600', 
          bg: 'bg-purple-50',
          name: '桌面端'
        };
    }
  };

  const deviceInfo = getDeviceInfo();
  const DeviceIcon = deviceInfo.icon;

  // 示例文本内容
  const sampleTexts = [
    {
      title: '技术文档',
      content: 'React 是一个用于构建用户界面的 JavaScript 库。它采用组件化的开发模式，使得代码更加模块化和可重用。通过虚拟 DOM 技术，React 能够高效地更新和渲染组件。',
      category: '技术'
    },
    {
      title: '产品介绍',
      content: '我们的 AI 文档编辑器结合了现代化的编辑体验和智能的 AI 辅助功能。用户可以通过直观的界面创建、编辑和管理文档，同时享受 AI 带来的写作辅助和内容优化功能。',
      category: '产品'
    },
    {
      title: '学术论文',
      content: '人工智能在文档处理领域的应用正在快速发展。通过自然语言处理技术，AI 系统能够理解文档内容，提供智能的编辑建议，并协助用户完成各种写作任务。',
      category: '学术'
    },
    {
      title: '创意写作',
      content: '在一个充满科技感的未来世界里，AI 助手成为了每个人不可或缺的伙伴。它们不仅能够帮助处理日常工作，还能激发人类的创造力，共同创作出令人惊叹的作品。',
      category: '创意'
    }
  ];

  // 功能特性列表
  const features = [
    {
      id: 'collapsible',
      title: '可折叠面板',
      description: '侧边面板可以自由展开和收起',
      icon: LayersIcon,
      status: 'completed',
      demo: '点击 AI 按钮切换面板状态'
    },
    {
      id: 'categorized',
      title: 'AI 功能分类',
      description: '按功能类型组织 AI 工具',
      icon: SettingsIcon,
      status: 'completed',
      demo: '面板中的功能标签页'
    },
    {
      id: 'responsive',
      title: '响应式布局',
      description: '适配不同设备尺寸',
      icon: deviceInfo.icon,
      status: 'completed',
      demo: '调整窗口大小查看变化'
    },
    {
      id: 'mobile-optimized',
      title: '移动端优化',
      description: '触摸友好的交互设计',
      icon: TouchIcon,
      status: 'completed',
      demo: '在移动设备上体验'
    },
    {
      id: 'keyboard-shortcuts',
      title: '键盘快捷键',
      description: '支持快捷键操作',
      icon: KeyboardIcon,
      status: 'completed',
      demo: 'Ctrl+Shift+A 切换面板'
    },
    {
      id: 'visual-feedback',
      title: '视觉反馈',
      description: '清晰的状态指示和动画',
      icon: EyeIcon,
      status: 'completed',
      demo: '观察按钮和面板动画'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      <div className="max-w-6xl mx-auto p-4 md:p-6 space-y-6">
        {/* 页面标题 */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full">
              <SparklesIcon className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
              AI 助手面板
            </h1>
          </div>
          <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto">
            完整的 AI 助手面板实现，包含可折叠设计、功能分类导航和全设备响应式适配
          </p>
          <div className="flex items-center justify-center gap-2">
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <CheckCircleIcon className="h-3 w-3 mr-1" />
              任务 27 已完成
            </Badge>
          </div>
        </div>

        {/* 实时状态面板 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className={`${deviceInfo.bg} border-2`}>
            <CardContent className="p-4 text-center">
              <DeviceIcon className={`h-8 w-8 ${deviceInfo.color} mx-auto mb-2`} />
              <div className="font-semibold text-gray-900">{deviceInfo.name}</div>
              <div className="text-sm text-gray-600">{screenSize.width} × {screenSize.height}</div>
            </CardContent>
          </Card>
          
          <Card className="bg-blue-50 border-2 border-blue-200">
            <CardContent className="p-4 text-center">
              <ZapIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">面板状态</div>
              <div className="text-sm text-gray-600">
                {panelOpen ? '已打开' : '已关闭'}
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-purple-50 border-2 border-purple-200">
            <CardContent className="p-4 text-center">
              <PaletteIcon className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">交互次数</div>
              <div className="text-sm text-gray-600">{interactionCount}</div>
            </CardContent>
          </Card>
          
          <Card className="bg-orange-50 border-2 border-orange-200">
            <CardContent className="p-4 text-center">
              <InfoIcon className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">选中文本</div>
              <div className="text-sm text-gray-600">
                {selectedText ? `${selectedText.length} 字符` : '未选择'}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 功能特性展示 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircleIcon className="h-5 w-5 text-green-600" />
              实现的功能特性
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {features.map((feature) => {
                const IconComponent = feature.icon;
                return (
                  <div
                    key={feature.id}
                    className="p-4 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-blue-50 rounded-lg">
                        <IconComponent className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-gray-900">{feature.title}</h4>
                          <Badge 
                            variant="secondary" 
                            className="bg-green-100 text-green-800 text-xs"
                          >
                            ✓
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{feature.description}</p>
                        <p className="text-xs text-blue-600 font-medium">{feature.demo}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* 交互测试区域 */}
        <Card>
          <CardHeader>
            <CardTitle>交互测试区域</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="text-selection" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="text-selection">文本选择</TabsTrigger>
                <TabsTrigger value="ai-history">操作历史</TabsTrigger>
                <TabsTrigger value="responsive">响应式测试</TabsTrigger>
              </TabsList>
              
              <TabsContent value="text-selection" className="space-y-4">
                <p className="text-sm text-gray-600">
                  点击下面的文本段落来测试 AI 助手面板的文本选择功能：
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {sampleTexts.map((text, index) => (
                    <div
                      key={index}
                      className={`
                        p-4 rounded-lg border cursor-pointer transition-all duration-200
                        ${selectedText === text.content
                          ? 'bg-blue-50 border-blue-300 shadow-md' 
                          : 'bg-white border-gray-200 hover:bg-gray-50'
                        }
                      `}
                      onClick={() => setSelectedText(text.content)}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline" className="text-xs">
                          {text.category}
                        </Badge>
                        <h4 className="font-medium text-gray-900">{text.title}</h4>
                      </div>
                      <p className="text-sm text-gray-700 leading-relaxed">
                        {text.content}
                      </p>
                      {selectedText === text.content && (
                        <div className="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-800">
                          ✨ 已选择此文本，现在可以使用 AI 功能
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="ai-history" className="space-y-4">
                <p className="text-sm text-gray-600">
                  AI 操作历史记录：
                </p>
                {aiActionHistory.length > 0 ? (
                  <div className="space-y-2">
                    {aiActionHistory.map((item, index) => (
                      <div
                        key={index}
                        className={`
                          p-3 rounded-lg border flex items-center justify-between
                          ${item.success 
                            ? 'bg-green-50 border-green-200' 
                            : 'bg-red-50 border-red-200'
                          }
                        `}
                      >
                        <div className="flex items-center gap-3">
                          <div className={`
                            w-2 h-2 rounded-full
                            ${item.success ? 'bg-green-500' : 'bg-red-500'}
                          `}></div>
                          <span className="font-medium text-gray-900">
                            {item.action}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500">
                          {item.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <SparklesIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>还没有 AI 操作记录</p>
                    <p className="text-sm">使用 AI 功能后会显示在这里</p>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="responsive" className="space-y-4">
                <p className="text-sm text-gray-600">
                  响应式设计测试：
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <SmartphoneIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <h4 className="font-medium text-green-900">移动端</h4>
                    <p className="text-xs text-green-700 mt-1">
                      &lt; 768px<br />
                      全屏面板
                    </p>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <TabletIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <h4 className="font-medium text-blue-900">平板端</h4>
                    <p className="text-xs text-blue-700 mt-1">
                      768px - 1024px<br />
                      中等面板
                    </p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <MonitorIcon className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <h4 className="font-medium text-purple-900">桌面端</h4>
                    <p className="text-xs text-purple-700 mt-1">
                      &gt; 1024px<br />
                      侧边面板
                    </p>
                  </div>
                </div>
                <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex items-start gap-3">
                    <InfoIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-900 mb-1">测试建议</h4>
                      <ul className="text-sm text-yellow-800 space-y-1">
                        <li>• 调整浏览器窗口大小观察布局变化</li>
                        <li>• 在不同设备上测试触摸交互</li>
                        <li>• 尝试键盘快捷键 Ctrl+Shift+A</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* 使用指南 */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-900">使用指南</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium text-blue-900 flex items-center gap-2">
                  <ArrowRightIcon className="h-4 w-4" />
                  基本操作
                </h4>
                <ul className="text-sm text-blue-800 space-y-1 ml-6">
                  <li>• 点击右下角 AI 按钮打开面板</li>
                  <li>• 选择文本后使用相关 AI 功能</li>
                  <li>• 使用功能和快捷标签页切换</li>
                  <li>• 观察处理状态和结果展示</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-medium text-blue-900 flex items-center gap-2">
                  <ArrowRightIcon className="h-4 w-4" />
                  高级功能
                </h4>
                <ul className="text-sm text-blue-800 space-y-1 ml-6">
                  <li>• 键盘快捷键快速切换面板</li>
                  <li>• 查看操作历史和统计</li>
                  <li>• 响应式布局自动适配</li>
                  <li>• 移动端全屏优化体验</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI 助手面板 */}
      <AIAssistantContainer
        position="right"
        width={deviceType === 'mobile' ? undefined : 350}
        onAIAction={handleAIAction}

        selectedText={selectedText}
      />
    </div>
  );
}