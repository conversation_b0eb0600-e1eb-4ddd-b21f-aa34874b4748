'use client';

import { DocumentStorageTest } from '@/components/storage/DocumentStorageTest';

export default function StorageTestPage() {
  // For testing purposes, we'll use a hardcoded user ID
  // In a real app, this would come from authentication
  const testUserId = 'test-user-123';

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Document Storage Test
          </h1>
          <p className="text-gray-600">
            Test the local IndexedDB document storage functionality
          </p>
        </div>
        
        <DocumentStorageTest userId={testUserId} />
      </div>
    </div>
  );
}