/**
 * AI 配置默认设置 API
 * 设置指定配置为默认配置
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * 设置默认 AI 配置
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 验证配置是否存在且属于当前用户
    const config = await prisma.aIConfiguration.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    });

    if (!config) {
      return NextResponse.json(
        { error: '配置不存在' },
        { status: 404 }
      );
    }

    // 如果已经是默认配置，直接返回
    if (config.isDefault) {
      return NextResponse.json({ 
        success: true, 
        message: '该配置已经是默认配置' 
      });
    }

    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 取消所有默认配置
      await tx.aIConfiguration.updateMany({
        where: {
          userId: session.user.id,
          isDefault: true
        },
        data: {
          isDefault: false
        }
      });

      // 设置新的默认配置
      await tx.aIConfiguration.update({
        where: {
          id: params.id
        },
        data: {
          isDefault: true
        }
      });
    });

    return NextResponse.json({ 
      success: true, 
      message: '默认配置设置成功' 
    });

  } catch (error) {
    console.error('设置默认 AI 配置失败:', error);
    return NextResponse.json(
      { error: '设置默认配置失败' },
      { status: 500 }
    );
  }
}