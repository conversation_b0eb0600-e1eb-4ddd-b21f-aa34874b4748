# 文档同步服务实现指南

## 概述

本文档描述了文档同步服务的实现，包括客户端和服务器之间的同步机制、自动同步和手动同步功能，以及同步状态指示和错误处理。

## 核心功能

### 1. 同步机制

- **自动同步**: 每30秒自动检查并同步待处理的文档
- **手动同步**: 用户可以手动触发同步操作
- **批量同步**: 支持批量处理多个文档，提高同步效率
- **冲突检测**: 自动检测本地和远程版本之间的冲突
- **冲突解决**: 提供多种冲突解决策略

### 2. 同步状态管理

- **网络状态监听**: 自动检测网络连接状态
- **同步进度跟踪**: 实时显示同步进度和状态
- **错误处理**: 完善的错误处理和重试机制
- **事件系统**: 基于事件的状态更新和通知

## 文件结构

```
src/
├── types/
│   └── sync.ts                    # 同步相关类型定义
├── lib/
│   └── services/
│       ├── sync-service.ts        # 核心同步服务
│       └── __tests__/
│           └── sync-service.test.ts # 同步服务测试
├── hooks/
│   └── useSync.ts                 # 同步状态管理Hook
├── components/
│   └── sync/
│       ├── SyncStatusIndicator.tsx # 同步状态指示器
│       ├── SyncProgress.tsx       # 同步进度显示
│       ├── ConflictResolver.tsx   # 冲突解决组件
│       ├── SyncControlPanel.tsx   # 同步控制面板
│       └── index.ts               # 组件导出
└── app/
    ├── api/
    │   └── documents/
    │       ├── sync/
    │       │   └── route.ts       # 同步API路由
    │       └── [id]/
    │           └── sync/
    │               └── route.ts   # 单文档同步API
    └── sync-demo/
        └── page.tsx               # 同步功能演示页面
```

## 使用方法

### 1. 基础使用

```typescript
import { useSync } from '@/hooks/useSync';
import { SyncStatusIndicator } from '@/components/sync';

function MyComponent() {
  const { 
    isOnline, 
    isSyncing, 
    manualSync, 
    conflicts,
    resolveConflict 
  } = useSync();

  return (
    <div>
      <SyncStatusIndicator />
      <button onClick={() => manualSync()}>
        手动同步
      </button>
    </div>
  );
}
```

### 2. 同步控制面板

```typescript
import { SyncControlPanel } from '@/components/sync';

function SyncPage() {
  return (
    <div>
      <h1>文档同步</h1>
      <SyncControlPanel />
    </div>
  );
}
```

### 3. 冲突处理

```typescript
import { ConflictList } from '@/components/sync';

function ConflictPage() {
  const { conflicts, resolveConflict } = useSync();

  return (
    <ConflictList
      conflicts={conflicts}
      onResolveConflict={resolveConflict}
    />
  );
}
```

## API 接口

### 1. 获取同步文档列表

```
GET /api/documents/sync?lastSyncAt=2023-01-01T00:00:00Z&includeDeleted=false
```

**响应**:
```json
[
  {
    "id": "doc1",
    "title": "文档标题",
    "content": {...},
    "updatedAt": "2023-01-01T12:00:00Z",
    "metadata": {...}
  }
]
```

### 2. 上传文档同步

```
POST /api/documents/sync
```

**请求体**:
```json
{
  "document": {
    "id": "doc1",
    "title": "文档标题",
    "content": {...},
    "updatedAt": "2023-01-01T12:00:00Z"
  },
  "force": false
}
```

### 3. 批量同步

```
PUT /api/documents/sync
```

**请求体**:
```json
{
  "documents": [...],
  "options": {
    "force": false,
    "batchSize": 10
  }
}
```

### 4. 单文档同步操作

```
POST /api/documents/[id]/sync
```

**请求体**:
```json
{
  "action": "force_upload|force_download|resolve_conflict",
  "data": {...}
}
```

## 同步流程

### 1. 自动同步流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Service as 同步服务
    participant API as 服务器API
    participant DB as 数据库

    loop 每30秒
        Service->>Client: 检查网络状态
        alt 在线且未同步
            Service->>Client: 获取脏文档
            Service->>API: 批量上传文档
            API->>DB: 保存文档
            API->>Service: 返回结果
            Service->>API: 下载远程更新
            API->>Service: 返回更新列表
            Service->>Client: 更新本地文档
            Service->>Client: 触发同步完成事件
        end
    end
```

### 2. 冲突检测和解决

```mermaid
flowchart TD
    A[检测到文档更新] --> B{本地和远程都有更新?}
    B -->|是| C[检测冲突类型]
    B -->|否| D[直接同步]
    
    C --> E{冲突类型}
    E -->|内容冲突| F[标记为内容冲突]
    E -->|标题冲突| G[标记为标题冲突]
    E -->|元数据冲突| H[标记为元数据冲突]
    
    F --> I[等待用户解决]
    G --> I
    H --> I
    
    I --> J{用户选择}
    J -->|本地版本| K[使用本地版本]
    J -->|远程版本| L[使用远程版本]
    J -->|手动合并| M[打开合并编辑器]
    
    K --> N[更新服务器]
    L --> O[更新本地]
    M --> P[保存合并结果]
    
    N --> Q[同步完成]
    O --> Q
    P --> Q
```

## 配置选项

### 1. 同步服务配置

```typescript
const syncOptions: SyncOptions = {
  force: false,              // 是否强制同步（忽略冲突）
  includeDeleted: false,     // 是否包含已删除的文档
  conflictResolution: 'manual', // 冲突解决策略
  batchSize: 10              // 批处理大小
};
```

### 2. Hook配置

```typescript
const syncHookOptions: UseSyncOptions = {
  autoSync: true,            // 是否启用自动同步
  syncInterval: 30000,       // 自动同步间隔（毫秒）
  onSyncComplete: (results) => {}, // 同步完成回调
  onSyncError: (error) => {},      // 同步错误回调
  onConflictDetected: (conflict) => {} // 冲突检测回调
};
```

## 错误处理

### 1. 网络错误

- 自动重试机制（最多3次）
- 指数退避策略
- 离线状态处理

### 2. 冲突错误

- 自动检测冲突类型
- 提供多种解决方案
- 用户友好的冲突界面

### 3. 服务器错误

- 详细的错误信息
- 错误分类和处理
- 用户通知机制

## 性能优化

### 1. 批量处理

- 将多个文档打包成批次
- 减少网络请求次数
- 提高同步效率

### 2. 增量同步

- 只同步有变更的文档
- 基于时间戳的增量检测
- 减少数据传输量

### 3. 缓存机制

- 本地状态缓存
- 避免重复计算
- 提高响应速度

## 测试

### 1. 单元测试

```bash
npm test src/lib/services/__tests__/sync-service.test.ts
```

### 2. 集成测试

```bash
npm run test:integration
```

### 3. 端到端测试

```bash
npm run test:e2e
```

## 部署注意事项

### 1. 环境变量

确保设置以下环境变量：
- `DATABASE_URL`: 数据库连接字符串
- `NEXTAUTH_SECRET`: 认证密钥
- `NEXTAUTH_URL`: 应用程序URL

### 2. 数据库迁移

```bash
npm run db:migrate
```

### 3. 性能监控

- 监控同步频率和成功率
- 跟踪冲突发生率
- 监控API响应时间

## 故障排除

### 1. 同步失败

- 检查网络连接
- 验证用户认证状态
- 查看服务器日志

### 2. 冲突无法解决

- 检查文档权限
- 验证数据完整性
- 重置同步状态

### 3. 性能问题

- 调整批处理大小
- 优化同步频率
- 检查数据库性能

## 未来改进

1. **实时同步**: 基于WebSocket的实时同步
2. **协作编辑**: 多用户实时协作功能
3. **版本控制**: 完整的文档版本历史
4. **智能合并**: AI辅助的冲突解决
5. **离线支持**: 更好的离线编辑体验