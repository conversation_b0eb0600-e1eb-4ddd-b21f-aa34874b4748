/**
 * AI 文件命名建议 API 路由
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { aiFileNaming } from '@/lib/services/ai-file-naming';
import { z } from 'zod';

// 请求参数验证模式
const namingSuggestionSchema = z.object({
  documentId: z.string().min(1, '文档ID不能为空'),
  options: z.object({
    style: z.enum(['concise', 'descriptive', 'formal', 'technical', 'creative']).optional(),
    maxLength: z.number().min(10).max(100).optional(),
    includeDate: z.boolean().optional(),
    dateFormat: z.string().optional(),
    includeTypePrefix: z.boolean().optional(),
    language: z.enum(['zh', 'en', 'auto']).optional(),
    customPrefix: z.string().optional(),
    customSuffix: z.string().optional(),
    excludeWords: z.array(z.string()).optional(),
    suggestionCount: z.number().min(1).max(10).optional()
  }).optional()
});

const batchNamingSchema = z.object({
  documentIds: z.array(z.string()).min(1, '至少需要一个文档ID').max(20, '最多支持20个文档'),
  options: z.object({
    style: z.enum(['concise', 'descriptive', 'formal', 'technical', 'creative']).optional(),
    maxLength: z.number().min(10).max(100).optional(),
    includeDate: z.boolean().optional(),
    dateFormat: z.string().optional(),
    includeTypePrefix: z.boolean().optional(),
    language: z.enum(['zh', 'en', 'auto']).optional(),
    customPrefix: z.string().optional(),
    customSuffix: z.string().optional(),
    excludeWords: z.array(z.string()).optional(),
    suggestionCount: z.number().min(1).max(10).optional()
  }).optional()
});

const summarySchema = z.object({
  documentId: z.string().min(1, '文档ID不能为空'),
  options: z.object({
    length: z.enum(['short', 'medium', 'long']).optional(),
    type: z.enum(['overview', 'key_points', 'abstract']).optional(),
    language: z.enum(['zh', 'en', 'auto']).optional(),
    includeKeywords: z.boolean().optional(),
    maxWords: z.number().min(10).max(500).optional()
  }).optional()
});

const validateNameSchema = z.object({
  fileName: z.string().min(1, '文件名不能为空'),
  content: z.string().optional()
});

/**
 * POST /api/ai/file-naming
 * 生成文件命名建议
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { documentId, options } = namingSuggestionSchema.parse(body);

    // 生成命名建议
    const suggestions = await aiFileNaming.generateNamingSuggestions(
      documentId,
      session.user.id,
      options
    );

    return NextResponse.json({
      success: true,
      data: suggestions
    });

  } catch (error) {
    console.error('生成命名建议失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '生成建议失败' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/ai/file-naming
 * 批量生成命名建议
 */
export async function PUT(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { documentIds, options } = batchNamingSchema.parse(body);

    // 批量生成建议
    const result = await aiFileNaming.batchGenerateNamingSuggestions({
      documentIds,
      options: options || {},
      userId: session.user.id
    });

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('批量生成命名建议失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '批量生成失败' },
      { status: 500 }
    );
  }
}
/**

 * PATCH /api/ai/file-naming/summary
 * 生成文档摘要
 */
export async function PATCH(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { documentId, options } = summarySchema.parse(body);

    // 生成文档摘要
    const summary = await aiFileNaming.generateDocumentSummary(
      documentId,
      session.user.id,
      options
    );

    return NextResponse.json({
      success: true,
      data: summary
    });

  } catch (error) {
    console.error('生成文档摘要失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '生成摘要失败' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/ai/file-naming/smart-rename
 * 获取智能重命名建议
 */
export async function GET() {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取智能重命名建议
    const suggestions = await aiFileNaming.getSmartRenameSuggestions(session.user.id);

    return NextResponse.json({
      success: true,
      data: suggestions
    });

  } catch (error) {
    console.error('获取重命名建议失败:', error);
    
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '获取建议失败' },
      { status: 500 }
    );
  }
}
