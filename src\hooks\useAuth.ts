import { useSession, signIn, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";

/**
 * 认证相关的自定义 Hook
 * 提供用户登录、注册、退出等功能
 * 在客户端组件中使用
 */
export function useAuth() {
  const { data: session, status } = useSession();
  const router = useRouter();

  /**
   * 用户登录函数
   * @param email 邮箱地址
   * @param password 密码
   * @returns 登录结果
   */
  const login = async (email: string, password: string) => {
    const result = await signIn("credentials", {
      email,
      password,
      redirect: false, // 不自动重定向，手动处理
    });

    if (result?.error) {
      throw new Error(result.error);
    }

    // 登录成功后跳转到仪表板
    if (result?.ok) {
      router.push("/dashboard");
    }

    return result;
  };

  /**
   * 用户退出登录函数
   */
  const logout = async () => {
    await signOut({ redirect: false }); // 不自动重定向
    router.push("/"); // 手动跳转到首页
  };

  /**
   * 用户注册函数
   * @param name 用户姓名
   * @param email 邮箱地址
   * @param password 密码
   * @returns 注册结果
   */
  const register = async (name: string, email: string, password: string) => {
    const response = await fetch("/api/auth/register", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ name, email, password }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || "注册失败");
    }

    return data;
  };

  return {
    user: session?.user, // 当前登录用户信息
    isLoading: status === "loading", // 是否正在加载会话
    isAuthenticated: !!session, // 是否已认证
    login, // 登录函数
    logout, // 退出函数
    register, // 注册函数
  };
}