# 技术栈

## 核心框架
- **Next.js 14** 使用 App Router
- **React 18** 配合 TypeScript
- **Node.js 18+** 运行时环境

## 数据库与ORM
- **Prisma** ORM，开发环境使用 SQLite，生产环境使用 PostgreSQL
- 通过 Prisma 管理数据库迁移
- 支持 TypeScript 数据库种子文件

## 样式与UI
- **Tailwind CSS** 用于样式设计
- **Lucide React** 图标库
- 自定义UI组件位于 `src/components/ui/`
- 响应式设计模式

## 编辑器与内容
- **TipTap** 富文本编辑器（基于 ProseMirror）
- 基于 JSON 的内容存储
- 字符和单词计数跟踪
- 斜杠命令快速格式化

## 状态管理
- **Zustand** 客户端状态管理
- 自定义 hooks 进行数据获取
- 使用 Dexie 集成本地存储

## 身份认证
- **NextAuth.js** 配合 Prisma 适配器
- 支持凭据和 OAuth 提供商
- JWT 令牌管理

## AI集成
- 多提供商支持：OpenAI、Ollama、Gemini
- 用户可配置的AI设置
- 令牌使用量跟踪

## 开发工具
- **TypeScript** 严格模式
- **ESLint** 配合 Next.js 和 TypeScript 规则
- **Prettier** 配合 Tailwind 插件
- **tsx** 用于 TypeScript 执行

## 常用命令

### 开发相关
```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # 运行 ESLint
npm run type-check   # TypeScript 类型检查
```

### 数据库相关
```bash
npm run db:generate  # 生成 Prisma 客户端
npm run db:push      # 推送模式更改
npm run db:migrate   # 运行迁移
npm run db:studio    # 打开 Prisma Studio
npm run db:seed      # 数据库种子
npm run db:reset     # 重置数据库
npm run db:deploy    # 部署迁移（生产环境）
```

### 测试相关
```bash
npm run db:test      # 测试数据库连接
```

## 环境变量
- `DATABASE_URL` - 数据库连接字符串
- `NEXTAUTH_SECRET` - NextAuth.js 密钥
- `NEXTAUTH_URL` - 应用程序URL
- `OPENAI_API_KEY` - OpenAI API密钥（可选）
- `GEMINI_API_KEY` - Gemini API密钥（可选）
- `OLLAMA_ENDPOINT` - Ollama 端点（可选）