/**
 * AI 文件命名建议相关的类型定义
 */

/**
 * 文件命名建议
 */
export interface FileNamingSuggestion {
  /** 建议的文件名 */
  suggestedName: string;
  /** 建议原因 */
  reason: string;
  /** 置信度 (0-1) */
  confidence: number;
  /** 建议类型 */
  type: NamingSuggestionType;
  /** 基于的内容摘要 */
  basedOnSummary: string;
  /** 关键词 */
  keywords: string[];
  /** 生成时间 */
  generatedAt: Date;
}

/**
 * 命名建议类型
 */
export enum NamingSuggestionType {
  /** 基于内容主题 */
  CONTENT_BASED = 'content_based',
  /** 基于文档类型 */
  TYPE_BASED = 'type_based',
  /** 基于关键词 */
  KEYWORD_BASED = 'keyword_based',
  /** 基于日期和主题 */
  DATE_TOPIC_BASED = 'date_topic_based',
  /** 基于项目或分类 */
  PROJECT_BASED = 'project_based'
}

/**
 * 批量命名建议请求
 */
export interface BatchNamingRequest {
  /** 文档ID列表 */
  documentIds: string[];
  /** 命名选项 */
  options: NamingOptions;
  /** 用户ID */
  userId: string;
}

/**
 * 批量命名建议结果
 */
export interface BatchNamingResult {
  /** 请求ID */
  requestId: string;
  /** 处理状态 */
  status: 'pending' | 'processing' | 'completed' | 'failed';
  /** 总文档数 */
  totalDocuments: number;
  /** 已处理文档数 */
  processedDocuments: number;
  /** 命名建议 */
  suggestions: DocumentNamingSuggestion[];
  /** 错误信息 */
  errors: string[];
  /** 开始时间 */
  startedAt: Date;
  /** 完成时间 */
  completedAt?: Date;
}

/**
 * 文档命名建议
 */
export interface DocumentNamingSuggestion {
  /** 文档ID */
  documentId: string;
  /** 当前文件名 */
  currentName: string;
  /** 建议列表 */
  suggestions: FileNamingSuggestion[];
  /** 最佳建议 */
  bestSuggestion: FileNamingSuggestion;
  /** 文档摘要 */
  documentSummary: string;
  /** 分析时间 */
  analyzedAt: Date;
}

/**
 * 命名选项
 */
export interface NamingOptions {
  /** 命名风格 */
  style: NamingStyle;
  /** 最大长度 */
  maxLength: number;
  /** 是否包含日期 */
  includeDate: boolean;
  /** 日期格式 */
  dateFormat: string;
  /** 是否包含类型后缀 */
  includeTypePrefix: boolean;
  /** 语言偏好 */
  language: 'zh' | 'en' | 'auto';
  /** 自定义前缀 */
  customPrefix?: string;
  /** 自定义后缀 */
  customSuffix?: string;
  /** 禁用词列表 */
  excludeWords: string[];
  /** 建议数量 */
  suggestionCount: number;
}

/**
 * 命名风格
 */
export enum NamingStyle {
  /** 简洁风格 */
  CONCISE = 'concise',
  /** 描述性风格 */
  DESCRIPTIVE = 'descriptive',
  /** 正式风格 */
  FORMAL = 'formal',
  /** 技术风格 */
  TECHNICAL = 'technical',
  /** 创意风格 */
  CREATIVE = 'creative'
}

/**
 * 文档摘要生成选项
 */
export interface SummaryOptions {
  /** 摘要长度 */
  length: 'short' | 'medium' | 'long';
  /** 摘要类型 */
  type: 'overview' | 'key_points' | 'abstract';
  /** 语言 */
  language: 'zh' | 'en' | 'auto';
  /** 是否包含关键词 */
  includeKeywords: boolean;
  /** 最大字数 */
  maxWords: number;
}

/**
 * 文档摘要结果
 */
export interface DocumentSummary {
  /** 文档ID */
  documentId: string;
  /** 摘要内容 */
  summary: string;
  /** 关键词 */
  keywords: string[];
  /** 主要主题 */
  mainTopics: string[];
  /** 摘要类型 */
  type: string;
  /** 字数统计 */
  wordCount: number;
  /** 生成时间 */
  generatedAt: Date;
  /** 置信度 */
  confidence: number;
}

/**
 * 智能重命名建议
 */
export interface SmartRenameSuggestion {
  /** 建议ID */
  id: string;
  /** 文档ID */
  documentId: string;
  /** 当前名称 */
  currentName: string;
  /** 建议名称 */
  suggestedName: string;
  /** 改进原因 */
  improvementReason: string;
  /** 命名质量评分 (0-100) */
  qualityScore: number;
  /** 建议优先级 */
  priority: 'high' | 'medium' | 'low';
  /** 建议类型 */
  suggestionType: RenameSuggestionType;
  /** 是否已应用 */
  applied: boolean;
  /** 创建时间 */
  createdAt: Date;
}

/**
 * 重命名建议类型
 */
export enum RenameSuggestionType {
  /** 内容不匹配 */
  CONTENT_MISMATCH = 'content_mismatch',
  /** 名称过于通用 */
  TOO_GENERIC = 'too_generic',
  /** 名称过长 */
  TOO_LONG = 'too_long',
  /** 名称不清晰 */
  UNCLEAR = 'unclear',
  /** 缺少上下文 */
  MISSING_CONTEXT = 'missing_context',
  /** 格式不一致 */
  INCONSISTENT_FORMAT = 'inconsistent_format'
}

/**
 * 命名模式分析
 */
export interface NamingPatternAnalysis {
  /** 用户ID */
  userId: string;
  /** 常用命名模式 */
  commonPatterns: NamingPattern[];
  /** 命名偏好 */
  preferences: NamingPreferences;
  /** 分析的文档数量 */
  analyzedDocuments: number;
  /** 分析时间 */
  analyzedAt: Date;
}

/**
 * 命名模式
 */
export interface NamingPattern {
  /** 模式描述 */
  pattern: string;
  /** 使用频率 */
  frequency: number;
  /** 示例 */
  examples: string[];
  /** 适用场景 */
  context: string;
}

/**
 * 命名偏好
 */
export interface NamingPreferences {
  /** 偏好的长度范围 */
  preferredLength: { min: number; max: number };
  /** 偏好的风格 */
  preferredStyle: NamingStyle;
  /** 常用词汇 */
  commonWords: string[];
  /** 日期使用习惯 */
  dateUsage: boolean;
  /** 分隔符偏好 */
  separatorPreference: string;
  /** 大小写偏好 */
  casePreference: 'camelCase' | 'snake_case' | 'kebab-case' | 'PascalCase' | 'mixed';
}

/**
 * 命名建议配置
 */
export interface NamingSuggestionConfig {
  /** 是否启用自动建议 */
  enableAutoSuggestion: boolean;
  /** 是否在保存时建议 */
  suggestOnSave: boolean;
  /** 是否分析用户模式 */
  analyzeUserPatterns: boolean;
  /** 建议触发阈值 */
  suggestionThreshold: number;
  /** 默认命名选项 */
  defaultOptions: NamingOptions;
}

/**
 * 文件命名验证结果
 */
export interface FileNameValidation {
  /** 是否有效 */
  isValid: boolean;
  /** 验证错误 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
  /** 建议改进 */
  suggestions: string[];
  /** 质量评分 */
  qualityScore: number;
}