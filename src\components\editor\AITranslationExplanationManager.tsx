'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Editor } from '@tiptap/react';
import { AITranslationExplanationPanel, AITranslationExplanationLoading } from './AITranslationExplanationPanel';
import { 
  TranslationExplanationService, 
  TranslationResult,
  ExplanationResult,
  CustomInstructionResult,
  CreativeWritingResult,
  SupportedLanguage,
  TranslationQuality,
  ExplanationType,
  ContentFormat,
  CreativeWritingType
} from '@/lib/services/ai/translation-explanation-service';
import { aiServiceManager } from '@/lib/services/ai/ai-service-factory';
import { AIServiceError } from '@/types/ai.types';

/**
 * AI 翻译和解释管理器的属性
 */
interface AITranslationExplanationManagerProps {
  /** TipTap 编辑器实例 */
  editor: Editor;
  /** 是否启用 AI 功能 */
  enabled?: boolean;
}

/**
 * 处理状态
 */
interface ProcessingState {
  /** 是否正在处理 */
  isProcessing: boolean;
  /** 是否显示结果 */
  showResult: boolean;
  /** 处理类型 */
  type?: 'translation' | 'explanation' | 'instruction' | 'creative';
  /** 翻译结果 */
  translationResult?: TranslationResult;
  /** 解释结果 */
  explanationResult?: ExplanationResult;
  /** 自定义指令结果 */
  instructionResult?: CustomInstructionResult;
  /** 创意写作结果 */
  creativeResult?: CreativeWritingResult;
  /** 错误信息 */
  error?: string;
  /** 原始选择范围 */
  originalSelection?: { from: number; to: number };
  /** 最后的请求参数（用于重新生成） */
  lastRequest?: any;
}

/**
 * AI 翻译和解释管理器组件
 * 负责管理 AI 翻译、解释、自定义指令和创意写作的整个流程
 */
export function AITranslationExplanationManager({ 
  editor, 
  enabled = true 
}: AITranslationExplanationManagerProps) {
  const [state, setState] = useState<ProcessingState>({
    isProcessing: false,
    showResult: false
  });
  
  const translationExplanationService = useRef<TranslationExplanationService>();
  const abortController = useRef<AbortController>();

  // 初始化翻译和解释服务
  useEffect(() => {
    try {
      const aiService = aiServiceManager.getDefaultService();
      translationExplanationService.current = new TranslationExplanationService(aiService);
    } catch (error) {
      console.warn('AI 服务未配置，AI 翻译和解释功能将不可用');
    }
  }, []);

  /**
   * 翻译文本
   */
  const translateText = useCallback(async (
    targetLanguage: SupportedLanguage,
    options: {
      sourceLanguage?: SupportedLanguage;
      quality?: TranslationQuality;
      domain?: string;
    } = {}
  ) => {
    if (!enabled || !translationExplanationService.current || state.isProcessing) {
      return;
    }

    try {
      // 获取选中的文本
      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);
      
      if (!selectedText.trim()) {
        setState(prev => ({ 
          ...prev, 
          error: '请先选择要翻译的文本' 
        }));
        return;
      }

      // 获取上下文
      const beforeText = editor.state.doc.textBetween(Math.max(0, from - 200), from);
      const afterText = editor.state.doc.textBetween(to, Math.min(editor.state.doc.content.size, to + 200));
      const context = `${beforeText}${selectedText}${afterText}`;

      const request = {
        text: selectedText,
        targetLanguage,
        context,
        ...options
      };

      // 创建取消控制器
      abortController.current = new AbortController();

      // 更新状态为处理中
      setState({
        isProcessing: true,
        showResult: false,
        type: 'translation',
        error: undefined,
        originalSelection: { from, to },
        lastRequest: { type: 'translation', ...request }
      });

      // 执行翻译
      const result = await translationExplanationService.current.translateText(request);

      // 更新状态为显示结果
      setState(prev => ({
        ...prev,
        isProcessing: false,
        showResult: true,
        translationResult: result,
        error: undefined
      }));

    } catch (error) {
      console.error('AI 翻译失败:', error);
      
      let errorMessage = '翻译失败，请稍后重试';
      if (error instanceof AIServiceError) {
        errorMessage = error.message;
      }

      setState(prev => ({
        ...prev,
        isProcessing: false,
        showResult: false,
        error: errorMessage
      }));
    }
  }, [editor, enabled, state.isProcessing]);

  /**
   * 解释文本
   */
  const explainText = useCallback(async (
    type: ExplanationType,
    options: {
      audienceLevel?: 'beginner' | 'intermediate' | 'advanced';
      domain?: string;
    } = {}
  ) => {
    if (!enabled || !translationExplanationService.current || state.isProcessing) {
      return;
    }

    try {
      // 获取选中的文本
      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);
      
      if (!selectedText.trim()) {
        setState(prev => ({ 
          ...prev, 
          error: '请先选择要解释的文本' 
        }));
        return;
      }

      // 获取上下文
      const fullContent = editor.getText();
      const context = fullContent.substring(0, 1000); // 限制上下文长度

      const request = {
        text: selectedText,
        type,
        context,
        ...options
      };

      // 创建取消控制器
      abortController.current = new AbortController();

      // 更新状态为处理中
      setState({
        isProcessing: true,
        showResult: false,
        type: 'explanation',
        error: undefined,
        originalSelection: { from, to },
        lastRequest: { ...request, type: 'explanation' }
      });

      // 执行解释
      const result = await translationExplanationService.current.explainText(request);

      // 更新状态为显示结果
      setState(prev => ({
        ...prev,
        isProcessing: false,
        showResult: true,
        explanationResult: result,
        error: undefined
      }));

    } catch (error) {
      console.error('AI 解释失败:', error);
      
      let errorMessage = '解释失败，请稍后重试';
      if (error instanceof AIServiceError) {
        errorMessage = error.message;
      }

      setState(prev => ({
        ...prev,
        isProcessing: false,
        showResult: false,
        error: errorMessage
      }));
    }
  }, [editor, enabled, state.isProcessing]);

  /**
   * 执行自定义指令
   */
  const executeCustomInstruction = useCallback(async (
    instruction: string,
    options: {
      outputFormat?: ContentFormat;
      requirements?: string[];
    } = {}
  ) => {
    if (!enabled || !translationExplanationService.current || state.isProcessing) {
      return;
    }

    try {
      // 获取选中的文本（可选）
      const { from, to } = editor.state.selection;
      const selectedText = from !== to ? editor.state.doc.textBetween(from, to) : undefined;

      // 获取上下文
      const fullContent = editor.getText();
      const context = fullContent.substring(0, 1000);

      const request = {
        instruction,
        inputText: selectedText,
        context,
        ...options
      };

      // 创建取消控制器
      abortController.current = new AbortController();

      // 更新状态为处理中
      setState({
        isProcessing: true,
        showResult: false,
        type: 'instruction',
        error: undefined,
        originalSelection: { from, to },
        lastRequest: { type: 'instruction', ...request }
      });

      // 执行指令
      const result = await translationExplanationService.current.executeCustomInstruction(request);

      // 更新状态为显示结果
      setState(prev => ({
        ...prev,
        isProcessing: false,
        showResult: true,
        instructionResult: result,
        error: undefined
      }));

    } catch (error) {
      console.error('AI 指令执行失败:', error);
      
      let errorMessage = '指令执行失败，请稍后重试';
      if (error instanceof AIServiceError) {
        errorMessage = error.message;
      }

      setState(prev => ({
        ...prev,
        isProcessing: false,
        showResult: false,
        error: errorMessage
      }));
    }
  }, [editor, enabled, state.isProcessing]);

  /**
   * 创意写作
   */
  const createContent = useCallback(async (
    prompt: string,
    type: CreativeWritingType,
    options: {
      length?: 'short' | 'medium' | 'long';
      style?: string;
      tone?: string;
      requirements?: string[];
    } = {}
  ) => {
    if (!enabled || !translationExplanationService.current || state.isProcessing) {
      return;
    }

    try {
      const request = {
        prompt,
        type,
        ...options
      };

      // 创建取消控制器
      abortController.current = new AbortController();

      // 更新状态为处理中
      setState({
        isProcessing: true,
        showResult: false,
        type: 'creative',
        error: undefined,
        lastRequest: { ...request, type: 'creative' }
      });

      // 执行创作
      const result = await translationExplanationService.current.createContent(request);

      // 更新状态为显示结果
      setState(prev => ({
        ...prev,
        isProcessing: false,
        showResult: true,
        creativeResult: result,
        error: undefined
      }));

    } catch (error) {
      console.error('AI 创意写作失败:', error);
      
      let errorMessage = '创意写作失败，请稍后重试';
      if (error instanceof AIServiceError) {
        errorMessage = error.message;
      }

      setState(prev => ({
        ...prev,
        isProcessing: false,
        showResult: false,
        error: errorMessage
      }));
    }
  }, [enabled, state.isProcessing]);

  /**
   * 关闭结果面板
   */
  const closeResult = useCallback(() => {
    setState({
      isProcessing: false,
      showResult: false
    });
  }, []);

  /**
   * 重新生成结果
   */
  const regenerateResult = useCallback(async () => {
    if (!state.lastRequest) return;

    const { type, ...request } = state.lastRequest;

    switch (type) {
      case 'translation':
        await translateText(request.targetLanguage, request);
        break;
      case 'explanation':
        await explainText(request.type, request);
        break;
      case 'instruction':
        await executeCustomInstruction(request.instruction, request);
        break;
      case 'creative':
        await createContent(request.prompt, request.type, request);
        break;
    }
  }, [state.lastRequest, translateText, explainText, executeCustomInstruction, createContent]);

  /**
   * 应用结果到编辑器
   */
  const applyResult = useCallback((content: string) => {
    if (state.originalSelection) {
      const { from, to } = state.originalSelection;
      editor
        .chain()
        .focus()
        .setTextSelection({ from, to })
        .insertContent(content)
        .run();
    } else {
      // 如果没有原始选择，在当前位置插入
      editor
        .chain()
        .focus()
        .insertContent(content)
        .run();
    }
  }, [editor, state.originalSelection]);

  /**
   * 取消处理
   */
  const cancelProcessing = useCallback(() => {
    if (abortController.current) {
      abortController.current.abort();
    }
    
    setState({
      isProcessing: false,
      showResult: false
    });
  }, []);

  // 暴露方法给父组件
  useEffect(() => {
    if (editor) {
      // 将方法添加到编辑器实例上，方便其他组件调用
      (editor as any).aiTranslationExplanation = {
        // 翻译方法
        translateToEnglish: () => translateText('en'),
        translateToChinese: () => translateText('zh-CN'),
        translateToJapanese: () => translateText('ja'),
        translateToKorean: () => translateText('ko'),
        translateToFrench: () => translateText('fr'),
        translateToGerman: () => translateText('de'),
        translateToSpanish: () => translateText('es'),
        translate: (targetLang: SupportedLanguage, options?: any) => translateText(targetLang, options),
        
        // 解释方法
        explainConcept: () => explainText('concept'),
        explainTerm: () => explainText('term'),
        explainProcess: () => explainText('process'),
        explainWithExample: () => explainText('example'),
        explainSimply: () => explainText('simple'),
        explain: (type: ExplanationType, options?: any) => explainText(type, options),
        
        // 自定义指令
        executeInstruction: executeCustomInstruction,
        
        // 创意写作
        writeStory: (prompt: string) => createContent(prompt, 'story'),
        writePoem: (prompt: string) => createContent(prompt, 'poem'),
        writeDialogue: (prompt: string) => createContent(prompt, 'dialogue'),
        writeDescription: (prompt: string) => createContent(prompt, 'description'),
        createContent,
        
        // 状态
        isProcessing: state.isProcessing
      };
    }
  }, [editor, translateText, explainText, executeCustomInstruction, createContent, state.isProcessing]);

  if (!enabled || !translationExplanationService.current) {
    return null;
  }

  return (
    <>
      {/* 处理中的加载状态 */}
      <AITranslationExplanationLoading
        visible={state.isProcessing}
        onCancel={cancelProcessing}
        message="正在处理请求..."
        type={state.type}
      />

      {/* 结果显示面板 */}
      <AITranslationExplanationPanel
        translationResult={state.translationResult}
        explanationResult={state.explanationResult}
        instructionResult={state.instructionResult}
        creativeResult={state.creativeResult}
        visible={state.showResult}
        onClose={closeResult}
        onRegenerate={regenerateResult}
        onApply={applyResult}
        isProcessing={state.isProcessing}
      />

      {/* 错误提示 */}
      {state.error && (
        <div className="fixed top-4 right-4 z-50">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-w-md shadow-lg">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className="text-red-600 mt-0.5">⚠️</div>
                <div>
                  <div className="font-medium text-red-800">处理失败</div>
                  <div className="text-sm text-red-700 mt-1">{state.error}</div>
                </div>
              </div>
              <button
                onClick={() => setState(prev => ({ ...prev, error: undefined }))}
                className="text-red-500 hover:text-red-700 ml-4"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

/**
 * 扩展编辑器类型定义
 */
declare module '@tiptap/react' {
  interface Editor {
    aiTranslationExplanation?: {
      // 翻译方法
      translateToEnglish: () => Promise<void>;
      translateToChinese: () => Promise<void>;
      translateToJapanese: () => Promise<void>;
      translateToKorean: () => Promise<void>;
      translateToFrench: () => Promise<void>;
      translateToGerman: () => Promise<void>;
      translateToSpanish: () => Promise<void>;
      translate: (targetLang: SupportedLanguage, options?: any) => Promise<void>;
      
      // 解释方法
      explainConcept: () => Promise<void>;
      explainTerm: () => Promise<void>;
      explainProcess: () => Promise<void>;
      explainWithExample: () => Promise<void>;
      explainSimply: () => Promise<void>;
      explain: (type: ExplanationType, options?: any) => Promise<void>;
      
      // 自定义指令
      executeInstruction: (instruction: string, options?: any) => Promise<void>;
      
      // 创意写作
      writeStory: (prompt: string) => Promise<void>;
      writePoem: (prompt: string) => Promise<void>;
      writeDialogue: (prompt: string) => Promise<void>;
      writeDescription: (prompt: string) => Promise<void>;
      createContent: (prompt: string, type: CreativeWritingType, options?: any) => Promise<void>;
      
      // 状态
      isProcessing: boolean;
    };
  }
}