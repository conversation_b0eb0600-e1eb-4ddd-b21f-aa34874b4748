'use client';

import React, { useState, useEffect } from 'react';
import { DetailedConflict, ConflictSeverity } from '@/lib/services/conflict-detection';
import { DocumentVersion, VersionComparison } from '@/lib/services/version-history';
import { 
  AlertTriangle, 
  FileText, 
  Clock, 
  User,
  Check,
  X,
  GitMerge,
  History,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronRight,
  Info,
  AlertCircle,
  CheckCircle2,
  XCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AdvancedConflictResolverProps {
  conflict: DetailedConflict;
  versionHistory?: DocumentVersion[];
  onResolve: (resolution: 'local' | 'remote' | 'merge', mergedData?: any) => Promise<void>;
  onCancel?: () => void;
  onViewVersion?: (version: DocumentVersion) => void;
  className?: string;
}

/**
 * 高级冲突解决组件
 * 提供详细的冲突分析和多种解决方案
 */
export function AdvancedConflictResolver({ 
  conflict, 
  versionHistory = [],
  onResolve, 
  onCancel,
  onViewVersion,
  className 
}: AdvancedConflictResolverProps) {
  const [selectedResolution, setSelectedResolution] = useState<'local' | 'remote' | 'merge' | null>(
    conflict.suggestedResolution === 'manual' ? null : conflict.suggestedResolution
  );
  const [isResolving, setIsResolving] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [mergedContent, setMergedContent] = useState<any>(null);

  const handleResolve = async () => {
    if (!selectedResolution) return;

    setIsResolving(true);
    try {
      await onResolve(selectedResolution, mergedContent);
    } catch (error) {
      console.error('解决冲突失败:', error);
    } finally {
      setIsResolving(false);
    }
  };

  const getSeverityInfo = (severity: ConflictSeverity) => {
    switch (severity) {
      case ConflictSeverity.LOW:
        return {
          icon: Info,
          color: 'text-blue-500',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          text: '轻微冲突'
        };
      case ConflictSeverity.MEDIUM:
        return {
          icon: AlertCircle,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          text: '中等冲突'
        };
      case ConflictSeverity.HIGH:
        return {
          icon: AlertTriangle,
          color: 'text-orange-500',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          text: '严重冲突'
        };
      case ConflictSeverity.CRITICAL:
        return {
          icon: XCircle,
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          text: '严重冲突'
        };
    }
  };

  const severityInfo = getSeverityInfo(conflict.severity);
  const SeverityIcon = severityInfo.icon;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const localDoc = conflict.localVersion;
  const remoteDoc = conflict.remoteVersion;

  return (
    <div className={cn('bg-white border rounded-lg shadow-lg', className)}>
      {/* 冲突标题和严重程度 */}
      <div className={cn(
        'flex items-center gap-3 p-4 border-b',
        severityInfo.bgColor,
        severityInfo.borderColor
      )}>
        <SeverityIcon className={cn('h-5 w-5', severityInfo.color)} />
        <div className="flex-1">
          <h3 className="font-medium text-gray-900">
            检测到同步冲突 - {severityInfo.text}
          </h3>
          <p className="text-sm text-gray-700">
            {conflict.affectedSections.join('、')} 存在冲突，需要选择解决方案
          </p>
        </div>
        <div className="flex items-center gap-2">
          {conflict.autoMergeable && (
            <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded">
              <CheckCircle2 className="h-3 w-3" />
              可自动合并
            </span>
          )}
          <div className="text-xs text-gray-600">
            {formatDate(conflict.timestamp)}
          </div>
        </div>
      </div>

      {/* 文档信息 */}
      <div className="p-4 border-b">
        <div className="flex items-center gap-2 mb-2">
          <FileText className="h-4 w-4 text-gray-500" />
          <span className="font-medium text-gray-900">
            {localDoc.title || remoteDoc.title}
          </span>
        </div>
        <div className="text-sm text-gray-600">
          文档ID: {conflict.documentId}
        </div>
      </div>

      {/* 冲突详情 */}
      <div className="p-4 border-b">
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900"
        >
          {showDetails ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
          冲突详情 ({conflict.conflictDetails.length} 项)
        </button>

        {showDetails && (
          <div className="mt-3 space-y-2">
            {conflict.conflictDetails.map((detail, index) => (
              <div key={index} className="border rounded-lg p-3 bg-gray-50">
                <div className="flex items-center gap-2 mb-2">
                  <span className={cn(
                    'inline-flex items-center px-2 py-1 text-xs rounded',
                    detail.type === 'addition' ? 'bg-green-100 text-green-700' :
                    detail.type === 'deletion' ? 'bg-red-100 text-red-700' :
                    'bg-yellow-100 text-yellow-700'
                  )}>
                    {detail.type === 'addition' ? '新增' :
                     detail.type === 'deletion' ? '删除' : '修改'}
                  </span>
                  <span className="text-sm font-medium text-gray-700">
                    {detail.section}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  {detail.description}
                </p>
                <div className="grid grid-cols-2 gap-3 text-xs">
                  <div>
                    <div className="font-medium text-gray-700 mb-1">本地版本:</div>
                    <div className="bg-white p-2 rounded border">
                      {typeof detail.localValue === 'string' 
                        ? detail.localValue 
                        : JSON.stringify(detail.localValue)}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-gray-700 mb-1">远程版本:</div>
                    <div className="bg-white p-2 rounded border">
                      {typeof detail.remoteValue === 'string' 
                        ? detail.remoteValue 
                        : JSON.stringify(detail.remoteValue)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 版本历史 */}
      {versionHistory.length > 0 && (
        <div className="p-4 border-b">
          <button
            onClick={() => setShowVersionHistory(!showVersionHistory)}
            className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900"
          >
            {showVersionHistory ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
            <History className="h-4 w-4" />
            版本历史 ({versionHistory.length} 个版本)
          </button>

          {showVersionHistory && (
            <div className="mt-3 space-y-2 max-h-40 overflow-y-auto">
              {versionHistory.slice(0, 10).map((version) => (
                <div key={version.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">
                      版本 {version.version}
                    </div>
                    <div className="text-xs text-gray-600">
                      {formatDate(version.createdAt)} - {version.changeType}
                    </div>
                    {version.changeDescription && (
                      <div className="text-xs text-gray-500">
                        {version.changeDescription}
                      </div>
                    )}
                  </div>
                  {onViewVersion && (
                    <button
                      onClick={() => onViewVersion(version)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                      title="查看版本"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* 解决方案选择 */}
      <div className="p-4 space-y-4">
        <div className="text-sm font-medium text-gray-700 mb-3">
          选择解决方案：
        </div>

        {/* 建议的解决方案 */}
        {conflict.suggestedResolution !== 'manual' && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 text-blue-700">
              <Info className="h-4 w-4" />
              <span className="text-sm font-medium">建议解决方案</span>
            </div>
            <p className="text-sm text-blue-600 mt-1">
              基于冲突分析，建议使用
              <span className="font-medium">
                {conflict.suggestedResolution === 'local' ? '本地版本' :
                 conflict.suggestedResolution === 'remote' ? '远程版本' : '合并版本'}
              </span>
            </p>
          </div>
        )}

        {/* 本地版本 */}
        <div
          className={cn(
            'border rounded-lg p-3 cursor-pointer transition-colors',
            selectedResolution === 'local'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
          )}
          onClick={() => setSelectedResolution('local')}
        >
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <div className={cn(
                'w-4 h-4 rounded-full border-2',
                selectedResolution === 'local'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300'
              )}>
                {selectedResolution === 'local' && (
                  <Check className="h-3 w-3 text-white" />
                )}
              </div>
              <span className="font-medium text-gray-900">使用本地版本</span>
              {conflict.suggestedResolution === 'local' && (
                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                  推荐
                </span>
              )}
            </div>
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Clock className="h-3 w-3" />
              {formatDate(new Date(localDoc.updatedAt))}
            </div>
          </div>
          <div className="text-sm text-gray-600 ml-6">
            <div>标题: {localDoc.title}</div>
            <div>字数: {localDoc.metadata?.wordCount || 0}</div>
            <div>保留本地的所有更改</div>
          </div>
        </div>

        {/* 远程版本 */}
        <div
          className={cn(
            'border rounded-lg p-3 cursor-pointer transition-colors',
            selectedResolution === 'remote'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
          )}
          onClick={() => setSelectedResolution('remote')}
        >
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <div className={cn(
                'w-4 h-4 rounded-full border-2',
                selectedResolution === 'remote'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300'
              )}>
                {selectedResolution === 'remote' && (
                  <Check className="h-3 w-3 text-white" />
                )}
              </div>
              <span className="font-medium text-gray-900">使用远程版本</span>
              {conflict.suggestedResolution === 'remote' && (
                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                  推荐
                </span>
              )}
            </div>
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Clock className="h-3 w-3" />
              {formatDate(new Date(remoteDoc.updatedAt))}
            </div>
          </div>
          <div className="text-sm text-gray-600 ml-6">
            <div>标题: {remoteDoc.title}</div>
            <div>字数: {remoteDoc.metadata?.wordCount || 0}</div>
            <div>使用服务器上的最新版本</div>
          </div>
        </div>

        {/* 自动合并选项 */}
        {conflict.autoMergeable && (
          <div
            className={cn(
              'border rounded-lg p-3 cursor-pointer transition-colors',
              selectedResolution === 'merge'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            )}
            onClick={() => setSelectedResolution('merge')}
          >
            <div className="flex items-center gap-2">
              <div className={cn(
                'w-4 h-4 rounded-full border-2',
                selectedResolution === 'merge'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300'
              )}>
                {selectedResolution === 'merge' && (
                  <Check className="h-3 w-3 text-white" />
                )}
              </div>
              <GitMerge className="h-4 w-4 text-gray-500" />
              <span className="font-medium text-gray-900">自动合并</span>
              {conflict.suggestedResolution === 'merge' && (
                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                  推荐
                </span>
              )}
              <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                智能合并
              </span>
            </div>
            <div className="text-sm text-gray-600 ml-6 mt-1">
              自动合并两个版本的兼容更改
            </div>
          </div>
        )}

        {/* 手动合并选项 */}
        {!conflict.autoMergeable && (
          <div className="border rounded-lg p-3 bg-gray-50">
            <div className="flex items-center gap-2 text-gray-600">
              <GitMerge className="h-4 w-4" />
              <span className="font-medium">手动合并</span>
              <span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">
                需要手动处理
              </span>
            </div>
            <div className="text-sm text-gray-600 ml-6 mt-1">
              冲突过于复杂，需要在编辑器中手动合并
            </div>
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-end gap-3 p-4 bg-gray-50 border-t">
        {onCancel && (
          <button
            onClick={onCancel}
            className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800"
            disabled={isResolving}
          >
            取消
          </button>
        )}
        <button
          onClick={handleResolve}
          disabled={!selectedResolution || isResolving}
          className={cn(
            'px-4 py-2 text-sm font-medium rounded-md',
            selectedResolution && !isResolving
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          )}
        >
          {isResolving ? '解决中...' : '解决冲突'}
        </button>
      </div>
    </div>
  );
}

/**
 * 冲突解决状态指示器
 */
interface ConflictStatusIndicatorProps {
  conflict: DetailedConflict;
  className?: string;
}

export function ConflictStatusIndicator({ conflict, className }: ConflictStatusIndicatorProps) {
  const severityInfo = {
    [ConflictSeverity.LOW]: { color: 'text-blue-500', bg: 'bg-blue-100' },
    [ConflictSeverity.MEDIUM]: { color: 'text-yellow-500', bg: 'bg-yellow-100' },
    [ConflictSeverity.HIGH]: { color: 'text-orange-500', bg: 'bg-orange-100' },
    [ConflictSeverity.CRITICAL]: { color: 'text-red-500', bg: 'bg-red-100' },
  }[conflict.severity];

  return (
    <div className={cn('inline-flex items-center gap-2', className)}>
      <div className={cn('w-2 h-2 rounded-full', severityInfo.bg)}>
        <div className={cn('w-full h-full rounded-full', severityInfo.color.replace('text-', 'bg-'))} />
      </div>
      <span className={cn('text-sm font-medium', severityInfo.color)}>
        {conflict.severity === ConflictSeverity.LOW ? '轻微' :
         conflict.severity === ConflictSeverity.MEDIUM ? '中等' :
         conflict.severity === ConflictSeverity.HIGH ? '严重' : '严重'}冲突
      </span>
      {conflict.autoMergeable && (
        <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
          可自动合并
        </span>
      )}
    </div>
  );
}