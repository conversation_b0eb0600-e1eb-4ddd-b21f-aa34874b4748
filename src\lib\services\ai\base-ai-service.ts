/**
 * AI 服务抽象基类
 * 定义所有 AI 服务的通用接口和行为
 */

import {
  AIServiceConfig,
  AIRequest,
  AIResponse,
  RewriteOptions,
  TextAnalysis,
  AIServiceError,
  AIErrorType,
  RetryConfig,
  AIProvider
} from '@/types/ai.types';
import { aiCacheManager } from './ai-cache-manager';
import { aiBatchProcessor } from './ai-batch-processor';

/**
 * AI 服务接口
 * 定义所有 AI 服务必须实现的方法
 */
export interface IAIService {
  /** 服务提供商标识 */
  readonly provider: AIProvider;

  /** 服务配置 */
  readonly config: AIServiceConfig;

  /**
   * 生成文本内容
   * @param request AI 请求参数
   * @returns 生成的文本响应
   */
  generateText(request: AIRequest): Promise<AIResponse>;

  /**
   * 改写文本内容
   * @param text 原始文本
   * @param options 改写选项
   * @returns 改写后的文本数组
   */
  rewriteText(text: string, options?: RewriteOptions): Promise<string[]>;

  /**
   * 总结文本内容
   * @param text 原始文本
   * @param maxLength 摘要最大长度
   * @returns 文本摘要
   */
  summarizeText(text: string, maxLength?: number): Promise<string>;

  /**
   * 分析文本内容
   * @param text 原始文本
   * @returns 文本分析结果
   */
  analyzeText(text: string): Promise<TextAnalysis>;

  /**
   * 翻译文本
   * @param text 原始文本
   * @param targetLanguage 目标语言
   * @param sourceLanguage 源语言（可选）
   * @returns 翻译后的文本
   */
  translateText(text: string, targetLanguage: string, sourceLanguage?: string): Promise<string>;

  /**
   * 解释文本或概念
   * @param text 需要解释的文本
   * @param context 上下文信息
   * @returns 解释内容
   */
  explainText(text: string, context?: string): Promise<string>;

  /**
   * 测试服务连接
   * @returns 连接是否成功
   */
  testConnection(): Promise<boolean>;

  /**
   * 获取可用模型列表
   * @returns 模型列表
   */
  getAvailableModels(): Promise<string[]>;
}

/**
 * AI 服务抽象基类
 * 提供通用的错误处理和重试机制
 */
export abstract class BaseAIService implements IAIService {
  abstract readonly provider: AIProvider;

  protected retryConfig: RetryConfig = {
    maxRetries: 3,
    initialDelay: 1000,
    backoffMultiplier: 2,
    maxDelay: 10000,
    retryableErrors: [
      AIErrorType.NETWORK_ERROR,
      AIErrorType.TIMEOUT,
      AIErrorType.SERVICE_UNAVAILABLE
    ]
  };

  constructor(public readonly config: AIServiceConfig) {}

  /**
   * 抽象方法：生成文本内容（内部实现）
   */
  protected abstract _generateText(request: AIRequest): Promise<AIResponse>;

  /**
   * 生成文本内容（带缓存和批处理）
   */
  async generateText(request: AIRequest): Promise<AIResponse> {
    // 尝试从缓存获取
    const cachedResponse = await aiCacheManager.get(request, this.provider);
    if (cachedResponse) {
      return cachedResponse;
    }

    // 使用批处理
    return aiBatchProcessor.addBatch(
      [request],
      this.provider,
      async (requests) => {
        const responses = await Promise.all(
          requests.map(req => this._generateText(req))
        );
        return responses;
      },
      'medium'
    ).then(responses => responses[0]);
  }

  /**
   * 改写文本内容的默认实现
   */
  async rewriteText(text: string, options: RewriteOptions = {}): Promise<string[]> {
    const { style = 'neutral', tone = 'professional', length = 'same' } = options;

    const prompt = this.buildRewritePrompt(text, style, tone, length);
    const response = await this.generateText({ prompt });

    // 尝试解析多个改写版本
    const versions = this.parseRewriteVersions(response.content);
    return versions.length > 0 ? versions : [response.content];
  }

  /**
   * 总结文本内容的默认实现
   */
  async summarizeText(text: string, maxLength = 200): Promise<string> {
    const prompt = `请总结以下文本，摘要长度不超过${maxLength}字：\n\n${text}`;
    const response = await this.generateText({ prompt });
    return response.content;
  }

  /**
   * 分析文本内容的默认实现
   */
  async analyzeText(text: string): Promise<TextAnalysis> {
    const prompt = `请分析以下文本的情感、关键词、主题和可读性，并提供改进建议：\n\n${text}`;
    const response = await this.generateText({ prompt });

    // 解析分析结果（这里简化处理，实际应该更复杂）
    return {
      sentiment: 'neutral',
      keywords: [],
      topics: [],
      readabilityScore: 75,
      suggestions: [response.content],
      wordCount: text.split(/\s+/).length,
      characterCount: text.length
    };
  }

  /**
   * 翻译文本的默认实现
   */
  async translateText(text: string, targetLanguage: string, sourceLanguage?: string): Promise<string> {
    const sourceLang = sourceLanguage ? `从${sourceLanguage}` : '';
    const prompt = `请将以下文本${sourceLang}翻译为${targetLanguage}：\n\n${text}`;
    const response = await this.generateText({ prompt });
    return response.content;
  }

  /**
   * 解释文本的默认实现
   */
  async explainText(text: string, context?: string): Promise<string> {
    const contextInfo = context ? `\n\n上下文：${context}` : '';
    const prompt = `请解释以下内容：\n\n${text}${contextInfo}`;
    const response = await this.generateText({ prompt });
    return response.content;
  }

  /**
   * 测试连接的默认实现
   */
  async testConnection(): Promise<boolean> {
    try {
      // 使用简单的测试请求，避免复杂的缓存和批处理逻辑
      const response = await this._generateText({
        prompt: '测试',
        maxTokens: 5
      });
      return response && response.content && response.content.length > 0;
    } catch (error) {
      console.error('连接测试失败:', error);
      return false;
    }
  }

  /**
   * 获取可用模型列表的默认实现
   */
  async getAvailableModels(): Promise<string[]> {
    return [this.config.model];
  }

  /**
   * 带重试机制的请求执行
   */
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: string = 'AI request'
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        // 检查是否为 AI 服务错误
        if (error instanceof AIServiceError) {
          // 检查是否可重试
          if (!this.retryConfig.retryableErrors.includes(error.type)) {
            throw error;
          }

          // 如果是最后一次尝试，直接抛出错误
          if (attempt === this.retryConfig.maxRetries) {
            throw error;
          }

          // 计算延迟时间
          const delay = Math.min(
            this.retryConfig.initialDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt),
            this.retryConfig.maxDelay
          );

          console.warn(`${context} 失败，${delay}ms 后重试 (${attempt + 1}/${this.retryConfig.maxRetries}):`, error.message);
          await this.sleep(delay);
        } else {
          // 非 AI 服务错误，直接抛出
          throw error;
        }
      }
    }

    throw lastError!;
  }

  /**
   * 构建改写提示
   */
  protected buildRewritePrompt(text: string, style: string, tone: string, length: string): string {
    let prompt = `请改写以下文本，要求：\n`;
    prompt += `- 风格：${style}\n`;
    prompt += `- 语调：${tone}\n`;
    prompt += `- 长度：${length === 'shorter' ? '更简洁' : length === 'longer' ? '更详细' : '保持相似'}\n`;
    prompt += `- 提供2-3个不同的改写版本，用"---"分隔\n\n`;
    prompt += `原文：${text}`;
    return prompt;
  }

  /**
   * 解析改写版本
   */
  protected parseRewriteVersions(content: string): string[] {
    const versions = content.split('---').map(v => v.trim()).filter(v => v.length > 0);
    return versions.length > 1 ? versions : [];
  }

  /**
   * 休眠函数
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 处理 HTTP 错误
   */
  protected handleHttpError(error: any, context: string): AIServiceError {
    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
      return new AIServiceError(
        AIErrorType.TIMEOUT,
        `请求超时: ${context}`,
        this.provider,
        error
      );
    }

    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return new AIServiceError(
        AIErrorType.NETWORK_ERROR,
        `网络连接失败: ${context}`,
        this.provider,
        error
      );
    }

    const status = error.response?.status || error.status;

    switch (status) {
      case 401:
        return new AIServiceError(
          AIErrorType.INVALID_API_KEY,
          'API 密钥无效或已过期',
          this.provider,
          error
        );
      case 429:
        return new AIServiceError(
          AIErrorType.QUOTA_EXCEEDED,
          '请求频率超限或配额不足',
          this.provider,
          error
        );
      case 503:
        return new AIServiceError(
          AIErrorType.SERVICE_UNAVAILABLE,
          '服务暂时不可用',
          this.provider,
          error
        );
      default:
        return new AIServiceError(
          AIErrorType.UNKNOWN_ERROR,
          `未知错误: ${error.message || '请求失败'}`,
          this.provider,
          error
        );
    }
  }
}