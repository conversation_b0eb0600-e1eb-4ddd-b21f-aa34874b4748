'use client';

import React, { useState } from 'react';
import { Editor } from '@/components/editor/Editor';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

/**
 * AI 翻译和解释功能演示页面
 */
export default function AITranslationExplanationDemoPage() {
  const [content, setContent] = useState(`
    <h1>AI 翻译和解释功能演示</h1>
    
    <p>欢迎使用 AI 翻译和解释功能！这些功能可以帮助您：</p>
    
    <ul>
      <li>将文本翻译为多种语言</li>
      <li>解释复杂的概念和术语</li>
      <li>执行自定义的 AI 指令</li>
      <li>进行创意写作</li>
    </ul>
    
    <h2>翻译功能</h2>
    
    <p>支持多种语言之间的翻译，包括：</p>
    
    <blockquote>
      <p>Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.</p>
    </blockquote>
    
    <blockquote>
      <p>人工智能是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。</p>
    </blockquote>
    
    <h2>解释功能</h2>
    
    <p>以下是一些可以用来测试解释功能的专业术语：</p>
    
    <ul>
      <li><strong>深度学习</strong>：机器学习的一个子集</li>
      <li><strong>神经网络</strong>：模拟人脑神经元的计算模型</li>
      <li><strong>自然语言处理</strong>：让计算机理解和生成人类语言</li>
      <li><strong>计算机视觉</strong>：让计算机理解和分析视觉信息</li>
    </ul>
    
    <h2>自定义指令</h2>
    
    <p>您可以使用自定义指令来执行特定的任务，例如：</p>
    
    <ul>
      <li>将文本转换为列表格式</li>
      <li>生成表格</li>
      <li>创建代码示例</li>
      <li>制作问答对</li>
    </ul>
    
    <h2>创意写作</h2>
    
    <p>AI 可以帮助您进行各种创意写作，包括：</p>
    
    <ul>
      <li>故事创作</li>
      <li>诗歌写作</li>
      <li>对话生成</li>
      <li>场景描述</li>
    </ul>
    
    <p>选择任意文本，然后使用斜杠命令或快捷键来体验这些功能！</p>
  `);

  const [selectedDemo, setSelectedDemo] = useState('translation');

  const demoTexts = {
    translation: {
      english: 'Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.',
      chinese: '人工智能是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。',
      japanese: 'コンピュータサイエンスは、計算機とアルゴリズムの設計と分析を扱う学問分野です。',
      korean: '컴퓨터 과학은 컴퓨터와 알고리즘의 설계 및 분석을 다루는 학문 분야입니다.'
    },
    explanation: {
      technical: '深度学习',
      concept: '神经网络',
      process: '自然语言处理',
      complex: '量子计算'
    },
    instruction: {
      format: '将以下内容转换为表格格式',
      generate: '生成一个关于人工智能的简单介绍',
      transform: '将这段文字改写为问答形式'
    },
    creative: {
      story: '一个关于未来世界的科幻故事',
      poem: '描述春天的诗歌',
      dialogue: '两个AI之间的对话',
      description: '描述一个神秘的森林'
    }
  };

  const insertDemoText = (text: string) => {
    const newContent = content + `<p>${text}</p>`;
    setContent(newContent);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AI 翻译和解释功能演示
          </h1>
          <p className="text-gray-600">
            体验强大的 AI 翻译、解释、自定义指令和创意写作功能
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 侧边栏 - 功能说明和示例文本 */}
          <div className="lg:col-span-1 space-y-4">
            <Card className="p-4">
              <h3 className="font-semibold mb-3">功能特点</h3>
              <ul className="text-sm space-y-2 text-gray-600">
                <li>🌐 多语言翻译</li>
                <li>💡 概念解释</li>
                <li>🔧 自定义指令</li>
                <li>✨ 创意写作</li>
                <li>📝 格式转换</li>
                <li>🎯 智能处理</li>
              </ul>
            </Card>

            <Card className="p-4">
              <h3 className="font-semibold mb-3">使用方法</h3>
              <ol className="text-sm space-y-2 text-gray-600">
                <li>1. 选择要处理的文本</li>
                <li>2. 使用斜杠命令</li>
                <li>3. 查看处理结果</li>
                <li>4. 应用或复制结果</li>
              </ol>
            </Card>

            <Card className="p-4">
              <h3 className="font-semibold mb-3">示例文本</h3>
              <Tabs value={selectedDemo} onValueChange={setSelectedDemo}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="translation">翻译</TabsTrigger>
                  <TabsTrigger value="explanation">解释</TabsTrigger>
                </TabsList>
                <TabsContent value="translation" className="space-y-2">
                  <div className="text-xs text-gray-500 mb-2">点击插入示例文本：</div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText(demoTexts.translation.english)}
                  >
                    <div className="text-xs">
                      <div className="font-medium">英文文本</div>
                      <div className="text-gray-500 truncate">Machine learning...</div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText(demoTexts.translation.chinese)}
                  >
                    <div className="text-xs">
                      <div className="font-medium">中文文本</div>
                      <div className="text-gray-500 truncate">人工智能是...</div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText(demoTexts.translation.japanese)}
                  >
                    <div className="text-xs">
                      <div className="font-medium">日文文本</div>
                      <div className="text-gray-500 truncate">コンピュータ...</div>
                    </div>
                  </Button>
                </TabsContent>
                <TabsContent value="explanation" className="space-y-2">
                  <div className="text-xs text-gray-500 mb-2">点击插入专业术语：</div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText(demoTexts.explanation.technical)}
                  >
                    <div className="text-xs">
                      <div className="font-medium">技术术语</div>
                      <div className="text-gray-500">深度学习</div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText(demoTexts.explanation.concept)}
                  >
                    <div className="text-xs">
                      <div className="font-medium">概念术语</div>
                      <div className="text-gray-500">神经网络</div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText(demoTexts.explanation.process)}
                  >
                    <div className="text-xs">
                      <div className="font-medium">流程概念</div>
                      <div className="text-gray-500">自然语言处理</div>
                    </div>
                  </Button>
                </TabsContent>
              </Tabs>
            </Card>

            <Card className="p-4">
              <h3 className="font-semibold mb-3">斜杠命令</h3>
              <div className="text-sm space-y-1 text-gray-600">
                <div><code className="px-1 py-0.5 bg-gray-100 rounded text-xs">/ai-translate</code> 翻译</div>
                <div><code className="px-1 py-0.5 bg-gray-100 rounded text-xs">/ai-explain</code> 解释</div>
                <div><code className="px-1 py-0.5 bg-gray-100 rounded text-xs">/ai-translate-chinese</code> 中文</div>
                <div><code className="px-1 py-0.5 bg-gray-100 rounded text-xs">/ai-translate-english</code> 英文</div>
              </div>
            </Card>
          </div>

          {/* 主编辑区域 */}
          <div className="lg:col-span-3">
            <Card className="p-6">
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-xl font-semibold">编辑器</h2>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <span>选择文本后使用斜杠命令</span>
                </div>
              </div>
              
              <div className="border rounded-lg overflow-hidden">
                <Editor
                  content={content}
                  onChange={setContent}
                  placeholder="输入内容或插入示例文本..."
                  enableAI={true}
                  className="min-h-[600px]"
                />
              </div>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">💡 使用提示</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 选择文本后使用 <code>/ai-translate</code> 进行翻译</li>
                  <li>• 选择专业术语使用 <code>/ai-explain</code> 获取解释</li>
                  <li>• 使用 <code>/ai-translate-chinese</code> 翻译为中文</li>
                  <li>• 使用 <code>/ai-explain-simple</code> 获取简单解释</li>
                  <li>• 翻译和解释结果支持应用到编辑器或复制</li>
                </ul>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}