import { LoginForm } from "@/components/auth/LoginForm";
import { AuthRedirect } from "@/components/auth/AuthRedirect";
import Link from "next/link";

export default function SignInPage() {
  return (
    <AuthRedirect redirectWhen="authenticated" redirectTo="/dashboard">
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-8 sm:py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-6 sm:space-y-8">
          <div className="text-center">
            <h1 className="text-2xl sm:text-3xl font-extrabold text-gray-900">
              AI 文档编辑器
            </h1>
            <p className="mt-2 text-sm text-gray-600">
              登录您的账户以开始使用
            </p>
          </div>
          
          <LoginForm />
          
          <div className="text-center">
            <p className="text-sm text-gray-600">
              还没有账户？{" "}
              <Link
                href="/auth/signup"
                className="font-medium text-blue-600 hover:text-blue-500 touch-manipulation"
              >
                立即注册
              </Link>
            </p>
          </div>
          
          {/* 返回首页链接 */}
          <div className="text-center pt-4 border-t border-gray-200">
            <Link
              href="/"
              className="text-sm text-gray-500 hover:text-gray-700 touch-manipulation"
            >
              ← 返回首页
            </Link>
          </div>
        </div>
      </div>
    </AuthRedirect>
  );
}