/**
 * 测试 Gemini 代理连接
 */

require('dotenv').config();

async function testGeminiProxy() {
  console.log('🧪 测试 Gemini 代理连接...');

  try {
    const { GeminiService } = require('../src/lib/services/ai/gemini-service.ts');

    const config = {
      provider: 'gemini',
      apiKey: 'AIzaSyBABZ7zimsYl4zdKxcKXnGu-h8ebR9GKeU', // 需要真实的 API 密钥
      endpoint: 'http://127.0.0.1:57800', // 代理地址
      model: 'gemini-2.5-flash-lite',
      maxTokens: 10,
      temperature: 0.7,
      timeout: 60000 // 60秒超时
    };

    console.log('配置:', {
      ...config,
      apiKey: config.apiKey.substring(0, 10) + '...' // 隐藏 API 密钥
    });

    const service = new GeminiService(config);
    console.log('Gemini 服务创建成功');

    const connected = await service.testConnection();

    if (connected) {
      console.log('✅ Gemini 代理连接成功!');

      // 尝试生成文本
      try {
        const response = await service._generateText({
          prompt: '你好，请简单回复',
          maxTokens: 10
        });
        console.log('📝 生成文本成功:', response.content);
        console.log('🔢 使用令牌数:', response.tokensUsed);
        console.log('⏱️ 响应时间:', response.responseTime + 'ms');
      } catch (error) {
        console.log('📝 生成文本失败:', error.message);
      }
    } else {
      console.log('❌ Gemini 代理连接失败');
    }
  } catch (error) {
    console.log('❌ 测试异常:', error.message);
    if (error.message.includes('API key')) {
      console.log('💡 请在测试文件中设置有效的 Gemini API 密钥');
    }
  }
}

console.log('📋 Gemini 代理配置说明:');
console.log('1. Gemini 现在支持通过环境变量或页面配置设置代理');
console.log('2. 代理配置优先级：页面配置 > 环境变量 > 直连');
console.log('3. 在 AI 配置页面中，Gemini 现在也会显示"HTTP 代理地址"字段');
console.log('4. 支持的代理格式：http://127.0.0.1:7890');
console.log('');

testGeminiProxy();


// NAME
// huihui_ai/deepseek-r1-abliterated:32b-qwen-distill-q6_K
// gemma3:27b
// qwen2.5-coder:14b-instruct-q6_K
// huihui_ai/deepseek-r1-abliterated:32b
// gemma3:27b-it-q8_0
// mistral:latest
// mistral-small:22b-instruct-2409-q6_K
// qwen2.5:32b-instruct-q6_K
// qwen3:32b-q8_0
// mistral-nemo:12b-instruct-2407-q6_K
// qwen3:14b-fp16
// mychen76/openhands_32b-cline-roocode:Q6
// deepseek-r1:32b
// llama3.2-vision:11b-instruct-q8_0

