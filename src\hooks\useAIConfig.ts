/**
 * AI 配置管理 Hook
 * 提供 AI 配置的 CRUD 操作和状态管理
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import type { AIServiceConfig } from '@/types/ai.types';

/**
 * AI 配置数据接口
 */
export interface AIConfigData {
  id: string;
  provider: string;
  model: string;
  endpoint?: string;
  maxTokens: number;
  temperature: number;
  isDefault: boolean;
  hasApiKey: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 完整的 AI 配置接口（包含敏感信息）
 */
export interface FullAIConfigData extends Omit<AIConfigData, 'hasApiKey'> {
  apiKey?: string;
}

/**
 * 连接测试结果接口
 */
export interface ConnectionTestResult {
  success: boolean;
  error?: string;
  responseTime?: number;
  provider: string;
  model: string;
}

/**
 * AI 配置管理 Hook
 */
export function useAIConfig() {
  const [configs, setConfigs] = useState<AIConfigData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 使用 ref 来保存 fetchConfigs 函数的引用，避免循环依赖
  const fetchConfigsRef = useRef<() => Promise<void>>();

  /**
   * 获取所有 AI 配置
   */
  const fetchConfigs = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/ai-config');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '获取配置失败');
      }

      setConfigs(data.configs);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取配置失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 更新 ref 引用
  fetchConfigsRef.current = fetchConfigs;

  /**
   * 获取单个配置的完整信息
   */
  const getConfig = useCallback(
    async (id: string): Promise<FullAIConfigData | null> => {
      try {
        const response = await fetch(`/api/ai-config/${id}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || '获取配置失败');
        }

        return data.config;
      } catch (err) {
        console.error('获取配置失败:', err);
        return null;
      }
    },
    []
  );

  /**
   * 创建新配置
   */
  const createConfig = useCallback(
    async (
      configData: Omit<FullAIConfigData, 'id' | 'createdAt' | 'updatedAt'>
    ): Promise<boolean> => {
      try {
        const response = await fetch('/api/ai-config', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(configData),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || '创建配置失败');
        }

        // 刷新配置列表
        fetchConfigsRef.current?.();
        return true;
      } catch (err) {
        setError(err instanceof Error ? err.message : '创建配置失败');
        return false;
      }
    },
    []
  ); // 移除 fetchConfigs 依赖

  /**
   * 更新配置
   */
  const updateConfig = useCallback(
    async (
      id: string,
      configData: Partial<FullAIConfigData>
    ): Promise<boolean> => {
      try {
        const response = await fetch(`/api/ai-config/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(configData),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || '更新配置失败');
        }

        // 刷新配置列表
        fetchConfigsRef.current?.();
        return true;
      } catch (err) {
        setError(err instanceof Error ? err.message : '更新配置失败');
        return false;
      }
    },
    []
  ); // 移除 fetchConfigs 依赖

  /**
   * 删除配置
   */
  const deleteConfig = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/ai-config/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '删除配置失败');
      }

      // 刷新配置列表
      fetchConfigsRef.current?.();
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除配置失败');
      return false;
    }
  }, []); // 移除 fetchConfigs 依赖

  /**
   * 设置默认配置
   */
  const setDefaultConfig = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/ai-config/${id}/default`, {
        method: 'POST',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '设置默认配置失败');
      }

      // 刷新配置列表
      fetchConfigsRef.current?.();
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : '设置默认配置失败');
      return false;
    }
  }, []); // 移除 fetchConfigs 依赖

  /**
   * 测试配置连接
   */
  const testConnection = useCallback(
    async (configData: AIServiceConfig): Promise<ConnectionTestResult> => {
      try {
        const response = await fetch('/api/ai-config/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(configData),
        });

        const data = await response.json();

        return {
          success: data.success,
          error: data.error,
          responseTime: data.responseTime,
          provider: data.provider,
          model: data.model,
        };
      } catch (err) {
        return {
          success: false,
          error: err instanceof Error ? err.message : '连接测试失败',
          provider: configData.provider,
          model: configData.model,
        };
      }
    },
    []
  );

  /**
   * 获取默认配置
   */
  const getDefaultConfig = useCallback((): AIConfigData | null => {
    return configs.find((config) => config.isDefault) || null;
  }, [configs]);

  // 初始加载
  useEffect(() => {
    fetchConfigs();
  }, []); // 只在组件挂载时执行一次

  return {
    configs,
    loading,
    error,
    fetchConfigs,
    getConfig,
    createConfig,
    updateConfig,
    deleteConfig,
    setDefaultConfig,
    testConnection,
    getDefaultConfig,
    clearError: () => setError(null),
  };
}
