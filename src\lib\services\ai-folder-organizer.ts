/**
 * AI 智能文件夹组织服务
 * 提供基于文档内容的智能文件夹结构建议
 */

import { prisma } from '@/lib/db/prisma';
import { aiServiceManager } from './ai/ai-service-factory';
import { aiDocumentClassifier } from './ai-document-classifier';
import {
  FolderStructureSuggestion,
  SmartOrganizationSuggestion,
  OrganizationAction,
  DocumentClassification
} from '@/types/ai-classification.types';

/**
 * AI 文件夹组织服务类
 */
export class AIFolderOrganizerService {
  
  /**
   * 分析用户的文档结构并提供文件夹组织建议
   */
  async analyzeFolderStructure(userId: string): Promise<FolderStructureSuggestion[]> {
    // 获取用户的所有文档和文件夹
    const [documents, folders] = await Promise.all([
      prisma.document.findMany({
        where: { userId },
        select: {
          id: true,
          title: true,
          content: true,
          folderId: true,
          createdAt: true,
          updatedAt: true
        }
      }),
      prisma.folder.findMany({
        where: { userId },
        select: {
          id: true,
          name: true,
          parentId: true,
          _count: { select: { documents: true } }
        }
      })
    ]);

    if (documents.length === 0) {
      return [];
    }

    // 分析文档分类
    const classifications = await this.getDocumentClassifications(documents, userId);
    
    // 生成文件夹结构建议
    const suggestions = await this.generateFolderSuggestions(
      classifications,
      folders,
      documents
    );

    return suggestions;
  }

  /**
   * 生成智能整理建议
   */
  async generateSmartOrganizationSuggestions(userId: string): Promise<SmartOrganizationSuggestion[]> {
    const folderSuggestions = await this.analyzeFolderStructure(userId);
    const duplicateSuggestions = await this.findDuplicateDocuments(userId);
    const tagSuggestions = await this.generateTagSuggestions(userId);

    const suggestions: SmartOrganizationSuggestion[] = [];

    // 转换文件夹建议
    folderSuggestions.forEach((suggestion, index) => {
      suggestions.push({
        id: `folder_${index}`,
        type: suggestion.type === 'create' ? 'folder_creation' : 'document_move',
        title: suggestion.type === 'create' 
          ? `创建文件夹: ${suggestion.name}`
          : `移动文档到: ${suggestion.name}`,
        description: suggestion.reason,
        affectedDocuments: [], // 需要根据具体情况填充
        actions: [{
          type: suggestion.type === 'create' ? 'create_folder' : 'move_document',
          target: suggestion.name,
          params: {
            name: suggestion.name,
            parentId: suggestion.parentId,
            path: suggestion.path
          },
          description: suggestion.reason
        }],
        expectedBenefit: `提高文档组织效率，影响 ${suggestion.documentCount} 个文档`,
        priority: suggestion.priority,
        applied: false,
        createdAt: new Date()
      });
    });

    // 添加重复文档合并建议
    duplicateSuggestions.forEach((duplicate, index) => {
      suggestions.push({
        id: `duplicate_${index}`,
        type: 'duplicate_merge',
        title: `合并重复文档: ${duplicate.title}`,
        description: `发现 ${duplicate.documents.length} 个相似文档，建议合并`,
        affectedDocuments: duplicate.documents,
        actions: [{
          type: 'merge_documents',
          target: duplicate.documents[0],
          params: {
            sourceDocuments: duplicate.documents.slice(1),
            targetDocument: duplicate.documents[0]
          },
          description: '合并相似内容的文档'
        }],
        expectedBenefit: '减少重复内容，提高文档管理效率',
        priority: 3,
        applied: false,
        createdAt: new Date()
      });
    });

    // 添加标签建议
    tagSuggestions.forEach((tagSuggestion, index) => {
      suggestions.push({
        id: `tag_${index}`,
        type: 'tag_addition',
        title: `添加标签: ${tagSuggestion.tag}`,
        description: `为 ${tagSuggestion.documents.length} 个文档添加标签`,
        affectedDocuments: tagSuggestion.documents,
        actions: [{
          type: 'add_tag',
          target: tagSuggestion.tag,
          params: {
            tag: tagSuggestion.tag,
            documents: tagSuggestion.documents
          },
          description: `基于内容分析建议添加标签: ${tagSuggestion.tag}`
        }],
        expectedBenefit: '提高文档检索和分类效率',
        priority: 2,
        applied: false,
        createdAt: new Date()
      });
    });

    // 按优先级排序
    return suggestions.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 应用整理建议
   */
  async applyOrganizationSuggestion(
    suggestionId: string,
    userId: string
  ): Promise<{ success: boolean; message: string }> {
    // 这里实现具体的整理操作
    // 由于涉及数据库操作，需要根据具体的action类型执行相应操作
    
    try {
      // 示例实现 - 创建文件夹
      // const suggestion = await this.getSuggestionById(suggestionId);
      // if (suggestion.type === 'folder_creation') {
      //   await prisma.folder.create({
      //     data: {
      //       name: suggestion.actions[0].params.name,
      //       parentId: suggestion.actions[0].params.parentId,
      //       userId
      //     }
      //   });
      // }
      
      return {
        success: true,
        message: '整理建议已成功应用'
      };
    } catch (error) {
      return {
        success: false,
        message: `应用建议失败: ${(error as Error).message}`
      };
    }
  }

  /**
   * 获取文档分类信息
   */
  private async getDocumentClassifications(
    documents: any[],
    userId: string
  ): Promise<DocumentClassification[]> {
    const classifications: DocumentClassification[] = [];
    
    // 批量分类文档（这里简化处理，实际应该使用批量API）
    for (const doc of documents.slice(0, 20)) { // 限制处理数量避免超时
      try {
        const classification = await aiDocumentClassifier.classifyDocument(doc.id, userId);
        classifications.push(classification);
      } catch (error) {
        console.error(`分类文档 ${doc.id} 失败:`, error);
      }
    }
    
    return classifications;
  }

  /**
   * 生成文件夹结构建议
   */
  private async generateFolderSuggestions(
    classifications: DocumentClassification[],
    existingFolders: any[],
    documents: any[]
  ): Promise<FolderStructureSuggestion[]> {
    // 统计分类信息
    const categoryStats = new Map<string, number>();
    classifications.forEach(classification => {
      const count = categoryStats.get(classification.primaryCategory) || 0;
      categoryStats.set(classification.primaryCategory, count + 1);
    });

    const suggestions: FolderStructureSuggestion[] = [];
    const existingFolderNames = new Set(existingFolders.map(f => f.name));

    // 为主要分类创建文件夹建议
    for (const [category, count] of categoryStats.entries()) {
      if (count >= 3 && !existingFolderNames.has(category)) {
        suggestions.push({
          name: category,
          path: `/${category}`,
          reason: `发现 ${count} 个"${category}"类型的文档，建议创建专门的文件夹进行组织`,
          documentCount: count,
          priority: Math.min(5, Math.floor(count / 2)),
          type: 'create'
        });
      }
    }

    // 使用AI生成更智能的文件夹结构建议
    if (classifications.length > 0) {
      const aiSuggestions = await this.generateAIFolderSuggestions(
        classifications,
        existingFolders
      );
      suggestions.push(...aiSuggestions);
    }

    return suggestions.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 使用AI生成文件夹结构建议
   */
  private async generateAIFolderSuggestions(
    classifications: DocumentClassification[],
    existingFolders: any[]
  ): Promise<FolderStructureSuggestion[]> {
    const classificationSummary = classifications.map(c => ({
      category: c.primaryCategory,
      tags: c.suggestedTags,
      confidence: c.confidence
    }));

    const existingStructure = existingFolders.map(f => ({
      name: f.name,
      documentCount: f._count.documents
    }));

    const prompt = `
基于以下文档分类结果和现有文件夹结构，请提供智能的文件夹组织建议：

文档分类统计:
${JSON.stringify(classificationSummary, null, 2)}

现有文件夹结构:
${JSON.stringify(existingStructure, null, 2)}

请分析并返回JSON格式的文件夹结构建议：
{
  "suggestions": [
    {
      "name": "建议的文件夹名称",
      "parentPath": "父文件夹路径（可选）",
      "reason": "建议原因",
      "documentCount": 预计文档数量,
      "priority": 1-5,
      "type": "create|move|merge"
    }
  ]
}

请考虑：
1. 避免创建过于细分的文件夹
2. 合并相似的分类
3. 保持层级结构简洁
4. 优先处理文档数量较多的分类
`;

    try {
      const aiService = aiServiceManager.getDefaultService();
      const response = await aiService.generateText({
        prompt,
        maxTokens: 1000
      });

      const parsed = JSON.parse(response.content);
      return (parsed.suggestions || []).map((suggestion: any) => ({
        name: suggestion.name,
        parentId: undefined, // 需要根据parentPath查找
        path: suggestion.parentPath ? `${suggestion.parentPath}/${suggestion.name}` : `/${suggestion.name}`,
        reason: suggestion.reason,
        documentCount: suggestion.documentCount || 0,
        priority: suggestion.priority || 3,
        type: suggestion.type || 'create'
      }));
    } catch (error) {
      console.error('AI文件夹建议生成失败:', error);
      return [];
    }
  }

  /**
   * 查找重复文档
   */
  private async findDuplicateDocuments(userId: string): Promise<Array<{
    title: string;
    documents: string[];
    similarity: number;
  }>> {
    const documents = await prisma.document.findMany({
      where: { userId },
      select: { id: true, title: true, content: true }
    });

    const duplicates: Array<{
      title: string;
      documents: string[];
      similarity: number;
    }> = [];

    // 简单的重复检测（基于标题相似性）
    for (let i = 0; i < documents.length; i++) {
      for (let j = i + 1; j < documents.length; j++) {
        const similarity = this.calculateTitleSimilarity(
          documents[i].title,
          documents[j].title
        );
        
        if (similarity > 0.8) {
          duplicates.push({
            title: documents[i].title,
            documents: [documents[i].id, documents[j].id],
            similarity
          });
        }
      }
    }

    return duplicates;
  }

  /**
   * 生成标签建议
   */
  private async generateTagSuggestions(userId: string): Promise<Array<{
    tag: string;
    documents: string[];
    relevance: number;
  }>> {
    // 获取所有文档的分类信息
    const documents = await prisma.document.findMany({
      where: { userId },
      select: { id: true, title: true, content: true }
    });

    const tagSuggestions: Array<{
      tag: string;
      documents: string[];
      relevance: number;
    }> = [];

    // 这里可以实现更复杂的标签建议逻辑
    // 暂时返回空数组
    return tagSuggestions;
  }

  /**
   * 计算标题相似性
   */
  private calculateTitleSimilarity(title1: string, title2: string): number {
    const words1 = title1.toLowerCase().split(/\s+/);
    const words2 = title2.toLowerCase().split(/\s+/);
    
    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];
    
    return intersection.length / union.length;
  }
}

// 导出单例实例
export const aiFolderOrganizer = new AIFolderOrganizerService();