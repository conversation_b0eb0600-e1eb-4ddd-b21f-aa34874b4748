/**
 * 同步服务简单测试
 * 验证基本功能是否正常工作
 */

import { SyncService } from './sync-service';

// 创建同步服务实例
const syncService = new SyncService();

// 测试基本功能
console.log('🔄 测试同步服务基本功能...');

// 1. 测试获取同步状态
const initialState = syncService.getSyncState();
console.log('✅ 初始同步状态:', {
  isOnline: initialState.isOnline,
  isSyncing: initialState.isSyncing,
  pendingChanges: initialState.pendingChanges.length,
  conflicts: initialState.conflicts.length
});

// 2. 测试事件监听器
let eventReceived = false;
const testListener = (event: any) => {
  eventReceived = true;
  console.log('✅ 收到事件:', event.type);
};

syncService.addEventListener('sync_started', testListener);
syncService['emitEvent']('sync_started', { test: true });

if (eventReceived) {
  console.log('✅ 事件系统工作正常');
} else {
  console.log('❌ 事件系统异常');
}

// 3. 测试冲突检测
const localDoc = {
  id: 'test-doc',
  title: 'Test Document',
  content: { type: 'doc', content: [] },
  updatedAt: new Date('2023-01-02'),
  lastSyncAt: new Date('2023-01-01'),
  metadata: { wordCount: 10, characterCount: 50, tags: [], isPublic: false }
};

const remoteDoc = {
  id: 'test-doc',
  title: 'Test Document Modified',
  content: { type: 'doc', content: [] },
  updatedAt: new Date('2023-01-03'),
  metadata: { wordCount: 15, characterCount: 60, tags: [], isPublic: false }
};

const conflict = syncService['detectConflict'](localDoc as any, remoteDoc);
if (conflict) {
  console.log('✅ 冲突检测工作正常:', conflict.conflictType);
} else {
  console.log('❌ 冲突检测异常');
}

// 4. 测试批处理
const items = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
const batches = syncService['createBatches'](items, 3);
if (batches.length === 4 && batches[0].length === 3) {
  console.log('✅ 批处理功能正常');
} else {
  console.log('❌ 批处理功能异常');
}

// 清理资源
syncService.removeEventListener('sync_started', testListener);
syncService.dispose();

console.log('🎉 同步服务测试完成！');

export { syncService };