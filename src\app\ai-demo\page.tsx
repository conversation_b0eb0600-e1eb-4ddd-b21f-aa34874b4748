'use client';

/**
 * AI 服务演示页面
 * 用于测试和演示 AI 服务功能
 */

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, Zap } from 'lucide-react';

import {
  AIServiceFactory,
  aiServiceManager,
  validateAIConfig,
  testAIConnection,
  formatAIError,
  getRecommendedModels,
  estimateTokens
} from '@/lib/services/ai';
import type { AIServiceConfig, AIProvider, AIRequest } from '@/types/ai.types';

export default function AIDemoPage() {
  // 配置状态
  const [config, setConfig] = useState<AIServiceConfig>({
    provider: 'openai',
    model: 'gpt-3.5-turbo',
    apiKey: '',
    maxTokens: 2000,
    temperature: 0.7
  });

  // 测试状态
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionResult, setConnectionResult] = useState<{
    success: boolean;
    error?: string;
    responseTime?: number;
  } | null>(null);

  // AI 请求状态
  const [request, setRequest] = useState<AIRequest>({
    prompt: '请写一段关于人工智能的简短介绍',
    context: '',
    systemPrompt: ''
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [response, setResponse] = useState<string>('');
  const [error, setError] = useState<string>('');

  // 处理配置变更
  const handleConfigChange = (field: keyof AIServiceConfig, value: any) => {
    setConfig(prev => ({ ...prev, [field]: value }));
    setConnectionResult(null); // 清除之前的连接测试结果
  };

  // 处理提供商变更
  const handleProviderChange = (provider: AIProvider) => {
    const defaultConfig = AIServiceFactory.getDefaultConfig(provider);
    setConfig(prev => ({
      ...prev,
      ...defaultConfig,
      apiKey: prev.apiKey // 保留 API 密钥
    }));
    setConnectionResult(null);
  };

  // 测试连接
  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    setConnectionResult(null);

    try {
      const result = await testAIConnection(config);
      setConnectionResult(result);
    } catch (err) {
      setConnectionResult({
        success: false,
        error: formatAIError(err)
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  // 生成文本
  const handleGenerateText = async () => {
    setIsGenerating(true);
    setResponse('');
    setError('');

    try {
      // 验证配置
      const validation = validateAIConfig(config);
      if (!validation.valid) {
        throw new Error(validation.errors.join(', '));
      }

      // 创建服务并生成文本
      const service = AIServiceFactory.createService(config);
      const result = await service.generateText(request);
      
      setResponse(result.content);
    } catch (err) {
      setError(formatAIError(err));
    } finally {
      setIsGenerating(false);
    }
  };

  // 获取推荐模型
  const recommendedModels = getRecommendedModels(config.provider);

  // 估算令牌数
  const estimatedTokens = estimateTokens(request.prompt + (request.context || '') + (request.systemPrompt || ''));

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">AI 服务演示</h1>
        <p className="text-muted-foreground">
          测试和演示 AI 服务的各种功能，包括 OpenAI、Ollama 和 Gemini
        </p>
      </div>

      <Tabs defaultValue="config" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="config">服务配置</TabsTrigger>
          <TabsTrigger value="test">功能测试</TabsTrigger>
          <TabsTrigger value="advanced">高级功能</TabsTrigger>
        </TabsList>

        {/* 服务配置 */}
        <TabsContent value="config" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>AI 服务配置</CardTitle>
              <CardDescription>
                配置不同的 AI 服务提供商和参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="provider">服务提供商</Label>
                  <Select
                    value={config.provider}
                    onValueChange={handleProviderChange}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="openai">OpenAI</SelectItem>
                      <SelectItem value="ollama">Ollama</SelectItem>
                      <SelectItem value="gemini">Gemini</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model">模型</Label>
                  <Select
                    value={config.model}
                    onValueChange={(value) => handleConfigChange('model', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {recommendedModels.map((model: string) => (
                        <SelectItem key={model} value={model}>
                          {model}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {(config.provider === 'openai' || config.provider === 'gemini') && (
                  <div className="space-y-2">
                    <Label htmlFor="apiKey">API 密钥</Label>
                    <Input
                      id="apiKey"
                      type="password"
                      value={config.apiKey || ''}
                      onChange={(e) => handleConfigChange('apiKey', e.target.value)}
                      placeholder="输入 API 密钥"
                    />
                  </div>
                )}

                {config.provider === 'ollama' && (
                  <div className="space-y-2">
                    <Label htmlFor="endpoint">服务端点</Label>
                    <Input
                      id="endpoint"
                      value={config.endpoint || ''}
                      onChange={(e) => handleConfigChange('endpoint', e.target.value)}
                      placeholder="http://localhost:11434"
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="maxTokens">最大令牌数</Label>
                  <Input
                    id="maxTokens"
                    type="number"
                    value={config.maxTokens || ''}
                    onChange={(e) => handleConfigChange('maxTokens', parseInt(e.target.value))}
                    min="1"
                    max="100000"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="temperature">温度参数</Label>
                  <Input
                    id="temperature"
                    type="number"
                    step="0.1"
                    value={config.temperature || ''}
                    onChange={(e) => handleConfigChange('temperature', parseFloat(e.target.value))}
                    min="0"
                    max="2"
                  />
                </div>
              </div>

              <div className="flex items-center gap-4">
                <Button
                  onClick={handleTestConnection}
                  disabled={isTestingConnection}
                  variant="outline"
                >
                  {isTestingConnection ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      测试中...
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4 mr-2" />
                      测试连接
                    </>
                  )}
                </Button>

                {connectionResult && (
                  <div className="flex items-center gap-2">
                    {connectionResult.success ? (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="text-green-600">
                          连接成功 ({connectionResult.responseTime}ms)
                        </span>
                      </>
                    ) : (
                      <>
                        <XCircle className="w-4 h-4 text-red-500" />
                        <span className="text-red-600">
                          连接失败: {connectionResult.error}
                        </span>
                      </>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 功能测试 */}
        <TabsContent value="test" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>文本生成测试</CardTitle>
              <CardDescription>
                测试 AI 服务的文本生成功能
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="prompt">用户提示</Label>
                <Textarea
                  id="prompt"
                  value={request.prompt}
                  onChange={(e) => setRequest(prev => ({ ...prev, prompt: e.target.value }))}
                  placeholder="输入您的提示..."
                  rows={3}
                />
                <div className="text-sm text-muted-foreground">
                  估算令牌数: {estimatedTokens}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="context">上下文（可选）</Label>
                <Textarea
                  id="context"
                  value={request.context || ''}
                  onChange={(e) => setRequest(prev => ({ ...prev, context: e.target.value }))}
                  placeholder="提供相关上下文..."
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="systemPrompt">系统提示（可选）</Label>
                <Textarea
                  id="systemPrompt"
                  value={request.systemPrompt || ''}
                  onChange={(e) => setRequest(prev => ({ ...prev, systemPrompt: e.target.value }))}
                  placeholder="设置系统行为..."
                  rows={2}
                />
              </div>

              <Button
                onClick={handleGenerateText}
                disabled={isGenerating || !request.prompt.trim()}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    生成中...
                  </>
                ) : (
                  '生成文本'
                )}
              </Button>

              {error && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {response && (
                <div className="space-y-2">
                  <Label>AI 响应</Label>
                  <div className="p-4 bg-muted rounded-lg">
                    <pre className="whitespace-pre-wrap text-sm">{response}</pre>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 高级功能 */}
        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>高级功能</CardTitle>
              <CardDescription>
                测试改写、总结、翻译等高级 AI 功能
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                高级功能演示即将推出...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}