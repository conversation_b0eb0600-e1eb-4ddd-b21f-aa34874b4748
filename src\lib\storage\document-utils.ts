import { JSONContent } from '@tiptap/react';
import { DocumentMetadata } from '@/types';

/**
 * Extract plain text from TipTap JSONContent
 */
export function extractTextFromContent(content: JSONContent): string {
  if (!content) return '';
  
  let text = '';
  
  if (content.text) {
    text += content.text;
  }
  
  if (content.content && Array.isArray(content.content)) {
    for (const child of content.content) {
      text += extractTextFromContent(child);
      // Add space between blocks for better word counting
      if (child.type === 'paragraph' || child.type === 'heading') {
        text += ' ';
      }
    }
  }
  
  return text;
}

/**
 * Calculate word count from content
 */
export function calculateWordCount(content: JSONContent): number {
  const text = extractTextFromContent(content);
  if (!text.trim()) return 0;
  
  // Split by whitespace and filter out empty strings
  const words = text.trim().split(/\s+/).filter(word => word.length > 0);
  return words.length;
}

/**
 * Calculate character count from content
 */
export function calculateCharacterCount(content: JSONContent): number {
  const text = extractTextFromContent(content);
  return text.length;
}

/**
 * Calculate character count without spaces
 */
export function calculateCharacterCountNoSpaces(content: JSONContent): number {
  const text = extractTextFromContent(content);
  return text.replace(/\s/g, '').length;
}

/**
 * Calculate reading time estimate (average 200 words per minute)
 */
export function calculateReadingTime(content: JSONContent): number {
  const wordCount = calculateWordCount(content);
  return Math.ceil(wordCount / 200); // minutes
}

/**
 * Get document summary (first paragraph or first N characters)
 */
export function getDocumentSummary(content: JSONContent, maxLength: number = 150): string {
  const text = extractTextFromContent(content);
  if (text.length <= maxLength) return text;
  
  // Try to cut at sentence boundary
  const truncated = text.substring(0, maxLength);
  const lastSentence = truncated.lastIndexOf('.');
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSentence > maxLength * 0.7) {
    return truncated.substring(0, lastSentence + 1);
  } else if (lastSpace > maxLength * 0.7) {
    return truncated.substring(0, lastSpace) + '...';
  } else {
    return truncated + '...';
  }
}

/**
 * Update document metadata based on content
 */
export function updateDocumentMetadata(
  content: JSONContent, 
  existingMetadata: DocumentMetadata
): DocumentMetadata {
  return {
    ...existingMetadata,
    wordCount: calculateWordCount(content),
    characterCount: calculateCharacterCount(content),
  };
}

/**
 * Check if content is empty
 */
export function isContentEmpty(content: JSONContent): boolean {
  const text = extractTextFromContent(content).trim();
  return text.length === 0;
}

/**
 * Create empty document content
 */
export function createEmptyContent(): JSONContent {
  return {
    type: 'doc',
    content: [
      {
        type: 'paragraph',
        content: []
      }
    ]
  };
}

/**
 * Validate document content structure
 */
export function validateContent(content: JSONContent): boolean {
  try {
    // Basic validation - content should have type and be an object
    if (!content || typeof content !== 'object') return false;
    if (!content.type) return false;
    
    // If it has content array, validate recursively
    if (content.content && Array.isArray(content.content)) {
      return content.content.every(child => validateContent(child));
    }
    
    return true;
  } catch {
    return false;
  }
}

/**
 * Sanitize document title
 */
export function sanitizeTitle(title: string): string {
  return title
    .trim()
    .replace(/[<>:"/\\|?*]/g, '') // Remove invalid filename characters
    .substring(0, 255); // Limit length
}

/**
 * Generate document title from content if title is empty
 */
export function generateTitleFromContent(content: JSONContent): string {
  const text = extractTextFromContent(content).trim();
  if (!text) return 'Untitled Document';
  
  // Take first line or first 50 characters
  const firstLine = text.split('\n')[0];
  const title = firstLine.length > 50 ? firstLine.substring(0, 47) + '...' : firstLine;
  
  return sanitizeTitle(title) || 'Untitled Document';
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * Format document statistics for display
 */
export function formatDocumentStats(metadata: DocumentMetadata): {
  words: string;
  characters: string;
  readingTime: string;
} {
  const readingTime = Math.ceil(metadata.wordCount / 200);
  
  return {
    words: metadata.wordCount.toLocaleString(),
    characters: metadata.characterCount.toLocaleString(),
    readingTime: readingTime === 1 ? '1 minute' : `${readingTime} minutes`
  };
}

/**
 * Search within document content
 */
export function searchInContent(content: JSONContent, query: string): boolean {
  const text = extractTextFromContent(content).toLowerCase();
  return text.includes(query.toLowerCase());
}

/**
 * Highlight search terms in text (returns HTML)
 */
export function highlightSearchTerms(text: string, query: string): string {
  if (!query.trim()) return text;
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}
