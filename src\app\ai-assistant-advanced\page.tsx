'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { AdvancedAIAssistantPanel } from '@/components/ai/AdvancedAIAssistantPanel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { 
  SparklesIcon,
  CheckCircleIcon,
  PlayIcon,
  PauseIcon,
  RotateCcwIcon,
  SettingsIcon,
  MonitorIcon,
  SmartphoneIcon,
  TabletIcon,
  ZapIcon,
  TrendingUpIcon,
  BarChartIcon,
  ClockIcon,
  StarIcon,
  InfoIcon,
  CheckSquareIcon,
  CheckIcon,
  HeartIcon,
  PaletteIcon,
  AccessibilityIcon,
  KeyboardIcon,
  VolumeXIcon,
  Volume2Icon,
  SunIcon,
  MoonIcon,
  EyeIcon,
  LayoutIcon
} from 'lucide-react';

/**
 * 高级 AI 助手面板演示页面
 * 展示任务 27 的完善版实现
 */
export default function AdvancedAIAssistantPage() {
  const [panelOpen, setPanelOpen] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('');
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 });
  const [userPreferences, setUserPreferences] = useState({
    theme: 'light' as 'light' | 'dark' | 'auto',
    compactMode: false,
    showShortcuts: true,
    enableAnimations: true,
    enableSounds: false,
    autoCollapse: false,
    defaultTab: 'features',
    favoriteFeatures: ['ai-continue', 'ai-rewrite'],
    pinnedCategories: ['writing'],
    customCategoryOrder: {},
    accessibilityMode: false,
    highContrast: false,
    reducedMotion: false
  });

  // 检测设备类型
  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({ width, height });
      
      if (width < 768) {
        setDeviceType('mobile');
      } else if (width < 1024) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    updateDeviceInfo();
    window.addEventListener('resize', updateDeviceInfo);
    
    return () => window.removeEventListener('resize', updateDeviceInfo);
  }, []);

  /**
   * 处理 AI 操作
   */
  const handleAIAction = useCallback(async (actionId: string, data?: any) => {
    console.log('AI Action:', actionId, data);
    
    const startTime = Date.now();
    setIsProcessing(true);
    
    // 设置处理状态消息
    const statusMessages: Record<string, string> = {
      'ai-continue': '正在生成续写内容...',
      'ai-rewrite': '正在改写文本...',
      'ai-summarize': '正在生成摘要...',
      'ai-translate': '正在翻译文本...',
      'ai-explain': '正在生成解释...',
      'ai-keywords': '正在提取关键词...',
      'ai-outline': '正在生成大纲...',
      'ai-analysis': '正在分析内容...',
      'ai-creative': '正在创作内容...',
      'ai-custom': '正在执行自定义指令...',
      'ai-chat': '正在准备对话...',
      'ai-grammar': '正在检查语法...',
      'ai-expand': '正在扩展内容...',
      'ai-settings': '正在打开设置...'
    };
    
    setProcessingStatus(statusMessages[actionId] || '正在处理请求...');
    
    try {
      // 模拟处理延迟
      await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2000));
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // 模拟成功结果
      console.log(`AI action ${actionId} completed in ${responseTime}ms`);
      
    } catch (error) {
      console.error('AI action failed:', error);
    } finally {
      setIsProcessing(false);
      setProcessingStatus('');
    }
  }, []);

  /**
   * 切换面板状态
   */
  const togglePanel = useCallback(() => {
    setPanelOpen(prev => !prev);
  }, []);

  /**
   * 处理偏好设置变更
   */
  const handlePreferencesChange = useCallback((newPreferences: any) => {
    setUserPreferences(newPreferences);
    console.log('Preferences updated:', newPreferences);
  }, []);

  /**
   * 获取设备信息
   */
  const getDeviceInfo = () => {
    switch (deviceType) {
      case 'mobile':
        return { 
          icon: SmartphoneIcon, 
          name: '移动端', 
          color: 'text-green-600',
          bg: 'bg-green-50'
        };
      case 'tablet':
        return { 
          icon: TabletIcon, 
          name: '平板端', 
          color: 'text-blue-600',
          bg: 'bg-blue-50'
        };
      default:
        return { 
          icon: MonitorIcon, 
          name: '桌面端', 
          color: 'text-purple-600',
          bg: 'bg-purple-50'
        };
    }
  };

  const deviceInfo = getDeviceInfo();
  const DeviceIcon = deviceInfo.icon;

  // 示例文本内容
  const sampleTexts = [
    {
      title: '高级AI助手面板特性',
      content: '本次完善的高级AI助手面板新增了个性化设置、无障碍支持、功能收藏、主题切换等高级功能。用户可以根据自己的使用习惯自定义面板的外观和行为，提供更加个性化的使用体验。',
      category: '功能介绍'
    },
    {
      title: '用户体验优化',
      content: '面板支持紧凑模式、动画效果、键盘快捷键、高对比度模式等多种个性化选项。同时增加了功能收藏、使用统计、操作历史等实用功能，让用户能够更高效地使用AI助手。',
      category: '体验优化'
    },
    {
      title: '无障碍功能支持',
      content: '为了确保所有用户都能顺畅使用AI助手，我们特别加强了无障碍功能支持，包括键盘导航、屏幕阅读器兼容、高对比度模式、减少动画等选项，让每个用户都能享受到AI助手的便利。',
      category: '无障碍'
    }
  ];

  // 功能特性展示
  const advancedFeatures = [
    {
      icon: PaletteIcon,
      title: '个性化主题',
      description: '支持明暗主题切换，高对比度模式',
      color: 'text-purple-600',
      bg: 'bg-purple-50'
    },
    {
      icon: HeartIcon,
      title: '功能收藏',
      description: '收藏常用功能，快速访问',
      color: 'text-red-600',
      bg: 'bg-red-50'
    },
    {
      icon: LayoutIcon,
      title: '紧凑模式',
      description: '节省空间的紧凑界面布局',
      color: 'text-blue-600',
      bg: 'bg-blue-50'
    },
    {
      icon: KeyboardIcon,
      title: '键盘快捷键',
      description: '完整的键盘导航支持',
      color: 'text-green-600',
      bg: 'bg-green-50'
    },
    {
      icon: AccessibilityIcon,
      title: '无障碍支持',
      description: '屏幕阅读器兼容，键盘导航',
      color: 'text-orange-600',
      bg: 'bg-orange-50'
    },
    {
      icon: BarChartIcon,
      title: '使用统计',
      description: '详细的功能使用统计分析',
      color: 'text-indigo-600',
      bg: 'bg-indigo-50'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">
      <div className="max-w-7xl mx-auto p-4 md:p-6 space-y-6">
        {/* 页面标题 */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
              <SparklesIcon className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
              高级 AI 助手面板
            </h1>
          </div>
          <p className="text-base md:text-lg text-gray-600 max-w-4xl mx-auto">
            任务 27 的完善版实现 - 增加个性化设置、无障碍支持和高级功能
          </p>
          <div className="flex items-center justify-center gap-2">
            <Badge variant="secondary" className="bg-purple-100 text-purple-800">
              <SparklesIcon className="h-3 w-3 mr-1" />
              高级版本
            </Badge>
            <Badge variant="outline">
              个性化 + 无障碍 + 高级功能
            </Badge>
          </div>
        </div>

        {/* 实时状态面板 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className={`${deviceInfo.bg} border-2`}>
            <CardContent className="p-4 text-center">
              <DeviceIcon className={`h-8 w-8 ${deviceInfo.color} mx-auto mb-2`} />
              <div className="font-semibold text-gray-900">{deviceInfo.name}</div>
              <div className="text-sm text-gray-600">{screenSize.width} × {screenSize.height}</div>
            </CardContent>
          </Card>
          
          <Card className="bg-blue-50 border-2 border-blue-200">
            <CardContent className="p-4 text-center">
              <ZapIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">面板状态</div>
              <div className="text-sm text-gray-600">
                {panelOpen ? '已打开' : '已关闭'}
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-purple-50 border-2 border-purple-200">
            <CardContent className="p-4 text-center">
              <PaletteIcon className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">当前主题</div>
              <div className="text-sm text-gray-600 capitalize">
                {userPreferences.theme}
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-green-50 border-2 border-green-200">
            <CardContent className="p-4 text-center">
              <HeartIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">收藏功能</div>
              <div className="text-sm text-gray-600">
                {userPreferences.favoriteFeatures.length} 个
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 控制面板 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <SettingsIcon className="h-5 w-5 text-purple-600" />
              演示控制与设置
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 基础控制 */}
              <div className="flex flex-wrap items-center gap-3">
                <Button
                  onClick={togglePanel}
                  className={`flex items-center gap-2 ${panelOpen ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'} text-white`}
                >
                  {panelOpen ? (
                    <>
                      <PauseIcon className="h-4 w-4" />
                      关闭面板
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4" />
                      打开面板
                    </>
                  )}
                </Button>
                
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <SparklesIcon className="h-4 w-4" />
                  <span>高级功能演示</span>
                </div>
                
                {isProcessing && (
                  <div className="flex items-center gap-2 text-sm text-blue-600">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span>AI 处理中...</span>
                  </div>
                )}
              </div>

              {/* 快速设置 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <Button
                  variant={userPreferences.theme === 'dark' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePreferencesChange({
                    ...userPreferences,
                    theme: userPreferences.theme === 'dark' ? 'light' : 'dark'
                  })}
                  className="flex items-center gap-2"
                >
                  {userPreferences.theme === 'dark' ? <SunIcon className="h-4 w-4" /> : <MoonIcon className="h-4 w-4" />}
                  {userPreferences.theme === 'dark' ? '明亮' : '暗色'}
                </Button>
                
                <Button
                  variant={userPreferences.compactMode ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePreferencesChange({
                    ...userPreferences,
                    compactMode: !userPreferences.compactMode
                  })}
                  className="flex items-center gap-2"
                >
                  <LayoutIcon className="h-4 w-4" />
                  紧凑模式
                </Button>
                
                <Button
                  variant={userPreferences.enableAnimations ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePreferencesChange({
                    ...userPreferences,
                    enableAnimations: !userPreferences.enableAnimations
                  })}
                  className="flex items-center gap-2"
                >
                  <SparklesIcon className="h-4 w-4" />
                  动画效果
                </Button>
                
                <Button
                  variant={userPreferences.accessibilityMode ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePreferencesChange({
                    ...userPreferences,
                    accessibilityMode: !userPreferences.accessibilityMode
                  })}
                  className="flex items-center gap-2"
                >
                  <AccessibilityIcon className="h-4 w-4" />
                  无障碍
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 高级功能展示 */}
        <Card>
          <CardHeader>
            <CardTitle>新增高级功能</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {advancedFeatures.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div
                    key={index}
                    className={`p-4 rounded-lg border ${feature.bg} border-gray-200`}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <IconComponent className={`h-6 w-6 ${feature.color}`} />
                      <h4 className="font-medium text-gray-900">{feature.title}</h4>
                    </div>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* 交互测试区域 */}
        <Card>
          <CardHeader>
            <CardTitle>功能测试区域</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600">
              点击下面的文本来测试高级AI助手面板的各项功能。选择文本后，面板中的相关功能将被激活。
            </p>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {sampleTexts.map((text, index) => (
                <div
                  key={index}
                  className={`
                    p-4 rounded-lg border cursor-pointer transition-all duration-200
                    ${selectedText === text.content
                      ? 'bg-purple-50 border-purple-300 shadow-lg transform scale-[1.02]' 
                      : 'bg-white border-gray-200 hover:bg-gray-50 hover:shadow-md'
                    }
                  `}
                  onClick={() => setSelectedText(text.content)}
                >
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="outline" className="text-xs">
                      {text.category}
                    </Badge>
                    <h4 className="font-medium text-gray-900">{text.title}</h4>
                  </div>
                  <p className="text-sm text-gray-700 leading-relaxed">
                    {text.content}
                  </p>
                  {selectedText === text.content && (
                    <div className="mt-3 p-2 bg-purple-100 rounded text-xs text-purple-800 flex items-center gap-2">
                      <CheckCircleIcon className="h-3 w-3" />
                      已选择此文本，AI 功能已激活
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 使用说明 */}
        <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
          <CardHeader>
            <CardTitle className="text-purple-900">高级功能使用说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium text-purple-900">个性化设置</h4>
                <ul className="text-sm text-purple-800 space-y-1">
                  <li>• 切换明暗主题和高对比度模式</li>
                  <li>• 启用紧凑模式节省空间</li>
                  <li>• 自定义动画效果和音效</li>
                  <li>• 设置自动折叠和默认标签页</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-medium text-purple-900">高级功能</h4>
                <ul className="text-sm text-purple-800 space-y-1">
                  <li>• 收藏常用功能快速访问</li>
                  <li>• 查看详细的使用统计和历史</li>
                  <li>• 使用键盘快捷键提高效率</li>
                  <li>• 启用无障碍模式支持辅助技术</li>
                </ul>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-purple-100 rounded-lg">
              <div className="flex items-start gap-2">
                <InfoIcon className="h-4 w-4 text-purple-600 mt-0.5" />
                <div className="text-sm text-purple-800">
                  <strong>完善说明：</strong>本高级版本在原有任务 27 基础上，新增了个性化设置、无障碍支持、
                  功能收藏、使用统计等高级功能，提供更加完整和人性化的用户体验。所有功能都经过精心设计，
                  确保在不同设备和使用场景下都能提供最佳的交互体验。
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 高级 AI 助手面板 */}
      <AdvancedAIAssistantPanel
        isOpen={panelOpen}
        onToggle={togglePanel}
        position="right"
        width={deviceType === 'mobile' ? undefined : 450}
        isMobile={deviceType === 'mobile'}
        onAIAction={handleAIAction}
        selectedText={selectedText}
        isProcessing={isProcessing}
        processingStatus={processingStatus}
        enableAdvancedFeatures={true}
        initialTheme={userPreferences.theme}
        enableSearch={true}
        enableStats={true}
        userPreferences={userPreferences}
        onPreferencesChange={handlePreferencesChange}
      />
    </div>
  );
}