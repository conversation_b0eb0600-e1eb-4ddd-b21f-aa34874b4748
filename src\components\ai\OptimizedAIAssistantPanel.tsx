'use client';

import React, { useState, useCallback, useEffect, useRef, useMemo, memo } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  SparklesIcon,
  EditIcon,
  FileTextIcon,
  LanguagesIcon,
  MessageSquareIcon,
  SettingsIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  HelpCircleIcon,
  PenToolIcon,
  BarChartIcon,
  TagIcon,
  ListIcon,
  TerminalIcon,
  BookOpenIcon,
  LightbulbIcon,
  RefreshCwIcon,
  XIcon,
  HistoryIcon,
  TrendingUpIcon,
  ZapIcon,
  StarIcon,
  ClockIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  MaximizeIcon,
  MinimizeIcon,
  SearchIcon,
  FilterIcon,
  HeartIcon,
  EyeIcon,
  Loader2Icon
} from 'lucide-react';

/**
 * 虚拟滚动组件
 */
interface VirtualScrollProps {
  items: any[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: any, index: number) => React.ReactNode;
  overscan?: number;
}

const VirtualScroll = memo<VirtualScrollProps>(({ 
  items, 
  itemHeight, 
  containerHeight, 
  renderItem, 
  overscan = 5 
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [items, visibleRange]);

  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.startIndex * itemHeight;

  return (
    <div
      ref={scrollElementRef}
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div key={visibleRange.startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, visibleRange.startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

VirtualScroll.displayName = 'VirtualScroll';

/**
 * 懒加载图片组件
 */
interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: React.ReactNode;
}

const LazyImage = memo<LazyImageProps>(({ src, alt, className, placeholder }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div ref={imgRef} className={className}>
      {isInView ? (
        <img
          src={src}
          alt={alt}
          className={`transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
          onLoad={() => setIsLoaded(true)}
        />
      ) : (
        placeholder || <div className="bg-gray-200 animate-pulse w-full h-full" />
      )}
    </div>
  );
});

LazyImage.displayName = 'LazyImage';

/**
 * 性能优化的功能按钮组件
 */
interface OptimizedFeatureButtonProps {
  feature: any;
  isRecent: boolean;
  isProcessing: boolean;
  preferences: any;
  onToggleFavorite: (id: string) => void;
}

const OptimizedFeatureButton = memo<OptimizedFeatureButtonProps>(({
  feature,
  isRecent,
  isProcessing,
  preferences,
  onToggleFavorite
}) => {
  const IconComponent = feature.icon;
  const hasUsage = (feature.usage || 0) > 0;
  const isFavorite = feature.favorite;

  const handleClick = useCallback(() => {
    if (!feature.disabled && !isProcessing) {
      feature.action();
    }
  }, [feature, isProcessing]);

  const handleFavoriteClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite(feature.id);
  }, [feature.id, onToggleFavorite]);

  return (
    <div className={`relative group ${preferences.compactMode ? 'mb-1' : 'mb-2'}`}>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClick}
        disabled={feature.disabled || isProcessing}
        className={`
          w-full justify-start h-auto text-left relative
          ${preferences.compactMode ? 'p-2' : 'p-3'}
          ${feature.disabled ? 'opacity-50' : 'hover:bg-gray-50'}
          ${isRecent ? 'bg-blue-50 border-l-2 border-l-blue-500' : ''}
          ${preferences.theme === 'dark' ? 'hover:bg-gray-800 text-gray-100' : ''}
          transition-all duration-200
          ${preferences.enableAnimations ? 'hover:scale-[1.02] hover:shadow-md' : ''}
        `}
      >
        <div className="flex items-start gap-3 w-full">
          <IconComponent className={`
            ${preferences.compactMode ? 'h-3 w-3' : 'h-4 w-4'} 
            mt-0.5 flex-shrink-0
          `} />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className={`
                font-medium 
                ${preferences.compactMode ? 'text-xs' : 'text-sm'}
              `}>
                {feature.name}
              </span>
              {feature.premium && (
                <Badge variant="secondary" className="text-xs px-1 py-0 bg-yellow-100 text-yellow-800">
                  Pro
                </Badge>
              )}
              {isRecent && (
                <Badge variant="secondary" className="text-xs px-1 py-0 bg-green-100 text-green-800">
                  最近
                </Badge>
              )}
              {isFavorite && (
                <HeartIcon className="h-3 w-3 text-red-500 fill-current" />
              )}
              {preferences.showShortcuts && feature.shortcut && (
                <Badge variant="outline" className="text-xs px-1 py-0 ml-auto">
                  {feature.shortcut}
                </Badge>
              )}
            </div>
            {!preferences.compactMode && (
              <p className="text-xs text-gray-600 mb-1">{feature.description}</p>
            )}
            <div className="flex items-center gap-3 text-xs text-gray-500">
              {hasUsage && (
                <div className="flex items-center gap-1">
                  <ZapIcon className="h-3 w-3" />
                  <span>使用 {feature.usage} 次</span>
                </div>
              )}
              {feature.estimatedTime && (
                <div className="flex items-center gap-1">
                  <ClockIcon className="h-3 w-3" />
                  <span>{Math.round(feature.estimatedTime / 1000)}s</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={handleFavoriteClick}
        className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <HeartIcon className={`h-3 w-3 ${isFavorite ? 'text-red-500 fill-current' : 'text-gray-400'}`} />
      </Button>
    </div>
  );
});

OptimizedFeatureButton.displayName = 'OptimizedFeatureButton';

/**
 * 性能优化的AI助手面板属性
 */
interface OptimizedAIAssistantPanelProps {
  isOpen: boolean;
  onToggle: () => void;
  position?: 'left' | 'right';
  width?: number;
  isMobile?: boolean;
  className?: string;
  onAIAction?: (actionId: string, data?: any) => void;
  selectedText?: string;
  isProcessing?: boolean;
  processingStatus?: string;
  enableAdvancedFeatures?: boolean;
  initialTheme?: 'light' | 'dark' | 'auto';
  enableSearch?: boolean;
  enableStats?: boolean;
  userPreferences?: any;
  onPreferencesChange?: (preferences: any) => void;
  enableVirtualScroll?: boolean;
  maxHistoryItems?: number;
}

/**
 * 性能优化的AI助手面板组件
 * 支持虚拟滚动、懒加载和性能优化
 */
export function OptimizedAIAssistantPanel({
  isOpen,
  onToggle,
  position = 'right',
  width = 420,
  isMobile = false,
  className = '',
  onAIAction,
  selectedText,
  isProcessing = false,
  processingStatus,
  enableAdvancedFeatures = true,
  initialTheme = 'light',
  enableSearch = true,
  enableStats = true,
  userPreferences = {},
  onPreferencesChange,
  enableVirtualScroll = true,
  maxHistoryItems = 1000
}: OptimizedAIAssistantPanelProps) {
  // 状态管理
  const [activeTab, setActiveTab] = useState('features');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['writing']));
  const [recentActions, setRecentActions] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [isMaximized, setIsMaximized] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 用户偏好类型定义
  interface UserPreferences {
    theme: string;
    compactMode: boolean;
    showShortcuts: boolean;
    enableAnimations: boolean;
    enableSounds: boolean;
    autoCollapse: boolean;
    defaultTab: string;
    favoriteFeatures: string[];
  }

  // 用户偏好状态
  const [preferences, setPreferences] = useState<UserPreferences>({
    theme: initialTheme,
    compactMode: false,
    showShortcuts: true,
    enableAnimations: true,
    enableSounds: false,
    autoCollapse: false,
    defaultTab: 'features',
    favoriteFeatures: [],
    pinnedCategories: [],
    customCategoryOrder: {},
    accessibilityMode: false,
    highContrast: false,
    reducedMotion: false,
    ...userPreferences
  });

  // 操作历史状态（使用环形缓冲区优化内存）
  const [actionHistory, setActionHistory] = useState<Array<{
    id: string;
    action: string;
    timestamp: Date;
    success: boolean;
    duration?: number;
    category: string;
    context?: string;
  }>>([]);

  // 引用
  const panelRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * 防抖搜索
   */
  const debouncedSearch = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (query: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setSearchQuery(query);
      }, 300);
    };
  }, []);

  /**
   * 更新用户偏好（使用浅比较优化）
   */
  const updatePreferences = useCallback((updates: Partial<UserPreferences>) => {
    setPreferences((prev: UserPreferences) => {
      const newPreferences = { ...prev, ...updates };
      // 只有真正改变时才触发回调
      if (JSON.stringify(prev) !== JSON.stringify(newPreferences)) {
        onPreferencesChange?.(newPreferences);
      }
      return newPreferences;
    });
  }, [onPreferencesChange]);

  /**
   * 优化的AI操作处理
   */
  const handleAIAction = useCallback(async (actionId: string, data?: any) => {
    if (!onAIAction) return;

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    const startTime = Date.now();
    setIsLoading(true);

    try {
      await onAIAction(actionId, { ...data, signal: abortController.signal });
      
      const duration = Date.now() - startTime;
      const historyItem = {
        id: Math.random().toString(36).substr(2, 9),
        action: actionId,
        timestamp: new Date(),
        success: true,
        duration,
        category: 'unknown',
        context: selectedText ? `选中文本: ${selectedText.substring(0, 50)}...` : undefined
      };
      
      // 使用环形缓冲区限制历史记录数量
      setActionHistory(prev => {
        const newHistory = [historyItem, ...prev];
        return newHistory.slice(0, maxHistoryItems);
      });
      
      setRecentActions(prev => {
        const newActions = [actionId, ...prev.filter(id => id !== actionId)].slice(0, 20);
        return newActions;
      });

    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        const historyItem = {
          id: Math.random().toString(36).substr(2, 9),
          action: actionId,
          timestamp: new Date(),
          success: false,
          category: 'unknown',
          context: error.message
        };
        
        setActionHistory(prev => [historyItem, ...prev.slice(0, maxHistoryItems - 1)]);
      }
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [onAIAction, selectedText, maxHistoryItems]);

  /**
   * 切换收藏状态
   */
  const toggleFavorite = useCallback((featureId: string) => {
    const newFavorites = preferences.favoriteFeatures.includes(featureId)
      ? preferences.favoriteFeatures.filter((id: string) => id !== featureId)
      : [...preferences.favoriteFeatures, featureId];
    
    updatePreferences({ favoriteFeatures: newFavorites });
  }, [preferences.favoriteFeatures, updatePreferences]);

  /**
   * AI功能分类配置（使用useMemo优化）
   */
  const aiCategories = useMemo(() => [
    {
      id: 'writing',
      name: '写作助手',
      icon: PenToolIcon,
      description: '文本生成、续写和创作',
      color: 'blue',
      priority: 1,
      features: [
        {
          id: 'ai-continue',
          name: 'AI 续写',
          description: '基于上下文继续写作',
          icon: SparklesIcon,
          shortcut: 'Ctrl+Shift+C',
          action: () => handleAIAction('ai-continue'),
          usage: recentActions.filter(a => a === 'ai-continue').length,
          category: 'writing',
          tags: ['生成', '续写', '创作'],
          favorite: preferences.favoriteFeatures.includes('ai-continue'),
          estimatedTime: 3000,
          difficulty: 'easy'
        },
        {
          id: 'ai-rewrite',
          name: 'AI 改写',
          description: '改写和优化选中文本',
          icon: EditIcon,
          shortcut: 'Ctrl+Shift+R',
          action: () => handleAIAction('ai-rewrite', { text: selectedText }),
          disabled: !selectedText,
          usage: recentActions.filter(a => a === 'ai-rewrite').length,
          category: 'writing',
          tags: ['改写', '优化', '编辑'],
          favorite: preferences.favoriteFeatures.includes('ai-rewrite'),
          estimatedTime: 2500,
          difficulty: 'easy'
        }
      ]
    }
  ], [preferences, recentActions, selectedText, handleAIAction]);

  /**
   * 过滤功能（使用useMemo优化）
   */
  const filteredFeatures = useMemo(() => {
    let allFeatures = aiCategories.flatMap(cat => cat.features);
    
    if (searchQuery) {
      allFeatures = allFeatures.filter(feature => 
        feature.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        feature.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        feature.tags.some((tag: string) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }
    
    if (selectedFilter !== 'all') {
      if (selectedFilter === 'favorites') {
        allFeatures = allFeatures.filter(feature => feature.favorite);
      } else {
        allFeatures = allFeatures.filter(feature => feature.category === selectedFilter);
      }
    }
    
    return allFeatures;
  }, [searchQuery, selectedFilter, aiCategories]);

  /**
   * 渲染功能按钮（优化版）
   */
  const renderFeatureButton = useCallback((feature: any, index: number) => {
    const isRecent = recentActions.includes(feature.id);
    
    return (
      <OptimizedFeatureButton
        key={feature.id}
        feature={feature}
        isRecent={isRecent}
        isProcessing={isProcessing || isLoading}
        preferences={preferences}
        onToggleFavorite={toggleFavorite}
      />
    );
  }, [recentActions, isProcessing, isLoading, preferences, toggleFavorite]);

  /**
   * 渲染历史记录项
   */
  const renderHistoryItem = useCallback((item: any, index: number) => (
    <div
      key={item.id}
      className={`
        p-3 rounded-lg border text-xs mb-2
        ${item.success 
          ? 'bg-green-50 border-green-200 text-green-800' 
          : 'bg-red-50 border-red-200 text-red-800'
        }
      `}
    >
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center gap-2">
          {item.success ? (
            <CheckCircleIcon className="h-3 w-3" />
          ) : (
            <AlertCircleIcon className="h-3 w-3" />
          )}
          <span className="font-medium">{item.action}</span>
          <Badge variant="outline" className="text-xs">
            {item.category}
          </Badge>
        </div>
        <div className="flex items-center gap-2 text-xs opacity-75">
          {item.duration && (
            <span>{item.duration}ms</span>
          )}
          <ClockIcon className="h-3 w-3" />
          <span>{item.timestamp.toLocaleTimeString()}</span>
        </div>
      </div>
      {item.context && (
        <div className="text-xs opacity-75 mt-1">
          {item.context}
        </div>
      )}
    </div>
  ), []);

  // 清理函数
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // 移动端全屏覆盖样式
  const mobileOverlayStyle = isMobile && isOpen ? 'fixed inset-0 z-50 bg-white' : '';
  
  // 桌面端侧边面板样式
  const desktopPanelStyle = !isMobile ? `
    fixed top-0 ${position === 'right' ? 'right-0' : 'left-0'} h-full z-40
    transform transition-transform duration-300 ease-in-out
    ${isOpen ? 'translate-x-0' : position === 'right' ? 'translate-x-full' : '-translate-x-full'}
    shadow-xl border-l border-gray-200
  ` : '';

  // 最大化样式
  const maximizedStyle = isMaximized && !isMobile ? 'fixed inset-4 z-50 rounded-lg shadow-2xl' : '';

  return (
    <>
      {/* 移动端背景遮罩 */}
      {isMobile && isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40"
          onClick={onToggle}
        />
      )}
      
      {/* 面板主体 */}
      <div
        ref={panelRef}
        className={`
          bg-white flex flex-col
          ${isMobile ? mobileOverlayStyle : maximizedStyle || desktopPanelStyle}
          ${preferences.theme === 'dark' ? 'bg-gray-900 text-gray-100' : ''}
          ${className}
        `}
        style={{ 
          width: isMobile ? '100%' : isMaximized ? '100%' : width,
          height: isMaximized ? '100%' : undefined
        }}
        role="dialog"
        aria-label="AI 助手面板"
        aria-modal={isMobile}
      >
        {/* 面板头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center gap-2">
            <SparklesIcon className="h-5 w-5 text-blue-600" />
            <h2 className="font-semibold text-gray-900">AI 助手</h2>
            {selectedText && (
              <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                {selectedText.length} 字符
              </Badge>
            )}
            {(isProcessing || isLoading) && (
              <Loader2Icon className="h-4 w-4 animate-spin text-blue-600" />
            )}
          </div>
          <div className="flex items-center gap-1">
            {!isMobile && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMaximized(!isMaximized)}
                className="h-8 w-8 p-0"
                title={isMaximized ? "还原" : "最大化"}
              >
                {isMaximized ? (
                  <MinimizeIcon className="h-4 w-4" />
                ) : (
                  <MaximizeIcon className="h-4 w-4" />
                )}
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="h-8 w-8 p-0"
              title="设置"
            >
              <SettingsIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggle}
              className="h-8 w-8 p-0"
              title="关闭"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 处理状态 */}
        {(isProcessing || isLoading) && (
          <div className="p-4 bg-blue-50 border-b border-blue-100">
            <div className="flex items-center gap-3">
              <RefreshCwIcon className="h-4 w-4 animate-spin text-blue-600" />
              <div className="flex-1">
                <div className="font-medium text-blue-900">AI 处理中...</div>
                {processingStatus && (
                  <div className="text-xs text-blue-700">{processingStatus}</div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 搜索和过滤 */}
        {enableSearch && (
          <div className="p-4 border-b border-gray-100 space-y-3">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="搜索 AI 功能..."
                onChange={(e) => debouncedSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div className="flex items-center gap-2">
              <FilterIcon className="h-4 w-4 text-gray-400" />
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="text-xs border border-gray-200 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">全部</option>
                <option value="favorites">收藏</option>
                <option value="writing">写作助手</option>
                <option value="analysis">文档分析</option>
                <option value="language">语言工具</option>
                <option value="custom">自定义</option>
              </select>
            </div>
          </div>
        )}

        {/* 面板内容 */}
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="w-full grid grid-cols-4 m-4 mb-0">
              <TabsTrigger value="features" className="text-xs">功能</TabsTrigger>
              <TabsTrigger value="favorites" className="text-xs">收藏</TabsTrigger>
              <TabsTrigger value="history" className="text-xs">历史</TabsTrigger>
              {enableStats && <TabsTrigger value="stats" className="text-xs">统计</TabsTrigger>}
            </TabsList>
            
            <div className="flex-1 overflow-hidden">
              <TabsContent value="features" className="mt-0 h-full">
                <div className="h-full overflow-y-auto p-4">
                  {enableVirtualScroll && filteredFeatures.length > 50 ? (
                    <VirtualScroll
                      items={filteredFeatures}
                      itemHeight={preferences.compactMode ? 60 : 80}
                      containerHeight={400}
                      renderItem={renderFeatureButton}
                    />
                  ) : (
                    <div className="space-y-1">
                      {filteredFeatures.map(renderFeatureButton)}
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="favorites" className="mt-0 h-full">
                <div className="p-4 space-y-4 h-full overflow-y-auto">
                  <h3 className="font-medium text-sm text-gray-900 flex items-center gap-2">
                    <HeartIcon className="h-4 w-4 text-red-500" />
                    收藏的功能
                  </h3>
                  {preferences.favoriteFeatures.length > 0 ? (
                    <div className="space-y-1">
                      {aiCategories
                        .flatMap(cat => cat.features)
                        .filter(feature => feature.favorite)
                        .map(renderFeatureButton)}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <HeartIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">暂无收藏的功能</p>
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="history" className="mt-0 h-full">
                <div className="p-4 space-y-4 h-full overflow-hidden">
                  <h3 className="font-medium text-sm text-gray-900 flex items-center gap-2">
                    <HistoryIcon className="h-4 w-4" />
                    操作历史
                  </h3>
                  {actionHistory.length > 0 ? (
                    enableVirtualScroll ? (
                      <VirtualScroll
                        items={actionHistory}
                        itemHeight={80}
                        containerHeight={300}
                        renderItem={renderHistoryItem}
                      />
                    ) : (
                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {actionHistory.map(renderHistoryItem)}
                      </div>
                    )
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <HistoryIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">暂无操作历史</p>
                    </div>
                  )}
                </div>
              </TabsContent>
              
              {enableStats && (
                <TabsContent value="stats" className="mt-0 h-full">
                  <div className="p-4 space-y-4 h-full overflow-y-auto">
                    <h3 className="font-medium text-sm text-gray-900 flex items-center gap-2">
                      <BarChartIcon className="h-4 w-4" />
                      使用统计
                    </h3>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <div className="text-lg font-bold text-blue-600">{actionHistory.length}</div>
                        <div className="text-xs text-blue-700">总操作数</div>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="text-lg font-bold text-green-600">
                          {actionHistory.length > 0 
                            ? ((actionHistory.filter(item => item.success).length / actionHistory.length) * 100).toFixed(1)
                            : '0'
                          }%
                        </div>
                        <div className="text-xs text-green-700">成功率</div>
                      </div>
                      <div className="text-center p-3 bg-purple-50 rounded-lg">
                        <div className="text-lg font-bold text-purple-600">{preferences.favoriteFeatures.length}</div>
                        <div className="text-xs text-purple-700">收藏功能</div>
                      </div>
                      <div className="text-center p-3 bg-orange-50 rounded-lg">
                        <div className="text-lg font-bold text-orange-600">
                          {actionHistory.filter(item => item.duration).length > 0
                            ? Math.round(
                                actionHistory
                                  .filter(item => item.duration)
                                  .reduce((sum, item) => sum + (item.duration || 0), 0) / 
                                actionHistory.filter(item => item.duration).length
                              )
                            : 0
                          }ms
                        </div>
                        <div className="text-xs text-orange-700">平均响应</div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              )}
            </div>
          </Tabs>
        </div>

        {/* 面板底部 */}
        <div className="p-3 border-t border-gray-200 bg-gray-50">
          <div className="text-xs text-gray-600 text-center">
            {selectedText ? (
              <div className="flex items-center justify-center gap-2">
                <CheckCircleIcon className="h-3 w-3 text-green-600" />
                <span>已选择 {selectedText.length} 个字符</span>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2">
                <HelpCircleIcon className="h-3 w-3 text-gray-400" />
                <span>选择文本以启用更多功能</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default OptimizedAIAssistantPanel;