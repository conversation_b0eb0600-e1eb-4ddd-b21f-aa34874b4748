import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { VersionHistoryService } from '@/lib/services/version-history';
import { DocumentService } from '@/lib/services/document-service';

interface RouteParams {
  params: {
    id: string;
    version: string;
  };
}

/**
 * 获取特定版本
 * GET /api/documents/[id]/versions/[version]
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const documentId = params.id;
    const versionNumber = parseInt(params.version);

    // 验证文档访问权限
    const document = await DocumentService.validateDocumentAccess(
      documentId,
      session.user.id
    );

    if (!document) {
      return NextResponse.json(
        { error: '文档不存在或无权限访问' },
        { status: 404 }
      );
    }

    // 获取特定版本
    const version = await VersionHistoryService.getVersion(documentId, versionNumber);

    if (!version) {
      return NextResponse.json(
        { error: '版本不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json(version);

  } catch (error) {
    console.error('获取版本失败:', error);
    return NextResponse.json(
      { error: '获取版本失败' },
      { status: 500 }
    );
  }
}

/**
 * 恢复到指定版本
 * POST /api/documents/[id]/versions/[version]/restore
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const documentId = params.id;
    const versionNumber = parseInt(params.version);

    // 验证文档访问权限
    const document = await DocumentService.validateDocumentAccess(
      documentId,
      session.user.id
    );

    if (!document) {
      return NextResponse.json(
        { error: '文档不存在或无权限访问' },
        { status: 404 }
      );
    }

    // 恢复到指定版本
    const restoredVersion = await VersionHistoryService.restoreToVersion(
      documentId,
      versionNumber,
      session.user.id
    );

    if (!restoredVersion) {
      return NextResponse.json(
        { error: '版本不存在或恢复失败' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      version: restoredVersion,
      message: `已恢复到版本 ${versionNumber}`
    });

  } catch (error) {
    console.error('恢复版本失败:', error);
    return NextResponse.json(
      { error: '恢复版本失败' },
      { status: 500 }
    );
  }
}