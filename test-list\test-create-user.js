const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function addPasswordToDemo() {
  const prisma = new PrismaClient();

  try {
    const hashedPassword = await bcrypt.hash('demo123', 12);

    const user = await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: { password: hashedPassword }
    });

    console.log('✅ 演示用户密码已设置');
    console.log('邮箱: <EMAIL>');
    console.log('密码: demo123');

  } catch (error) {
    console.error('❌ 设置密码失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

addPasswordToDemo();
