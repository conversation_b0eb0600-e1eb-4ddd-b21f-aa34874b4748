'use client';

import React, { useState } from 'react';
import { SyncConflict } from '@/types/sync';
import { 
  AlertTriangle, 
  FileText, 
  Clock, 
  User,
  Check,
  X,
  GitMerge
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConflictResolverProps {
  conflict: SyncConflict;
  onResolve: (resolution: 'local' | 'remote' | 'merge', mergedData?: any) => Promise<void>;
  onCancel?: () => void;
  className?: string;
}

/**
 * 同步冲突解决组件
 * 允许用户选择如何解决文档同步冲突
 */
export function ConflictResolver({ 
  conflict, 
  onResolve, 
  onCancel,
  className 
}: ConflictResolverProps) {
  const [selectedResolution, setSelectedResolution] = useState<'local' | 'remote' | 'merge' | null>(null);
  const [isResolving, setIsResolving] = useState(false);
  const [showDiff, setShowDiff] = useState(false);

  const handleResolve = async () => {
    if (!selectedResolution) return;

    setIsResolving(true);
    try {
      await onResolve(selectedResolution);
    } catch (error) {
      console.error('解决冲突失败:', error);
    } finally {
      setIsResolving(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getConflictTypeText = (type: string) => {
    switch (type) {
      case 'content':
        return '内容冲突';
      case 'title':
        return '标题冲突';
      case 'metadata':
        return '元数据冲突';
      default:
        return '未知冲突';
    }
  };

  const localDoc = conflict.localVersion;
  const remoteDoc = conflict.remoteVersion;

  return (
    <div className={cn('bg-white border border-red-200 rounded-lg shadow-lg', className)}>
      {/* 冲突标题 */}
      <div className="flex items-center gap-3 p-4 bg-red-50 border-b border-red-200">
        <AlertTriangle className="h-5 w-5 text-red-500" />
        <div className="flex-1">
          <h3 className="font-medium text-red-900">
            检测到同步冲突
          </h3>
          <p className="text-sm text-red-700">
            {getConflictTypeText(conflict.conflictType)} - 需要选择保留哪个版本
          </p>
        </div>
        <div className="text-xs text-red-600">
          {formatDate(conflict.timestamp)}
        </div>
      </div>

      {/* 文档信息 */}
      <div className="p-4 border-b">
        <div className="flex items-center gap-2 mb-2">
          <FileText className="h-4 w-4 text-gray-500" />
          <span className="font-medium text-gray-900">
            {localDoc.title || remoteDoc.title}
          </span>
        </div>
        <div className="text-sm text-gray-600">
          文档ID: {conflict.documentId}
        </div>
      </div>

      {/* 版本选择 */}
      <div className="p-4 space-y-4">
        <div className="text-sm font-medium text-gray-700 mb-3">
          选择要保留的版本：
        </div>

        {/* 本地版本 */}
        <div
          className={cn(
            'border rounded-lg p-3 cursor-pointer transition-colors',
            selectedResolution === 'local'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
          )}
          onClick={() => setSelectedResolution('local')}
        >
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <div className={cn(
                'w-4 h-4 rounded-full border-2',
                selectedResolution === 'local'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300'
              )}>
                {selectedResolution === 'local' && (
                  <Check className="h-3 w-3 text-white" />
                )}
              </div>
              <span className="font-medium text-gray-900">本地版本</span>
            </div>
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Clock className="h-3 w-3" />
              {formatDate(new Date(localDoc.updatedAt))}
            </div>
          </div>
          <div className="text-sm text-gray-600 ml-6">
            <div>标题: {localDoc.title}</div>
            <div>字数: {localDoc.metadata?.wordCount || 0}</div>
          </div>
        </div>

        {/* 远程版本 */}
        <div
          className={cn(
            'border rounded-lg p-3 cursor-pointer transition-colors',
            selectedResolution === 'remote'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
          )}
          onClick={() => setSelectedResolution('remote')}
        >
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <div className={cn(
                'w-4 h-4 rounded-full border-2',
                selectedResolution === 'remote'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300'
              )}>
                {selectedResolution === 'remote' && (
                  <Check className="h-3 w-3 text-white" />
                )}
              </div>
              <span className="font-medium text-gray-900">远程版本</span>
            </div>
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Clock className="h-3 w-3" />
              {formatDate(new Date(remoteDoc.updatedAt))}
            </div>
          </div>
          <div className="text-sm text-gray-600 ml-6">
            <div>标题: {remoteDoc.title}</div>
            <div>字数: {remoteDoc.metadata?.wordCount || 0}</div>
          </div>
        </div>

        {/* 手动合并选项 */}
        <div
          className={cn(
            'border rounded-lg p-3 cursor-pointer transition-colors',
            selectedResolution === 'merge'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
          )}
          onClick={() => setSelectedResolution('merge')}
        >
          <div className="flex items-center gap-2">
            <div className={cn(
              'w-4 h-4 rounded-full border-2',
              selectedResolution === 'merge'
                ? 'border-blue-500 bg-blue-500'
                : 'border-gray-300'
            )}>
              {selectedResolution === 'merge' && (
                <Check className="h-3 w-3 text-white" />
              )}
            </div>
            <GitMerge className="h-4 w-4 text-gray-500" />
            <span className="font-medium text-gray-900">手动合并</span>
          </div>
          <div className="text-sm text-gray-600 ml-6 mt-1">
            在编辑器中手动合并两个版本的内容
          </div>
        </div>

        {/* 显示差异按钮 */}
        <button
          onClick={() => setShowDiff(!showDiff)}
          className="text-sm text-blue-600 hover:text-blue-800 underline"
        >
          {showDiff ? '隐藏差异' : '显示差异'}
        </button>

        {/* 差异显示 */}
        {showDiff && (
          <div className="border rounded-lg p-3 bg-gray-50">
            <div className="text-sm font-medium text-gray-700 mb-2">
              版本差异：
            </div>
            <div className="space-y-2 text-sm">
              {localDoc.title !== remoteDoc.title && (
                <div>
                  <span className="text-red-600">- 标题: {localDoc.title}</span>
                  <br />
                  <span className="text-green-600">+ 标题: {remoteDoc.title}</span>
                </div>
              )}
              <div className="text-gray-600">
                内容长度差异: {
                  (localDoc.metadata?.characterCount || 0) - 
                  (remoteDoc.metadata?.characterCount || 0)
                } 字符
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-end gap-3 p-4 bg-gray-50 border-t">
        {onCancel && (
          <button
            onClick={onCancel}
            className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800"
            disabled={isResolving}
          >
            取消
          </button>
        )}
        <button
          onClick={handleResolve}
          disabled={!selectedResolution || isResolving}
          className={cn(
            'px-4 py-2 text-sm font-medium rounded-md',
            selectedResolution && !isResolving
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          )}
        >
          {isResolving ? '解决中...' : '解决冲突'}
        </button>
      </div>
    </div>
  );
}

/**
 * 冲突列表组件
 */
interface ConflictListProps {
  conflicts: SyncConflict[];
  onResolveConflict: (conflictId: string, resolution: 'local' | 'remote' | 'merge', mergedData?: any) => Promise<void>;
  className?: string;
}

export function ConflictList({ 
  conflicts, 
  onResolveConflict, 
  className 
}: ConflictListProps) {
  const [expandedConflict, setExpandedConflict] = useState<string | null>(null);

  if (conflicts.length === 0) {
    return null;
  }

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center gap-2 text-red-600">
        <AlertTriangle className="h-5 w-5" />
        <span className="font-medium">
          发现 {conflicts.length} 个同步冲突
        </span>
      </div>

      {conflicts.map((conflict) => (
        <div key={conflict.id}>
          {expandedConflict === conflict.id ? (
            <ConflictResolver
              conflict={conflict}
              onResolve={async (resolution, mergedData) => {
                await onResolveConflict(conflict.id, resolution, mergedData);
                setExpandedConflict(null);
              }}
              onCancel={() => setExpandedConflict(null)}
            />
          ) : (
            <div
              className="border border-red-200 rounded-lg p-3 cursor-pointer hover:bg-red-50"
              onClick={() => setExpandedConflict(conflict.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <span className="font-medium text-gray-900">
                    {conflict.localVersion.title || '未命名文档'}
                  </span>
                  <span className="text-sm text-red-600">
                    ({getConflictTypeText(conflict.conflictType)})
                  </span>
                </div>
                <span className="text-xs text-gray-500">
                  点击解决
                </span>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );

  function getConflictTypeText(type: string) {
    switch (type) {
      case 'content':
        return '内容冲突';
      case 'title':
        return '标题冲突';
      case 'metadata':
        return '元数据冲突';
      default:
        return '未知冲突';
    }
  }
}