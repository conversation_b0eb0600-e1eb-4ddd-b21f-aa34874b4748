import bcrypt from "bcryptjs";

/**
 * 密码加密的盐值轮数
 * 数值越高安全性越高，但计算时间也越长
 */
const SALT_ROUNDS = 12;

/**
 * 加密密码
 * 使用 bcrypt 算法对密码进行哈希加密
 * @param password 原始密码
 * @returns 加密后的密码哈希值
 */
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, SALT_ROUNDS);
}

/**
 * 验证密码
 * 比较原始密码与加密后的哈希值是否匹配
 * @param password 原始密码
 * @param hashedPassword 加密后的密码哈希值
 * @returns 密码是否匹配
 */
export async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

/**
 * 验证密码强度
 * 检查密码是否符合安全要求
 * @param password 待验证的密码
 * @returns 验证结果，包含是否有效和错误信息
 */
export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 检查密码长度
  if (password.length < 6) {
    errors.push("密码长度至少需要6个字符");
  }

  if (password.length > 128) {
    errors.push("密码长度不能超过128个字符");
  }

  // 检查是否包含小写字母
  if (!/[a-z]/.test(password)) {
    errors.push("密码必须包含至少一个小写字母");
  }

  // 检查是否包含大写字母
  if (!/[A-Z]/.test(password)) {
    errors.push("密码必须包含至少一个大写字母");
  }

  // 检查是否包含数字
  if (!/\d/.test(password)) {
    errors.push("密码必须包含至少一个数字");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}