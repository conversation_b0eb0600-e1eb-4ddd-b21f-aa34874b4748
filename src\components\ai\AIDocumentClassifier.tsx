/**
 * AI 文档分类组件
 * 提供文档自动分类和分类管理功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  Tag, 
  FolderOpen, 
  Sparkles, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { AIProcessingIndicator } from './AIProcessingIndicator';
import type { 
  DocumentClassification, 
  ClassificationOptions,
  BatchClassificationResult 
} from '@/types/ai-classification.types';

interface AIDocumentClassifierProps {
  /** 文档ID（单个分类时使用） */
  documentId?: string;
  /** 文档ID列表（批量分类时使用） */
  documentIds?: string[];
  /** 分类完成回调 */
  onClassificationComplete?: (result: DocumentClassification | BatchClassificationResult) => void;
  /** 是否显示详细选项 */
  showAdvancedOptions?: boolean;
}

/**
 * AI 文档分类组件
 */
export function AIDocumentClassifier({
  documentId,
  documentIds,
  onClassificationComplete,
  showAdvancedOptions = false
}: AIDocumentClassifierProps) {
  const { toast } = useToast();
  const [isClassifying, setIsClassifying] = useState(false);
  const [classification, setClassification] = useState<DocumentClassification | null>(null);
  const [batchResult, setBatchResult] = useState<BatchClassificationResult | null>(null);
  const [suggestedCategories, setSuggestedCategories] = useState<string[]>([]);
  const [options, setOptions] = useState<ClassificationOptions>({
    enableAutoClassification: true,
    enableFolderSuggestions: true,
    enableRelatedDocuments: true,
    minConfidenceThreshold: 0.7,
    maxSuggestions: 10,
    analysisDepth: 'detailed'
  });

  const isBatchMode = documentIds && documentIds.length > 1;

  // 加载建议分类
  useEffect(() => {
    loadSuggestedCategories();
  }, []);

  /**
   * 加载建议分类列表
   */
  const loadSuggestedCategories = async () => {
    try {
      const response = await fetch('/api/ai/classify-document/categories');
      if (response.ok) {
        const data = await response.json();
        setSuggestedCategories(data.data || []);
      }
    } catch (error) {
      console.error('加载建议分类失败:', error);
    }
  };

  /**
   * 执行文档分类
   */
  const handleClassify = async () => {
    if (!documentId && !documentIds?.length) {
      toast({
        title: '错误',
        description: '请选择要分类的文档',
        variant: 'destructive'
      });
      return;
    }

    setIsClassifying(true);
    setClassification(null);
    setBatchResult(null);

    try {
      if (isBatchMode) {
        // 批量分类
        const response = await fetch('/api/ai/classify-document', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            documentIds,
            options
          })
        });

        if (!response.ok) {
          throw new Error('批量分类请求失败');
        }

        const data = await response.json();
        setBatchResult(data.data);
        onClassificationComplete?.(data.data);

        toast({
          title: '批量分类完成',
          description: `成功分类 ${data.data.processedDocuments} 个文档`
        });
      } else {
        // 单个分类
        const response = await fetch('/api/ai/classify-document', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            documentId,
            options
          })
        });

        if (!response.ok) {
          throw new Error('文档分类请求失败');
        }

        const data = await response.json();
        setClassification(data.data);
        onClassificationComplete?.(data.data);

        toast({
          title: '分类完成',
          description: `文档已分类为: ${data.data.primaryCategory}`
        });
      }
    } catch (error) {
      console.error('文档分类失败:', error);
      toast({
        title: '分类失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      });
    } finally {
      setIsClassifying(false);
    }
  };

  /**
   * 渲染分类结果
   */
  const renderClassificationResult = () => {
    if (!classification) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            分类结果
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-600">主要分类</label>
            <div className="mt-1">
              <Badge variant="default" className="text-sm">
                {classification.primaryCategory}
              </Badge>
            </div>
          </div>

          {classification.secondaryCategories.length > 0 && (
            <div>
              <label className="text-sm font-medium text-gray-600">次要分类</label>
              <div className="mt-1 flex flex-wrap gap-2">
                {classification.secondaryCategories.map((category, index) => (
                  <Badge key={index} variant="secondary" className="text-sm">
                    {category}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <div>
            <label className="text-sm font-medium text-gray-600">置信度</label>
            <div className="mt-1 flex items-center gap-2">
              <Progress value={classification.confidence * 100} className="flex-1" />
              <span className="text-sm text-gray-500">
                {Math.round(classification.confidence * 100)}%
              </span>
            </div>
          </div>

          {classification.suggestedTags.length > 0 && (
            <div>
              <label className="text-sm font-medium text-gray-600">建议标签</label>
              <div className="mt-1 flex flex-wrap gap-2">
                {classification.suggestedTags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-sm">
                    <Tag className="h-3 w-3 mr-1" />
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <div>
            <label className="text-sm font-medium text-gray-600">分类原因</label>
            <p className="mt-1 text-sm text-gray-700">{classification.reasoning}</p>
          </div>
        </CardContent>
      </Card>
    );
  };

  /**
   * 渲染批量分类结果
   */
  const renderBatchResult = () => {
    if (!batchResult) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            批量分类结果
          </CardTitle>
          <CardDescription>
            处理了 {batchResult.processedDocuments} / {batchResult.totalDocuments} 个文档
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <Progress 
              value={(batchResult.processedDocuments / batchResult.totalDocuments) * 100} 
              className="flex-1" 
            />
            <span className="text-sm text-gray-500">
              {Math.round((batchResult.processedDocuments / batchResult.totalDocuments) * 100)}%
            </span>
          </div>

          {batchResult.errors.length > 0 && (
            <div className="p-3 bg-red-50 rounded-lg">
              <div className="flex items-center gap-2 text-red-700 mb-2">
                <AlertCircle className="h-4 w-4" />
                <span className="font-medium">处理错误</span>
              </div>
              <ul className="text-sm text-red-600 space-y-1">
                {batchResult.errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="space-y-2">
            <h4 className="font-medium">分类统计</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">成功分类:</span>
                <span className="ml-2 font-medium">{batchResult.classifications.length}</span>
              </div>
              <div>
                <span className="text-gray-600">文件夹建议:</span>
                <span className="ml-2 font-medium">{batchResult.folderSuggestions.length}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  /**
   * 渲染高级选项
   */
  const renderAdvancedOptions = () => {
    if (!showAdvancedOptions) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">分类选项</CardTitle>
          <CardDescription>自定义AI分类的行为和参数</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.enableAutoClassification}
                onChange={(e) => setOptions(prev => ({
                  ...prev,
                  enableAutoClassification: e.target.checked
                }))}
                className="rounded"
              />
              <span className="text-sm">启用自动分类</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.enableFolderSuggestions}
                onChange={(e) => setOptions(prev => ({
                  ...prev,
                  enableFolderSuggestions: e.target.checked
                }))}
                className="rounded"
              />
              <span className="text-sm">文件夹建议</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.enableRelatedDocuments}
                onChange={(e) => setOptions(prev => ({
                  ...prev,
                  enableRelatedDocuments: e.target.checked
                }))}
                className="rounded"
              />
              <span className="text-sm">相关文档推荐</span>
            </label>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">最小置信度阈值</label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={options.minConfidenceThreshold}
              onChange={(e) => setOptions(prev => ({
                ...prev,
                minConfidenceThreshold: parseFloat(e.target.value)
              }))}
              className="w-full"
            />
            <div className="text-xs text-gray-500">
              当前值: {Math.round(options.minConfidenceThreshold * 100)}%
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">分析深度</label>
            <select
              value={options.analysisDepth}
              onChange={(e) => setOptions(prev => ({
                ...prev,
                analysisDepth: e.target.value as 'basic' | 'detailed' | 'comprehensive'
              }))}
              className="w-full p-2 border rounded-md"
            >
              <option value="basic">基础分析</option>
              <option value="detailed">详细分析</option>
              <option value="comprehensive">全面分析</option>
            </select>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* 主要操作区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI 文档分类
          </CardTitle>
          <CardDescription>
            {isBatchMode 
              ? `使用AI智能分析和分类 ${documentIds?.length} 个文档`
              : '使用AI智能分析和分类文档内容'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button
              onClick={handleClassify}
              disabled={isClassifying}
              className="flex items-center gap-2"
            >
              {isClassifying ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Sparkles className="h-4 w-4" />
              )}
              {isClassifying ? '分类中...' : '开始分类'}
            </Button>

            <Button
              variant="outline"
              onClick={loadSuggestedCategories}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              刷新分类
            </Button>
          </div>

          {/* AI处理状态指示器 */}
          {isClassifying && (
            <div className="mt-4">
              <AIProcessingIndicator
                processingId={`classify_${documentId || 'batch'}_${Date.now()}`}
                showProgress={true}
                showTokenCount={true}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* 高级选项 */}
      {renderAdvancedOptions()}

      {/* 结果展示 */}
      <Tabs defaultValue="result" className="w-full">
        <TabsList>
          <TabsTrigger value="result">分类结果</TabsTrigger>
          <TabsTrigger value="categories">建议分类</TabsTrigger>
        </TabsList>

        <TabsContent value="result" className="space-y-4">
          {isBatchMode ? renderBatchResult() : renderClassificationResult()}
        </TabsContent>

        <TabsContent value="categories">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5" />
                建议分类
              </CardTitle>
              <CardDescription>基于您的文档内容生成的智能分类建议</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {suggestedCategories.map((category, index) => (
                  <Badge key={index} variant="outline" className="text-sm">
                    {category}
                  </Badge>
                ))}
              </div>
              {suggestedCategories.length === 0 && (
                <p className="text-sm text-gray-500">暂无建议分类</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}