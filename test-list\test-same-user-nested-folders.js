/**
 * 同一用户在不同父文件夹下创建同名子文件夹测试用例
 * 
 * 文件作用：
 * 1. 测试同一用户是否可以在不同的父文件夹下创建同名的子文件夹
 * 2. 验证文件夹层级结构的唯一性约束
 * 3. 确认数据库设计对嵌套文件夹的处理
 * 
 * 测试场景：
 * - 用户在根目录创建 "ParentA" 和 "ParentB" 两个文件夹
 * - 在 "ParentA" 下创建子文件夹 "test1234"
 * - 在 "ParentB" 下也创建子文件夹 "test1234"
 * - 验证是否都能成功创建
 * 
 * 预期结果：
 * - 应该能够成功创建，因为它们在不同的父文件夹下
 * - 文件夹的唯一性应该基于 (userId, name, parentId) 组合
 * 
 * 实际应用场景：
 * - 用户在 "工作项目" 下创建 "文档" 文件夹
 * - 用户在 "个人笔记" 下也创建 "文档" 文件夹
 * - 这是合理的需求，应该被允许
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * 创建文件夹的辅助函数
 * @param {string} name - 文件夹名称
 * @param {string} userId - 用户ID
 * @param {string|null} parentId - 父文件夹ID
 * @returns {Promise<Object>} 创建结果
 */
async function createFolder(name, userId, parentId = null) {
  try {
    const parentInfo = parentId ? `父文件夹ID: ${parentId.slice(-8)}...` : '根目录';
    console.log(`📁 创建文件夹 "${name}" (${parentInfo})`);
    
    const folder = await prisma.folder.create({
      data: {
        name: name,
        userId: userId,
        parentId: parentId,
        isDeleted: false
      }
    });
    
    console.log(`   ✅ 创建成功! ID: ${folder.id.slice(-8)}...`);
    return { success: true, folder: folder };
    
  } catch (error) {
    console.log(`   ❌ 创建失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * 显示文件夹树结构
 * @param {string} userId - 用户ID
 */
async function showFolderTree(userId) {
  console.log('\n🌳 当前文件夹树结构:');
  
  // 获取所有文件夹
  const allFolders = await prisma.folder.findMany({
    where: { 
      userId: userId,
      isDeleted: false 
    },
    orderBy: { createdAt: 'asc' }
  });
  
  // 构建树结构
  const rootFolders = allFolders.filter(f => !f.parentId);
  const childFolders = allFolders.filter(f => f.parentId);
  
  // 显示根文件夹
  rootFolders.forEach(folder => {
    console.log(`📁 ${folder.name} (ID: ${folder.id.slice(-8)}...)`);
    
    // 显示子文件夹
    const children = childFolders.filter(f => f.parentId === folder.id);
    children.forEach(child => {
      console.log(`   └── 📁 ${child.name} (ID: ${child.id.slice(-8)}...)`);
    });
  });
  
  console.log(`📊 总计: ${rootFolders.length} 个根文件夹, ${childFolders.length} 个子文件夹`);
}

/**
 * 检查同名文件夹的分布情况
 * @param {string} userId - 用户ID
 * @param {string} folderName - 要检查的文件夹名称
 */
async function analyzeSameNameFolders(userId, folderName) {
  console.log(`\n🔍 分析同名文件夹 "${folderName}" 的分布:`);
  
  const sameNameFolders = await prisma.folder.findMany({
    where: {
      userId: userId,
      name: folderName,
      isDeleted: false
    },
    include: {
      parent: {
        select: { name: true, id: true }
      }
    },
    orderBy: { createdAt: 'asc' }
  });
  
  console.log(`📊 找到 ${sameNameFolders.length} 个名为 "${folderName}" 的文件夹:`);
  
  sameNameFolders.forEach((folder, index) => {
    const location = folder.parent 
      ? `在 "${folder.parent.name}" 文件夹下`
      : '在根目录下';
    console.log(`   ${index + 1}. ID: ${folder.id.slice(-8)}... ${location}`);
  });
  
  // 检查是否有重复的 (parentId, name) 组合
  const locationGroups = {};
  sameNameFolders.forEach(folder => {
    const key = folder.parentId || 'root';
    if (!locationGroups[key]) {
      locationGroups[key] = [];
    }
    locationGroups[key].push(folder);
  });
  
  const duplicateLocations = Object.entries(locationGroups).filter(([key, folders]) => folders.length > 1);
  
  if (duplicateLocations.length > 0) {
    console.log(`⚠️  发现重复位置:`);
    duplicateLocations.forEach(([parentId, folders]) => {
      const location = parentId === 'root' ? '根目录' : `父文件夹 ${parentId.slice(-8)}...`;
      console.log(`   ${location} 下有 ${folders.length} 个同名文件夹`);
    });
  } else {
    console.log(`✅ 所有同名文件夹都在不同的位置，没有冲突`);
  }
}

/**
 * 主测试函数：同一用户嵌套文件夹同名测试
 */
async function testSameUserNestedFolders() {
  console.log('🚀 开始同一用户嵌套文件夹同名测试');
  console.log('=' .repeat(60));
  
  const createdFolders = []; // 记录创建的文件夹，用于清理
  
  try {
    // 步骤1: 获取测试用户
    console.log('\n👤 步骤1: 获取测试用户');
    const user = await prisma.user.findFirst();
    if (!user) {
      console.log('❌ 没有测试用户，请先运行 npm run db:seed');
      return;
    }
    console.log(`📝 使用用户: ${user.name || user.email} (ID: ${user.id.slice(-8)}...)`);
    
    // 步骤2: 创建两个父文件夹
    console.log('\n📁 步骤2: 创建父文件夹');
    const parentA = await createFolder('ParentA', user.id);
    const parentB = await createFolder('ParentB', user.id);
    
    if (parentA.success) createdFolders.push(parentA.folder);
    if (parentB.success) createdFolders.push(parentB.folder);
    
    if (!parentA.success || !parentB.success) {
      console.log('❌ 父文件夹创建失败，无法继续测试');
      return;
    }
    
    // 步骤3: 在不同父文件夹下创建同名子文件夹
    console.log('\n📁 步骤3: 在不同父文件夹下创建同名子文件夹');
    const testSubFolderName = 'test1234';
    
    console.log(`\n--- 在 ParentA 下创建 "${testSubFolderName}" ---`);
    const childA = await createFolder(testSubFolderName, user.id, parentA.folder.id);
    
    console.log(`\n--- 在 ParentB 下创建 "${testSubFolderName}" ---`);
    const childB = await createFolder(testSubFolderName, user.id, parentB.folder.id);
    
    if (childA.success) createdFolders.push(childA.folder);
    if (childB.success) createdFolders.push(childB.folder);
    
    // 步骤4: 显示文件夹结构
    console.log('\n🌳 步骤4: 显示完整的文件夹结构');
    await showFolderTree(user.id);
    
    // 步骤5: 分析同名文件夹
    console.log('\n🔍 步骤5: 分析同名文件夹分布');
    await analyzeSameNameFolders(user.id, testSubFolderName);
    
    // 步骤6: 测试在同一父文件夹下创建重复名称
    console.log('\n🧪 步骤6: 测试在同一父文件夹下创建重复名称');
    console.log(`尝试在 ParentA 下再次创建 "${testSubFolderName}"`);
    const duplicateChild = await createFolder(testSubFolderName, user.id, parentA.folder.id);
    
    if (duplicateChild.success) {
      createdFolders.push(duplicateChild.folder);
      console.log(`⚠️  同一父文件夹下可以创建重复名称的子文件夹`);
    } else {
      console.log(`✅ 同一父文件夹下不能创建重复名称的子文件夹`);
    }
    
    // 步骤7: 最终分析
    await analyzeSameNameFolders(user.id, testSubFolderName);
    
    // 测试结论
    console.log('\n🎯 测试结论:');
    if (childA.success && childB.success) {
      console.log(`   ✅ 同一用户可以在不同父文件夹下创建同名子文件夹`);
      console.log(`   ✅ 文件夹层级结构提供了天然的命名空间隔离`);
      console.log(`   📝 这是合理的设计，符合用户的使用习惯`);
    } else {
      console.log(`   ❌ 存在限制，无法在不同父文件夹下创建同名子文件夹`);
    }
    
    if (duplicateChild.success) {
      console.log(`   ⚠️  当前设计允许同一父文件夹下有重复名称的子文件夹`);
      console.log(`   💡 可能需要添加 UNIQUE(userId, name, parentId) 约束`);
    } else {
      console.log(`   ✅ 同一父文件夹下不允许重复名称，设计合理`);
    }
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
    console.error('   错误详情:', error);
  } finally {
    // 清理测试数据
    console.log('\n🧹 清理测试数据...');
    
    if (createdFolders.length > 0) {
      // 按创建顺序倒序删除（先删除子文件夹，再删除父文件夹）
      const sortedFolders = createdFolders.reverse();
      
      for (const folder of sortedFolders) {
        try {
          await prisma.folder.delete({ where: { id: folder.id } });
          console.log(`   ✅ 删除文件夹: ${folder.name} (${folder.id.slice(-8)}...)`);
        } catch (error) {
          console.log(`   ❌ 删除失败: ${folder.name} - ${error.message}`);
        }
      }
    }
    
    await prisma.$disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

// 执行测试
console.log('🚀 开始执行同一用户嵌套文件夹同名测试...');
testSameUserNestedFolders();
