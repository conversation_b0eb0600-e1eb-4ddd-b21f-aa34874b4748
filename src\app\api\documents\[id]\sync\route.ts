import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';
import { DocumentService } from '@/lib/services/document-service';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * 获取单个文档的远程版本（用于冲突检测）
 * GET /api/documents/[id]/sync
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const documentId = params.id;

    // 验证文档访问权限
    const document = await DocumentService.validateDocumentAccess(
      documentId,
      session.user.id
    );

    if (!document) {
      return NextResponse.json(
        { error: '文档不存在或无权限访问' },
        { status: 404 }
      );
    }

    // 获取完整的文档信息
    const fullDocument = await prisma.document.findUnique({
      where: { id: documentId },
      include: {
        folder: {
          select: {
            id: true,
            name: true,
          },
        },
        history: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1, // 只获取最新的历史记录
        },
      },
    });

    if (!fullDocument) {
      return NextResponse.json(
        { error: '文档不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      id: fullDocument.id,
      title: fullDocument.title,
      content: fullDocument.content,
      folderId: fullDocument.folderId,
      userId: fullDocument.userId,
      wordCount: fullDocument.wordCount,
      charCount: fullDocument.charCount,
      isPublic: fullDocument.isPublic,
      shareToken: fullDocument.shareToken,
      lastSyncAt: fullDocument.lastSyncAt,
      createdAt: fullDocument.createdAt,
      updatedAt: fullDocument.updatedAt,
      folder: fullDocument.folder,
      metadata: {
        wordCount: fullDocument.wordCount,
        characterCount: fullDocument.charCount,
        tags: [], // TODO: 实现标签系统
        isPublic: fullDocument.isPublic,
        shareToken: fullDocument.shareToken,
      },
      lastVersion: fullDocument.history[0]?.version || 0,
    });

  } catch (error) {
    console.error('获取文档同步信息失败:', error);
    return NextResponse.json(
      { error: '获取文档同步信息失败' },
      { status: 500 }
    );
  }
}

/**
 * 强制同步单个文档
 * POST /api/documents/[id]/sync
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const documentId = params.id;
    const { action, data } = await request.json();

    // 验证文档访问权限
    const document = await DocumentService.validateDocumentAccess(
      documentId,
      session.user.id
    );

    if (!document) {
      return NextResponse.json(
        { error: '文档不存在或无权限访问' },
        { status: 404 }
      );
    }

    switch (action) {
      case 'force_upload':
        return await handleForceUpload(documentId, data, session.user.id);
      
      case 'force_download':
        return await handleForceDownload(documentId, session.user.id);
      
      case 'resolve_conflict':
        return await handleResolveConflict(documentId, data, session.user.id);
      
      default:
        return NextResponse.json(
          { error: '不支持的同步操作' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('文档同步操作失败:', error);
    return NextResponse.json(
      { error: '文档同步操作失败' },
      { status: 500 }
    );
  }
}

/**
 * 强制上传文档
 */
async function handleForceUpload(documentId: string, data: any, userId: string) {
  try {
    // 计算文本统计
    const stats = DocumentService.calculateTextStats(data.content || '');

    // 更新文档
    const updatedDocument = await prisma.document.update({
      where: { id: documentId },
      data: {
        title: data.title,
        content: data.content,
        folderId: data.folderId,
        wordCount: stats.wordCount,
        charCount: stats.charCount,
        isPublic: data.metadata?.isPublic || false,
        shareToken: data.metadata?.shareToken,
        lastSyncAt: new Date(),
      },
      include: {
        folder: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // 创建历史记录
    await DocumentService.createDocumentHistory(
      documentId,
      data.content,
      'user'
    );

    return NextResponse.json({
      success: true,
      document: updatedDocument,
      message: '文档强制上传成功'
    });

  } catch (error) {
    throw new Error(`强制上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 强制下载文档（获取最新的远程版本）
 */
async function handleForceDownload(documentId: string, userId: string) {
  try {
    const document = await prisma.document.findUnique({
      where: { id: documentId },
      include: {
        folder: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!document) {
      throw new Error('文档不存在');
    }

    // 更新同步时间
    const updatedDocument = await prisma.document.update({
      where: { id: documentId },
      data: {
        lastSyncAt: new Date(),
      },
      include: {
        folder: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      document: updatedDocument,
      message: '文档强制下载成功'
    });

  } catch (error) {
    throw new Error(`强制下载失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 解决同步冲突
 */
async function handleResolveConflict(documentId: string, data: any, userId: string) {
  try {
    const { resolution, mergedData } = data;

    if (!['local', 'remote', 'merge'].includes(resolution)) {
      throw new Error('无效的冲突解决方案');
    }

    let finalData;

    switch (resolution) {
      case 'local':
        finalData = data.localVersion;
        break;
      case 'remote':
        finalData = data.remoteVersion;
        break;
      case 'merge':
        if (!mergedData) {
          throw new Error('合并数据不能为空');
        }
        finalData = mergedData;
        break;
    }

    // 计算文本统计
    const stats = DocumentService.calculateTextStats(finalData.content || '');

    // 更新文档
    const updatedDocument = await prisma.document.update({
      where: { id: documentId },
      data: {
        title: finalData.title,
        content: finalData.content,
        folderId: finalData.folderId,
        wordCount: stats.wordCount,
        charCount: stats.charCount,
        isPublic: finalData.metadata?.isPublic || false,
        shareToken: finalData.metadata?.shareToken,
        lastSyncAt: new Date(),
      },
      include: {
        folder: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // 创建历史记录
    await DocumentService.createDocumentHistory(
      documentId,
      finalData.content,
      resolution === 'merge' ? 'ai' : 'user'
    );

    return NextResponse.json({
      success: true,
      document: updatedDocument,
      resolution,
      message: `冲突解决成功 (${resolution})`
    });

  } catch (error) {
    throw new Error(`解决冲突失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}