'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  PanelRightIcon,
  PanelLeftIcon,
  SparklesIcon,
  EditIcon,
  FileTextIcon,
  LanguagesIcon,
  MessageSquareIcon,
  SettingsIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  HelpCircleIcon,
  PenToolIcon,
  BarChartIcon,
  TagIcon,
  ListIcon,
  TerminalIcon,
  BookOpenIcon,
  LightbulbIcon,
  RefreshCwIcon,
  XIcon
} from 'lucide-react';

/**
 * AI 功能类别定义
 */
interface AIFeatureCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  features: AIFeature[];
}

/**
 * AI 功能定义
 */
interface AIFeature {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  shortcut?: string;
  action: () => void;
  disabled?: boolean;
}

/**
 * AI 助手面板属性
 */
interface AIAssistantPanelProps {
  /** 是否显示面板 */
  isOpen: boolean;
  /** 切换面板显示状态 */
  onToggle: () => void;
  /** 面板位置 */
  position?: 'left' | 'right';
  /** 面板宽度 */
  width?: number;
  /** 是否为移动端 */
  isMobile?: boolean;
  /** 自定义类名 */
  className?: string;
  /** AI 功能回调 */
  onAIAction?: (actionId: string, data?: any) => void;
  /** 当前选中的文本 */
  selectedText?: string;
  /** 是否正在处理 AI 请求 */
  isProcessing?: boolean;
  /** 处理状态信息 */
  processingStatus?: string;
}

/**
 * AI 助手面板组件
 * 提供可折叠的 AI 功能导航和操作界面
 */
export function AIAssistantPanel({
  isOpen,
  onToggle,
  position = 'right',
  width = 320,
  isMobile = false,
  className = '',
  onAIAction,
  selectedText,
  isProcessing = false,
  processingStatus
}: AIAssistantPanelProps) {
  const [activeTab, setActiveTab] = useState('writing');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['writing']));
  const [recentActions, setRecentActions] = useState<string[]>([]);

  /**
   * 切换类别展开状态
   */
  const toggleCategory = useCallback((categoryId: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  }, []);

  /**
   * 执行 AI 功能
   */
  const handleAIAction = useCallback((actionId: string, data?: any) => {
    if (onAIAction) {
      onAIAction(actionId, data);
      
      // 记录最近使用的功能
      setRecentActions(prev => {
        const newActions = [actionId, ...prev.filter(id => id !== actionId)].slice(0, 5);
        return newActions;
      });
    }
  }, [onAIAction]);

  /**
   * AI 功能分类配置
   */
  const aiCategories: AIFeatureCategory[] = [
    {
      id: 'writing',
      name: '写作助手',
      icon: PenToolIcon,
      description: '文本生成、续写和创作',
      features: [
        {
          id: 'ai-continue',
          name: 'AI 续写',
          description: '基于上下文继续写作',
          icon: SparklesIcon,
          shortcut: 'Ctrl+Shift+C',
          action: () => handleAIAction('ai-continue')
        },
        {
          id: 'ai-rewrite',
          name: 'AI 改写',
          description: '改写和优化选中文本',
          icon: EditIcon,
          shortcut: 'Ctrl+Shift+R',
          action: () => handleAIAction('ai-rewrite', { text: selectedText }),
          disabled: !selectedText
        },
        {
          id: 'ai-creative',
          name: '创意写作',
          description: '创作故事、诗歌等内容',
          icon: LightbulbIcon,
          action: () => handleAIAction('ai-creative')
        },
        {
          id: 'ai-expand',
          name: '内容扩展',
          description: '扩展和详细化内容',
          icon: ChevronRightIcon,
          action: () => handleAIAction('ai-expand', { text: selectedText }),
          disabled: !selectedText
        }
      ]
    },
    {
      id: 'analysis',
      name: '文档分析',
      icon: BarChartIcon,
      description: '分析、总结和提取信息',
      features: [
        {
          id: 'ai-summarize',
          name: '文档摘要',
          description: '生成文档摘要',
          icon: FileTextIcon,
          shortcut: 'Ctrl+Shift+S',
          action: () => handleAIAction('ai-summarize')
        },
        {
          id: 'ai-keywords',
          name: '关键词提取',
          description: '提取关键词和主题',
          icon: TagIcon,
          action: () => handleAIAction('ai-keywords')
        },
        {
          id: 'ai-outline',
          name: '生成大纲',
          description: '自动生成文档大纲',
          icon: ListIcon,
          action: () => handleAIAction('ai-outline')
        },
        {
          id: 'ai-analysis',
          name: '内容分析',
          description: '分析语调、结构和质量',
          icon: BarChartIcon,
          action: () => handleAIAction('ai-analysis')
        }
      ]
    },
    {
      id: 'language',
      name: '语言工具',
      icon: LanguagesIcon,
      description: '翻译、解释和语言处理',
      features: [
        {
          id: 'ai-translate',
          name: 'AI 翻译',
          description: '翻译选中文本',
          icon: LanguagesIcon,
          shortcut: 'Ctrl+Shift+T',
          action: () => handleAIAction('ai-translate', { text: selectedText }),
          disabled: !selectedText
        },
        {
          id: 'ai-explain',
          name: 'AI 解释',
          description: '解释复杂概念',
          icon: HelpCircleIcon,
          action: () => handleAIAction('ai-explain', { text: selectedText }),
          disabled: !selectedText
        },
        {
          id: 'ai-grammar',
          name: '语法检查',
          description: '检查和修正语法',
          icon: BookOpenIcon,
          action: () => handleAIAction('ai-grammar', { text: selectedText }),
          disabled: !selectedText
        }
      ]
    },
    {
      id: 'custom',
      name: '自定义指令',
      icon: TerminalIcon,
      description: '执行自定义 AI 指令',
      features: [
        {
          id: 'ai-custom',
          name: '自定义指令',
          description: '执行自定义 AI 指令',
          icon: TerminalIcon,
          action: () => handleAIAction('ai-custom')
        },
        {
          id: 'ai-chat',
          name: 'AI 对话',
          description: '与 AI 进行对话',
          icon: MessageSquareIcon,
          action: () => handleAIAction('ai-chat')
        }
      ]
    }
  ];

  /**
   * 渲染功能按钮
   */
  const renderFeatureButton = useCallback((feature: AIFeature) => {
    const IconComponent = feature.icon;
    const isRecent = recentActions.includes(feature.id);
    
    return (
      <Button
        key={feature.id}
        variant="ghost"
        size="sm"
        onClick={feature.action}
        disabled={feature.disabled || isProcessing}
        className={`
          w-full justify-start h-auto p-3 text-left
          ${feature.disabled ? 'opacity-50' : 'hover:bg-gray-50'}
          ${isRecent ? 'bg-blue-50 border-l-2 border-l-blue-500' : ''}
        `}
      >
        <div className="flex items-start gap-3 w-full">
          <IconComponent className="h-4 w-4 mt-0.5 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm">{feature.name}</span>
              {isRecent && (
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  最近
                </Badge>
              )}
              {feature.shortcut && (
                <Badge variant="outline" className="text-xs px-1 py-0 ml-auto">
                  {feature.shortcut}
                </Badge>
              )}
            </div>
            <p className="text-xs text-gray-600 mt-1">{feature.description}</p>
          </div>
        </div>
      </Button>
    );
  }, [recentActions, isProcessing]);

  /**
   * 渲染功能分类
   */
  const renderCategory = useCallback((category: AIFeatureCategory) => {
    const IconComponent = category.icon;
    const isExpanded = expandedCategories.has(category.id);
    
    return (
      <div key={category.id} className="border-b border-gray-100 last:border-b-0">
        <Button
          variant="ghost"
          onClick={() => toggleCategory(category.id)}
          className="w-full justify-between p-4 h-auto"
        >
          <div className="flex items-center gap-3">
            <IconComponent className="h-5 w-5" />
            <div className="text-left">
              <div className="font-medium">{category.name}</div>
              <div className="text-xs text-gray-600">{category.description}</div>
            </div>
          </div>
          {isExpanded ? (
            <ChevronDownIcon className="h-4 w-4" />
          ) : (
            <ChevronRightIcon className="h-4 w-4" />
          )}
        </Button>
        
        {isExpanded && (
          <div className="px-2 pb-2 space-y-1">
            {category.features.map(renderFeatureButton)}
          </div>
        )}
      </div>
    );
  }, [expandedCategories, toggleCategory, renderFeatureButton]);

  /**
   * 渲染处理状态
   */
  const renderProcessingStatus = useCallback(() => {
    if (!isProcessing) return null;
    
    return (
      <div className="p-4 bg-blue-50 border-b border-blue-100">
        <div className="flex items-center gap-3">
          <RefreshCwIcon className="h-4 w-4 animate-spin text-blue-600" />
          <div className="flex-1">
            <div className="font-medium text-blue-900">AI 处理中...</div>
            {processingStatus && (
              <div className="text-xs text-blue-700">{processingStatus}</div>
            )}
          </div>
        </div>
      </div>
    );
  }, [isProcessing, processingStatus]);

  /**
   * 渲染快速操作
   */
  const renderQuickActions = useCallback(() => {
    const quickActions = [
      { id: 'ai-continue', name: '续写', icon: SparklesIcon },
      { id: 'ai-rewrite', name: '改写', icon: EditIcon, disabled: !selectedText },
      { id: 'ai-summarize', name: '摘要', icon: FileTextIcon },
      { id: 'ai-translate', name: '翻译', icon: LanguagesIcon, disabled: !selectedText }
    ];
    
    return (
      <div className="p-4 border-b border-gray-100">
        <h3 className="font-medium text-sm text-gray-900 mb-3">快速操作</h3>
        <div className="grid grid-cols-2 gap-2">
          {quickActions.map(action => {
            const IconComponent = action.icon;
            return (
              <Button
                key={action.id}
                variant="outline"
                size="sm"
                onClick={() => handleAIAction(action.id, { text: selectedText })}
                disabled={action.disabled || isProcessing}
                className="h-auto p-2 flex flex-col items-center gap-1"
              >
                <IconComponent className="h-4 w-4" />
                <span className="text-xs">{action.name}</span>
              </Button>
            );
          })}
        </div>
      </div>
    );
  }, [selectedText, isProcessing, handleAIAction]);

  // 移动端全屏覆盖样式
  const mobileOverlayStyle = isMobile && isOpen ? 'fixed inset-0 z-50 bg-white' : '';
  
  // 桌面端侧边面板样式
  const desktopPanelStyle = !isMobile ? `
    fixed top-0 ${position === 'right' ? 'right-0' : 'left-0'} h-full z-40
    transform transition-transform duration-300 ease-in-out
    ${isOpen ? 'translate-x-0' : position === 'right' ? 'translate-x-full' : '-translate-x-full'}
    shadow-lg border-l border-gray-200
  ` : '';

  return (
    <>
      {/* 移动端背景遮罩 */}
      {isMobile && isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40"
          onClick={onToggle}
        />
      )}
      
      {/* 面板主体 */}
      <div
        className={`
          bg-white
          ${isMobile ? mobileOverlayStyle : desktopPanelStyle}
          ${className}
        `}
        style={{ width: isMobile ? '100%' : width }}
      >
        {/* 面板头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <SparklesIcon className="h-5 w-5 text-blue-600" />
            <h2 className="font-semibold text-gray-900">AI 助手</h2>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleAIAction('ai-settings')}
              className="h-8 w-8 p-0"
            >
              <SettingsIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggle}
              className="h-8 w-8 p-0"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 处理状态 */}
        {renderProcessingStatus()}

        {/* 面板内容 */}
        <div className="flex-1 overflow-y-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
            <TabsList className="w-full grid grid-cols-2 m-4 mb-0">
              <TabsTrigger value="features">功能</TabsTrigger>
              <TabsTrigger value="quick">快捷</TabsTrigger>
            </TabsList>
            
            <TabsContent value="features" className="mt-0 h-full">
              <div className="space-y-0">
                {aiCategories.map(renderCategory)}
              </div>
            </TabsContent>
            
            <TabsContent value="quick" className="mt-0">
              {renderQuickActions()}
              
              {/* 最近使用 */}
              {recentActions.length > 0 && (
                <div className="p-4">
                  <h3 className="font-medium text-sm text-gray-900 mb-3">最近使用</h3>
                  <div className="space-y-1">
                    {recentActions.slice(0, 3).map(actionId => {
                      const feature = aiCategories
                        .flatMap(cat => cat.features)
                        .find(f => f.id === actionId);
                      
                      if (!feature) return null;
                      
                      return renderFeatureButton(feature);
                    })}
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>

        {/* 面板底部 */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-xs text-gray-600 text-center">
            {selectedText ? (
              <span>已选择 {selectedText.length} 个字符</span>
            ) : (
              <span>选择文本以启用更多功能</span>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

/**
 * AI 助手面板切换按钮
 */
interface AIAssistantToggleProps {
  isOpen: boolean;
  onToggle: () => void;
  position?: 'left' | 'right';
  className?: string;
}

export function AIAssistantToggle({
  isOpen,
  onToggle,
  position = 'right',
  className = ''
}: AIAssistantToggleProps) {
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={onToggle}
      className={`
        fixed top-1/2 -translate-y-1/2 z-30
        ${position === 'right' ? 'right-4' : 'left-4'}
        ${isOpen ? 'opacity-50' : 'opacity-100'}
        shadow-lg border-gray-300
        ${className}
      `}
    >
      {isOpen ? (
        position === 'right' ? <PanelRightIcon className="h-4 w-4" /> : <PanelLeftIcon className="h-4 w-4" />
      ) : (
        <>
          <SparklesIcon className="h-4 w-4 mr-1" />
          AI
        </>
      )}
    </Button>
  );
}

/**
 * AI 助手面板容器组件
 * 包含面板和切换按钮的完整实现
 */
interface AIAssistantContainerProps extends Omit<AIAssistantPanelProps, 'isOpen' | 'onToggle'> {
  /** 初始打开状态 */
  defaultOpen?: boolean;
}

export function AIAssistantContainer({
  defaultOpen = false,
  ...props
}: AIAssistantContainerProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const handleToggle = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  // 响应式检测
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <>
      <AIAssistantPanel
        {...props}
        isOpen={isOpen}
        onToggle={handleToggle}
        isMobile={isMobile}
      />
      <AIAssistantToggle
        isOpen={isOpen}
        onToggle={handleToggle}
        position={props.position}
      />
    </>
  );
}