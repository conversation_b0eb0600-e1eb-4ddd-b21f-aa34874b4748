import { PrismaClient } from '@prisma/client';

/**
 * 全局 Prisma 客户端类型定义
 * 用于在开发环境中避免热重载时创建多个 Prisma 实例
 */
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

/**
 * Prisma 数据库客户端实例
 * 在开发环境中复用全局实例，在生产环境中创建新实例
 * 这样可以避免在开发时因热重载导致的连接池耗尽问题
 */
export const prisma = globalForPrisma.prisma ?? new PrismaClient();

// 在非生产环境中将 Prisma 实例保存到全局变量
// 这样在开发时热重载不会创建新的数据库连接
if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;