import { useState, useEffect, useCallback } from 'react';
import { DocumentVersion, VersionComparison } from '@/lib/services/version-history';

export interface UseVersionHistoryOptions {
  documentId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface UseVersionHistoryReturn {
  // 版本数据
  versions: DocumentVersion[];
  currentVersion?: DocumentVersion;
  isLoading: boolean;
  error: string | null;
  
  // 版本操作
  refreshVersions: () => Promise<void>;
  restoreVersion: (version: DocumentVersion) => Promise<void>;
  createVersion: (content: any, title: string, changeDescription?: string) => Promise<void>;
  
  // 版本比较
  compareVersions: (version1: DocumentVersion, version2: DocumentVersion) => Promise<VersionComparison | null>;
  
  // 搜索和过滤
  searchVersions: (query: string) => Promise<void>;
  filterVersions: (type: 'all' | 'user' | 'ai' | 'sync' | 'merge') => DocumentVersion[];
  
  // 统计信息
  stats: {
    totalVersions: number;
    oldestVersion: Date | null;
    newestVersion: Date | null;
    changeTypes: Record<string, number>;
  };
}

/**
 * 版本历史管理Hook
 */
export function useVersionHistory({ 
  documentId, 
  autoRefresh = false, 
  refreshInterval = 30000 
}: UseVersionHistoryOptions): UseVersionHistoryReturn {
  const [versions, setVersions] = useState<DocumentVersion[]>([]);
  const [currentVersion, setCurrentVersion] = useState<DocumentVersion | undefined>();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalVersions: 0,
    oldestVersion: null as Date | null,
    newestVersion: null as Date | null,
    changeTypes: {} as Record<string, number>
  });

  // 获取版本历史
  const refreshVersions = useCallback(async () => {
    if (!documentId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/documents/${documentId}/versions`);
      
      if (!response.ok) {
        throw new Error('获取版本历史失败');
      }

      const data = await response.json();
      setVersions(data.versions || []);
      setStats(data.stats || {
        totalVersions: 0,
        oldestVersion: null,
        newestVersion: null,
        changeTypes: {}
      });

      // 设置当前版本（最新版本）
      if (data.versions && data.versions.length > 0) {
        setCurrentVersion(data.versions[0]);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取版本历史失败';
      setError(errorMessage);
      console.error('获取版本历史失败:', err);
    } finally {
      setIsLoading(false);
    }
  }, [documentId]);

  // 恢复版本
  const restoreVersion = useCallback(async (version: DocumentVersion) => {
    if (!documentId) return;

    try {
      const response = await fetch(
        `/api/documents/${documentId}/versions/${version.version}/restore`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error('恢复版本失败');
      }

      const result = await response.json();
      
      // 刷新版本列表
      await refreshVersions();
      
      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '恢复版本失败';
      setError(errorMessage);
      throw err;
    }
  }, [documentId, refreshVersions]);

  // 创建版本
  const createVersion = useCallback(async (
    content: any, 
    title: string, 
    changeDescription?: string
  ) => {
    if (!documentId) return;

    try {
      const response = await fetch(`/api/documents/${documentId}/versions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          title,
          changeType: 'user',
          changeDescription
        }),
      });

      if (!response.ok) {
        throw new Error('创建版本失败');
      }

      const result = await response.json();
      
      // 刷新版本列表
      await refreshVersions();
      
      return result.version;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建版本失败';
      setError(errorMessage);
      throw err;
    }
  }, [documentId, refreshVersions]);

  // 比较版本
  const compareVersions = useCallback(async (
    version1: DocumentVersion, 
    version2: DocumentVersion
  ): Promise<VersionComparison | null> => {
    if (!documentId) return null;

    try {
      const response = await fetch(`/api/documents/${documentId}/versions/compare`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          version1: version1.version,
          version2: version2.version
        }),
      });

      if (!response.ok) {
        throw new Error('版本比较失败');
      }

      const result = await response.json();
      return result.comparison;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '版本比较失败';
      setError(errorMessage);
      console.error('版本比较失败:', err);
      return null;
    }
  }, [documentId]);

  // 搜索版本
  const searchVersions = useCallback(async (query: string) => {
    if (!documentId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/documents/${documentId}/versions?search=${encodeURIComponent(query)}`
      );
      
      if (!response.ok) {
        throw new Error('搜索版本失败');
      }

      const data = await response.json();
      setVersions(data.versions || []);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '搜索版本失败';
      setError(errorMessage);
      console.error('搜索版本失败:', err);
    } finally {
      setIsLoading(false);
    }
  }, [documentId]);

  // 过滤版本
  const filterVersions = useCallback((type: 'all' | 'user' | 'ai' | 'sync' | 'merge') => {
    if (type === 'all') {
      return versions;
    }
    return versions.filter(version => version.changeType === type);
  }, [versions]);

  // 初始加载
  useEffect(() => {
    if (documentId) {
      refreshVersions();
    }
  }, [documentId, refreshVersions]);

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh || !documentId) return;

    const interval = setInterval(refreshVersions, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, documentId, refreshVersions]);

  return {
    // 版本数据
    versions,
    currentVersion,
    isLoading,
    error,
    
    // 版本操作
    refreshVersions,
    restoreVersion,
    createVersion,
    
    // 版本比较
    compareVersions,
    
    // 搜索和过滤
    searchVersions,
    filterVersions,
    
    // 统计信息
    stats
  };
}

/**
 * 简化版版本历史Hook，只提供基本功能
 */
export function useVersionHistoryBasic(documentId: string) {
  const [versions, setVersions] = useState<DocumentVersion[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const loadVersions = useCallback(async () => {
    if (!documentId) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/documents/${documentId}/versions?limit=10`);
      if (response.ok) {
        const data = await response.json();
        setVersions(data.versions || []);
      }
    } catch (error) {
      console.error('加载版本历史失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, [documentId]);

  useEffect(() => {
    loadVersions();
  }, [loadVersions]);

  return {
    versions: versions.slice(0, 5), // 只返回最近5个版本
    isLoading,
    refresh: loadVersions
  };
}