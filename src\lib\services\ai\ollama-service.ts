/**
 * Ollama 服务实现
 * 提供本地 Ollama 服务的集成
 */

import { BaseAIService } from './base-ai-service';
import {
  AIServiceConfig,
  AIRequest,
  AIResponse,
  AIServiceError,
  AIErrorType,
  AIProvider
} from '@/types/ai.types';

/**
 * Ollama API 响应接口
 */
interface OllamaResponse {
  response: string;
  done: boolean;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

/**
 * Ollama 模型信息接口
 */
interface OllamaModel {
  name: string;
  modified_at: string;
  size: number;
  digest: string;
}

/**
 * Ollama 服务实现
 */
export class OllamaService extends BaseAIService {
  readonly provider: AIProvider = 'ollama';

  private readonly defaultEndpoint = 'http://localhost:11454';

  constructor(config: AIServiceConfig) {
    super(config);

    if (!config.endpoint) {
      config.endpoint = this.defaultEndpoint;
    }
  }

  /**
   * 生成文本内容
   */
  async _generateText(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    return this.executeWithRetry(async () => {
      const prompt = this.buildPrompt(request);

      const response = await this.makeRequest('/api/generate', {
        model: this.config.model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: request.temperature ?? this.config.temperature ?? 0.7,
          num_predict: request.maxTokens || this.config.maxTokens || 2000,
        }
      });

      const data = response as OllamaResponse;

      if (!data.response) {
        throw new AIServiceError(
          AIErrorType.INVALID_REQUEST,
          'Ollama 返回了空的响应',
          this.provider
        );
      }

      // 估算令牌使用量（Ollama 不直接提供）
      const tokensUsed = this.estimateTokens(prompt + data.response);

      return {
        content: data.response.trim(),
        tokensUsed,
        responseTime: Date.now() - startTime,
        model: this.config.model,
        provider: this.provider
      };
    }, 'Ollama text generation');
  }

  /**
   * 获取可用模型列表
   */
  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.makeRequest('/api/tags');
      const data = response as { models: OllamaModel[] };

      return data.models.map(model => model.name).sort();
    } catch (error) {
      console.warn('获取 Ollama 模型列表失败:', error);
      return [this.config.model];
    }
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.makeRequest('/api/tags');
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 构建完整提示
   */
  private buildPrompt(request: AIRequest): string {
    let prompt = '';

    // 添加系统提示
    if (request.systemPrompt) {
      prompt += `系统指令：${request.systemPrompt}\n\n`;
    }

    // 添加上下文
    if (request.context) {
      prompt += `上下文信息：\n${request.context}\n\n`;
    }

    // 添加用户提示
    prompt += `用户请求：${request.prompt}`;

    return prompt;
  }

  /**
   * 估算令牌数量
   * 简单估算：中文按字符数，英文按单词数 * 1.3
   */
  private estimateTokens(text: string): number {
    // 分离中文和英文
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishText = text.replace(/[\u4e00-\u9fff]/g, '');
    const englishWords = englishText.split(/\s+/).filter(word => word.length > 0).length;

    // 中文字符按 1:1 计算，英文单词按 1:1.3 计算
    return Math.ceil(chineseChars + englishWords * 1.3);
  }

  /**
   * 发起 HTTP 请求
   */
  private async makeRequest(endpoint: string, body?: any): Promise<any> {
    const url = `${this.config.endpoint}${endpoint}`;
    const timeout = this.config.timeout || 60000; // Ollama 可能需要更长时间

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        method: body ? 'POST' : 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'AI-Document-Editor/1.0'
        },
        body: body ? JSON.stringify(body) : undefined,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text().catch(() => '');
        throw this.createErrorFromResponse(response.status, errorText);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof AIServiceError) {
        throw error;
      }

      throw this.handleHttpError(error, 'Ollama API request');
    }
  }

  /**
   * 根据响应状态创建错误
   */
  private createErrorFromResponse(status: number, errorText: string): AIServiceError {
    switch (status) {
      case 400:
        return new AIServiceError(
          AIErrorType.INVALID_REQUEST,
          `Ollama 请求参数无效: ${errorText}`,
          this.provider
        );
      case 404:
        return new AIServiceError(
          AIErrorType.INVALID_REQUEST,
          `Ollama 模型不存在: ${this.config.model}`,
          this.provider
        );
      case 500:
      case 502:
      case 503:
      case 504:
        return new AIServiceError(
          AIErrorType.SERVICE_UNAVAILABLE,
          'Ollama 服务暂时不可用',
          this.provider
        );
      default:
        return new AIServiceError(
          AIErrorType.UNKNOWN_ERROR,
          `Ollama API 错误 (${status}): ${errorText}`,
          this.provider
        );
    }
  }
}