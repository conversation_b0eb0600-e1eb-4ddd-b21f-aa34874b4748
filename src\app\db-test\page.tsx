'use client';

import { useState } from 'react';

export default function DatabaseTestPage() {
  const [testResult, setTestResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testDatabase = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/db/test');
      const result = await response.json();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        error: 'Failed to test database',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto max-w-4xl p-8">
      <h1 className="mb-8 text-3xl font-bold">Database Test</h1>
      
      <div className="space-y-6">
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">Connection Test</h2>
          <button
            onClick={testDatabase}
            disabled={loading}
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Database Connection'}
          </button>
        </div>

        {testResult && (
          <div className="rounded-lg border p-6">
            <h2 className="mb-4 text-xl font-semibold">Test Results</h2>
            <pre className="overflow-auto rounded bg-gray-100 p-4 text-sm">
              {JSON.stringify(testResult, null, 2)}
            </pre>
          </div>
        )}

        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">Database Commands</h2>
          <div className="space-y-2 text-sm">
            <p><code className="rounded bg-gray-100 px-2 py-1">npm run db:generate</code> - Generate Prisma client</p>
            <p><code className="rounded bg-gray-100 px-2 py-1">npm run db:push</code> - Push schema to database</p>
            <p><code className="rounded bg-gray-100 px-2 py-1">npm run db:seed</code> - Seed database with demo data</p>
            <p><code className="rounded bg-gray-100 px-2 py-1">npm run db:studio</code> - Open Prisma Studio</p>
            <p><code className="rounded bg-gray-100 px-2 py-1">npm run db:reset</code> - Reset database</p>
          </div>
        </div>

        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">Setup Instructions</h2>
          <ol className="list-decimal space-y-2 pl-6 text-sm">
            <li>Run <code className="rounded bg-gray-100 px-2 py-1">npm install</code> to install dependencies</li>
            <li>Run <code className="rounded bg-gray-100 px-2 py-1">npm run db:generate</code> to generate Prisma client</li>
            <li>Run <code className="rounded bg-gray-100 px-2 py-1">npm run db:push</code> to create database tables</li>
            <li>Run <code className="rounded bg-gray-100 px-2 py-1">npm run db:seed</code> to add demo data</li>
            <li>Test the connection using the button above</li>
          </ol>
        </div>
      </div>
    </div>
  );
}