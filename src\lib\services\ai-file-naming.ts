/**
 * AI 文件命名建议服务
 * 提供基于内容的智能文件命名和重命名建议功能
 */

import { prisma } from '@/lib/db/prisma';
import { aiServiceManager } from './ai/ai-service-factory';
import { aiProcessingManager } from './ai-processing-manager';
import {
  FileNamingSuggestion,
  DocumentNamingSuggestion,
  DocumentSummary,
  SmartRenameSuggestion,
  NamingOptions,
  SummaryOptions,
  NamingStyle,
  NamingSuggestionType,
  RenameSuggestionType,
  BatchNamingRequest,
  BatchNamingResult,
  NamingPatternAnalysis,
  NamingPattern,
  NamingPreferences,
  FileNameValidation
} from '@/types/ai-naming.types';
import { AIProcessingContext } from '@/types/ai-status.types';

/**
 * AI 文件命名服务类
 */
export class AIFileNamingService {
  private readonly defaultNamingOptions: NamingOptions = {
    style: NamingStyle.DESCRIPTIVE,
    maxLength: 50,
    includeDate: false,
    dateFormat: 'YYYY-MM-DD',
    includeTypePrefix: false,
    language: 'zh',
    excludeWords: ['文档', '新建', '未命名', 'document', 'untitled'],
    suggestionCount: 5
  };

  private readonly defaultSummaryOptions: SummaryOptions = {
    length: 'medium',
    type: 'overview',
    language: 'zh',
    includeKeywords: true,
    maxWords: 100
  };

  /**
   * 为文档生成命名建议
   */
  async generateNamingSuggestions(
    documentId: string,
    userId: string,
    options: Partial<NamingOptions> = {}
  ): Promise<DocumentNamingSuggestion> {
    const finalOptions = { ...this.defaultNamingOptions, ...options };

    // 获取文档信息
    const document = await prisma.document.findFirst({
      where: { id: documentId, userId },
      select: { id: true, title: true, content: true, createdAt: true }
    });

    if (!document) {
      throw new Error('文档不存在或无权限访问');
    }

    // 创建AI处理上下文
    const processingId = `naming_${documentId}_${Date.now()}`;
    const context: AIProcessingContext = {
      id: processingId,
      documentId,
      userId,
      type: 'file_naming',
      input: document.content,
      options: { showDetailedProgress: true },
      startTime: new Date()
    };

    await aiProcessingManager.startProcessing(context);

    try {
      // 生成文档摘要
      const summary = await this.generateDocumentSummary(documentId, userId);
      
      // 分析用户命名模式
      const userPatterns = await this.analyzeUserNamingPatterns(userId);
      
      // 生成多种类型的命名建议
      const suggestions = await this.generateMultipleNamingSuggestions(
        document,
        summary,
        userPatterns,
        finalOptions
      );

      // 选择最佳建议
      const bestSuggestion = this.selectBestSuggestion(suggestions);

      const result: DocumentNamingSuggestion = {
        documentId,
        currentName: document.title,
        suggestions,
        bestSuggestion,
        documentSummary: summary.summary,
        analyzedAt: new Date()
      };

      // 完成处理
      aiProcessingManager.completeProcessing(processingId, {
        id: processingId,
        type: 'file_naming',
        input: document.content,
        output: JSON.stringify(result),
        provider: 'ai-naming',
        model: 'naming-model',
        tokensUsed: 100,
        processingTime: Date.now() - context.startTime.getTime(),
        createdAt: new Date()
      });

      return result;
    } catch (error) {
      aiProcessingManager.handleError(processingId, error as Error);
      throw error;
    }
  }

  /**
   * 批量生成命名建议
   */
  async batchGenerateNamingSuggestions(request: BatchNamingRequest): Promise<BatchNamingResult> {
    const result: BatchNamingResult = {
      requestId: `batch_naming_${Date.now()}`,
      status: 'processing',
      totalDocuments: request.documentIds.length,
      processedDocuments: 0,
      suggestions: [],
      errors: [],
      startedAt: new Date()
    };

    try {
      for (const documentId of request.documentIds) {
        try {
          const suggestion = await this.generateNamingSuggestions(
            documentId,
            request.userId,
            request.options
          );
          result.suggestions.push(suggestion);
          result.processedDocuments++;
        } catch (error) {
          result.errors.push(`文档 ${documentId}: ${(error as Error).message}`);
        }
      }

      result.status = 'completed';
      result.completedAt = new Date();
      
      return result;
    } catch (error) {
      result.status = 'failed';
      result.errors.push(`批量处理失败: ${(error as Error).message}`);
      return result;
    }
  }

  /**
   * 生成文档摘要
   */
  async generateDocumentSummary(
    documentId: string,
    userId: string,
    options: Partial<SummaryOptions> = {}
  ): Promise<DocumentSummary> {
    const finalOptions = { ...this.defaultSummaryOptions, ...options };

    // 获取文档内容
    const document = await prisma.document.findFirst({
      where: { id: documentId, userId },
      select: { id: true, title: true, content: true }
    });

    if (!document) {
      throw new Error('文档不存在或无权限访问');
    }

    const textContent = this.extractTextFromContent(document.content);
    
    if (textContent.length < 50) {
      return {
        documentId,
        summary: '文档内容过短，无法生成有效摘要',
        keywords: [],
        mainTopics: [],
        type: finalOptions.type,
        wordCount: textContent.length,
        generatedAt: new Date(),
        confidence: 0.3
      };
    }

    // 构建摘要生成提示
    const summaryPrompt = this.buildSummaryPrompt(
      document.title,
      textContent,
      finalOptions
    );

    try {
      const aiService = aiServiceManager.getDefaultService();
      const response = await aiService.generateText({
        prompt: summaryPrompt,
        maxTokens: 500
      });

      const parsed = this.parseSummaryResponse(response.content, documentId, finalOptions);
      return parsed;
    } catch (error) {
      console.error('生成文档摘要失败:', error);
      return {
        documentId,
        summary: '摘要生成失败',
        keywords: [],
        mainTopics: [],
        type: finalOptions.type,
        wordCount: textContent.length,
        generatedAt: new Date(),
        confidence: 0.1
      };
    }
  }

  /**
   * 获取智能重命名建议
   */
  async getSmartRenameSuggestions(userId: string): Promise<SmartRenameSuggestion[]> {
    // 获取用户的文档
    const documents = await prisma.document.findMany({
      where: { userId },
      select: { id: true, title: true, content: true, createdAt: true },
      orderBy: { updatedAt: 'desc' },
      take: 50
    });

    const suggestions: SmartRenameSuggestion[] = [];

    for (const doc of documents) {
      const renameSuggestion = await this.analyzeDocumentNaming(doc, userId);
      if (renameSuggestion) {
        suggestions.push(renameSuggestion);
      }
    }

    return suggestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * 验证文件名质量
   */
  async validateFileName(fileName: string, content?: string): Promise<FileNameValidation> {
    const validation: FileNameValidation = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: [],
      qualityScore: 100
    };

    // 基础验证
    if (!fileName || fileName.trim().length === 0) {
      validation.isValid = false;
      validation.errors.push('文件名不能为空');
      validation.qualityScore -= 50;
    }

    if (fileName.length > 100) {
      validation.warnings.push('文件名过长，建议控制在50字符以内');
      validation.qualityScore -= 20;
    }

    // 检查通用名称
    const genericNames = ['新建文档', '未命名', '文档', 'document', 'untitled', 'new'];
    if (genericNames.some(name => fileName.toLowerCase().includes(name.toLowerCase()))) {
      validation.warnings.push('文件名过于通用，建议使用更具描述性的名称');
      validation.suggestions.push('使用能反映文档内容的具体名称');
      validation.qualityScore -= 30;
    }

    // 检查特殊字符
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(fileName)) {
      validation.errors.push('文件名包含无效字符');
      validation.isValid = false;
      validation.qualityScore -= 40;
    }

    // 如果有内容，检查名称与内容的匹配度
    if (content) {
      const contentRelevance = await this.checkNameContentRelevance(fileName, content);
      if (contentRelevance < 0.5) {
        validation.warnings.push('文件名与内容相关性较低');
        validation.suggestions.push('考虑使用更能反映文档主题的名称');
        validation.qualityScore -= 25;
      }
    }

    validation.qualityScore = Math.max(0, validation.qualityScore);
    return validation;
  }

  /**
   * 分析用户命名模式
   */
  private async analyzeUserNamingPatterns(userId: string): Promise<NamingPatternAnalysis> {
    const documents = await prisma.document.findMany({
      where: { userId },
      select: { title: true, createdAt: true },
      orderBy: { createdAt: 'desc' },
      take: 100
    });

    const patterns = this.extractNamingPatterns(documents.map(d => d.title));
    const preferences = this.analyzeNamingPreferences(documents.map(d => d.title));

    return {
      userId,
      commonPatterns: patterns,
      preferences,
      analyzedDocuments: documents.length,
      analyzedAt: new Date()
    };
  }

  /**
   * 生成多种类型的命名建议
   */
  private async generateMultipleNamingSuggestions(
    document: any,
    summary: DocumentSummary,
    userPatterns: NamingPatternAnalysis,
    options: NamingOptions
  ): Promise<FileNamingSuggestion[]> {
    const suggestions: FileNamingSuggestion[] = [];

    // 基于内容的建议
    const contentSuggestions = await this.generateContentBasedSuggestions(
      document,
      summary,
      options
    );
    suggestions.push(...contentSuggestions);

    // 基于关键词的建议
    const keywordSuggestions = this.generateKeywordBasedSuggestions(
      summary.keywords,
      options
    );
    suggestions.push(...keywordSuggestions);

    // 基于用户模式的建议
    const patternSuggestions = this.generatePatternBasedSuggestions(
      summary,
      userPatterns,
      options
    );
    suggestions.push(...patternSuggestions);

    // 基于日期和主题的建议
    if (options.includeDate) {
      const dateSuggestions = this.generateDateBasedSuggestions(
        document,
        summary,
        options
      );
      suggestions.push(...dateSuggestions);
    }

    return suggestions.slice(0, options.suggestionCount);
  }

  /**
   * 生成基于内容的建议
   */
  private async generateContentBasedSuggestions(
    document: any,
    summary: DocumentSummary,
    options: NamingOptions
  ): Promise<FileNamingSuggestion[]> {
    const prompt = `
基于以下文档信息，生成${options.suggestionCount}个文件名建议：

文档标题: ${document.title}
文档摘要: ${summary.summary}
主要主题: ${summary.mainTopics.join(', ')}
关键词: ${summary.keywords.join(', ')}

命名要求：
- 风格: ${options.style}
- 最大长度: ${options.maxLength}字符
- 语言: ${options.language}
- 避免使用: ${options.excludeWords.join(', ')}

请返回JSON格式：
{
  "suggestions": [
    {
      "name": "建议的文件名",
      "reason": "建议原因",
      "confidence": 0.0-1.0
    }
  ]
}
`;

    try {
      const aiService = aiServiceManager.getDefaultService();
      const response = await aiService.generateText({
        prompt,
        maxTokens: 800
      });

      const parsed = JSON.parse(response.content);
      return (parsed.suggestions || []).map((s: any) => ({
        suggestedName: s.name,
        reason: s.reason,
        confidence: s.confidence || 0.7,
        type: NamingSuggestionType.CONTENT_BASED,
        basedOnSummary: summary.summary,
        keywords: summary.keywords,
        generatedAt: new Date()
      }));
    } catch (error) {
      console.error('生成内容建议失败:', error);
      return [];
    }
  }

  /**
   * 生成基于关键词的建议
   */
  private generateKeywordBasedSuggestions(
    keywords: string[],
    options: NamingOptions
  ): FileNamingSuggestion[] {
    if (keywords.length === 0) return [];

    const suggestions: FileNamingSuggestion[] = [];
    const topKeywords = keywords.slice(0, 3);

    // 单个关键词
    topKeywords.forEach(keyword => {
      if (keyword.length <= options.maxLength) {
        suggestions.push({
          suggestedName: this.formatFileName(keyword, options),
          reason: `基于关键词"${keyword}"`,
          confidence: 0.6,
          type: NamingSuggestionType.KEYWORD_BASED,
          basedOnSummary: `关键词: ${keyword}`,
          keywords: [keyword],
          generatedAt: new Date()
        });
      }
    });

    // 组合关键词
    if (topKeywords.length >= 2) {
      const combined = topKeywords.slice(0, 2).join('-');
      if (combined.length <= options.maxLength) {
        suggestions.push({
          suggestedName: this.formatFileName(combined, options),
          reason: `基于关键词组合"${combined}"`,
          confidence: 0.7,
          type: NamingSuggestionType.KEYWORD_BASED,
          basedOnSummary: `关键词组合: ${combined}`,
          keywords: topKeywords.slice(0, 2),
          generatedAt: new Date()
        });
      }
    }

    return suggestions;
  }

  /**
   * 生成基于用户模式的建议
   */
  private generatePatternBasedSuggestions(
    summary: DocumentSummary,
    userPatterns: NamingPatternAnalysis,
    options: NamingOptions
  ): FileNamingSuggestion[] {
    const suggestions: FileNamingSuggestion[] = [];
    
    // 使用用户常用模式
    userPatterns.commonPatterns.slice(0, 2).forEach(pattern => {
      const mainTopic = summary.mainTopics[0] || summary.keywords[0];
      if (mainTopic) {
        const suggestion = pattern.pattern.replace('{topic}', mainTopic);
        if (suggestion.length <= options.maxLength) {
          suggestions.push({
            suggestedName: this.formatFileName(suggestion, options),
            reason: `基于您的常用命名模式: ${pattern.pattern}`,
            confidence: 0.8,
            type: NamingSuggestionType.PROJECT_BASED,
            basedOnSummary: summary.summary,
            keywords: summary.keywords,
            generatedAt: new Date()
          });
        }
      }
    });

    return suggestions;
  }

  /**
   * 生成基于日期的建议
   */
  private generateDateBasedSuggestions(
    document: any,
    summary: DocumentSummary,
    options: NamingOptions
  ): FileNamingSuggestion[] {
    const suggestions: FileNamingSuggestion[] = [];
    const date = new Date(document.createdAt);
    const dateStr = this.formatDate(date, options.dateFormat);
    
    const mainTopic = summary.mainTopics[0] || summary.keywords[0] || '文档';
    const dateTopicName = `${dateStr}-${mainTopic}`;
    
    if (dateTopicName.length <= options.maxLength) {
      suggestions.push({
        suggestedName: this.formatFileName(dateTopicName, options),
        reason: `基于创建日期和主题`,
        confidence: 0.7,
        type: NamingSuggestionType.DATE_TOPIC_BASED,
        basedOnSummary: summary.summary,
        keywords: summary.keywords,
        generatedAt: new Date()
      });
    }

    return suggestions;
  }

  /**
   * 选择最佳建议
   */
  private selectBestSuggestion(suggestions: FileNamingSuggestion[]): FileNamingSuggestion {
    if (suggestions.length === 0) {
      return {
        suggestedName: '未命名文档',
        reason: '无法生成有效建议',
        confidence: 0.1,
        type: NamingSuggestionType.CONTENT_BASED,
        basedOnSummary: '',
        keywords: [],
        generatedAt: new Date()
      };
    }

    // 按置信度排序，选择最高的
    return suggestions.sort((a, b) => b.confidence - a.confidence)[0];
  }

  /**
   * 分析文档命名质量
   */
  private async analyzeDocumentNaming(
    document: any,
    userId: string
  ): Promise<SmartRenameSuggestion | null> {
    const textContent = this.extractTextFromContent(document.content);
    
    // 检查是否需要重命名建议
    const needsRename = await this.checkIfNeedsRename(document.title, textContent);
    
    if (!needsRename.needs) {
      return null;
    }

    // 生成新的命名建议
    const namingSuggestion = await this.generateNamingSuggestions(document.id, userId);
    
    return {
      id: `rename_${document.id}_${Date.now()}`,
      documentId: document.id,
      currentName: document.title,
      suggestedName: namingSuggestion.bestSuggestion.suggestedName,
      improvementReason: needsRename.reason,
      qualityScore: this.calculateNamingQuality(document.title, textContent),
      priority: needsRename.priority,
      suggestionType: needsRename.type,
      applied: false,
      createdAt: new Date()
    };
  }

  /**
   * 检查是否需要重命名
   */
  private async checkIfNeedsRename(
    currentName: string,
    content: string
  ): Promise<{
    needs: boolean;
    reason: string;
    priority: 'high' | 'medium' | 'low';
    type: RenameSuggestionType;
  }> {
    // 检查通用名称
    const genericNames = ['新建文档', '未命名', '文档', 'document', 'untitled'];
    if (genericNames.some(name => currentName.toLowerCase().includes(name.toLowerCase()))) {
      return {
        needs: true,
        reason: '文件名过于通用，建议使用更具描述性的名称',
        priority: 'high',
        type: RenameSuggestionType.TOO_GENERIC
      };
    }

    // 检查长度
    if (currentName.length > 80) {
      return {
        needs: true,
        reason: '文件名过长，建议简化',
        priority: 'medium',
        type: RenameSuggestionType.TOO_LONG
      };
    }

    // 检查内容相关性
    const relevance = await this.checkNameContentRelevance(currentName, content);
    if (relevance < 0.3) {
      return {
        needs: true,
        reason: '文件名与内容相关性较低，建议更新',
        priority: 'medium',
        type: RenameSuggestionType.CONTENT_MISMATCH
      };
    }

    return { needs: false, reason: '', priority: 'low', type: RenameSuggestionType.UNCLEAR };
  }

  /**
   * 检查名称与内容的相关性
   */
  private async checkNameContentRelevance(name: string, content: string): Promise<number> {
    const textContent = this.extractTextFromContent(content);
    
    if (textContent.length < 50) return 0.5; // 内容太短，无法判断
    
    // 简单的关键词匹配
    const nameWords = name.toLowerCase().split(/\s+/);
    const contentWords = textContent.toLowerCase().split(/\s+/).slice(0, 100);
    
    let matches = 0;
    nameWords.forEach(word => {
      if (word.length > 2 && contentWords.includes(word)) {
        matches++;
      }
    });
    
    return nameWords.length > 0 ? matches / nameWords.length : 0;
  }

  /**
   * 计算命名质量评分
   */
  private calculateNamingQuality(name: string, content: string): number {
    let score = 100;
    
    // 长度检查
    if (name.length > 50) score -= 20;
    if (name.length < 5) score -= 30;
    
    // 通用性检查
    const genericNames = ['新建', '未命名', '文档', 'document', 'untitled'];
    if (genericNames.some(generic => name.toLowerCase().includes(generic))) {
      score -= 40;
    }
    
    // 描述性检查
    if (name.split(/\s+/).length < 2) score -= 15;
    
    return Math.max(0, score);
  }

  /**
   * 提取命名模式
   */
  private extractNamingPatterns(names: string[]): NamingPattern[] {
    const patterns: NamingPattern[] = [];
    
    // 分析常见模式
    const datePattern = /\d{4}-\d{2}-\d{2}/;
    const dateNames = names.filter(name => datePattern.test(name));
    
    if (dateNames.length > 2) {
      patterns.push({
        pattern: 'YYYY-MM-DD-{topic}',
        frequency: dateNames.length / names.length,
        examples: dateNames.slice(0, 3),
        context: '日期主题格式'
      });
    }
    
    return patterns;
  }

  /**
   * 分析命名偏好
   */
  private analyzeNamingPreferences(names: string[]): NamingPreferences {
    const lengths = names.map(name => name.length);
    const avgLength = lengths.reduce((a, b) => a + b, 0) / lengths.length;
    
    return {
      preferredLength: {
        min: Math.min(...lengths),
        max: Math.max(...lengths)
      },
      preferredStyle: NamingStyle.DESCRIPTIVE,
      commonWords: this.extractCommonWords(names),
      dateUsage: names.some(name => /\d{4}-\d{2}-\d{2}/.test(name)),
      separatorPreference: names.some(name => name.includes('-')) ? '-' : ' ',
      casePreference: 'mixed'
    };
  }

  /**
   * 提取常用词汇
   */
  private extractCommonWords(names: string[]): string[] {
    const wordCount = new Map<string, number>();
    
    names.forEach(name => {
      const words = name.toLowerCase().split(/\s+/);
      words.forEach(word => {
        if (word.length > 2) {
          wordCount.set(word, (wordCount.get(word) || 0) + 1);
        }
      });
    });
    
    return Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);
  }

  /**
   * 格式化文件名
   */
  private formatFileName(name: string, options: NamingOptions): string {
    let formatted = name.trim();
    
    // 添加前缀
    if (options.customPrefix) {
      formatted = `${options.customPrefix}${formatted}`;
    }
    
    // 添加后缀
    if (options.customSuffix) {
      formatted = `${formatted}${options.customSuffix}`;
    }
    
    // 长度限制
    if (formatted.length > options.maxLength) {
      formatted = formatted.substring(0, options.maxLength - 3) + '...';
    }
    
    return formatted;
  }

  /**
   * 格式化日期
   */
  private formatDate(date: Date, format: string): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day);
  }

  /**
   * 构建摘要生成提示
   */
  private buildSummaryPrompt(title: string, content: string, options: SummaryOptions): string {
    const lengthMap = {
      short: '50字以内',
      medium: '100字以内',
      long: '200字以内'
    };

    const typeMap = {
      overview: '概述性摘要',
      key_points: '要点摘要',
      abstract: '学术摘要'
    };

    return `
请为以下文档生成${typeMap[options.type]}：

文档标题: ${title}
文档内容: ${content.substring(0, 2000)}${content.length > 2000 ? '...' : ''}

要求：
- 摘要长度: ${lengthMap[options.length]}
- 语言: ${options.language === 'zh' ? '中文' : options.language === 'en' ? '英文' : '自动检测'}
- ${options.includeKeywords ? '包含关键词' : '不需要关键词'}

请返回JSON格式：
{
  "summary": "文档摘要",
  "keywords": ["关键词1", "关键词2"],
  "mainTopics": ["主题1", "主题2"],
  "confidence": 0.0-1.0
}
`;
  }

  /**
   * 解析摘要响应
   */
  private parseSummaryResponse(
    response: string,
    documentId: string,
    options: SummaryOptions
  ): DocumentSummary {
    try {
      const parsed = JSON.parse(response);
      return {
        documentId,
        summary: parsed.summary || '摘要生成失败',
        keywords: parsed.keywords || [],
        mainTopics: parsed.mainTopics || [],
        type: options.type,
        wordCount: (parsed.summary || '').length,
        generatedAt: new Date(),
        confidence: parsed.confidence || 0.7
      };
    } catch (error) {
      return {
        documentId,
        summary: '摘要解析失败',
        keywords: [],
        mainTopics: [],
        type: options.type,
        wordCount: 0,
        generatedAt: new Date(),
        confidence: 0.1
      };
    }
  }

  /**
   * 从文档内容中提取纯文本
   */
  private extractTextFromContent(content: string): string {
    try {
      const parsed = JSON.parse(content);
      return this.extractTextFromTipTapContent(parsed);
    } catch {
      return content;
    }
  }

  /**
   * 从TipTap内容中提取文本
   */
  private extractTextFromTipTapContent(content: any): string {
    if (!content || !content.content) return '';
    
    let text = '';
    
    const extractFromNode = (node: any): void => {
      if (node.text) {
        text += node.text + ' ';
      }
      
      if (node.content && Array.isArray(node.content)) {
        node.content.forEach(extractFromNode);
      }
    };
    
    content.content.forEach(extractFromNode);
    return text.trim();
  }
}

// 导出单例实例
export const aiFileNaming = new AIFileNamingService();