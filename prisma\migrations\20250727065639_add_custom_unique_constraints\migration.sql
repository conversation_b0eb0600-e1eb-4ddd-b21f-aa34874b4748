/*
  自定义唯一约束迁移

  说明：
  - 由于 Prisma 的 @@unique 不支持 WHERE 条件
  - 我们通过自定义 SQL 索引实现支持软删除的唯一约束
  - 这些约束确保同一用户在同一位置不能有重复名称的活跃项目

  约束逻辑：
  - 只对 isDeleted = false 的记录生效
  - 使用 COALESCE 处理 NULL 值（根目录）
  - 支持软删除后重新创建同名项目
*/

-- 文档唯一约束：同一用户在同一文件夹下不能有重复标题的活跃文档
CREATE UNIQUE INDEX IF NOT EXISTS unique_active_document_title
ON documents(userId, title, COALESCE(folderId, ''))
WHERE isDeleted = false;

-- 文件夹唯一约束：同一用户在同一父文件夹下不能有重复名称的活跃文件夹
CREATE UNIQUE INDEX IF NOT EXISTS unique_active_folder_name
ON folders(userId, name, COALESCE(parentId, ''))
WHERE isDeleted = false;