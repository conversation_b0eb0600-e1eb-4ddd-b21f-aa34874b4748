import { useState, useCallback, useRef, useEffect } from 'react';
import { Editor } from '@tiptap/react';
import { SelectionMenuAction } from '@/components/editor/SelectionMenu';

/**
 * 选择菜单配置
 */
export interface SelectionMenuConfig {
  /** 最小选择文本长度 */
  minSelectionLength?: number;
  /** 菜单显示延迟（毫秒） */
  showDelay?: number;
  /** 菜单隐藏延迟（毫秒） */
  hideDelay?: number;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 选择菜单状态
 */
export interface SelectionMenuState {
  visible: boolean;
  position: { x: number; y: number };
  selectedText: string;
  selectionRange: { from: number; to: number };
}

/**
 * 选择菜单 Hook
 * 提供选择菜单的状态管理和事件处理逻辑
 */
export function useSelectionMenu(
  editor: Editor | null,
  config: SelectionMenuConfig = {}
) {
  const {
    minSelectionLength = 1,
    showDelay = 100,
    hideDelay = 300,
    enabled = true
  } = config;

  const [menuState, setMenuState] = useState<SelectionMenuState>({
    visible: false,
    position: { x: 0, y: 0 },
    selectedText: '',
    selectionRange: { from: 0, to: 0 }
  });

  const showTimeoutRef = useRef<NodeJS.Timeout>();
  const hideTimeoutRef = useRef<NodeJS.Timeout>();

  /**
   * 计算菜单的最佳显示位置
   */
  const calculateMenuPosition = useCallback((
    selectionCoords: { left: number; top: number; bottom: number },
    editorRect: DOMRect
  ) => {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // 基础位置：选择文本下方
    let x = selectionCoords.left - editorRect.left;
    let y = selectionCoords.bottom - editorRect.top + 8;
    
    // 估算菜单尺寸
    const estimatedMenuWidth = 280;
    const estimatedMenuHeight = 200;
    
    // 防止菜单超出右边界
    if (x + estimatedMenuWidth > viewportWidth - 20) {
      x = Math.max(10, viewportWidth - estimatedMenuWidth - 20);
    }
    
    // 防止菜单超出下边界，显示在选择文本上方
    if (selectionCoords.bottom + estimatedMenuHeight > viewportHeight - 20) {
      y = selectionCoords.top - editorRect.top - estimatedMenuHeight - 8;
    }
    
    // 确保菜单不会超出左边界和上边界
    x = Math.max(10, x);
    y = Math.max(10, y);
    
    return { x, y };
  }, []);

  /**
   * 显示菜单
   */
  const showMenu = useCallback((selectedText: string, range: { from: number; to: number }) => {
    if (!editor || !enabled) return;

    // 清除隐藏定时器
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = undefined;
    }

    // 计算菜单位置
    try {
      const coords = editor.view.coordsAtPos(range.to);
      const editorElement = editor.view.dom;
      const editorRect = editorElement.getBoundingClientRect();
      const position = calculateMenuPosition(coords, editorRect);

      setMenuState({
        visible: true,
        position,
        selectedText,
        selectionRange: range
      });
    } catch (error) {
      console.warn('无法计算菜单位置:', error);
    }
  }, [editor, enabled, calculateMenuPosition]);

  /**
   * 隐藏菜单
   */
  const hideMenu = useCallback(() => {
    // 清除显示定时器
    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current);
      showTimeoutRef.current = undefined;
    }

    setMenuState(prev => ({ ...prev, visible: false }));
  }, []);

  /**
   * 延迟隐藏菜单
   */
  const delayedHideMenu = useCallback(() => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }
    
    hideTimeoutRef.current = setTimeout(() => {
      hideMenu();
    }, hideDelay);
  }, [hideMenu, hideDelay]);

  /**
   * 处理文本选择变化
   */
  const handleSelectionChange = useCallback(() => {
    if (!editor || !enabled) return;

    const { from, to } = editor.state.selection;
    const selectedText = editor.state.doc.textBetween(from, to);

    // 如果没有选中文本或文本太短，隐藏菜单
    if (!selectedText.trim() || selectedText.length < minSelectionLength) {
      delayedHideMenu();
      return;
    }

    // 延迟显示菜单，避免频繁触发
    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current);
    }

    showTimeoutRef.current = setTimeout(() => {
      showMenu(selectedText, { from, to });
    }, showDelay);
  }, [editor, enabled, minSelectionLength, showDelay, showMenu, delayedHideMenu]);

  /**
   * 执行动作
   */
  const executeAction = useCallback((action: SelectionMenuAction) => {
    if (!editor) return;

    const { selectedText, selectionRange } = menuState;
    
    // 确保选择范围正确
    editor.chain().focus().setTextSelection(selectionRange).run();
    
    // 执行动作
    action.action(selectedText, editor, selectionRange);
    
    // 隐藏菜单
    hideMenu();
  }, [editor, menuState, hideMenu]);

  /**
   * 手动触发选择检测
   */
  const triggerSelectionCheck = useCallback(() => {
    setTimeout(handleSelectionChange, 10);
  }, [handleSelectionChange]);

  // 监听编辑器选择变化
  useEffect(() => {
    if (!editor) return;

    const handleUpdate = () => {
      triggerSelectionCheck();
    };

    editor.on('selectionUpdate', handleUpdate);
    
    return () => {
      editor.off('selectionUpdate', handleUpdate);
    };
  }, [editor, triggerSelectionCheck]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (showTimeoutRef.current) {
        clearTimeout(showTimeoutRef.current);
      }
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, []);

  return {
    menuState,
    showMenu,
    hideMenu,
    delayedHideMenu,
    executeAction,
    triggerSelectionCheck,
    handleSelectionChange
  };
}

/**
 * 选择菜单事件处理器 Hook
 * 提供鼠标和键盘事件的处理逻辑
 */
export function useSelectionMenuEvents(
  editor: Editor | null,
  menuVisible: boolean,
  menuElement: HTMLElement | null,
  onHide: () => void,
  onDelayedHide: () => void,
  onSelectionChange: () => void
) {
  // 监听鼠标事件
  useEffect(() => {
    const handleMouseDown = (event: MouseEvent) => {
      const target = event.target as Element;
      
      // 如果点击在菜单内部，不隐藏菜单
      if (menuElement?.contains(target)) {
        return;
      }
      
      // 如果点击在编辑器内部，延迟隐藏菜单（可能是新的选择）
      if (editor?.view.dom.contains(target)) {
        onDelayedHide();
        return;
      }
      
      // 点击在外部，立即隐藏菜单
      onHide();
    };

    const handleMouseUp = () => {
      // 鼠标释放后检查选择状态
      setTimeout(onSelectionChange, 10);
    };

    if (menuVisible) {
      document.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousedown', handleMouseDown);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [menuVisible, editor, menuElement, onHide, onDelayedHide, onSelectionChange]);

  // 监听键盘事件
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!menuVisible) return;

      // ESC 键隐藏菜单
      if (event.key === 'Escape') {
        event.preventDefault();
        onHide();
        return;
      }

      // 方向键或其他导航键隐藏菜单
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(event.key)) {
        onDelayedHide();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [menuVisible, onHide, onDelayedHide]);
}