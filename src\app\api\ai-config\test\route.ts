/**
 * AI 配置连接测试 API
 * 测试 AI 服务配置的连接状态
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { testAIConnection, validateAIConfig } from '@/lib/services/ai';
import type { AIServiceConfig } from '@/types/ai.types';

/**
 * 测试 AI 配置连接
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      provider,
      apiKey,
      endpoint,
      model,
      maxTokens = 2000,
      temperature = 0.7
    } = body;

    // 验证配置格式
    const config: AIServiceConfig = {
      provider,
      apiKey,
      endpoint,
      model,
      maxTokens,
      temperature
    };

    const validation = validateAIConfig(config);
    if (!validation.valid) {
      return NextResponse.json({
        success: false,
        error: '配置验证失败',
        details: validation.errors
      }, { status: 400 });
    }

    // 测试连接
    const testResult = await testAIConnection(config);

    return NextResponse.json({
      success: testResult.success,
      error: testResult.error,
      responseTime: testResult.responseTime,
      provider: config.provider,
      model: config.model
    });

  } catch (error) {
    console.error('AI 配置连接测试失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '连接测试失败'
    }, { status: 500 });
  }
}
