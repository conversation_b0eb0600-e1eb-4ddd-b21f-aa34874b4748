/**
 * AI 历史记录面板组件
 * 在 AI 助手面板中显示最近的交互记录
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  MessageSquare, 
  Bot, 
  FileText, 
  ChevronRight,
  RefreshCw,
  History,
  Search,
  Trash2
} from 'lucide-react';
import { useAIInteractionHistory } from '@/hooks/use-ai-interaction-history';
import type { AIInteractionWithDetails } from '@/lib/services/ai-interaction-history';

/**
 * AI 历史记录面板属性
 */
interface AIHistoryPanelProps {
  /** 显示的记录数量 */
  limit?: number;
  /** 是否显示搜索框 */
  showSearch?: boolean;
  /** 是否显示统计信息 */
  showStats?: boolean;
  /** 点击记录时的回调 */
  onInteractionClick?: (interaction: AIInteractionWithDetails) => void;
  /** 点击查看全部时的回调 */
  onViewAll?: () => void;
}

/**
 * AI 历史记录面板组件
 */
export function AIHistoryPanel({
  limit = 10,
  showSearch = true,
  showStats = true,
  onInteractionClick,
  onViewAll,
}: AIHistoryPanelProps) {
  const {
    fetchRecentInteractions,
    stats,
    fetchStats,
    deleteInteractions,
  } = useAIInteractionHistory();

  const [recentInteractions, setRecentInteractions] = useState<AIInteractionWithDetails[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedInteractions, setSelectedInteractions] = useState<string[]>([]);

  // 加载最近的交互记录
  const loadRecentInteractions = async () => {
    setLoading(true);
    try {
      const interactions = await fetchRecentInteractions(limit);
      setRecentInteractions(interactions);
    } catch (error) {
      console.error('加载最近交互记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    loadRecentInteractions();
    if (showStats) {
      fetchStats();
    }
  }, [limit, showStats]);

  /**
   * 过滤交互记录
   */
  const filteredInteractions = recentInteractions.filter(interaction => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return (
      interaction.input.toLowerCase().includes(searchLower) ||
      interaction.output.toLowerCase().includes(searchLower) ||
      interaction.document.title.toLowerCase().includes(searchLower) ||
      interaction.type.toLowerCase().includes(searchLower)
    );
  });

  /**
   * 格式化交互类型
   */
  const formatInteractionType = (type: string) => {
    const typeMap: Record<string, string> = {
      generate: '生成',
      rewrite: '改写',
      summarize: '总结',
      analyze: '分析',
      translate: '翻译',
      explain: '解释',
    };
    return typeMap[type] || type;
  };

  /**
   * 格式化提供商名称
   */
  const formatProvider = (provider: string) => {
    const providerMap: Record<string, string> = {
      openai: 'OpenAI',
      ollama: 'Ollama',
      gemini: 'Gemini',
    };
    return providerMap[provider] || provider;
  };

  /**
   * 格式化相对时间
   */
  const formatRelativeTime = (date: string) => {
    const now = new Date();
    const past = new Date(date);
    const diffMs = now.getTime() - past.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins} 分钟前`;
    if (diffHours < 24) return `${diffHours} 小时前`;
    if (diffDays < 7) return `${diffDays} 天前`;
    
    return past.toLocaleDateString('zh-CN');
  };

  /**
   * 截断文本
   */
  const truncateText = (text: string, maxLength = 60) => {
    return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
  };

  /**
   * 选择/取消选择交互记录
   */
  const toggleInteractionSelection = (interactionId: string) => {
    setSelectedInteractions(prev => 
      prev.includes(interactionId)
        ? prev.filter(id => id !== interactionId)
        : [...prev, interactionId]
    );
  };

  /**
   * 删除选中的交互记录
   */
  const handleDeleteSelected = async () => {
    if (selectedInteractions.length === 0) return;
    
    try {
      await deleteInteractions(selectedInteractions);
      setSelectedInteractions([]);
      await loadRecentInteractions(); // 重新加载数据
    } catch (error) {
      console.error('删除记录失败:', error);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* 头部 */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <History className="w-4 h-4 text-gray-600" />
          <h3 className="font-medium text-gray-900">交互历史</h3>
        </div>
        
        <div className="flex items-center space-x-1">
          {/* 删除选中 */}
          {selectedInteractions.length > 0 && (
            <button
              onClick={handleDeleteSelected}
              className="p-1 text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
              title="删除选中"
            >
              <Trash2 className="w-3 h-3" />
            </button>
          )}
          
          {/* 刷新按钮 */}
          <button
            onClick={loadRecentInteractions}
            disabled={loading}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors disabled:opacity-50"
            title="刷新"
          >
            <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
          </button>
          
          {/* 查看全部按钮 */}
          {onViewAll && (
            <button
              onClick={onViewAll}
              className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
              title="查看全部"
            >
              <ChevronRight className="w-3 h-3" />
            </button>
          )}
        </div>
      </div>

      {/* 统计信息 */}
      {showStats && stats.stats && (
        <div className="p-3 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-2 gap-2 text-center">
            <div>
              <div className="text-lg font-semibold text-blue-600">
                {stats.stats.totalInteractions}
              </div>
              <div className="text-xs text-gray-600">总交互</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-green-600">
                {stats.stats.totalTokens.toLocaleString()}
              </div>
              <div className="text-xs text-gray-600">总令牌</div>
            </div>
          </div>
        </div>
      )}

      {/* 搜索框 */}
      {showSearch && (
        <div className="p-3 border-b border-gray-200">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索历史记录..."
              className="w-full pl-7 pr-3 py-1.5 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      )}

      {/* 交互记录列表 */}
      <div className="flex-1 overflow-auto">
        {loading ? (
          <div className="flex items-center justify-center h-20">
            <RefreshCw className="w-4 h-4 animate-spin text-gray-400" />
            <span className="ml-2 text-sm text-gray-600">加载中...</span>
          </div>
        ) : filteredInteractions.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-20 text-gray-500">
            <MessageSquare className="w-8 h-8 mb-1 opacity-50" />
            <p className="text-sm">
              {searchTerm ? '未找到匹配的记录' : '暂无交互记录'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredInteractions.map((interaction) => (
              <div
                key={interaction.id}
                className={`p-3 hover:bg-gray-50 cursor-pointer transition-colors ${
                  selectedInteractions.includes(interaction.id) ? 'bg-blue-50' : ''
                }`}
                onClick={() => onInteractionClick?.(interaction)}
              >
                <div className="flex items-start space-x-2">
                  {/* 选择框 */}
                  <input
                    type="checkbox"
                    checked={selectedInteractions.includes(interaction.id)}
                    onChange={(e) => {
                      e.stopPropagation();
                      toggleInteractionSelection(interaction.id);
                    }}
                    className="w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1"
                  />

                  {/* AI 图标 */}
                  <div className="flex-shrink-0">
                    <Bot className="w-5 h-5 text-blue-600 bg-blue-100 rounded-full p-0.5" />
                  </div>

                  {/* 内容 */}
                  <div className="flex-1 min-w-0">
                    {/* 类型和时间 */}
                    <div className="flex items-center justify-between mb-1">
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        {formatInteractionType(interaction.type)}
                      </span>
                      <span className="text-xs text-gray-500">
                        {formatRelativeTime(interaction.createdAt.toISOString())}
                      </span>
                    </div>

                    {/* 文档标题 */}
                    <div className="flex items-center mb-1 text-xs text-gray-600">
                      <FileText className="w-3 h-3 mr-1" />
                      <span className="truncate">{interaction.document.title}</span>
                    </div>

                    {/* 输入预览 */}
                    <div className="text-xs text-gray-700 mb-1">
                      <span className="font-medium">输入：</span>
                      {truncateText(interaction.input, 40)}
                    </div>

                    {/* 输出预览 */}
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">输出：</span>
                      {truncateText(interaction.output, 40)}
                    </div>

                    {/* 提供商和令牌 */}
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs text-gray-500">
                        {formatProvider(interaction.provider)}
                      </span>
                      {interaction.tokens > 0 && (
                        <span className="text-xs text-gray-500">
                          {interaction.tokens} tokens
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部操作 */}
      {filteredInteractions.length > 0 && onViewAll && (
        <div className="p-3 border-t border-gray-200">
          <button
            onClick={onViewAll}
            className="w-full py-2 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors"
          >
            查看全部历史记录
          </button>
        </div>
      )}
    </div>
  );
}