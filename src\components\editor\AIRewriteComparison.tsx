'use client';

import React, { useState, useCallback } from 'react';
import { TextRewriteResult, TextChange } from '@/lib/services/ai/text-rewrite-service';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  CheckIcon, 
  XIcon, 
  RefreshCwIcon, 
  CopyIcon,
  EyeIcon,
  EditIcon,
  GitCompareIcon
} from 'lucide-react';

/**
 * 改写对比组件的属性
 */
interface AIRewriteComparisonProps {
  /** 改写结果 */
  result: TextRewriteResult;
  /** 是否显示 */
  visible: boolean;
  /** 接受改写结果的回调 */
  onAccept: (text: string) => void;
  /** 拒绝改写结果的回调 */
  onReject: () => void;
  /** 重新生成的回调 */
  onRegenerate: () => void;
  /** 是否正在重新生成 */
  isRegenerating?: boolean;
  /** 显示位置 */
  position?: { top: number; left: number };
  /** 自定义类名 */
  className?: string;
}

/**
 * 改写类型的显示名称
 */
const REWRITE_TYPE_NAMES = {
  improve: '改进优化',
  grammar: '语法检查',
  style: '风格调整',
  simplify: '简化',
  expand: '扩展',
  tone: '语调调整'
};

/**
 * 改动类型的显示名称和颜色
 */
const CHANGE_TYPE_CONFIG = {
  grammar: { name: '语法', color: 'bg-red-100 text-red-800' },
  style: { name: '风格', color: 'bg-blue-100 text-blue-800' },
  structure: { name: '结构', color: 'bg-green-100 text-green-800' },
  vocabulary: { name: '用词', color: 'bg-purple-100 text-purple-800' },
  tone: { name: '语调', color: 'bg-orange-100 text-orange-800' }
};

/**
 * AI 改写对比组件
 * 显示原文和改写结果的对比，支持多个版本选择
 */
export function AIRewriteComparison({
  result,
  visible,
  onAccept,
  onReject,
  onRegenerate,
  isRegenerating = false,
  position,
  className = ''
}: AIRewriteComparisonProps) {
  const [selectedVersion, setSelectedVersion] = useState(0);
  const [viewMode, setViewMode] = useState<'side-by-side' | 'overlay'>('side-by-side');
  const [copiedText, setCopiedText] = useState<string | null>(null);

  // 获取所有版本（主结果 + 备选方案）
  const allVersions = [result.rewrittenText, ...result.alternatives];

  /**
   * 复制文本到剪贴板
   */
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(text);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  }, []);

  /**
   * 接受选中的版本
   */
  const handleAccept = useCallback(() => {
    const selectedText = allVersions[selectedVersion];
    onAccept(selectedText);
  }, [allVersions, selectedVersion, onAccept]);

  /**
   * 渲染改动标记
   */
  const renderChanges = useCallback((changes: TextChange[]) => {
    if (changes.length === 0) return null;

    return (
      <div className="mt-3 space-y-2">
        <h4 className="text-sm font-medium text-gray-700">主要改动：</h4>
        <div className="space-y-1">
          {changes.map((change, index) => (
            <div key={index} className="flex items-start gap-2 text-sm">
              <Badge 
                variant="secondary" 
                className={`text-xs ${CHANGE_TYPE_CONFIG[change.type]?.color || 'bg-gray-100 text-gray-800'}`}
              >
                {CHANGE_TYPE_CONFIG[change.type]?.name || change.type}
              </Badge>
              <span className="text-gray-600 flex-1">{change.description}</span>
            </div>
          ))}
        </div>
      </div>
    );
  }, []);

  /**
   * 渲染文本对比
   */
  const renderTextComparison = useCallback(() => {
    const selectedText = allVersions[selectedVersion];

    if (viewMode === 'side-by-side') {
      return (
        <div className="grid grid-cols-2 gap-4">
          {/* 原文 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-700">原文</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(result.originalText)}
                className="h-6 w-6 p-0"
              >
                <CopyIcon className="h-3 w-3" />
              </Button>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg border text-sm leading-relaxed">
              {result.originalText}
            </div>
          </div>

          {/* 改写结果 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-700">改写结果</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(selectedText)}
                className="h-6 w-6 p-0"
              >
                <CopyIcon className="h-3 w-3" />
              </Button>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg border text-sm leading-relaxed">
              {selectedText}
            </div>
          </div>
        </div>
      );
    }

    // 叠加模式（暂时简化实现）
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode('side-by-side')}
            className={viewMode === 'side-by-side' ? 'bg-blue-100' : ''}
          >
            <GitCompareIcon className="h-4 w-4 mr-1" />
            对比
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode('overlay')}
            className={viewMode === 'overlay' ? 'bg-blue-100' : ''}
          >
            <EyeIcon className="h-4 w-4 mr-1" />
            预览
          </Button>
        </div>
        
        <div className="p-3 bg-blue-50 rounded-lg border text-sm leading-relaxed">
          {selectedText}
        </div>
      </div>
    );
  }, [allVersions, selectedVersion, viewMode, result.originalText, copyToClipboard]);

  if (!visible) return null;

  return (
    <div
      className={`
        fixed bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50
        max-w-4xl w-full max-h-[80vh] overflow-y-auto
        animate-in slide-in-from-bottom-2 fade-in duration-200
        ${className}
      `}
      style={position ? {
        top: position.top,
        left: Math.min(position.left, window.innerWidth - 800), // 防止超出屏幕
      } : {
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)'
      }}
    >
      {/* 头部 */}
      <div className="flex items-center justify-between mb-4 pb-3 border-b">
        <div className="flex items-center gap-2">
          <EditIcon className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold">
            {REWRITE_TYPE_NAMES[result.type] || '文本改写'}
          </h3>
          {result.style && (
            <Badge variant="outline" className="text-xs">
              {result.style}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">
            {result.responseTime}ms · {result.tokensUsed} tokens
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={onReject}
            className="h-6 w-6 p-0"
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 版本选择 */}
      {allVersions.length > 1 && (
        <div className="mb-4">
          <Tabs value={selectedVersion.toString()} onValueChange={(value) => setSelectedVersion(parseInt(value))}>
            <TabsList className="grid w-full grid-cols-3">
              {allVersions.map((_, index) => (
                <TabsTrigger key={index} value={index.toString()}>
                  版本 {index + 1}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>
      )}

      {/* 文本对比 */}
      <div className="mb-4">
        {renderTextComparison()}
      </div>

      {/* 改写说明 */}
      {result.explanation && (
        <div className="mb-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
          <h4 className="text-sm font-medium text-yellow-800 mb-1">改写说明</h4>
          <p className="text-sm text-yellow-700">{result.explanation}</p>
        </div>
      )}

      {/* 改动详情 */}
      {renderChanges(result.changes)}

      {/* 操作按钮 */}
      <div className="flex items-center justify-between pt-4 border-t mt-4">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onRegenerate}
            disabled={isRegenerating}
            className="flex items-center gap-1"
          >
            <RefreshCwIcon className={`h-4 w-4 ${isRegenerating ? 'animate-spin' : ''}`} />
            {isRegenerating ? '生成中...' : '重新生成'}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => copyToClipboard(allVersions[selectedVersion])}
            className="flex items-center gap-1"
          >
            <CopyIcon className="h-4 w-4" />
            {copiedText === allVersions[selectedVersion] ? '已复制' : '复制'}
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={onReject}
          >
            取消
          </Button>
          <Button
            onClick={handleAccept}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <CheckIcon className="h-4 w-4 mr-1" />
            应用改写
          </Button>
        </div>
      </div>
    </div>
  );
}

/**
 * 改写加载状态组件
 */
interface AIRewriteLoadingProps {
  visible: boolean;
  onCancel: () => void;
  message?: string;
  position?: { top: number; left: number };
}

export function AIRewriteLoading({
  visible,
  onCancel,
  message = 'AI 正在改写文本...',
  position
}: AIRewriteLoadingProps) {
  if (!visible) return null;

  return (
    <div
      className="
        fixed bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50
        animate-in slide-in-from-bottom-2 fade-in duration-200
      "
      style={position ? {
        top: position.top,
        left: position.left,
      } : {
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)'
      }}
    >
      <div className="flex items-center gap-3">
        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
        <span className="text-sm text-gray-700">{message}</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          className="h-6 w-6 p-0 ml-2"
        >
          <XIcon className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}