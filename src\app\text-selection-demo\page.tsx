'use client';

import React, { useState } from 'react';
import { Editor } from '@/components/editor/Editor';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/card';

/**
 * 文本选择菜单演示页面
 */
export default function TextSelectionDemoPage() {
  const [content, setContent] = useState(`
    <h1>文本选择菜单演示</h1>
    
    <p>这是一个演示文本选择菜单功能的页面。请尝试以下操作：</p>
    
    <h2>基本功能测试</h2>
    <p>选择这段文本来测试基本的选择菜单功能。菜单应该在选择文本后自动显示，包含格式化和实用工具选项。</p>
    
    <h2>AI 功能测试</h2>
    <p>选择这段文本来测试 AI 相关的 Chat 和 Edit 功能。这些功能将为用户提供智能的文本处理选项，包括解释、翻译、改写等。</p>
    
    <h2>长文本测试</h2>
    <p>这是一段较长的文本，用于测试选择菜单在处理长文本时的表现。选择菜单应该能够正确计算位置，避免超出视窗边界，并且能够处理不同长度的选择文本。当选择的文本很长时，菜单应该仍然能够正常显示和工作。</p>
    
    <h2>格式化文本测试</h2>
    <p>这段文本包含<strong>粗体</strong>、<em>斜体</em>和<u>下划线</u>等格式。选择包含格式的文本时，菜单应该能够正确处理，并提供相应的格式化选项。</p>
    
    <blockquote>
      <p>这是一个引用块。选择引用块中的文本来测试菜单在不同元素类型中的表现。</p>
    </blockquote>
    
    <ul>
      <li>列表项目 1 - 选择这个项目测试列表中的文本选择</li>
      <li>列表项目 2 - 菜单应该能够在列表项目中正常工作</li>
      <li>列表项目 3 - 包含多种格式和内容的项目</li>
    </ul>
    
    <h2>边界情况测试</h2>
    <p>短文本</p>
    <p>这是一个测试菜单位置计算的段落，特别是当选择的文本接近页面边缘时的情况。</p>
  `);

  const [logs, setLogs] = useState<string[]>([]);

  /**
   * 添加日志
   */
  const addLog = (message: string) => {
    setLogs(prev => [...prev.slice(-9), `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  /**
   * Chat 动作处理器
   */
  const handleChatAction = (type: string, selectedText: string) => {
    addLog(`Chat 动作: ${type} - "${selectedText.substring(0, 50)}${selectedText.length > 50 ? '...' : ''}"`);
  };

  /**
   * Edit 动作处理器
   */
  const handleEditAction = (type: string, selectedText: string) => {
    addLog(`Edit 动作: ${type} - "${selectedText.substring(0, 50)}${selectedText.length > 50 ? '...' : ''}"`);
  };

  /**
   * 清除日志
   */
  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            文本选择菜单演示
          </h1>
          <p className="text-gray-600 mb-6">
            这个页面演示了文本选择菜单的功能。选择编辑器中的任意文本来查看菜单。
          </p>
          
          {/* 使用说明 */}
          <Card className="p-4 mb-6 bg-blue-50 border-blue-200">
            <h3 className="font-semibold text-blue-900 mb-2">使用说明：</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 选择编辑器中的任意文本（至少2个字符）</li>
              <li>• 菜单将自动显示在选择文本附近</li>
              <li>• 菜单包含 Chat、Edit、格式化和实用工具选项</li>
              <li>• 点击菜单外部或按 ESC 键可隐藏菜单</li>
              <li>• 菜单会自动调整位置以避免超出视窗边界</li>
            </ul>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 编辑器区域 */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">编辑器</h2>
              <div className="border rounded-lg">
                <Editor
                  content={content}
                  onChange={setContent}
                  placeholder="开始编辑文档..."
                  enableAI={true}
                  className="min-h-[600px]"
                />
              </div>
            </Card>
          </div>

          {/* 日志区域 */}
          <div className="lg:col-span-1">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">操作日志</h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearLogs}
                  disabled={logs.length === 0}
                >
                  清除
                </Button>
              </div>
              
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {logs.length === 0 ? (
                  <p className="text-gray-500 text-sm">
                    选择文本并点击菜单选项来查看日志
                  </p>
                ) : (
                  logs.map((log, index) => (
                    <div
                      key={index}
                      className="text-xs p-2 bg-gray-100 rounded border-l-2 border-blue-400"
                    >
                      {log}
                    </div>
                  ))
                )}
              </div>
            </Card>

            {/* 功能状态 */}
            <Card className="p-6 mt-6">
              <h3 className="font-semibold mb-3">功能状态</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span>文本选择检测</span>
                  <span className="text-green-600 font-medium">✓ 启用</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>菜单自动显示</span>
                  <span className="text-green-600 font-medium">✓ 启用</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>位置自动调整</span>
                  <span className="text-green-600 font-medium">✓ 启用</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Chat 功能</span>
                  <span className="text-green-600 font-medium">✓ 启用</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Edit 功能</span>
                  <span className="text-green-600 font-medium">✓ 启用</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>格式化工具</span>
                  <span className="text-green-600 font-medium">✓ 启用</span>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* 测试指南 */}
        <Card className="p-6 mt-8">
          <h2 className="text-xl font-semibold mb-4">测试指南</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">基本功能测试</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>1. 选择短文本（2-10个字符）</li>
                <li>2. 选择长文本（50+个字符）</li>
                <li>3. 选择包含格式的文本</li>
                <li>4. 在不同位置选择文本</li>
                <li>5. 测试菜单的自动隐藏</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2">交互功能测试</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>1. 点击 Chat 选项（解释、翻译等）</li>
                <li>2. 点击 Edit 选项（改进、重写等）</li>
                <li>3. 使用格式化工具（加粗、斜体等）</li>
                <li>4. 测试键盘快捷键</li>
                <li>5. 测试菜单位置调整</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}