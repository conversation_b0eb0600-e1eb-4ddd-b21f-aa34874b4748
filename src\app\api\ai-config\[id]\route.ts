/**
 * 单个 AI 配置管理 API
 * 处理特定配置的获取、更新和删除操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';
import { validateAIConfig } from '@/lib/services/ai';
import type { AIServiceConfig } from '@/types/ai.types';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * 获取单个 AI 配置（包含完整信息）
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const config = await prisma.aIConfiguration.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    });

    if (!config) {
      return NextResponse.json(
        { error: '配置不存在' },
        { status: 404 }
      );
    }

    // 返回完整配置信息（用于编辑）
    const fullConfig = {
      id: config.id,
      provider: config.provider,
      apiKey: config.apiKey,
      endpoint: config.endpoint,
      model: config.model,
      maxTokens: config.maxTokens,
      temperature: config.temperature,
      isDefault: config.isDefault,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt
    };

    return NextResponse.json({ config: fullConfig });
  } catch (error) {
    console.error('获取 AI 配置失败:', error);
    return NextResponse.json(
      { error: '获取配置失败' },
      { status: 500 }
    );
  }
}

/**
 * 更新 AI 配置
 */
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 验证配置是否存在且属于当前用户
    const existingConfig = await prisma.aIConfiguration.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    });

    if (!existingConfig) {
      return NextResponse.json(
        { error: '配置不存在' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const {
      provider,
      apiKey,
      endpoint,
      model,
      maxTokens,
      temperature,
      isDefault
    } = body;

    // 验证新配置
    const config: AIServiceConfig = {
      provider,
      apiKey,
      endpoint,
      model,
      maxTokens,
      temperature
    };

    const validation = validateAIConfig(config);
    if (!validation.valid) {
      return NextResponse.json(
        { error: '配置验证失败', details: validation.errors },
        { status: 400 }
      );
    }

    // 如果设置为默认配置，先取消其他默认配置
    if (isDefault && !existingConfig.isDefault) {
      await prisma.aIConfiguration.updateMany({
        where: {
          userId: session.user.id,
          isDefault: true,
          id: { not: params.id }
        },
        data: {
          isDefault: false
        }
      });
    }

    // 更新配置
    const updatedConfig = await prisma.aIConfiguration.update({
      where: {
        id: params.id
      },
      data: {
        provider,
        apiKey,
        endpoint,
        model,
        maxTokens,
        temperature,
        isDefault
      }
    });

    // 返回安全的配置信息
    const safeConfig = {
      id: updatedConfig.id,
      provider: updatedConfig.provider,
      model: updatedConfig.model,
      endpoint: updatedConfig.endpoint,
      maxTokens: updatedConfig.maxTokens,
      temperature: updatedConfig.temperature,
      isDefault: updatedConfig.isDefault,
      hasApiKey: !!updatedConfig.apiKey,
      createdAt: updatedConfig.createdAt,
      updatedAt: updatedConfig.updatedAt
    };

    return NextResponse.json({ config: safeConfig });
  } catch (error) {
    console.error('更新 AI 配置失败:', error);
    return NextResponse.json(
      { error: '更新配置失败' },
      { status: 500 }
    );
  }
}

/**
 * 删除 AI 配置
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 验证配置是否存在且属于当前用户
    const existingConfig = await prisma.aIConfiguration.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    });

    if (!existingConfig) {
      return NextResponse.json(
        { error: '配置不存在' },
        { status: 404 }
      );
    }

    // 检查是否为默认配置
    if (existingConfig.isDefault) {
      // 检查是否还有其他配置
      const otherConfigs = await prisma.aIConfiguration.findMany({
        where: {
          userId: session.user.id,
          id: { not: params.id }
        },
        orderBy: { createdAt: 'desc' }
      });

      // 如果有其他配置，将最新的设为默认
      if (otherConfigs.length > 0) {
        await prisma.aIConfiguration.update({
          where: { id: otherConfigs[0].id },
          data: { isDefault: true }
        });
      }
    }

    // 删除配置
    await prisma.aIConfiguration.delete({
      where: { id: params.id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('删除 AI 配置失败:', error);
    return NextResponse.json(
      { error: '删除配置失败' },
      { status: 500 }
    );
  }
}