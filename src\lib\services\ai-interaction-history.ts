/**
 * AI 交互历史记录服务
 * 提供 AI 交互记录的存储、查询和管理功能
 */

import { prisma } from '@/lib/db';
import type { AIInteraction, User, Document } from '@prisma/client';

/**
 * AI 交互历史记录扩展类型
 */
export interface AIInteractionWithDetails extends AIInteraction {
  document: Pick<Document, 'id' | 'title'>;
  user: Pick<User, 'id' | 'name' | 'email'>;
}

/**
 * 历史记录查询参数
 */
export interface HistoryQueryParams {
  /** 用户 ID */
  userId: string;
  /** 文档 ID（可选） */
  documentId?: string;
  /** 交互类型过滤 */
  type?: string;
  /** 服务提供商过滤 */
  provider?: string;
  /** 搜索关键词 */
  search?: string;
  /** 日期范围 - 开始 */
  dateFrom?: Date;
  /** 日期范围 - 结束 */
  dateTo?: Date;
  /** 分页 - 页码 */
  page?: number;
  /** 分页 - 每页数量 */
  limit?: number;
  /** 排序字段 */
  sortBy?: 'createdAt' | 'type' | 'provider' | 'tokens';
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
}

/**
 * 历史记录查询结果
 */
export interface HistoryQueryResult {
  /** 交互记录列表 */
  interactions: AIInteractionWithDetails[];
  /** 总记录数 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 总页数 */
  totalPages: number;
}

/**
 * 历史记录统计信息
 */
export interface HistoryStats {
  /** 总交互次数 */
  totalInteractions: number;
  /** 总消耗令牌数 */
  totalTokens: number;
  /** 按类型分组的统计 */
  byType: Record<string, number>;
  /** 按提供商分组的统计 */
  byProvider: Record<string, number>;
  /** 按日期分组的统计（最近30天） */
  byDate: Array<{ date: string; count: number; tokens: number }>;
}

/**
 * AI 交互历史记录服务类
 */
export class AIInteractionHistoryService {
  /**
   * 创建新的 AI 交互记录
   */
  static async createInteraction(data: {
    documentId: string;
    userId: string;
    type: string;
    input: string;
    output: string;
    provider: string;
    model: string;
    tokens?: number;
  }): Promise<AIInteraction> {
    return await prisma.aIInteraction.create({
      data: {
        documentId: data.documentId,
        userId: data.userId,
        type: data.type,
        input: data.input,
        output: data.output,
        provider: data.provider,
        model: data.model,
        tokens: data.tokens || 0,
      },
    });
  }

  /**
   * 查询用户的 AI 交互历史记录
   */
  static async getInteractionHistory(
    params: HistoryQueryParams
  ): Promise<HistoryQueryResult> {
    const {
      userId,
      documentId,
      type,
      provider,
      search,
      dateFrom,
      dateTo,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = params;

    // 构建查询条件
    const where: any = {
      userId,
    };

    if (documentId) {
      where.documentId = documentId;
    }

    if (type) {
      where.type = type;
    }

    if (provider) {
      where.provider = provider;
    }

    if (search) {
      where.OR = [
        { input: { contains: search } },
        { output: { contains: search } },
        { document: { title: { contains: search } } },
      ];
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = dateFrom;
      }
      if (dateTo) {
        where.createdAt.lte = dateTo;
      }
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 执行查询
    const [interactions, total] = await Promise.all([
      prisma.aIInteraction.findMany({
        where,
        include: {
          document: {
            select: {
              id: true,
              title: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
      }),
      prisma.aIInteraction.count({ where }),
    ]);

    return {
      interactions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 获取用户的 AI 交互统计信息
   */
  static async getInteractionStats(userId: string): Promise<HistoryStats> {
    // 获取总体统计
    const totalStats = await prisma.aIInteraction.aggregate({
      where: { userId },
      _count: { id: true },
      _sum: { tokens: true },
    });

    // 按类型统计
    const typeStats = await prisma.aIInteraction.groupBy({
      by: ['type'],
      where: { userId },
      _count: { id: true },
    });

    // 按提供商统计
    const providerStats = await prisma.aIInteraction.groupBy({
      by: ['provider'],
      where: { userId },
      _count: { id: true },
    });

    // 按日期统计（最近30天）
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dateStats = await prisma.$queryRaw<
      Array<{ date: string; count: number; tokens: number }>
    >`
      SELECT 
        DATE(createdAt) as date,
        COUNT(*) as count,
        SUM(tokens) as tokens
      FROM ai_interactions 
      WHERE userId = ${userId} 
        AND createdAt >= ${thirtyDaysAgo}
      GROUP BY DATE(createdAt)
      ORDER BY date DESC
    `;

    return {
      totalInteractions: totalStats._count.id || 0,
      totalTokens: totalStats._sum.tokens || 0,
      byType: Object.fromEntries(
        typeStats.map((stat) => [stat.type, stat._count.id])
      ),
      byProvider: Object.fromEntries(
        providerStats.map((stat) => [stat.provider, stat._count.id])
      ),
      byDate: dateStats.map((stat) => ({
        date: stat.date,
        count: Number(stat.count),
        tokens: Number(stat.tokens),
      })),
    };
  }

  /**
   * 删除指定的 AI 交互记录
   */
  static async deleteInteraction(
    interactionId: string,
    userId: string
  ): Promise<boolean> {
    try {
      await prisma.aIInteraction.delete({
        where: {
          id: interactionId,
          userId, // 确保只能删除自己的记录
        },
      });
      return true;
    } catch (error) {
      console.error('删除 AI 交互记录失败:', error);
      return false;
    }
  }

  /**
   * 批量删除 AI 交互记录
   */
  static async deleteInteractions(
    interactionIds: string[],
    userId: string
  ): Promise<number> {
    try {
      const result = await prisma.aIInteraction.deleteMany({
        where: {
          id: { in: interactionIds },
          userId, // 确保只能删除自己的记录
        },
      });
      return result.count;
    } catch (error) {
      console.error('批量删除 AI 交互记录失败:', error);
      return 0;
    }
  }

  /**
   * 清空用户的所有 AI 交互记录
   */
  static async clearAllInteractions(userId: string): Promise<number> {
    try {
      const result = await prisma.aIInteraction.deleteMany({
        where: { userId },
      });
      return result.count;
    } catch (error) {
      console.error('清空 AI 交互记录失败:', error);
      return 0;
    }
  }

  /**
   * 获取指定文档的 AI 交互记录
   */
  static async getDocumentInteractions(
    documentId: string,
    userId: string,
    limit = 10
  ): Promise<AIInteractionWithDetails[]> {
    return await prisma.aIInteraction.findMany({
      where: {
        documentId,
        userId,
      },
      include: {
        document: {
          select: {
            id: true,
            title: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    });
  }

  /**
   * 获取最近的 AI 交互记录
   */
  static async getRecentInteractions(
    userId: string,
    limit = 5
  ): Promise<AIInteractionWithDetails[]> {
    return await prisma.aIInteraction.findMany({
      where: { userId },
      include: {
        document: {
          select: {
            id: true,
            title: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    });
  }
}