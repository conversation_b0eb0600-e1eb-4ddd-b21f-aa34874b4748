'use client';

import { LocalDocument } from '@/lib/storage/database';
import { AppError } from '@/types';
import { DocumentCard } from './DocumentCard';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';

interface RecentDocumentsProps {
  documents: LocalDocument[];
  loading: boolean;
  error: AppError | null;
  onRefresh: () => void;
}

export function RecentDocuments({ documents, loading, error, onRefresh }: RecentDocumentsProps) {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">最近文档</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="border border-gray-200 rounded-lg p-4 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4 mb-3"></div>
              <div className="flex justify-between">
                <div className="h-3 bg-gray-200 rounded w-16"></div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">最近文档</h2>
        <div className="text-center py-8">
          <div className="text-red-500 mb-2">❌</div>
          <p className="text-gray-600 mb-4">加载文档时出现错误</p>
          <p className="text-sm text-red-600 mb-4">{error.message}</p>
          <Button onClick={onRefresh} variant="outline">
            重试
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900">最近文档</h2>
        <div className="flex space-x-2">
          <Button onClick={onRefresh} variant="outline" size="sm">
            刷新
          </Button>
          <Link href="/document-manager">
            <Button variant="outline" size="sm">
              查看全部
            </Button>
          </Link>
        </div>
      </div>

      {documents.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📄</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            还没有文档
          </h3>
          <p className="text-gray-600 mb-6">
            创建您的第一个文档开始写作之旅
          </p>
          <Link href="/editor">
            <Button>创建新文档</Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {documents.map((document) => (
            <DocumentCard
              key={document.id}
              document={document}
            />
          ))}
        </div>
      )}

      {documents.length > 0 && (
        <div className="mt-6 text-center">
          <Link href="/document-manager">
            <Button variant="outline">
              查看所有文档 ({documents.length > 6 ? '更多' : documents.length})
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
export default RecentDocuments;