'use client';

import { useState, useCallback } from 'react';
import { Editor } from '@tiptap/react';
import { ImageUpload } from './ImageUpload';
import { LinkInsert } from './LinkInsert';
import { PDFInsert } from './PDFInsert';

interface MediaInsertManagerProps {
  editor: Editor | null;
  className?: string;
}

export type MediaType = 'image' | 'link' | 'pdf' | 'video' | null;

/**
 * 媒体插入管理器
 * 统一管理各种媒体插入组件的显示和隐藏
 */
export function MediaInsertManager({ editor, className }: MediaInsertManagerProps) {
  const [activeMedia, setActiveMedia] = useState<MediaType>(null);

  /**
   * 打开媒体插入界面
   */
  const openMediaInsert = useCallback((mediaType: MediaType) => {
    setActiveMedia(mediaType);
  }, []);

  /**
   * 关闭媒体插入界面
   */
  const closeMediaInsert = useCallback(() => {
    setActiveMedia(null);
  }, []);

  // 将管理器方法暴露给全局，供斜杠命令使用
  if (typeof window !== 'undefined') {
    (window as any).mediaInsertManager = {
      openMediaInsert,
      closeMediaInsert,
    };
  }

  if (!activeMedia) {
    return null;
  }

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${className}`}>
      {/* 背景遮罩 */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={closeMediaInsert}
      />
      
      {/* 媒体插入组件 */}
      <div className="relative z-10">
        {activeMedia === 'image' && (
          <ImageUpload
            editor={editor}
            onClose={closeMediaInsert}
          />
        )}
        
        {activeMedia === 'link' && (
          <LinkInsert
            editor={editor}
            onClose={closeMediaInsert}
          />
        )}
        
        {activeMedia === 'pdf' && (
          <PDFInsert
            editor={editor}
            onClose={closeMediaInsert}
          />
        )}
        
        {activeMedia === 'video' && (
          <div className="bg-background border border-border rounded-lg shadow-lg p-6 w-96">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-4">视频插入</h3>
              <p className="text-muted-foreground mb-4">视频插入功能即将推出</p>
              <button
                onClick={closeMediaInsert}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
              >
                关闭
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * 媒体插入工具函数
 * 供斜杠命令调用
 */
export const mediaInsertUtils = {
  /**
   * 打开图片插入界面
   */
  openImageInsert: () => {
    if (typeof window !== 'undefined' && (window as any).mediaInsertManager) {
      (window as any).mediaInsertManager.openMediaInsert('image');
    }
  },

  /**
   * 打开链接插入界面
   */
  openLinkInsert: () => {
    if (typeof window !== 'undefined' && (window as any).mediaInsertManager) {
      (window as any).mediaInsertManager.openMediaInsert('link');
    }
  },

  /**
   * 打开 PDF 插入界面
   */
  openPDFInsert: () => {
    if (typeof window !== 'undefined' && (window as any).mediaInsertManager) {
      (window as any).mediaInsertManager.openMediaInsert('pdf');
    }
  },

  /**
   * 打开视频插入界面
   */
  openVideoInsert: () => {
    if (typeof window !== 'undefined' && (window as any).mediaInsertManager) {
      (window as any).mediaInsertManager.openMediaInsert('video');
    }
  },
};
export default MediaInsertManager;