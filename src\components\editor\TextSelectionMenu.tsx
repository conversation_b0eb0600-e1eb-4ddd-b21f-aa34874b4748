'use client';

import React, { useRef } from 'react';
import { Editor } from '@tiptap/react';
import { 
  MessageCircleIcon, 
  EditIcon, 
  SparklesIcon,
  LanguagesIcon,
  BookOpenIcon,
  LightbulbIcon
} from 'lucide-react';
import { SelectionMenu, SelectionMenuAction } from './SelectionMenu';
import { useSelectionMenu, useSelectionMenuEvents } from '@/hooks/useSelectionMenu';

/**
 * 文本选择菜单属性
 */
interface TextSelectionMenuProps {
  /** TipTap 编辑器实例 */
  editor: Editor;
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用 AI 功能 */
  enableAI?: boolean;
  /** 是否启用 Chat 功能 */
  enableChat?: boolean;
  /** 是否启用 Edit 功能 */
  enableEdit?: boolean;
  /** Chat 功能回调 */
  onChatAction?: (type: string, selectedText: string, editor: Editor) => void;
  /** Edit 功能回调 */
  onEditAction?: (type: string, selectedText: string, editor: Editor) => void;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 文本选择菜单组件
 * 当用户选择文本时显示 Chat 和 Edit 选项以及其他操作
 */
export function TextSelectionMenu({
  editor,
  enabled = true,
  enableAI = true,
  enableChat = true,
  enableEdit = true,
  onChatAction,
  onEditAction,
  className = ''
}: TextSelectionMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);
  
  const {
    menuState,
    hideMenu,
    delayedHideMenu,
    executeAction,
    handleSelectionChange
  } = useSelectionMenu(editor, {
    enabled,
    minSelectionLength: 2,
    showDelay: 150,
    hideDelay: 300
  });

  // 使用事件处理器 Hook
  useSelectionMenuEvents(
    editor,
    menuState.visible,
    menuRef.current,
    hideMenu,
    delayedHideMenu,
    handleSelectionChange
  );

  /**
   * 获取 Chat 动作列表
   */
  const getChatActions = (): SelectionMenuAction[] => {
    if (!enableAI || !enableChat) return [];

    return [
      {
        id: 'chat-explain',
        label: '解释',
        icon: <LightbulbIcon className="h-4 w-4" />,
        type: 'chat',
        description: '让 AI 解释选中的内容',
        action: (selectedText, editor) => {
          onChatAction?.('explain', selectedText, editor);
        }
      },
      {
        id: 'chat-translate',
        label: '翻译',
        icon: <LanguagesIcon className="h-4 w-4" />,
        type: 'chat',
        description: '翻译选中的文本',
        action: (selectedText, editor) => {
          onChatAction?.('translate', selectedText, editor);
        }
      },
      {
        id: 'chat-summarize',
        label: '总结',
        icon: <BookOpenIcon className="h-4 w-4" />,
        type: 'chat',
        description: '总结选中的内容',
        action: (selectedText, editor) => {
          onChatAction?.('summarize', selectedText, editor);
        }
      },
      {
        id: 'chat-discuss',
        label: '讨论',
        icon: <MessageCircleIcon className="h-4 w-4" />,
        type: 'chat',
        description: '与 AI 讨论选中的内容',
        action: (selectedText, editor) => {
          onChatAction?.('discuss', selectedText, editor);
        }
      }
    ];
  };

  /**
   * 获取 Edit 动作列表
   */
  const getEditActions = (): SelectionMenuAction[] => {
    if (!enableAI || !enableEdit) return [];

    return [
      {
        id: 'edit-improve',
        label: '改进',
        icon: <SparklesIcon className="h-4 w-4" />,
        type: 'edit',
        description: '改进选中文本的表达',
        action: (selectedText, editor) => {
          onEditAction?.('improve', selectedText, editor);
        }
      },
      {
        id: 'edit-rewrite',
        label: '重写',
        icon: <EditIcon className="h-4 w-4" />,
        type: 'edit',
        description: '重新组织选中的文本',
        action: (selectedText, editor) => {
          onEditAction?.('rewrite', selectedText, editor);
        }
      },
      {
        id: 'edit-formal',
        label: '正式化',
        icon: <BookOpenIcon className="h-4 w-4" />,
        type: 'edit',
        description: '调整为正式的表达风格',
        action: (selectedText, editor) => {
          onEditAction?.('formal', selectedText, editor);
        }
      },
      {
        id: 'edit-casual',
        label: '口语化',
        icon: <MessageCircleIcon className="h-4 w-4" />,
        type: 'edit',
        description: '调整为轻松的表达风格',
        action: (selectedText, editor) => {
          onEditAction?.('casual', selectedText, editor);
        }
      }
    ];
  };

  /**
   * 获取所有自定义动作
   */
  const getCustomActions = (): SelectionMenuAction[] => {
    const actions: SelectionMenuAction[] = [];
    
    // 添加 Chat 动作
    actions.push(...getChatActions());
    
    // 添加 Edit 动作
    actions.push(...getEditActions());
    
    return actions;
  };

  return (
    <div ref={menuRef}>
      <SelectionMenu
        editor={editor}
        enabled={enabled}
        actions={getCustomActions()}
        minSelectionLength={2}
        showDelay={150}
        hideDelay={300}
        showFormatActions={true}
        showUtilityActions={true}
        className={className}
      />
    </div>
  );
}

/**
 * 默认的 Chat 动作处理器
 */
export const defaultChatActionHandler = (
  type: string, 
  selectedText: string, 
  editor: Editor
) => {
  console.log(`Chat action: ${type}`, { selectedText, editor });
  
  // 这里可以集成实际的 AI Chat 功能
  // 例如：打开 AI 助手面板，发送选中文本进行处理
  
  switch (type) {
    case 'explain':
      // 打开解释对话
      break;
    case 'translate':
      // 打开翻译对话
      break;
    case 'summarize':
      // 打开总结对话
      break;
    case 'discuss':
      // 打开讨论对话
      break;
    default:
      console.warn(`未知的 Chat 动作类型: ${type}`);
  }
};

/**
 * 默认的 Edit 动作处理器
 */
export const defaultEditActionHandler = (
  type: string, 
  selectedText: string, 
  editor: Editor
) => {
  console.log(`Edit action: ${type}`, { selectedText, editor });
  
  // 这里可以集成实际的 AI Edit 功能
  // 例如：调用 AI 服务进行文本改写，然后替换选中文本
  
  switch (type) {
    case 'improve':
      // 调用 AI 改进文本
      break;
    case 'rewrite':
      // 调用 AI 重写文本
      break;
    case 'formal':
      // 调用 AI 正式化文本
      break;
    case 'casual':
      // 调用 AI 口语化文本
      break;
    default:
      console.warn(`未知的 Edit 动作类型: ${type}`);
  }
};