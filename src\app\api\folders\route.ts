import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';
import { z } from 'zod';

// 文件夹创建的验证模式
const createFolderSchema = z.object({
  name: z.string().min(1, '文件夹名称不能为空').max(255, '文件夹名称长度不能超过255个字符'),
  parentId: z.string().optional(),
});

/**
 * GET /api/folders - 获取当前用户的所有文件夹
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const parentId = searchParams.get('parentId');
    const includeDocuments = searchParams.get('includeDocuments') === 'true';
    const flat = searchParams.get('flat') === 'true';

    // 构建查询条件
    const whereClause: any = {
      userId: session.user.id,
      isDeleted: false, // 默认不包含已删除的文件夹
    };

    // 如果指定了父文件夹ID，只获取该文件夹下的子文件夹
    if (parentId) {
      whereClause.parentId = parentId;
    } else if (searchParams.has('rootOnly')) {
      // 只获取根级文件夹
      whereClause.parentId = null;
    }

    const includeOptions: any = {
      parent: {
        select: {
          id: true,
          name: true,
        },
      },
    };

    // 是否包含子文件夹（递归）
    if (!flat) {
      const childrenInclude: any = {
        children: true,
        _count: {
          select: {
            documents: true,
            children: true,
          },
        },
      };

      // 如果包含文档，也要在children中包含文档
      if (includeDocuments) {
        childrenInclude.documents = {
          select: {
            id: true,
            title: true,
            createdAt: true,
            updatedAt: true,
            wordCount: true,
            charCount: true,
          },
          orderBy: {
            updatedAt: 'desc',
          },
        };
      }

      includeOptions.children = {
        include: childrenInclude,
      };
    }

    // 是否包含文档信息
    if (includeDocuments) {
      includeOptions.documents = {
        select: {
          id: true,
          title: true,
          createdAt: true,
          updatedAt: true,
          wordCount: true,
          charCount: true,
        },
        orderBy: {
          updatedAt: 'desc',
        },
      };
    }

    // 添加统计信息
    includeOptions._count = {
      select: {
        documents: true,
        children: true,
      },
    };

    const folders = await prisma.folder.findMany({
      where: whereClause,
      include: includeOptions,
      orderBy: [
        { name: 'asc' },
        { createdAt: 'desc' },
      ],
    });

    return NextResponse.json({
      folders,
      total: folders.length,
    });
  } catch (error) {
    console.error('获取文件夹列表失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/folders - 创建新文件夹
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createFolderSchema.parse(body);

    // 如果指定了父文件夹ID，验证父文件夹是否存在且属于当前用户
    if (validatedData.parentId) {
      const parentFolder = await prisma.folder.findFirst({
        where: {
          id: validatedData.parentId,
          userId: session.user.id,
        },
      });

      if (!parentFolder) {
        return NextResponse.json(
          { error: '父文件夹未找到' },
          { status: 404 }
        );
      }
    }

    // 检查同一位置是否已存在同名的活跃文件夹
    const existingFolder = await prisma.folder.findFirst({
      where: {
        name: validatedData.name,
        parentId: validatedData.parentId || null,
        userId: session.user.id,
        isDeleted: false, // 只检查活跃的文件夹
      },
    });

    if (existingFolder) {
      return NextResponse.json(
        { error: '同一位置已存在同名文件夹' },
        { status: 409 }
      );
    }

    console.log('创建文件夹请求:', {
      name: validatedData.name,
      parentId: validatedData.parentId,
      userId: session.user.id,
      userEmail: session.user.email
    });

    // 确保用户存在于数据库中
    let user = await prisma.user.findUnique({
      where: { id: session.user.id }
    });

    if (!user) {
      console.log('用户不存在，创建新用户:', session.user);
      user = await prisma.user.create({
        data: {
          id: session.user.id,
          email: session.user.email || '',
          name: session.user.name || '',
          image: session.user.image,
        }
      });
    }

    const folder = await prisma.folder.create({
      data: {
        name: validatedData.name,
        parentId: validatedData.parentId,
        userId: session.user.id,
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
          },
        },
        children: true,
        documents: {
          select: {
            id: true,
            title: true,
            createdAt: true,
            updatedAt: true,
            wordCount: true,
            charCount: true,
          },
        },
        _count: {
          select: {
            documents: true,
            children: true,
          },
        },
      },
    });

    console.log(`用户 ${session.user.id} 创建了新文件夹: ${folder.name}`);

    return NextResponse.json({ folder }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '输入参数无效', details: error.issues },
        { status: 400 }
      );
    }

    console.error('创建文件夹失败:', error);

    // 处理外键约束错误
    if (error instanceof Error && error.message.includes('Foreign key constraint')) {
      return NextResponse.json(
        { error: '用户验证失败，请重新登录' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}