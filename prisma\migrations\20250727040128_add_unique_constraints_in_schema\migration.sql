/*
  Warnings:

  - A unique constraint covering the columns `[userId,title,folderId]` on the table `documents` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,name,parentId]` on the table `folders` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE INDEX "idx_user_deleted_documents" ON "documents"("userId", "isDeleted");

-- CreateIndex
CREATE UNIQUE INDEX "unique_user_document_title" ON "documents"("userId", "title", "folderId");

-- CreateIndex
CREATE INDEX "idx_user_deleted_folders" ON "folders"("userId", "isDeleted");

-- CreateIndex
CREATE UNIQUE INDEX "unique_user_folder_name" ON "folders"("userId", "name", "parentId");
