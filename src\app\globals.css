@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入斜杠命令样式 */
@import '../styles/slash-command.css';

/* 导入 AI 生成内容样式 */
@import '../styles/ai-generated-content.css';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Editor specific styles */
.ProseMirror {
  outline: none;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.7;
  color: hsl(var(--foreground));
  font-feature-settings: 'liga' 1, 'calt' 1;
  font-variant-ligatures: common-ligatures;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ProseMirror p {
  margin: 0.75rem 0;
}

.ProseMirror p:first-child {
  margin-top: 0;
}

.ProseMirror p:last-child {
  margin-bottom: 0;
}

.ProseMirror h1 {
  font-size: 2.25rem;
  font-weight: 700;
  margin: 2rem 0 1rem 0;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.ProseMirror h2 {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 1.75rem 0 0.75rem 0;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

.ProseMirror h3 {
  font-size: 1.375rem;
  font-weight: 600;
  margin: 1.5rem 0 0.5rem 0;
  line-height: 1.4;
  letter-spacing: -0.025em;
}

.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.75rem;
  margin: 1rem 0;
}

.ProseMirror li {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.ProseMirror li p {
  margin: 0.25rem 0;
}

.ProseMirror blockquote {
  border-left: 4px solid hsl(var(--primary));
  padding-left: 1.25rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
  background-color: hsl(var(--muted) / 0.3);
  padding: 1rem 1.25rem;
  border-radius: 0.5rem;
}

.ProseMirror code {
  background-color: hsl(var(--muted));
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace;
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.ProseMirror pre {
  background-color: hsl(var(--muted));
  padding: 1.25rem;
  border-radius: 0.75rem;
  overflow-x: auto;
  margin: 1.5rem 0;
  border: 1px solid hsl(var(--border));
}

.ProseMirror pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.ProseMirror hr {
  border: none;
  border-top: 2px solid hsl(var(--border));
  margin: 2rem 0;
}

/* Placeholder styles */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground));
  pointer-events: none;
  height: 0;
  font-style: italic;
}

/* Focus styles */
.ProseMirror:focus {
  outline: none;
}

/* Selection styles */
.ProseMirror ::selection {
  background-color: hsl(var(--primary) / 0.2);
}

/* 移动端触摸优化 */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 编辑器内容区域允许文本选择 */
.ProseMirror {
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 移动端按钮触摸反馈 */
@media (hover: none) and (pointer: coarse) {
  button:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}

/* Responsive typography */
@media (max-width: 640px) {
  .ProseMirror {
    font-size: 16px; /* 移动端最小字体大小，避免缩放 */
    line-height: 1.6;
    padding: 0.5rem;
  }
  
  .ProseMirror h1 {
    font-size: 1.875rem;
    margin: 1.5rem 0 0.75rem 0;
    line-height: 1.1;
  }
  
  .ProseMirror h2 {
    font-size: 1.5rem;
    margin: 1.25rem 0 0.5rem 0;
    line-height: 1.2;
  }
  
  .ProseMirror h3 {
    font-size: 1.25rem;
    margin: 1rem 0 0.5rem 0;
    line-height: 1.3;
  }
  
  .ProseMirror ul,
  .ProseMirror ol {
    padding-left: 1.5rem;
    margin: 0.75rem 0;
  }
  
  .ProseMirror li {
    margin: 0.25rem 0;
    line-height: 1.5;
  }
  
  .ProseMirror blockquote {
    padding: 0.75rem 1rem;
    margin: 1rem 0;
    border-left-width: 3px;
  }
  
  .ProseMirror pre {
    padding: 1rem;
    margin: 1rem 0;
    font-size: 14px;
    overflow-x: auto;
  }
  
  .ProseMirror code {
    font-size: 14px;
    padding: 0.125rem 0.25rem;
  }
  
  /* 移动端表格优化 */
  .ProseMirror table {
    font-size: 14px;
    overflow-x: auto;
    display: block;
    white-space: nowrap;
  }
  
  .ProseMirror table td,
  .ProseMirror table th {
    padding: 0.5rem;
    min-width: 100px;
  }
  
  /* 移动端链接优化 */
  .ProseMirror a {
    word-break: break-all;
    text-decoration: underline;
    text-decoration-color: hsl(var(--primary));
  }
  
  /* 移动端图片优化 */
  .ProseMirror img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
  }
}

/* 平板端优化 */
@media (min-width: 641px) and (max-width: 1024px) {
  .ProseMirror {
    font-size: 16px;
    line-height: 1.65;
    padding: 1rem;
  }
  
  .ProseMirror h1 {
    font-size: 2rem;
    margin: 1.75rem 0 1rem 0;
  }
  
  .ProseMirror h2 {
    font-size: 1.625rem;
    margin: 1.5rem 0 0.75rem 0;
  }
  
  .ProseMirror h3 {
    font-size: 1.375rem;
    margin: 1.25rem 0 0.5rem 0;
  }
}

@media (min-width: 1024px) {
  .ProseMirror {
    font-size: 17px;
    line-height: 1.75;
  }
  
  .ProseMirror h1 {
    font-size: 2.5rem;
  }
  
  .ProseMirror h2 {
    font-size: 2rem;
  }
  
  .ProseMirror h3 {
    font-size: 1.5rem;
  }
}