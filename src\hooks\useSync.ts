import { useState, useEffect, useCallback, useRef } from 'react';
import { syncService } from '@/lib/services/sync-service';
import { 
  SyncState, 
  SyncResult, 
  SyncOptions, 
  SyncEvent,
  SyncConflict 
} from '@/types/sync';

export interface UseSyncOptions {
  autoSync?: boolean;
  syncInterval?: number;
  onSyncComplete?: (results: SyncResult[]) => void;
  onSyncError?: (error: Error) => void;
  onConflictDetected?: (conflict: SyncConflict) => void;
}

export interface UseSyncReturn {
  // 同步状态
  syncState: SyncState;
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncAt?: Date;
  syncProgress?: {
    total: number;
    completed: number;
    current?: string;
    phase: string;
  };
  
  // 冲突管理
  conflicts: SyncConflict[];
  hasConflicts: boolean;
  
  // 同步操作
  manualSync: (options?: SyncOptions) => Promise<SyncResult[]>;
  resolveConflict: (conflictId: string, resolution: 'local' | 'remote' | 'merge', mergedData?: any) => Promise<void>;
  
  // 事件监听
  addEventListener: (type: string, listener: (event: SyncEvent) => void) => void;
  removeEventListener: (type: string, listener: (event: SyncEvent) => void) => void;
  
  // 统计信息
  syncStats: {
    totalSynced: number;
    totalConflicts: number;
    totalErrors: number;
    lastSyncDuration?: number;
  };
}

/**
 * 同步状态管理Hook
 */
export function useSync(options: UseSyncOptions = {}): UseSyncReturn {
  const {
    autoSync = true,
    syncInterval = 30000,
    onSyncComplete,
    onSyncError,
    onConflictDetected
  } = options;

  const [syncState, setSyncState] = useState<SyncState>(syncService.getSyncState());
  const [syncStats, setSyncStats] = useState({
    totalSynced: 0,
    totalConflicts: 0,
    totalErrors: 0,
    lastSyncDuration: undefined as number | undefined,
  });

  const eventListenersRef = useRef<Map<string, (event: SyncEvent) => void>>(new Map());

  // 更新同步状态
  const updateSyncState = useCallback(() => {
    setSyncState(syncService.getSyncState());
  }, []);

  // 手动同步
  const manualSync = useCallback(async (syncOptions?: SyncOptions): Promise<SyncResult[]> => {
    try {
      const startTime = Date.now();
      const results = await syncService.manualSync(syncOptions);
      
      const duration = Date.now() - startTime;
      const successCount = results.filter(r => r.success).length;
      const conflictCount = results.filter(r => r.status === 'conflict').length;
      const errorCount = results.filter(r => r.status === 'error').length;

      setSyncStats(prev => ({
        totalSynced: prev.totalSynced + successCount,
        totalConflicts: prev.totalConflicts + conflictCount,
        totalErrors: prev.totalErrors + errorCount,
        lastSyncDuration: duration,
      }));

      if (onSyncComplete) {
        onSyncComplete(results);
      }

      return results;
    } catch (error) {
      setSyncStats(prev => ({
        ...prev,
        totalErrors: prev.totalErrors + 1,
      }));

      if (onSyncError && error instanceof Error) {
        onSyncError(error);
      }
      throw error;
    }
  }, [onSyncComplete, onSyncError]);

  // 解决冲突
  const resolveConflict = useCallback(async (
    conflictId: string, 
    resolution: 'local' | 'remote' | 'merge',
    mergedData?: any
  ): Promise<void> => {
    try {
      await syncService.resolveConflictManually(conflictId, resolution, mergedData);
      updateSyncState();
    } catch (error) {
      console.error('解决冲突失败:', error);
      throw error;
    }
  }, [updateSyncState]);

  // 添加事件监听器
  const addEventListener = useCallback((type: string, listener: (event: SyncEvent) => void) => {
    eventListenersRef.current.set(`${type}_${Date.now()}`, listener);
    syncService.addEventListener(type, listener);
  }, []);

  // 移除事件监听器
  const removeEventListener = useCallback((type: string, listener: (event: SyncEvent) => void) => {
    syncService.removeEventListener(type, listener);
    // 从本地引用中移除
    const entries = Array.from(eventListenersRef.current.entries());
    for (const [key, value] of entries) {
      if (value === listener) {
        eventListenersRef.current.delete(key);
        break;
      }
    }
  }, []);

  // 设置事件监听器
  useEffect(() => {
    const handleSyncStarted = (event: SyncEvent) => {
      updateSyncState();
    };

    const handleSyncProgress = (event: SyncEvent) => {
      updateSyncState();
    };

    const handleSyncCompleted = (event: SyncEvent) => {
      updateSyncState();
      if (onSyncComplete && event.data?.results) {
        onSyncComplete(event.data.results);
      }
    };

    const handleSyncError = (event: SyncEvent) => {
      updateSyncState();
      setSyncStats(prev => ({
        ...prev,
        totalErrors: prev.totalErrors + 1,
      }));
      
      if (onSyncError && event.data?.error) {
        onSyncError(event.data.error);
      }
    };

    const handleConflictDetected = (event: SyncEvent) => {
      updateSyncState();
      setSyncStats(prev => ({
        ...prev,
        totalConflicts: prev.totalConflicts + 1,
      }));
      
      if (onConflictDetected && event.data?.conflict) {
        onConflictDetected(event.data.conflict);
      }
    };

    const handleConnectionChanged = (event: SyncEvent) => {
      updateSyncState();
    };

    // 注册事件监听器
    syncService.addEventListener('sync_started', handleSyncStarted);
    syncService.addEventListener('sync_progress', handleSyncProgress);
    syncService.addEventListener('sync_completed', handleSyncCompleted);
    syncService.addEventListener('sync_error', handleSyncError);
    syncService.addEventListener('conflict_detected', handleConflictDetected);
    syncService.addEventListener('connection_changed', handleConnectionChanged);

    // 初始状态更新
    updateSyncState();

    // 清理函数
    return () => {
      syncService.removeEventListener('sync_started', handleSyncStarted);
      syncService.removeEventListener('sync_progress', handleSyncProgress);
      syncService.removeEventListener('sync_completed', handleSyncCompleted);
      syncService.removeEventListener('sync_error', handleSyncError);
      syncService.removeEventListener('conflict_detected', handleConflictDetected);
      syncService.removeEventListener('connection_changed', handleConnectionChanged);
    };
  }, [updateSyncState, onSyncComplete, onSyncError, onConflictDetected]);

  // 定期更新状态
  useEffect(() => {
    const interval = setInterval(updateSyncState, 1000);
    return () => clearInterval(interval);
  }, [updateSyncState]);

  // 清理资源
  useEffect(() => {
    return () => {
      // 清理所有事件监听器
      const entries = Array.from(eventListenersRef.current.entries());
      for (const [key, listener] of entries) {
        const type = key.split('_')[0];
        syncService.removeEventListener(type, listener);
      }
      eventListenersRef.current.clear();
    };
  }, []);

  return {
    // 同步状态
    syncState,
    isOnline: syncState.isOnline,
    isSyncing: syncState.isSyncing,
    lastSyncAt: syncState.lastSyncAt,
    syncProgress: syncState.syncProgress,
    
    // 冲突管理
    conflicts: syncState.conflicts,
    hasConflicts: syncState.conflicts.length > 0,
    
    // 同步操作
    manualSync,
    resolveConflict,
    
    // 事件监听
    addEventListener,
    removeEventListener,
    
    // 统计信息
    syncStats,
  };
}

/**
 * 简化版同步Hook，只提供基本的同步状态
 */
export function useSyncStatus() {
  const [isOnline, setIsOnline] = useState(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  );
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncAt, setLastSyncAt] = useState<Date | undefined>();

  useEffect(() => {
    const handleSyncStarted = () => setIsSyncing(true);
    const handleSyncCompleted = (event: SyncEvent) => {
      setIsSyncing(false);
      setLastSyncAt(new Date());
    };
    const handleSyncError = () => setIsSyncing(false);
    const handleConnectionChanged = (event: SyncEvent) => {
      setIsOnline(event.data?.isOnline ?? true);
    };

    syncService.addEventListener('sync_started', handleSyncStarted);
    syncService.addEventListener('sync_completed', handleSyncCompleted);
    syncService.addEventListener('sync_error', handleSyncError);
    syncService.addEventListener('connection_changed', handleConnectionChanged);

    // 初始状态
    const state = syncService.getSyncState();
    setIsSyncing(state.isSyncing);
    setLastSyncAt(state.lastSyncAt);
    setIsOnline(state.isOnline);

    return () => {
      syncService.removeEventListener('sync_started', handleSyncStarted);
      syncService.removeEventListener('sync_completed', handleSyncCompleted);
      syncService.removeEventListener('sync_error', handleSyncError);
      syncService.removeEventListener('connection_changed', handleConnectionChanged);
    };
  }, []);

  return {
    isOnline,
    isSyncing,
    lastSyncAt,
  };
}