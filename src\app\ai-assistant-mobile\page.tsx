'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { AIAssistantContainer } from '@/components/ai/AIAssistantPanel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { 
  SparklesIcon,
  SmartphoneIcon,
  TabletIcon,
  MonitorIcon,
  InfoIcon,
  TouchpadIcon as TouchIcon
} from 'lucide-react';

/**
 * AI 助手移动端演示页面
 * 专门展示移动端和响应式设计特性
 */
export default function AIAssistantMobilePage() {
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 });
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const [selectedText, setSelectedText] = useState('');
  const [touchInteractions, setTouchInteractions] = useState(0);

  // 检测屏幕尺寸和设备类型
  useEffect(() => {
    const updateScreenInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({ width, height });
      
      if (width < 768) {
        setDeviceType('mobile');
      } else if (width < 1024) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    updateScreenInfo();
    window.addEventListener('resize', updateScreenInfo);
    
    return () => window.removeEventListener('resize', updateScreenInfo);
  }, []);

  // 触摸交互计数
  useEffect(() => {
    const handleTouch = () => {
      setTouchInteractions(prev => prev + 1);
    };

    document.addEventListener('touchstart', handleTouch);
    return () => document.removeEventListener('touchstart', handleTouch);
  }, []);

  /**
   * 处理 AI 操作
   */
  const handleAIAction = useCallback(async (actionId: string, data?: any) => {
    console.log('AI Action:', actionId, data);
    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }, []);

  /**
   * 获取设备图标
   */
  const getDeviceIcon = () => {
    switch (deviceType) {
      case 'mobile':
        return <SmartphoneIcon className="h-5 w-5 text-green-600" />;
      case 'tablet':
        return <TabletIcon className="h-5 w-5 text-blue-600" />;
      default:
        return <MonitorIcon className="h-5 w-5 text-purple-600" />;
    }
  };

  /**
   * 获取设备特性描述
   */
  const getDeviceFeatures = () => {
    switch (deviceType) {
      case 'mobile':
        return {
          title: '移动端优化',
          features: [
            '全屏面板覆盖',
            '触摸友好的交互',
            '大按钮和间距',
            '滑动手势支持'
          ]
        };
      case 'tablet':
        return {
          title: '平板端适配',
          features: [
            '中等尺寸面板',
            '触摸和鼠标兼容',
            '灵活的布局调整',
            '横竖屏自适应'
          ]
        };
      default:
        return {
          title: '桌面端体验',
          features: [
            '侧边面板设计',
            '键盘快捷键',
            '精确的鼠标交互',
            '多窗口支持'
          ]
        };
    }
  };

  const deviceFeatures = getDeviceFeatures();

  // 示例文本内容（针对移动端优化）
  const mobileTexts = [
    '移动设备上的 AI 助手体验需要特别优化，确保触摸交互的便利性。',
    '响应式设计让 AI 功能在任何设备上都能完美工作。',
    '触摸屏幕需要更大的按钮和更清晰的视觉反馈。',
    '移动端的 AI 助手面板采用全屏设计，最大化可用空间。'
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* 页面标题 */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 flex items-center justify-center gap-2">
            <SparklesIcon className="h-6 w-6 md:h-8 md:w-8 text-blue-600" />
            移动端 AI 助手
          </h1>
          <p className="text-sm md:text-base text-gray-600">
            响应式设计演示 - 适配所有设备尺寸
          </p>
        </div>

        {/* 设备信息卡片 */}
        <Card className="border-2 border-dashed border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              {getDeviceIcon()}
              当前设备: {deviceType === 'mobile' ? '移动端' : deviceType === 'tablet' ? '平板端' : '桌面端'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="space-y-1">
                <div className="text-lg font-bold text-blue-600">{screenSize.width}px</div>
                <div className="text-xs text-gray-600">宽度</div>
              </div>
              <div className="space-y-1">
                <div className="text-lg font-bold text-green-600">{screenSize.height}px</div>
                <div className="text-xs text-gray-600">高度</div>
              </div>
              <div className="space-y-1">
                <div className="text-lg font-bold text-purple-600">{touchInteractions}</div>
                <div className="text-xs text-gray-600">触摸次数</div>
              </div>
              <div className="space-y-1">
                <div className="text-lg font-bold text-orange-600">
                  {(screenSize.width / screenSize.height).toFixed(2)}
                </div>
                <div className="text-xs text-gray-600">宽高比</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 设备特性 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <InfoIcon className="h-5 w-5 text-blue-600" />
              {deviceFeatures.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {deviceFeatures.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-gray-700">{feature}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 交互测试区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TouchIcon className="h-5 w-5 text-green-600" />
              交互测试
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600">
              点击下面的文本来测试 AI 助手面板的响应式行为：
            </p>
            
            <div className="space-y-3">
              {mobileTexts.map((text, index) => (
                <div
                  key={index}
                  className={`
                    p-4 rounded-lg border cursor-pointer transition-all duration-200
                    ${selectedText === text 
                      ? 'bg-blue-50 border-blue-300 text-blue-900 shadow-md' 
                      : 'bg-white border-gray-200 hover:bg-gray-50 hover:shadow-sm'
                    }
                    ${deviceType === 'mobile' ? 'text-base' : 'text-sm'}
                  `}
                  onClick={() => setSelectedText(text)}
                >
                  <p className="leading-relaxed">{text}</p>
                  {selectedText === text && (
                    <Badge variant="secondary" className="mt-2 text-xs">
                      已选择 ({text.length} 字符)
                    </Badge>
                  )}
                </div>
              ))}
            </div>

            {selectedText && (
              <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                <p className="text-sm text-blue-800 font-medium">
                  ✨ 文本已选择！现在点击右下角的 AI 按钮打开助手面板
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  在移动端，面板将以全屏模式显示
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 响应式特性说明 */}
        <Card>
          <CardHeader>
            <CardTitle>响应式特性</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <SmartphoneIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <h4 className="font-medium text-green-900">移动端 (&lt; 768px)</h4>
                <p className="text-xs text-green-700 mt-1">
                  全屏面板，大按钮，触摸优化
                </p>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <TabletIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <h4 className="font-medium text-blue-900">平板端 (768px - 1024px)</h4>
                <p className="text-xs text-blue-700 mt-1">
                  中等面板，混合交互
                </p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <MonitorIcon className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <h4 className="font-medium text-purple-900">桌面端 (&gt; 1024px)</h4>
                <p className="text-xs text-purple-700 mt-1">
                  侧边面板，键盘快捷键
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 使用提示 */}
        <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <InfoIcon className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                <h4 className="font-medium text-yellow-900">使用提示</h4>
                <ul className="text-sm text-yellow-800 space-y-1">
                  <li>• 尝试调整浏览器窗口大小来体验响应式变化</li>
                  <li>• 在移动设备上，面板会以全屏模式显示</li>
                  <li>• 使用触摸手势可以更好地操作面板</li>
                  <li>• 键盘快捷键 Ctrl+Shift+A 在桌面端可用</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI 助手面板 */}
      <AIAssistantContainer
        position="right"
        width={deviceType === 'mobile' ? undefined : 320}
        onAIAction={handleAIAction}
        selectedText={selectedText}
      />
    </div>
  );
}