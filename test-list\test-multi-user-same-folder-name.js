/**
 * 多用户同名文件夹创建测试用例
 * 
 * 文件作用：
 * 1. 测试多个不同用户是否可以创建同名文件夹
 * 2. 验证用户隔离机制是否正常工作
 * 3. 确认数据库设计对跨用户唯一性的处理
 * 
 * 测试场景：
 * - 用户A创建文件夹 "test111"
 * - 用户B也创建文件夹 "test111"  
 * - 用户C也创建文件夹 "test111"
 * - 验证是否都能成功创建
 * 
 * 预期结果：
 * - 所有用户都应该能成功创建同名文件夹
 * - 每个用户的文件夹通过 userId 进行隔离
 * - 不会产生唯一性约束冲突
 * 
 * 数据库设计分析：
 * - 当前没有 UNIQUE(name) 约束（全局唯一）
 * - 只有 userId 作为隔离字段
 * - 允许跨用户的同名文件夹存在
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * 创建测试用户
 * @param {string} email - 用户邮箱
 * @param {string} name - 用户名称
 * @returns {Promise<Object>} 用户对象
 */
async function createTestUser(email, name) {
  try {
    // 检查用户是否已存在
    let user = await prisma.user.findUnique({
      where: { email: email }
    });
    
    if (!user) {
      // 创建新用户
      user = await prisma.user.create({
        data: {
          email: email,
          name: name,
          password: 'test123' // 测试密码
        }
      });
      console.log(`✅ 创建测试用户: ${name} (${email})`);
    } else {
      console.log(`📝 使用现有用户: ${name} (${email})`);
    }
    
    return user;
  } catch (error) {
    console.error(`❌ 创建用户失败: ${error.message}`);
    throw error;
  }
}

/**
 * 为指定用户创建文件夹
 * @param {Object} user - 用户对象
 * @param {string} folderName - 文件夹名称
 * @returns {Promise<Object>} 创建结果
 */
async function createFolderForUser(user, folderName) {
  try {
    console.log(`📁 用户 ${user.name} 尝试创建文件夹: "${folderName}"`);
    
    const folder = await prisma.folder.create({
      data: {
        name: folderName,
        userId: user.id,
        isDeleted: false
      }
    });
    
    console.log(`   ✅ 创建成功! 文件夹ID: ${folder.id.slice(-8)}...`);
    return { success: true, folder: folder };
    
  } catch (error) {
    console.log(`   ❌ 创建失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * 主测试函数：多用户同名文件夹测试
 */
async function testMultiUserSameFolderName() {
  console.log('🚀 开始多用户同名文件夹测试');
  console.log('=' .repeat(60));
  
  const testFolderName = 'test111';
  const createdFolders = []; // 记录创建的文件夹，用于清理
  
  try {
    // 步骤1: 创建或获取测试用户
    console.log('\n👥 步骤1: 准备测试用户');
    const userA = await createTestUser('<EMAIL>', '用户A');
    const userB = await createTestUser('<EMAIL>', '用户B');  
    const userC = await createTestUser('<EMAIL>', '用户C');
    
    const users = [userA, userB, userC];
    console.log(`📊 共准备了 ${users.length} 个测试用户`);
    
    // 步骤2: 每个用户都尝试创建同名文件夹
    console.log(`\n📁 步骤2: 每个用户创建文件夹 "${testFolderName}"`);
    
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      console.log(`\n--- 第 ${i + 1} 个用户: ${user.name} ---`);
      
      const result = await createFolderForUser(user, testFolderName);
      
      if (result.success) {
        createdFolders.push(result.folder);
        console.log(`   📈 当前成功创建: ${createdFolders.length} 个同名文件夹`);
      }
    }
    
    // 步骤3: 验证数据库状态
    console.log(`\n🔍 步骤3: 验证数据库中的同名文件夹`);
    const allSameNameFolders = await prisma.folder.findMany({
      where: { 
        name: testFolderName,
        isDeleted: false 
      },
      include: {
        user: {
          select: { name: true, email: true }
        }
      },
      orderBy: { createdAt: 'asc' }
    });
    
    console.log(`📊 数据库中共有 ${allSameNameFolders.length} 个名为 "${testFolderName}" 的文件夹:`);
    allSameNameFolders.forEach((folder, index) => {
      console.log(`   ${index + 1}. ID: ${folder.id.slice(-8)}... | 用户: ${folder.user.name} | 邮箱: ${folder.user.email}`);
    });
    
    // 步骤4: 测试用户隔离（每个用户只能看到自己的文件夹）
    console.log(`\n🔒 步骤4: 验证用户隔离机制`);
    for (const user of users) {
      const userFolders = await prisma.folder.findMany({
        where: {
          name: testFolderName,
          userId: user.id,
          isDeleted: false
        }
      });
      console.log(`   用户 ${user.name} 可以看到 ${userFolders.length} 个名为 "${testFolderName}" 的文件夹`);
    }
    
    // 步骤5: 测试软删除场景
    console.log(`\n🗑️  步骤5: 测试软删除后的同名创建`);
    if (createdFolders.length > 0) {
      const firstFolder = createdFolders[0];
      const firstUser = users[0];
      
      console.log(`   软删除用户 ${firstUser.name} 的文件夹...`);
      await prisma.folder.update({
        where: { id: firstFolder.id },
        data: { isDeleted: true }
      });
      
      console.log(`   用户 ${firstUser.name} 尝试重新创建同名文件夹...`);
      const recreateResult = await createFolderForUser(firstUser, testFolderName);
      
      if (recreateResult.success) {
        createdFolders.push(recreateResult.folder);
        console.log(`   ✅ 软删除后可以重新创建同名文件夹`);
      }
    }
    
    // 测试结论
    console.log('\n🎯 测试结论:');
    console.log(`   ✅ 多个用户可以创建同名文件夹 "${testFolderName}"`);
    console.log(`   ✅ 用户隔离机制正常工作`);
    console.log(`   ✅ 软删除后可以重新创建同名文件夹`);
    console.log(`   📝 数据库设计允许跨用户的同名文件夹`);
    console.log(`   💡 这是合理的设计，因为不同用户的文件夹应该独立`);
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
    console.error('   错误详情:', error);
  } finally {
    // 清理测试数据
    console.log('\n🧹 清理测试数据...');
    
    if (createdFolders.length > 0) {
      await prisma.folder.deleteMany({
        where: {
          id: { in: createdFolders.map(f => f.id) }
        }
      });
      console.log(`✅ 清理了 ${createdFolders.length} 个测试文件夹`);
    }
    
    // 清理测试用户（可选，注释掉以保留用户）
    // await prisma.user.deleteMany({
    //   where: {
    //     email: { in: ['<EMAIL>', '<EMAIL>', '<EMAIL>'] }
    //   }
    // });
    // console.log('✅ 清理了测试用户');
    
    await prisma.$disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

// 执行测试
console.log('🚀 开始执行多用户同名文件夹测试...');
testMultiUserSameFolderName();
