import { db, LocalDocument } from './database';
import { Document, DocumentMetadata, ErrorType, AppError } from '@/types';
import { JSONContent } from '@tiptap/react';

export interface CreateDocumentData {
  title: string;
  content?: JSONContent;
  folderId?: string;
  userId: string;
  metadata?: Partial<DocumentMetadata>;
}

export interface UpdateDocumentData {
  title?: string;
  content?: JSONContent;
  folderId?: string;
  metadata?: Partial<DocumentMetadata>;
}

export interface DocumentFilter {
  userId?: string;
  folderId?: string;
  includeDeleted?: boolean;
  searchQuery?: string;
}

export interface DocumentStats {
  totalDocuments: number;
  totalWords: number;
  totalCharacters: number;
  lastModified?: Date;
}

export class DocumentService {
  /**
   * Create a new document
   */
  async createDocument(data: CreateDocumentData): Promise<LocalDocument> {
    try {
      const defaultContent: JSONContent = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: []
          }
        ]
      };

      const defaultMetadata: DocumentMetadata = {
        wordCount: 0,
        characterCount: 0,
        tags: [],
        isPublic: false,
        ...data.metadata
      };

      const document: Omit<LocalDocument, 'id'> = {
        title: data.title || 'Untitled Document',
        content: data.content || defaultContent,
        folderId: data.folderId,
        userId: data.userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: defaultMetadata,
        isDirty: true,
        isDeleted: false
      };

      const id = await db.documents.add(document as LocalDocument);
      const createdDocument = await db.documents.get(id);
      
      if (!createdDocument) {
        throw new Error('Failed to retrieve created document');
      }

      return createdDocument;
    } catch (error) {
      throw this.createStorageError('Failed to create document', error);
    }
  }

  /**
   * Get a document by ID
   */
  async getDocument(id: string): Promise<LocalDocument | null> {
    try {
      const document = await db.documents.get(id);
      return document && !document.isDeleted ? document : null;
    } catch (error) {
      throw this.createStorageError('Failed to get document', error);
    }
  }

  /**
   * Update a document
   */
  async updateDocument(id: string, data: UpdateDocumentData): Promise<LocalDocument> {
    try {
      const existingDocument = await db.documents.get(id);
      if (!existingDocument || existingDocument.isDeleted) {
        throw new Error('Document not found');
      }

      const updateData: any = {
        ...data,
        updatedAt: new Date(),
        isDirty: true
      };

      // Merge metadata if provided
      if (data.metadata) {
        updateData.metadata = {
          ...existingDocument.metadata,
          ...data.metadata
        };
      }

      await db.documents.update(id, updateData);
      const updatedDocument = await db.documents.get(id);
      
      if (!updatedDocument) {
        throw new Error('Failed to retrieve updated document');
      }

      return updatedDocument;
    } catch (error) {
      throw this.createStorageError('Failed to update document', error);
    }
  }

  /**
   * Delete a document (soft delete)
   */
  async deleteDocument(id: string): Promise<void> {
    try {
      const document = await db.documents.get(id);
      if (!document) {
        throw new Error('Document not found');
      }

      await db.documents.update(id, {
        isDeleted: true,
        updatedAt: new Date(),
        isDirty: true
      });
    } catch (error) {
      throw this.createStorageError('Failed to delete document', error);
    }
  }

  /**
   * Permanently delete a document
   */
  async permanentlyDeleteDocument(id: string): Promise<void> {
    try {
      await db.documents.delete(id);
    } catch (error) {
      throw this.createStorageError('Failed to permanently delete document', error);
    }
  }

  /**
   * Restore a deleted document
   */
  async restoreDocument(id: string): Promise<LocalDocument> {
    try {
      const document = await db.documents.get(id);
      if (!document) {
        throw new Error('Document not found');
      }

      await db.documents.update(id, {
        isDeleted: false,
        updatedAt: new Date(),
        isDirty: true
      });

      const restoredDocument = await db.documents.get(id);
      if (!restoredDocument) {
        throw new Error('Failed to retrieve restored document');
      }

      return restoredDocument;
    } catch (error) {
      throw this.createStorageError('Failed to restore document', error);
    }
  }

  /**
   * Get all documents with optional filtering
   */
  async getDocuments(filter: DocumentFilter = {}): Promise<LocalDocument[]> {
    try {
      let query = db.documents.toCollection();

      // Apply filters
      if (filter.userId) {
        query = query.filter(doc => doc.userId === filter.userId);
      }

      if (filter.folderId !== undefined) {
        query = query.filter(doc => doc.folderId === filter.folderId);
      }

      if (!filter.includeDeleted) {
        query = query.filter(doc => !doc.isDeleted);
      }

      let documents = await query.toArray();

      // Apply search filter if provided
      if (filter.searchQuery) {
        const searchLower = filter.searchQuery.toLowerCase();
        documents = documents.filter(doc => 
          doc.title.toLowerCase().includes(searchLower) ||
          this.extractTextFromContent(doc.content).toLowerCase().includes(searchLower) ||
          doc.metadata.tags.some(tag => tag.toLowerCase().includes(searchLower))
        );
      }

      // Sort by updatedAt descending
      documents.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());

      return documents;
    } catch (error) {
      throw this.createStorageError('Failed to get documents', error);
    }
  }

  /**
   * Get documents that need syncing (dirty documents)
   */
  async getDirtyDocuments(userId: string): Promise<LocalDocument[]> {
    try {
      return await db.documents
        .where('userId')
        .equals(userId)
        .and(doc => doc.isDirty && !doc.isDeleted)
        .toArray();
    } catch (error) {
      throw this.createStorageError('Failed to get dirty documents', error);
    }
  }

  /**
   * Mark document as synced
   */
  async markDocumentSynced(id: string): Promise<void> {
    try {
      await db.documents.update(id, {
        isDirty: false,
        lastSyncAt: new Date()
      });
    } catch (error) {
      throw this.createStorageError('Failed to mark document as synced', error);
    }
  }

  /**
   * Get document statistics
   */
  async getDocumentStats(userId: string): Promise<DocumentStats> {
    try {
      const documents = await db.documents
        .where('userId')
        .equals(userId)
        .and(doc => !doc.isDeleted)
        .toArray();

      const totalWords = documents.reduce((sum, doc) => sum + doc.metadata.wordCount, 0);
      const totalCharacters = documents.reduce((sum, doc) => sum + doc.metadata.characterCount, 0);
      const lastModified = documents.length > 0 
        ? new Date(Math.max(...documents.map(doc => doc.updatedAt.getTime())))
        : undefined;

      return {
        totalDocuments: documents.length,
        totalWords,
        totalCharacters,
        lastModified
      };
    } catch (error) {
      throw this.createStorageError('Failed to get document statistics', error);
    }
  }

  /**
   * Search documents by content
   */
  async searchDocuments(userId: string, query: string): Promise<LocalDocument[]> {
    try {
      const searchLower = query.toLowerCase();
      
      return await db.documents
        .where('userId')
        .equals(userId)
        .and(doc => 
          !doc.isDeleted && (
            doc.title.toLowerCase().includes(searchLower) ||
            this.extractTextFromContent(doc.content).toLowerCase().includes(searchLower) ||
            doc.metadata.tags.some(tag => tag.toLowerCase().includes(searchLower))
          )
        )
        .toArray();
    } catch (error) {
      throw this.createStorageError('Failed to search documents', error);
    }
  }

  /**
   * Duplicate a document
   */
  async duplicateDocument(id: string, newTitle?: string): Promise<LocalDocument> {
    try {
      const originalDocument = await this.getDocument(id);
      if (!originalDocument) {
        throw new Error('Document not found');
      }

      const duplicateData: CreateDocumentData = {
        title: newTitle || `${originalDocument.title} (Copy)`,
        content: originalDocument.content,
        folderId: originalDocument.folderId,
        userId: originalDocument.userId,
        metadata: {
          ...originalDocument.metadata,
          shareToken: undefined // Don't copy share token
        }
      };

      return await this.createDocument(duplicateData);
    } catch (error) {
      throw this.createStorageError('Failed to duplicate document', error);
    }
  }

  /**
   * Get recent documents
   */
  async getRecentDocuments(userId: string, limit: number = 10): Promise<LocalDocument[]> {
    try {
      const documents = await db.documents
        .where('userId')
        .equals(userId)
        .and(doc => !doc.isDeleted)
        .toArray();
      
      // Sort by updatedAt descending and limit
      return documents
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
        .slice(0, limit);
    } catch (error) {
      throw this.createStorageError('Failed to get recent documents', error);
    }
  }

  /**
   * Clear all documents for a user (for testing/cleanup)
   */
  async clearUserDocuments(userId: string): Promise<void> {
    try {
      await db.documents
        .where('userId')
        .equals(userId)
        .delete();
    } catch (error) {
      throw this.createStorageError('Failed to clear user documents', error);
    }
  }

  private extractTextFromContent(content: JSONContent): string {
    if (!content) return '';
    
    let text = '';
    
    if (content.text) {
      text += content.text;
    }
    
    if (content.content && Array.isArray(content.content)) {
      for (const child of content.content) {
        text += this.extractTextFromContent(child);
      }
    }
    
    return text;
  }

  private createStorageError(message: string, originalError: any): AppError {
    return {
      type: ErrorType.STORAGE_ERROR,
      message,
      details: originalError,
      timestamp: new Date(),
      recoverable: true
    };
  }
}

// Export singleton instance
export const documentService = new DocumentService();
