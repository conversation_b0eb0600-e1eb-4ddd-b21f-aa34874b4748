import { FolderWithRelations } from '@/lib/services/folder-service';
import { DocumentWithRelations } from '@/lib/services/document-service';

export interface HierarchyNode {
  id: string;
  name: string;
  type: 'folder' | 'document';
  parentId?: string | null;
  children?: HierarchyNode[];
  createdAt: Date;
  updatedAt: Date;
  // Additional properties for documents
  wordCount?: number;
  charCount?: number;
  // Additional properties for folders
  documentsCount?: number;
  subfoldersCount?: number;
}

/**
 * Convert folders and documents into a unified hierarchy tree
 */
export function buildHierarchyTree(
  folders: FolderWithRelations[],
  documents: DocumentWithRelations[]
): HierarchyNode[] {
  const nodeMap = new Map<string, HierarchyNode>();
  const rootNodes: HierarchyNode[] = [];

  // Create folder nodes
  folders.forEach(folder => {
    const node: HierarchyNode = {
      id: folder.id,
      name: folder.name,
      type: 'folder',
      parentId: folder.parentId,
      children: [],
      createdAt: folder.createdAt,
      updatedAt: folder.updatedAt,
      documentsCount: folder.documents?.length || 0,
      subfoldersCount: folder.children?.length || 0,
    };
    nodeMap.set(folder.id, node);
  });

  // Create document nodes
  documents.forEach(document => {
    const node: HierarchyNode = {
      id: document.id,
      name: document.title,
      type: 'document',
      parentId: document.folderId,
      createdAt: document.createdAt,
      updatedAt: document.updatedAt,
      wordCount: document.wordCount,
      charCount: document.charCount,
    };
    nodeMap.set(document.id, node);
  });

  // Build the tree structure
  nodeMap.forEach(node => {
    if (node.parentId) {
      const parent = nodeMap.get(node.parentId);
      if (parent && parent.children) {
        parent.children.push(node);
      }
    } else {
      rootNodes.push(node);
    }
  });

  // Sort children: folders first, then documents, both alphabetically
  const sortChildren = (nodes: HierarchyNode[]) => {
    nodes.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'folder' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });

    nodes.forEach(node => {
      if (node.children) {
        sortChildren(node.children);
      }
    });
  };

  sortChildren(rootNodes);
  return rootNodes;
}

/**
 * Find a node in the hierarchy tree by ID
 */
export function findNodeById(
  nodes: HierarchyNode[],
  id: string
): HierarchyNode | null {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
}

/**
 * Get the path to a node (breadcrumb)
 */
export function getNodePath(
  nodes: HierarchyNode[],
  targetId: string
): HierarchyNode[] {
  const path: HierarchyNode[] = [];

  function findPath(currentNodes: HierarchyNode[], currentPath: HierarchyNode[]): boolean {
    for (const node of currentNodes) {
      const newPath = [...currentPath, node];
      
      if (node.id === targetId) {
        path.push(...newPath);
        return true;
      }
      
      if (node.children && findPath(node.children, newPath)) {
        return true;
      }
    }
    return false;
  }

  findPath(nodes, []);
  return path;
}

/**
 * Get all descendant nodes of a specific node
 */
export function getDescendantNodes(
  nodes: HierarchyNode[],
  parentId: string
): HierarchyNode[] {
  const descendants: HierarchyNode[] = [];
  const parent = findNodeById(nodes, parentId);
  
  if (!parent || !parent.children) {
    return descendants;
  }

  function collectDescendants(currentNodes: HierarchyNode[]) {
    for (const node of currentNodes) {
      descendants.push(node);
      if (node.children) {
        collectDescendants(node.children);
      }
    }
  }

  collectDescendants(parent.children);
  return descendants;
}

/**
 * Filter nodes by type
 */
export function filterNodesByType(
  nodes: HierarchyNode[],
  type: 'folder' | 'document'
): HierarchyNode[] {
  const filtered: HierarchyNode[] = [];

  function collectByType(currentNodes: HierarchyNode[]) {
    for (const node of currentNodes) {
      if (node.type === type) {
        filtered.push(node);
      }
      if (node.children) {
        collectByType(node.children);
      }
    }
  }

  collectByType(nodes);
  return filtered;
}

/**
 * Search nodes by name
 */
export function searchNodes(
  nodes: HierarchyNode[],
  query: string
): HierarchyNode[] {
  const results: HierarchyNode[] = [];
  const lowerQuery = query.toLowerCase();

  function searchRecursive(currentNodes: HierarchyNode[]) {
    for (const node of currentNodes) {
      if (node.name.toLowerCase().includes(lowerQuery)) {
        results.push(node);
      }
      if (node.children) {
        searchRecursive(node.children);
      }
    }
  }

  searchRecursive(nodes);
  return results;
}

/**
 * Calculate folder statistics (total documents, total size, etc.)
 */
export function calculateFolderStats(node: HierarchyNode): {
  totalDocuments: number;
  totalFolders: number;
  totalWords: number;
  totalCharacters: number;
} {
  let totalDocuments = 0;
  let totalFolders = 0;
  let totalWords = 0;
  let totalCharacters = 0;

  function calculateRecursive(currentNode: HierarchyNode) {
    if (currentNode.type === 'folder') {
      totalFolders++;
      if (currentNode.children) {
        currentNode.children.forEach(calculateRecursive);
      }
    } else if (currentNode.type === 'document') {
      totalDocuments++;
      totalWords += currentNode.wordCount || 0;
      totalCharacters += currentNode.charCount || 0;
    }
  }

  if (node.children) {
    node.children.forEach(calculateRecursive);
  }

  return {
    totalDocuments,
    totalFolders,
    totalWords,
    totalCharacters,
  };
}

/**
 * Validate if a move operation would create a circular reference
 */
export function wouldCreateCircularReference(
  nodes: HierarchyNode[],
  nodeId: string,
  targetParentId: string
): boolean {
  // Can't move a node into itself
  if (nodeId === targetParentId) {
    return true;
  }

  // Check if target parent is a descendant of the node being moved
  const descendants = getDescendantNodes(nodes, nodeId);
  return descendants.some(descendant => descendant.id === targetParentId);
}

/**
 * Get the level/depth of a node in the hierarchy
 */
export function getNodeLevel(nodes: HierarchyNode[], nodeId: string): number {
  const path = getNodePath(nodes, nodeId);
  return path.length - 1; // Subtract 1 because path includes the node itself
}

/**
 * Flatten the hierarchy tree into a list
 */
export function flattenHierarchy(nodes: HierarchyNode[]): HierarchyNode[] {
  const flattened: HierarchyNode[] = [];

  function flattenRecursive(currentNodes: HierarchyNode[], level: number = 0) {
    for (const node of currentNodes) {
      // Add level information to the node
      const flatNode = { ...node, level };
      flattened.push(flatNode);
      
      if (node.children) {
        flattenRecursive(node.children, level + 1);
      }
    }
  }

  flattenRecursive(nodes);
  return flattened;
}
