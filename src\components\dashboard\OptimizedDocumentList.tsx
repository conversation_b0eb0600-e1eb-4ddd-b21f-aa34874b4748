'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { FixedSizeList as List } from 'react-window';
import { LocalDocument } from '@/lib/storage/database';
import { AppError } from '@/types';
import { DocumentCard } from './DocumentCard';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';

interface OptimizedDocumentListProps {
  documents: LocalDocument[];
  loading: boolean;
  error: AppError | null;
  onRefresh: () => void;
  /** 是否启用虚拟滚动 */
  enableVirtualization?: boolean;
  /** 虚拟滚动阈值 */
  virtualizationThreshold?: number;
  /** 每行显示的文档数 */
  itemsPerRow?: number;
  /** 每行的高度 */
  rowHeight?: number;
}

interface VirtualizedRowProps {
  index: number;
  style: any;
  data: {
    documents: LocalDocument[];
    itemsPerRow: number;
    onDocumentAction: (action: string, document: LocalDocument) => void;
  };
}

/**
 * 虚拟化行组件
 */
function VirtualizedRow({ index, style, data }: VirtualizedRowProps) {
  const { documents, itemsPerRow, onDocumentAction } = data;
  const startIndex = index * itemsPerRow;
  const endIndex = Math.min(startIndex + itemsPerRow, documents.length);
  const rowDocuments = documents.slice(startIndex, endIndex);

  return (
    <div style={style} className="flex space-x-4 px-4">
      {rowDocuments.map((document) => (
        <div key={document.id} className="flex-1 min-w-0">
          <DocumentCard
            document={document}
          />
        </div>
      ))}
      
      {/* 填充空白区域 */}
      {rowDocuments.length < itemsPerRow && (
        <>
          {Array.from({ length: itemsPerRow - rowDocuments.length }).map((_, i) => (
            <div key={`empty-${i}`} className="flex-1 min-w-0" />
          ))}
        </>
      )}
    </div>
  );
}

/**
 * 优化的文档列表组件
 * 支持虚拟滚动和性能优化
 */
export function OptimizedDocumentList({
  documents,
  loading,
  error,
  onRefresh,
  enableVirtualization = true,
  virtualizationThreshold = 50,
  itemsPerRow = 3,
  rowHeight = 200,
}: OptimizedDocumentListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'title' | 'updatedAt' | 'createdAt'>('updatedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(new Set());

  // 过滤和排序文档
  const filteredAndSortedDocuments = useMemo(() => {
    let filtered = documents;

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = documents.filter(doc => {
        // 搜索标题
        if (doc.title.toLowerCase().includes(query)) {
          return true;
        }
        
        // 搜索内容（将 JSONContent 转换为文本进行搜索）
        if (doc.content) {
          try {
            const contentText = JSON.stringify(doc.content).toLowerCase();
            return contentText.includes(query);
          } catch {
            return false;
          }
        }
        
        return false;
      });
    }

    // 排序
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'updatedAt':
          aValue = new Date(a.updatedAt).getTime();
          bValue = new Date(b.updatedAt).getTime();
          break;
        case 'createdAt':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        default:
          return 0;
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [documents, searchQuery, sortBy, sortOrder]);

  // 是否使用虚拟滚动
  const shouldVirtualize = enableVirtualization && 
    filteredAndSortedDocuments.length > virtualizationThreshold;

  // 计算虚拟滚动的行数
  const rowCount = Math.ceil(filteredAndSortedDocuments.length / itemsPerRow);

  // 处理文档操作
  const handleDocumentAction = useCallback((action: string, document: LocalDocument) => {
    switch (action) {
      case 'select':
        setSelectedDocuments(prev => {
          const newSet = new Set(prev);
          if (newSet.has(document.id)) {
            newSet.delete(document.id);
          } else {
            newSet.add(document.id);
          }
          return newSet;
        });
        break;
      case 'edit':
        window.location.href = `/editor?id=${document.id}`;
        break;
      case 'delete':
        // 处理删除逻辑
        console.log('删除文档:', document.id);
        break;
      default:
        console.log('未知操作:', action);
    }
  }, []);

  // 批量操作
  const handleBatchAction = useCallback((action: string) => {
    const selectedDocs = filteredAndSortedDocuments.filter(doc => 
      selectedDocuments.has(doc.id)
    );
    
    switch (action) {
      case 'delete':
        console.log('批量删除:', selectedDocs.map(doc => doc.id));
        setSelectedDocuments(new Set());
        break;
      case 'export':
        console.log('批量导出:', selectedDocs.map(doc => doc.id));
        break;
      default:
        console.log('未知批量操作:', action);
    }
  }, [filteredAndSortedDocuments, selectedDocuments]);

  // 加载状态
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">文档列表</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="border border-gray-200 rounded-lg p-4 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4 mb-3"></div>
              <div className="flex justify-between">
                <div className="h-3 bg-gray-200 rounded w-16"></div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">文档列表</h2>
        <div className="text-center py-8">
          <div className="text-red-500 mb-2">❌</div>
          <p className="text-gray-600 mb-4">加载文档时出现错误</p>
          <p className="text-sm text-red-600 mb-4">{error.message}</p>
          <Button onClick={onRefresh} variant="outline">
            重试
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      {/* 头部 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">
            文档列表 ({filteredAndSortedDocuments.length})
          </h2>
          {shouldVirtualize && (
            <p className="text-sm text-blue-600 mt-1">
              ⚡ 虚拟滚动已启用
            </p>
          )}
        </div>
        
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
          {/* 搜索框 */}
          <input
            type="text"
            placeholder="搜索文档..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          
          {/* 排序选择 */}
          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field as any);
              setSortOrder(order as any);
            }}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="updatedAt-desc">最近更新</option>
            <option value="updatedAt-asc">最早更新</option>
            <option value="createdAt-desc">最近创建</option>
            <option value="createdAt-asc">最早创建</option>
            <option value="title-asc">标题 A-Z</option>
            <option value="title-desc">标题 Z-A</option>
          </select>
          
          <Button onClick={onRefresh} variant="outline" size="sm">
            刷新
          </Button>
        </div>
      </div>

      {/* 批量操作栏 */}
      {selectedDocuments.size > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              已选择 {selectedDocuments.size} 个文档
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => handleBatchAction('export')}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
              >
                导出
              </button>
              <button
                onClick={() => handleBatchAction('delete')}
                className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
              >
                删除
              </button>
              <button
                onClick={() => setSelectedDocuments(new Set())}
                className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
              >
                取消选择
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 文档列表 */}
      {filteredAndSortedDocuments.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📄</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchQuery ? '未找到匹配的文档' : '还没有文档'}
          </h3>
          <p className="text-gray-600 mb-6">
            {searchQuery 
              ? '尝试调整搜索条件或清空搜索框'
              : '创建您的第一个文档开始写作之旅'
            }
          </p>
          {!searchQuery && (
            <Link href="/editor">
              <Button>创建新文档</Button>
            </Link>
          )}
        </div>
      ) : shouldVirtualize ? (
        // 虚拟滚动列表
        <div className="h-96 border border-gray-200 rounded-lg">
          <List
            height={384} // 24rem = 384px
            width="100%"
            itemCount={rowCount}
            itemSize={rowHeight}
            itemData={{
              documents: filteredAndSortedDocuments,
              itemsPerRow,
              onDocumentAction: handleDocumentAction,
            }}
            className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
          >
            {VirtualizedRow}
          </List>
        </div>
      ) : (
        // 标准网格布局
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredAndSortedDocuments.map((document) => (
            <DocumentCard
              key={document.id}
              document={document}
            />
          ))}
        </div>
      )}

      {/* 底部信息 */}
      {filteredAndSortedDocuments.length > 0 && (
        <div className="mt-6 flex justify-between items-center text-sm text-gray-600">
          <div>
            显示 {filteredAndSortedDocuments.length} 个文档
            {searchQuery && ` (搜索: "${searchQuery}")`}
          </div>
          
          {shouldVirtualize && (
            <div className="flex items-center space-x-2">
              <span>虚拟滚动模式</span>
              <button
                onClick={() => {
                  // 切换到标准模式的逻辑
                  console.log('切换到标准模式');
                }}
                className="text-blue-600 hover:text-blue-800"
              >
                切换到标准模式
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}