#!/bin/bash

# Production Database Setup Script
# This script helps set up the database for production deployment

echo "🚀 Setting up production database..."

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable is not set"
    echo "Please set DATABASE_URL to your PostgreSQL connection string"
    echo "Example: export DATABASE_URL='postgresql://username:password@localhost:5432/nextjs_document_editor'"
    exit 1
fi

echo "✅ DATABASE_URL is set"

# Backup current schema
if [ -f "prisma/schema.prisma" ]; then
    cp prisma/schema.prisma prisma/schema.backup.prisma
    echo "✅ Backed up current schema to schema.backup.prisma"
fi

# Copy production schema
cp prisma/schema.production.prisma prisma/schema.prisma
echo "✅ Switched to production schema (PostgreSQL)"

# Generate Prisma client
echo "📦 Generating Prisma client..."
npx prisma generate

# Deploy migrations
echo "🗄️ Deploying database migrations..."
npx prisma migrate deploy

# Optionally seed the database
read -p "Do you want to seed the database with demo data? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🌱 Seeding database..."
    npm run db:seed
fi

echo "🎉 Production database setup complete!"
echo ""
echo "Next steps:"
echo "1. Verify the database connection: npm run db:test"
echo "2. Start your application: npm run build && npm start"
echo "3. Monitor logs for any issues"