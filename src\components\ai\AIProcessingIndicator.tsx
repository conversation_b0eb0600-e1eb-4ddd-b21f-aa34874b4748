/**
 * AI 处理进度指示器组件
 * 显示 AI 处理的详细进度和状态
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  Loader2, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Clock, 
  Zap, 
  X,
  Pause,
  Play,
  RotateCcw
} from 'lucide-react';
import type { 
  AIProcessingProgress, 
  AIProcessingStatus,
  AIProcessingStage,
  AIProcessingOptions 
} from '@/types/ai-status.types';

/**
 * AI 处理指示器属性
 */
interface AIProcessingIndicatorProps {
  /** 处理ID */
  processingId?: string;
  /** 处理进度 */
  progress: AIProcessingProgress | null;
  /** 显示选项 */
  options?: AIProcessingOptions;
  /** 取消处理回调 */
  onCancel?: () => void;
  /** 重试处理回调 */
  onRetry?: () => void;
  /** 暂停/恢复处理回调 */
  onPauseResume?: () => void;
  /** 关闭指示器回调 */
  onClose?: () => void;
  /** 紧凑模式 */
  compact?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * AI 处理指示器组件
 */
export function AIProcessingIndicator({
  progress,
  options = {},
  onCancel,
  onRetry,
  onPauseResume,
  onClose,
  compact = false,
  className = '',
}: AIProcessingIndicatorProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  // 控制显示/隐藏
  useEffect(() => {
    if (progress) {
      setIsVisible(true);
      
      // 自动隐藏逻辑
      if (progress.status === 'completed' || progress.status === 'error' || progress.status === 'cancelled') {
        const delay = options.autoHideDelay || 5000;
        const timer = setTimeout(() => {
          setIsVisible(false);
        }, delay);
        
        return () => clearTimeout(timer);
      }
    }
  }, [progress, options.autoHideDelay]);

  if (!progress || !isVisible) {
    return null;
  }

  /**
   * 获取状态图标
   */
  const getStatusIcon = (status: AIProcessingStatus) => {
    switch (status) {
      case 'preparing':
      case 'connecting':
      case 'processing':
      case 'generating':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-600" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'cancelled':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  /**
   * 获取状态文本
   */
  const getStatusText = (status: AIProcessingStatus) => {
    switch (status) {
      case 'preparing':
        return '准备中...';
      case 'connecting':
        return '连接中...';
      case 'processing':
        return '处理中...';
      case 'generating':
        return '生成中...';
      case 'completed':
        return '处理完成';
      case 'error':
        return '处理失败';
      case 'cancelled':
        return '已取消';
      default:
        return '未知状态';
    }
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status: AIProcessingStatus) => {
    switch (status) {
      case 'preparing':
      case 'connecting':
      case 'processing':
      case 'generating':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'cancelled':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  /**
   * 格式化时间
   */
  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${Math.floor(ms / 60000)}m ${Math.floor((ms % 60000) / 1000)}s`;
  };

  /**
   * 格式化令牌数
   */
  const formatTokens = (tokens: number) => {
    if (tokens < 1000) return tokens.toString();
    if (tokens < 1000000) return `${(tokens / 1000).toFixed(1)}K`;
    return `${(tokens / 1000000).toFixed(1)}M`;
  };

  /**
   * 处理暂停/恢复
   */
  const handlePauseResume = () => {
    setIsPaused(!isPaused);
    onPauseResume?.();
  };

  if (compact) {
    return (
      <div className={`inline-flex items-center space-x-2 px-3 py-1 bg-white border border-gray-200 rounded-full shadow-sm ${className}`}>
        {getStatusIcon(progress.status)}
        <span className="text-sm font-medium text-gray-700">
          {Math.round(progress.overallProgress)}%
        </span>
        {progress.cancellable && onCancel && (
          <button
            onClick={onCancel}
            className="p-0.5 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
            title="取消"
          >
            <X className="w-3 h-3" />
          </button>
        )}
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-md ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          {getStatusIcon(progress.status)}
          <span className="font-medium text-gray-900">
            {getStatusText(progress.status)}
          </span>
        </div>
        
        <div className="flex items-center space-x-1">
          {/* 暂停/恢复按钮 */}
          {(progress.status === 'processing' || progress.status === 'generating') && onPauseResume && (
            <button
              onClick={handlePauseResume}
              className="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-100"
              title={isPaused ? '恢复' : '暂停'}
            >
              {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
            </button>
          )}
          
          {/* 取消按钮 */}
          {progress.cancellable && onCancel && (
            <button
              onClick={onCancel}
              className="p-1 text-gray-400 hover:text-red-600 rounded hover:bg-red-50"
              title="取消"
            >
              <X className="w-4 h-4" />
            </button>
          )}
          
          {/* 重试按钮 */}
          {progress.status === 'error' && onRetry && (
            <button
              onClick={onRetry}
              className="p-1 text-gray-400 hover:text-blue-600 rounded hover:bg-blue-50"
              title="重试"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          )}
          
          {/* 关闭按钮 */}
          {onClose && (progress.status === 'completed' || progress.status === 'error' || progress.status === 'cancelled') && (
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-100"
              title="关闭"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* 总体进度条 */}
      <div className="mb-3">
        <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
          <span>总体进度</span>
          <span>{Math.round(progress.overallProgress)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getStatusColor(progress.status)}`}
            style={{ width: `${progress.overallProgress}%` }}
          />
        </div>
      </div>

      {/* 详细阶段进度 */}
      {options.showDetailedProgress && progress.stages && (
        <div className="mb-3">
          <div className="text-sm font-medium text-gray-700 mb-2">处理阶段</div>
          <div className="space-y-2">
            {progress.stages.map((stage) => (
              <StageIndicator key={stage.id} stage={stage} />
            ))}
          </div>
        </div>
      )}

      {/* 统计信息 */}
      <div className="flex items-center justify-between text-xs text-gray-500">
        {/* 时间信息 */}
        {options.showTimeEstimate && (
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3" />
            <span>
              {progress.estimatedTimeRemaining 
                ? `剩余 ${formatTime(progress.estimatedTimeRemaining)}`
                : `已用 ${formatTime(Date.now() - progress.startTime.getTime())}`
              }
            </span>
          </div>
        )}

        {/* 令牌信息 */}
        {options.showTokenCount && (progress.tokensProcessed || progress.estimatedTotalTokens) && (
          <div className="flex items-center space-x-1">
            <Zap className="w-3 h-3" />
            <span>
              {progress.tokensProcessed && progress.estimatedTotalTokens
                ? `${formatTokens(progress.tokensProcessed)}/${formatTokens(progress.estimatedTotalTokens)} tokens`
                : progress.tokensProcessed
                ? `${formatTokens(progress.tokensProcessed)} tokens`
                : `~${formatTokens(progress.estimatedTotalTokens!)} tokens`
              }
            </span>
          </div>
        )}
      </div>

      {/* 错误信息 */}
      {progress.error && (
        <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
          {progress.error}
        </div>
      )}
    </div>
  );
}

/**
 * 阶段指示器组件
 */
interface StageIndicatorProps {
  stage: AIProcessingStage;
}

function StageIndicator({ stage }: StageIndicatorProps) {
  const getStageIcon = () => {
    if (stage.completed) {
      return <CheckCircle className="w-3 h-3 text-green-600" />;
    } else if (stage.current) {
      return <Loader2 className="w-3 h-3 animate-spin text-blue-600" />;
    } else {
      return <div className="w-3 h-3 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStageColor = () => {
    if (stage.completed) return 'bg-green-500';
    if (stage.current) return 'bg-blue-500';
    return 'bg-gray-300';
  };

  return (
    <div className="flex items-center space-x-2">
      {getStageIcon()}
      <div className="flex-1">
        <div className="flex items-center justify-between text-xs">
          <span className={`font-medium ${stage.current ? 'text-blue-700' : stage.completed ? 'text-green-700' : 'text-gray-500'}`}>
            {stage.name}
          </span>
          <span className="text-gray-500">
            {Math.round(stage.progress)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
          <div
            className={`h-1 rounded-full transition-all duration-300 ${getStageColor()}`}
            style={{ width: `${stage.progress}%` }}
          />
        </div>
        {stage.current && (
          <div className="text-xs text-gray-500 mt-1">
            {stage.description}
          </div>
        )}
      </div>
    </div>
  );
}