/**
 * OpenAI 服务实现
 * 使用官方 OpenAI SDK 提供 OpenAI API 的集成服务
 */

import OpenAI from 'openai';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { BaseAIService } from './base-ai-service';
import { aiEnvConfig } from '@/lib/config/env';
import {
  AIServiceConfig,
  AIRequest,
  AIResponse,
  AIServiceError,
  AIErrorType,
  AIProvider
} from '@/types/ai.types';

/**
 * OpenAI 服务实现
 */
export class OpenAIService extends BaseAIService {
  readonly provider: AIProvider = 'openai';

  private readonly baseURL = 'https://api.openai.com/v1';
  private openai: OpenAI;

  constructor(config: AIServiceConfig) {
    super(config);

    if (!config.apiKey) {
      throw new Error('OpenAI API 密钥是必需的');
    }

    // 创建 OpenAI 客户端
    this.openai = this.createOpenAIClient();
  }

  /**
   * 创建 OpenAI 客户端
   */
  private createOpenAIClient(): OpenAI {
    // 确定代理地址：配置 > 环境变量 > 无代理
    let proxyUrl = '';
    if (this.config.endpoint && this.config.endpoint.trim()) {
      // 配置的代理优先
      proxyUrl = this.config.endpoint;
    } else if (typeof window === 'undefined') {
      // 服务器端使用环境变量
      proxyUrl = aiEnvConfig.getProxyUrl();
    }

    const clientConfig: any = {
      apiKey: this.config.apiKey,
      baseURL: this.baseURL,
      timeout: this.config.timeout || 60000, // 增加超时时间到 60 秒
    };

    // 如果有代理配置，使用自定义 fetch 函数
    if (proxyUrl && typeof window === 'undefined') {
      console.log('OpenAI 客户端使用代理:', proxyUrl);

      try {
        // 创建自定义 fetch 函数
        const agent = new HttpsProxyAgent(proxyUrl);

        // 创建自定义 fetch 函数
        clientConfig.fetch = async (url: string, init: any = {}) => {
          const nodeFetch = (await import('node-fetch')).default;
          return nodeFetch(url, {
            ...init,
            agent: agent
          }) as any;
        };
      } catch (error) {
        console.warn('设置代理失败:', error);
      }
    }

    return new OpenAI(clientConfig);
  }

  /**
   * 生成文本内容
   */
  async _generateText(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    return this.executeWithRetry(async () => {
      try {
        const completion = await this.openai.chat.completions.create({
          model: this.config.model,
          messages: this.buildMessages(request),
          max_tokens: request.maxTokens || this.config.maxTokens || 2000,
          temperature: request.temperature ?? this.config.temperature ?? 0.7,
          stream: false
        });

        if (!completion.choices || completion.choices.length === 0) {
          throw new AIServiceError(
            AIErrorType.INVALID_REQUEST,
            'OpenAI 返回了空的响应',
            this.provider
          );
        }

        const choice = completion.choices[0];

        // 检查内容过滤
        if (choice.finish_reason === 'content_filter') {
          throw new AIServiceError(
            AIErrorType.CONTENT_FILTERED,
            '内容被 OpenAI 安全过滤器拦截',
            this.provider
          );
        }

        return {
          content: choice.message.content?.trim() || '',
          tokensUsed: completion.usage?.total_tokens || 0,
          responseTime: Date.now() - startTime,
          model: completion.model,
          provider: this.provider
        };
      } catch (error) {
        throw this.handleOpenAIError(error);
      }
    }, 'OpenAI text generation');
  }

  /**
   * 获取可用模型列表
   */
  async getAvailableModels(): Promise<string[]> {
    try {
      const models = await this.openai.models.list();

      // 过滤出 GPT 模型并排序
      return models.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id)
        .sort();
    } catch (error) {
      console.warn('获取 OpenAI 模型列表失败:', error);
      return ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo-preview'];
    }
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('开始 OpenAI 连接测试...');

      // 使用简单的聊天完成来测试连接，增加超时控制
      const completion = await Promise.race([
        this.openai.chat.completions.create({
          model: this.config.model || 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: '测试' }],
          max_tokens: 5,
          temperature: 0
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('连接测试超时')), 45000) // 45秒超时
        )
      ]) as OpenAI.Chat.Completions.ChatCompletion;

      const success = !!(completion.choices && completion.choices.length > 0);
      console.log('OpenAI 连接测试结果:', success);
      return success;
    } catch (error) {
      console.error('OpenAI 连接测试失败:', error);

      // 提供更详细的错误信息
      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('ETIMEDOUT')) {
          console.error('连接超时，可能是代理配置问题或网络问题');
        } else if (error.message.includes('ECONNREFUSED')) {
          console.error('连接被拒绝，请检查代理服务器是否运行');
        }
      }

      return false;
    }
  }

  /**
   * 构建消息数组
   */
  private buildMessages(request: AIRequest): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [];

    // 添加系统提示
    if (request.systemPrompt) {
      messages.push({
        role: 'system',
        content: request.systemPrompt
      });
    }

    // 添加上下文
    if (request.context) {
      messages.push({
        role: 'system',
        content: `上下文信息：\n${request.context}`
      });
    }

    // 添加用户提示
    messages.push({
      role: 'user',
      content: request.prompt
    });

    return messages;
  }

  /**
   * 处理 OpenAI 错误
   */
  private handleOpenAIError(error: any): AIServiceError {
    if (error instanceof OpenAI.APIError) {
      switch (error.status) {
        case 400:
          return new AIServiceError(
            AIErrorType.INVALID_REQUEST,
            `请求参数无效: ${error.message}`,
            this.provider,
            error
          );
        case 401:
          return new AIServiceError(
            AIErrorType.INVALID_API_KEY,
            'OpenAI API 密钥无效或已过期',
            this.provider,
            error
          );
        case 429:
          return new AIServiceError(
            AIErrorType.QUOTA_EXCEEDED,
            '请求频率超限或配额不足',
            this.provider,
            error
          );
        case 500:
        case 502:
        case 503:
        case 504:
          return new AIServiceError(
            AIErrorType.SERVICE_UNAVAILABLE,
            'OpenAI 服务暂时不可用',
            this.provider,
            error
          );
        default:
          return new AIServiceError(
            AIErrorType.UNKNOWN_ERROR,
            `OpenAI API 错误: ${error.message}`,
            this.provider,
            error
          );
      }
    }

    if (error instanceof OpenAI.APIConnectionError) {
      return new AIServiceError(
        AIErrorType.NETWORK_ERROR,
        '网络连接失败，请检查网络设置或代理配置',
        this.provider,
        error
      );
    }

    if (error.name === 'APITimeoutError' || error.message?.includes('timeout')) {
      return new AIServiceError(
        AIErrorType.TIMEOUT,
        '请求超时，请稍后再试',
        this.provider,
        error
      );
    }

    // 处理其他错误
    return this.handleHttpError(error, 'OpenAI API request');
  }
}