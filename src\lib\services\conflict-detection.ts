import { LocalDocument } from '@/lib/storage/database';
import { SyncConflict } from '@/types/sync';
import { JSONContent } from '@tiptap/react';

/**
 * 冲突检测类型
 */
export enum ConflictType {
  TITLE = 'title',
  CONTENT = 'content',
  METADATA = 'metadata',
  STRUCTURE = 'structure',
  SIMULTANEOUS = 'simultaneous'
}

/**
 * 冲突严重程度
 */
export enum ConflictSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 详细的冲突信息
 */
export interface DetailedConflict extends SyncConflict {
  severity: ConflictSeverity;
  affectedSections: string[];
  suggestedResolution: 'local' | 'remote' | 'merge' | 'manual';
  autoMergeable: boolean;
  conflictDetails: ConflictDetail[];
}

/**
 * 冲突详情
 */
export interface ConflictDetail {
  type: 'addition' | 'deletion' | 'modification';
  section: string;
  localValue: any;
  remoteValue: any;
  position?: number;
  description: string;
}

/**
 * 高级冲突检测服务
 */
export class ConflictDetectionService {
  /**
   * 检测文档冲突
   */
  static detectConflict(localDoc: LocalDocument, remoteDoc: any): DetailedConflict | null {
    const localUpdated = localDoc.updatedAt.getTime();
    const remoteUpdated = new Date(remoteDoc.updatedAt).getTime();
    const lastSync = localDoc.lastSyncAt?.getTime() || 0;

    // 基础时间冲突检测
    if (!(localUpdated > lastSync && remoteUpdated > lastSync && localUpdated !== remoteUpdated)) {
      return null;
    }

    const conflictDetails: ConflictDetail[] = [];
    const affectedSections: string[] = [];
    let primaryConflictType = ConflictType.CONTENT;
    let severity = ConflictSeverity.LOW;

    // 检测标题冲突
    if (localDoc.title !== remoteDoc.title) {
      conflictDetails.push({
        type: 'modification',
        section: 'title',
        localValue: localDoc.title,
        remoteValue: remoteDoc.title,
        description: `标题从 "${remoteDoc.title}" 更改为 "${localDoc.title}"`
      });
      affectedSections.push('title');
      primaryConflictType = ConflictType.TITLE;
      severity = ConflictSeverity.MEDIUM;
    }

    // 检测内容冲突
    const contentConflicts = this.detectContentConflicts(localDoc.content, remoteDoc.content);
    if (contentConflicts.length > 0) {
      conflictDetails.push(...contentConflicts);
      affectedSections.push('content');
      if (primaryConflictType === ConflictType.CONTENT) {
        severity = this.calculateContentConflictSeverity(contentConflicts);
      }
    }

    // 检测元数据冲突
    const metadataConflicts = this.detectMetadataConflicts(localDoc.metadata, remoteDoc.metadata);
    if (metadataConflicts.length > 0) {
      conflictDetails.push(...metadataConflicts);
      affectedSections.push('metadata');
      if (primaryConflictType === ConflictType.CONTENT && contentConflicts.length === 0) {
        primaryConflictType = ConflictType.METADATA;
      }
    }

    // 检测结构冲突
    const structureConflicts = this.detectStructureConflicts(localDoc.content, remoteDoc.content);
    if (structureConflicts.length > 0) {
      conflictDetails.push(...structureConflicts);
      affectedSections.push('structure');
      primaryConflictType = ConflictType.STRUCTURE;
      severity = ConflictSeverity.HIGH;
    }

    // 检测同时编辑冲突
    const timeDiff = Math.abs(localUpdated - remoteUpdated);
    if (timeDiff < 60000) { // 1分钟内的编辑认为是同时编辑
      primaryConflictType = ConflictType.SIMULTANEOUS;
      severity = ConflictSeverity.CRITICAL;
    }

    // 确定建议的解决方案
    const suggestedResolution = this.suggestResolution(conflictDetails, severity);
    const autoMergeable = this.isAutoMergeable(conflictDetails, severity);

    return {
      id: `conflict_${localDoc.id}_${Date.now()}`,
      documentId: localDoc.id,
      localVersion: localDoc,
      remoteVersion: remoteDoc,
      conflictType: primaryConflictType,
      timestamp: new Date(),
      resolved: false,
      severity,
      affectedSections,
      suggestedResolution,
      autoMergeable,
      conflictDetails
    };
  }

  /**
   * 检测内容冲突
   */
  private static detectContentConflicts(localContent: JSONContent, remoteContent: JSONContent): ConflictDetail[] {
    const conflicts: ConflictDetail[] = [];

    // 简化的内容比较 - 实际实现中可以使用更复杂的diff算法
    const localText = this.extractTextFromContent(localContent);
    const remoteText = this.extractTextFromContent(remoteContent);

    if (localText !== remoteText) {
      const localWords = localText.split(/\s+/).length;
      const remoteWords = remoteText.split(/\s+/).length;
      const wordDiff = Math.abs(localWords - remoteWords);

      conflicts.push({
        type: wordDiff > localWords * 0.3 ? 'modification' : 'modification',
        section: 'content',
        localValue: localText.substring(0, 100) + (localText.length > 100 ? '...' : ''),
        remoteValue: remoteText.substring(0, 100) + (remoteText.length > 100 ? '...' : ''),
        description: `内容发生变化，本地 ${localWords} 词，远程 ${remoteWords} 词`
      });
    }

    return conflicts;
  }

  /**
   * 检测元数据冲突
   */
  private static detectMetadataConflicts(localMeta: any, remoteMeta: any): ConflictDetail[] {
    const conflicts: ConflictDetail[] = [];

    // 检查字数统计冲突
    if (localMeta.wordCount !== remoteMeta.wordCount) {
      conflicts.push({
        type: 'modification',
        section: 'wordCount',
        localValue: localMeta.wordCount,
        remoteValue: remoteMeta.wordCount,
        description: `字数统计不一致：本地 ${localMeta.wordCount}，远程 ${remoteMeta.wordCount}`
      });
    }

    // 检查字符数冲突
    if (localMeta.characterCount !== remoteMeta.characterCount) {
      conflicts.push({
        type: 'modification',
        section: 'characterCount',
        localValue: localMeta.characterCount,
        remoteValue: remoteMeta.characterCount,
        description: `字符数不一致：本地 ${localMeta.characterCount}，远程 ${remoteMeta.characterCount}`
      });
    }

    // 检查标签冲突
    const localTags = localMeta.tags || [];
    const remoteTags = remoteMeta.tags || [];
    if (JSON.stringify(localTags.sort()) !== JSON.stringify(remoteTags.sort())) {
      conflicts.push({
        type: 'modification',
        section: 'tags',
        localValue: localTags,
        remoteValue: remoteTags,
        description: `标签不一致：本地 [${localTags.join(', ')}]，远程 [${remoteTags.join(', ')}]`
      });
    }

    // 检查公开状态冲突
    if (localMeta.isPublic !== remoteMeta.isPublic) {
      conflicts.push({
        type: 'modification',
        section: 'isPublic',
        localValue: localMeta.isPublic,
        remoteValue: remoteMeta.isPublic,
        description: `公开状态不一致：本地 ${localMeta.isPublic ? '公开' : '私有'}，远程 ${remoteMeta.isPublic ? '公开' : '私有'}`
      });
    }

    return conflicts;
  }

  /**
   * 检测结构冲突
   */
  private static detectStructureConflicts(localContent: JSONContent, remoteContent: JSONContent): ConflictDetail[] {
    const conflicts: ConflictDetail[] = [];

    // 检查文档结构变化
    const localStructure = this.analyzeDocumentStructure(localContent);
    const remoteStructure = this.analyzeDocumentStructure(remoteContent);

    if (localStructure.headingCount !== remoteStructure.headingCount) {
      conflicts.push({
        type: 'modification',
        section: 'structure',
        localValue: localStructure.headingCount,
        remoteValue: remoteStructure.headingCount,
        description: `标题数量变化：本地 ${localStructure.headingCount}，远程 ${remoteStructure.headingCount}`
      });
    }

    if (localStructure.paragraphCount !== remoteStructure.paragraphCount) {
      conflicts.push({
        type: 'modification',
        section: 'structure',
        localValue: localStructure.paragraphCount,
        remoteValue: remoteStructure.paragraphCount,
        description: `段落数量变化：本地 ${localStructure.paragraphCount}，远程 ${remoteStructure.paragraphCount}`
      });
    }

    return conflicts;
  }

  /**
   * 计算内容冲突严重程度
   */
  private static calculateContentConflictSeverity(conflicts: ConflictDetail[]): ConflictSeverity {
    if (conflicts.length === 0) return ConflictSeverity.LOW;
    if (conflicts.length === 1) return ConflictSeverity.MEDIUM;
    if (conflicts.length <= 3) return ConflictSeverity.HIGH;
    return ConflictSeverity.CRITICAL;
  }

  /**
   * 建议解决方案
   */
  private static suggestResolution(conflicts: ConflictDetail[], severity: ConflictSeverity): 'local' | 'remote' | 'merge' | 'manual' {
    // 如果只有元数据冲突，建议使用本地版本
    const hasContentConflict = conflicts.some(c => c.section === 'content' || c.section === 'title');
    if (!hasContentConflict && severity === ConflictSeverity.LOW) {
      return 'local';
    }

    // 如果冲突严重，建议手动解决
    if (severity === ConflictSeverity.CRITICAL) {
      return 'manual';
    }

    // 如果只有简单的内容变化，建议合并
    if (severity === ConflictSeverity.MEDIUM && conflicts.length <= 2) {
      return 'merge';
    }

    // 默认建议手动解决
    return 'manual';
  }

  /**
   * 判断是否可以自动合并
   */
  private static isAutoMergeable(conflicts: ConflictDetail[], severity: ConflictSeverity): boolean {
    // 只有低严重程度的元数据冲突可以自动合并
    if (severity !== ConflictSeverity.LOW) return false;

    // 检查是否只有元数据冲突
    const hasContentConflict = conflicts.some(c => 
      c.section === 'content' || c.section === 'title' || c.section === 'structure'
    );

    return !hasContentConflict;
  }

  /**
   * 从内容中提取文本
   */
  private static extractTextFromContent(content: JSONContent): string {
    if (!content) return '';

    let text = '';

    if (content.text) {
      text += content.text;
    }

    if (content.content && Array.isArray(content.content)) {
      for (const child of content.content) {
        text += this.extractTextFromContent(child);
      }
    }

    return text;
  }

  /**
   * 分析文档结构
   */
  private static analyzeDocumentStructure(content: JSONContent): {
    headingCount: number;
    paragraphCount: number;
    listCount: number;
    codeBlockCount: number;
  } {
    const structure = {
      headingCount: 0,
      paragraphCount: 0,
      listCount: 0,
      codeBlockCount: 0
    };

    const analyze = (node: JSONContent) => {
      if (!node) return;

      switch (node.type) {
        case 'heading':
          structure.headingCount++;
          break;
        case 'paragraph':
          structure.paragraphCount++;
          break;
        case 'bulletList':
        case 'orderedList':
          structure.listCount++;
          break;
        case 'codeBlock':
          structure.codeBlockCount++;
          break;
      }

      if (node.content && Array.isArray(node.content)) {
        for (const child of node.content) {
          analyze(child);
        }
      }
    };

    analyze(content);
    return structure;
  }

  /**
   * 自动合并简单冲突
   */
  static autoMergeConflicts(localDoc: LocalDocument, remoteDoc: any, conflict: DetailedConflict): LocalDocument | null {
    if (!conflict.autoMergeable) return null;

    // 创建合并后的文档
    const mergedDoc = { ...localDoc };

    // 合并元数据冲突
    for (const detail of conflict.conflictDetails) {
      switch (detail.section) {
        case 'wordCount':
        case 'characterCount':
          // 使用更新的统计数据
          mergedDoc.metadata = {
            ...mergedDoc.metadata,
            [detail.section]: Math.max(detail.localValue, detail.remoteValue)
          };
          break;
        case 'tags':
          // 合并标签
          const localTags = detail.localValue || [];
          const remoteTags = detail.remoteValue || [];
          const mergedTags = [...new Set([...localTags, ...remoteTags])];
          mergedDoc.metadata = {
            ...mergedDoc.metadata,
            tags: mergedTags
          };
          break;
        case 'isPublic':
          // 保持本地的公开状态设置
          break;
      }
    }

    return mergedDoc;
  }
}