// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  emailVerified DateTime?
  name          String?
  image         String?
  password      String?   // For credentials provider
  subscription  String    @default("free") // free, pro, enterprise
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts       Account[]
  sessions       Session[]
  folders        Folder[]
  documents      Document[]
  aiConfigs      AIConfiguration[]
  aiInteractions AIInteraction[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Folder {
  id        String   @id @default(cuid())
  name      String
  parentId  String?
  userId    String
  isDeleted Boolean  @default(false) //
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent    Folder?    @relation("FolderHierarchy", fields: [parentId], references: [id])
  children  Folder[]   @relation("FolderHierarchy")
  documents Document[]

  // 唯一约束：通过自定义 SQL 索引实现，支持软删除
  // 约束名称：unique_active_folder_name
  // 约束逻辑：同一用户在同一父文件夹下不能有重复名称的活跃文件夹
  // 实现方式：见迁移文件 20250727065639_add_custom_unique_constraints
  @@index([userId, isDeleted], map: "idx_user_deleted_folders")
  @@map("folders")
}

model Document {
  id          String    @id @default(cuid())
  title       String
  content     String    // JSON string of TipTap content
  folderId    String?
  userId      String
  wordCount   Int       @default(0)
  charCount   Int       @default(0)
  isPublic    Boolean   @default(false)
  shareToken  String?   @unique
  isDeleted   Boolean   @default(false)
  lastSyncAt  DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  user           User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  folder         Folder?           @relation(fields: [folderId], references: [id])
  history        DocumentHistory[]
  aiInteractions AIInteraction[]

  // 唯一约束：通过自定义 SQL 索引实现，支持软删除
  // 约束名称：unique_active_document_title
  // 约束逻辑：同一用户在同一文件夹下不能有重复标题的活跃文档
  // 实现方式：见迁移文件 20250727065639_add_custom_unique_constraints
  @@index([userId, isDeleted], map: "idx_user_deleted_documents")
  @@map("documents")
}

model DocumentHistory {
  id         String   @id @default(cuid())
  documentId String
  version    Int
  content    String   // JSON string
  changeType String   // user, ai
  createdAt  DateTime @default(now())

  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@map("document_history")
}

model AIConfiguration {
  id          String   @id @default(cuid())
  userId      String
  provider    String   // openai, ollama, gemini
  apiKey      String?
  endpoint    String?
  model       String
  maxTokens   Int      @default(2000)
  temperature Float    @default(0.7)
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ai_configurations")
}

model AIInteraction {
  id         String   @id @default(cuid())
  documentId String
  userId     String
  type       String   // generate, rewrite, summarize, analyze, translate, explain
  input      String
  output     String
  provider   String
  model      String
  tokens     Int      @default(0)
  createdAt  DateTime @default(now())

  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ai_interactions")
}