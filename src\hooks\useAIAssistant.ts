'use client';

import { useState, useCallback, useEffect, useRef } from 'react';

/**
 * AI 助手操作类型
 */
export type AIAssistantAction = 
  | 'ai-continue'
  | 'ai-rewrite'
  | 'ai-summarize'
  | 'ai-translate'
  | 'ai-explain'
  | 'ai-keywords'
  | 'ai-outline'
  | 'ai-analysis'
  | 'ai-creative'
  | 'ai-custom'
  | 'ai-chat'
  | 'ai-grammar'
  | 'ai-expand'
  | 'ai-settings';

/**
 * AI 助手操作数据
 */
export interface AIAssistantActionData {
  text?: string;
  instruction?: string;
  options?: Record<string, any>;
}

/**
 * AI 助手状态
 */
export interface AIAssistantState {
  /** 面板是否打开 */
  isOpen: boolean;
  /** 是否正在处理 */
  isProcessing: boolean;
  /** 处理状态消息 */
  processingStatus: string;
  /** 当前选中的文本 */
  selectedText: string;
  /** 最近使用的功能 */
  recentActions: string[];
  /** 操作历史 */
  actionHistory: Array<{
    id: string;
    action: AIAssistantAction;
    data?: AIAssistantActionData;
    timestamp: Date;
    success: boolean;
    error?: string;
  }>;
}

/**
 * AI 助手 Hook 配置
 */
export interface UseAIAssistantConfig {
  /** 初始打开状态 */
  defaultOpen?: boolean;
  /** 最大历史记录数 */
  maxHistorySize?: number;
  /** 最大最近操作数 */
  maxRecentActions?: number;
  /** 自动保存状态到 localStorage */
  persistState?: boolean;
  /** localStorage 键名 */
  storageKey?: string;
}

/**
 * AI 助手面板管理 Hook
 * 提供面板状态管理、操作历史记录等功能
 */
export function useAIAssistant(config: UseAIAssistantConfig = {}) {
  const {
    defaultOpen = false,
    maxHistorySize = 50,
    maxRecentActions = 10,
    persistState = true,
    storageKey = 'ai-assistant-state'
  } = config;

  // 状态管理
  const [state, setState] = useState<AIAssistantState>(() => {
    // 从 localStorage 恢复状态
    if (persistState && typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem(storageKey);
        if (saved) {
          const parsedState = JSON.parse(saved);
          return {
            ...parsedState,
            isOpen: defaultOpen, // 不恢复打开状态
            isProcessing: false, // 重置处理状态
            processingStatus: '',
            actionHistory: parsedState.actionHistory?.map((item: any) => ({
              ...item,
              timestamp: new Date(item.timestamp)
            })) || []
          };
        }
      } catch (error) {
        console.warn('Failed to restore AI assistant state:', error);
      }
    }

    return {
      isOpen: defaultOpen,
      isProcessing: false,
      processingStatus: '',
      selectedText: '',
      recentActions: [],
      actionHistory: []
    };
  });

  // 保存状态到 localStorage
  const saveState = useCallback((newState: AIAssistantState) => {
    if (persistState && typeof window !== 'undefined') {
      try {
        const stateToSave = {
          ...newState,
          isOpen: false, // 不保存打开状态
          isProcessing: false, // 不保存处理状态
          processingStatus: ''
        };
        localStorage.setItem(storageKey, JSON.stringify(stateToSave));
      } catch (error) {
        console.warn('Failed to save AI assistant state:', error);
      }
    }
  }, [persistState, storageKey]);

  // 更新状态的通用方法
  const updateState = useCallback((updater: Partial<AIAssistantState> | ((prev: AIAssistantState) => AIAssistantState)) => {
    setState(prev => {
      const newState = typeof updater === 'function' ? updater(prev) : { ...prev, ...updater };
      saveState(newState);
      return newState;
    });
  }, [saveState]);

  /**
   * 切换面板显示状态
   */
  const togglePanel = useCallback(() => {
    updateState(prev => ({ ...prev, isOpen: !prev.isOpen }));
  }, [updateState]);

  /**
   * 打开面板
   */
  const openPanel = useCallback(() => {
    updateState(prev => ({ ...prev, isOpen: true }));
  }, [updateState]);

  /**
   * 关闭面板
   */
  const closePanel = useCallback(() => {
    updateState(prev => ({ ...prev, isOpen: false }));
  }, [updateState]);

  /**
   * 设置选中文本
   */
  const setSelectedText = useCallback((text: string) => {
    updateState(prev => ({ ...prev, selectedText: text }));
  }, [updateState]);

  /**
   * 开始处理
   */
  const startProcessing = useCallback((status: string = '正在处理...') => {
    updateState(prev => ({
      ...prev,
      isProcessing: true,
      processingStatus: status
    }));
  }, [updateState]);

  /**
   * 结束处理
   */
  const stopProcessing = useCallback(() => {
    updateState(prev => ({
      ...prev,
      isProcessing: false,
      processingStatus: ''
    }));
  }, [updateState]);

  /**
   * 添加操作到历史记录
   */
  const addToHistory = useCallback((
    action: AIAssistantAction,
    data?: AIAssistantActionData,
    success: boolean = true,
    error?: string
  ) => {
    const historyItem = {
      id: Math.random().toString(36).substr(2, 9),
      action,
      data,
      timestamp: new Date(),
      success,
      error
    };

    updateState(prev => {
      // 更新历史记录
      const newHistory = [historyItem, ...prev.actionHistory].slice(0, maxHistorySize);
      
      // 更新最近操作（只记录成功的操作）
      let newRecentActions = prev.recentActions;
      if (success) {
        newRecentActions = [action, ...prev.recentActions.filter(a => a !== action)].slice(0, maxRecentActions);
      }

      return {
        ...prev,
        actionHistory: newHistory,
        recentActions: newRecentActions
      };
    });
  }, [updateState, maxHistorySize, maxRecentActions]);

  /**
   * 清空历史记录
   */
  const clearHistory = useCallback(() => {
    updateState(prev => ({
      ...prev,
      actionHistory: [],
      recentActions: []
    }));
  }, [updateState]);

  /**
   * 执行 AI 操作
   */
  const executeAction = useCallback(async (
    action: AIAssistantAction,
    data?: AIAssistantActionData,
    handler?: (action: AIAssistantAction, data?: AIAssistantActionData) => Promise<void>
  ) => {
    try {
      // 开始处理
      const statusMessages: Record<AIAssistantAction, string> = {
        'ai-continue': '正在生成续写内容...',
        'ai-rewrite': '正在改写文本...',
        'ai-summarize': '正在生成摘要...',
        'ai-translate': '正在翻译文本...',
        'ai-explain': '正在生成解释...',
        'ai-keywords': '正在提取关键词...',
        'ai-outline': '正在生成大纲...',
        'ai-analysis': '正在分析内容...',
        'ai-creative': '正在创作内容...',
        'ai-custom': '正在执行自定义指令...',
        'ai-chat': '正在准备对话...',
        'ai-grammar': '正在检查语法...',
        'ai-expand': '正在扩展内容...',
        'ai-settings': '正在打开设置...'
      };

      startProcessing(statusMessages[action] || '正在处理请求...');

      // 执行处理器
      if (handler) {
        await handler(action, data);
      }

      // 添加到历史记录
      addToHistory(action, data, true);
    } catch (error) {
      // 添加错误到历史记录
      addToHistory(action, data, false, error instanceof Error ? error.message : '未知错误');
      throw error;
    } finally {
      // 结束处理
      stopProcessing();
    }
  }, [startProcessing, stopProcessing, addToHistory]);

  /**
   * 获取功能是否可用
   */
  const isActionAvailable = useCallback((action: AIAssistantAction): boolean => {
    // 需要选中文本的功能
    const textRequiredActions: AIAssistantAction[] = [
      'ai-rewrite',
      'ai-translate',
      'ai-explain',
      'ai-grammar',
      'ai-expand'
    ];

    if (textRequiredActions.includes(action)) {
      return state.selectedText.length > 0;
    }

    return true;
  }, [state.selectedText]);

  /**
   * 获取最近使用的功能
   */
  const getRecentActions = useCallback(() => {
    return state.recentActions;
  }, [state.recentActions]);

  /**
   * 获取操作历史
   */
  const getActionHistory = useCallback((limit?: number) => {
    return limit ? state.actionHistory.slice(0, limit) : state.actionHistory;
  }, [state.actionHistory]);

  /**
   * 获取成功操作的统计
   */
  const getSuccessStats = useCallback(() => {
    const total = state.actionHistory.length;
    const successful = state.actionHistory.filter(item => item.success).length;
    return {
      total,
      successful,
      failed: total - successful,
      successRate: total > 0 ? (successful / total) * 100 : 0
    };
  }, [state.actionHistory]);

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + A: 切换面板
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'A') {
        event.preventDefault();
        togglePanel();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [togglePanel]);

  return {
    // 状态
    state,
    
    // 面板控制
    togglePanel,
    openPanel,
    closePanel,
    
    // 文本管理
    setSelectedText,
    
    // 处理状态
    startProcessing,
    stopProcessing,
    
    // 操作执行
    executeAction,
    
    // 历史管理
    addToHistory,
    clearHistory,
    
    // 查询方法
    isActionAvailable,
    getRecentActions,
    getActionHistory,
    getSuccessStats,
    
    // 便捷属性
    isOpen: state.isOpen,
    isProcessing: state.isProcessing,
    processingStatus: state.processingStatus,
    selectedText: state.selectedText,
    recentActions: state.recentActions,
    actionHistory: state.actionHistory
  };
}

/**
 * AI 助手面板的响应式检测 Hook
 */
export function useAIAssistantResponsive() {
  const [isMobile, setIsMobile] = useState(false);
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({ width, height });
      setIsMobile(width < 768);
    };

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  return {
    isMobile,
    screenSize,
    isTablet: screenSize.width >= 768 && screenSize.width < 1024,
    isDesktop: screenSize.width >= 1024
  };
}