'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/Button';
import { 
  MessageCircleIcon, 
  SendIcon, 
  XIcon,
  UserIcon,
  BotIcon,
  CopyIcon,
  RefreshCwIcon,
  LanguagesIcon,
  LightbulbIcon,
  BookOpenIcon
} from 'lucide-react';

/**
 * Chat 消息类型
 */
export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  selectedText?: string;
  action?: string;
}

/**
 * Chat 对话框属性
 */
interface ChatDialogProps {
  /** 是否显示对话框 */
  visible: boolean;
  /** 关闭对话框回调 */
  onClose: () => void;
  /** 选中的文本 */
  selectedText: string;
  /** 编辑器实例 */
  editor: Editor;
  /** 初始动作类型 */
  initialAction?: string;
  /** AI 响应回调 */
  onAIResponse?: (response: string) => void;
}

/**
 * 预设的 Chat 动作
 */
const CHAT_ACTIONS = [
  {
    id: 'explain',
    label: '解释',
    icon: <LightbulbIcon className="h-4 w-4" />,
    prompt: '请解释以下内容：'
  },
  {
    id: 'translate',
    label: '翻译',
    icon: <LanguagesIcon className="h-4 w-4" />,
    prompt: '请将以下内容翻译成中文：'
  },
  {
    id: 'summarize',
    label: '总结',
    icon: <BookOpenIcon className="h-4 w-4" />,
    prompt: '请总结以下内容：'
  },
  {
    id: 'discuss',
    label: '讨论',
    icon: <MessageCircleIcon className="h-4 w-4" />,
    prompt: '让我们讨论以下内容：'
  }
];

/**
 * Chat 对话框组件
 * 提供与 AI 的对话界面，支持对选中文本进行各种操作
 */
export function ChatDialog({
  visible,
  onClose,
  selectedText,
  editor,
  initialAction = 'explain',
  onAIResponse
}: ChatDialogProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentAction, setCurrentAction] = useState(initialAction);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  /**
   * 滚动到消息底部
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * 初始化对话
   */
  useEffect(() => {
    if (visible && selectedText && messages.length === 0) {
      const action = CHAT_ACTIONS.find(a => a.id === currentAction);
      if (action) {
        const initialMessage: ChatMessage = {
          id: Date.now().toString(),
          type: 'user',
          content: `${action.prompt}\n\n"${selectedText}"`,
          timestamp: new Date(),
          selectedText,
          action: currentAction
        };
        setMessages([initialMessage]);
        handleAIResponse(initialMessage);
      }
    }
  }, [visible, selectedText, currentAction]);

  /**
   * 滚动到底部
   */
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  /**
   * 聚焦输入框
   */
  useEffect(() => {
    if (visible) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [visible]);

  /**
   * 处理 AI 响应
   */
  const handleAIResponse = async (userMessage: ChatMessage) => {
    setIsLoading(true);
    
    try {
      // 模拟 AI 响应（实际项目中应该调用真实的 AI 服务）
      const response = await simulateAIResponse(userMessage);
      
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, aiMessage]);
      onAIResponse?.(response);
    } catch (error) {
      console.error('AI 响应失败:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: '抱歉，AI 服务暂时不可用。请稍后再试。',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 模拟 AI 响应
   */
  const simulateAIResponse = async (userMessage: ChatMessage): Promise<string> => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    const { action, selectedText } = userMessage;
    
    switch (action) {
      case 'explain':
        return `我来解释一下这段内容：\n\n"${selectedText}"\n\n这段文本的主要含义是...（这里是模拟的解释内容）。关键要点包括：\n\n1. 主要概念和定义\n2. 相关背景信息\n3. 实际应用场景\n\n希望这个解释对您有帮助！`;
      
      case 'translate':
        return `翻译结果：\n\n"${selectedText}"\n\n翻译为：\n\n"这是翻译后的内容（模拟）"\n\n备注：这是一个模拟翻译，实际使用时会调用真实的翻译服务。`;
      
      case 'summarize':
        return `内容总结：\n\n原文："${selectedText}"\n\n总结：\n\n• 主要观点1\n• 主要观点2\n• 主要观点3\n\n结论：这段内容主要讨论了...（模拟总结）`;
      
      case 'discuss':
        return `关于这段内容，我们可以从以下几个角度来讨论：\n\n"${selectedText}"\n\n1. **内容分析**：这段文本涉及的主要话题是...\n\n2. **相关思考**：这让我想到...\n\n3. **延伸问题**：我们可以进一步探讨...\n\n您对这个话题有什么看法呢？`;
      
      default:
        return `我已经收到您的请求，正在处理选中的文本：\n\n"${selectedText}"\n\n这是一个模拟响应。在实际应用中，这里会根据您的具体需求提供相应的 AI 服务。`;
    }
  };

  /**
   * 发送消息
   */
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    
    await handleAIResponse(userMessage);
  };

  /**
   * 处理键盘事件
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  /**
   * 复制消息内容
   */
  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  /**
   * 重新生成响应
   */
  const regenerateResponse = async (messageIndex: number) => {
    if (messageIndex > 0) {
      const userMessage = messages[messageIndex - 1];
      if (userMessage.type === 'user') {
        setMessages(prev => prev.slice(0, messageIndex));
        await handleAIResponse(userMessage);
      }
    }
  };

  /**
   * 切换动作类型
   */
  const switchAction = (actionId: string) => {
    setCurrentAction(actionId);
    setMessages([]);
    
    if (selectedText) {
      const action = CHAT_ACTIONS.find(a => a.id === actionId);
      if (action) {
        const newMessage: ChatMessage = {
          id: Date.now().toString(),
          type: 'user',
          content: `${action.prompt}\n\n"${selectedText}"`,
          timestamp: new Date(),
          selectedText,
          action: actionId
        };
        setMessages([newMessage]);
        handleAIResponse(newMessage);
      }
    }
  };

  if (!visible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl h-[600px] flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <MessageCircleIcon className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold">AI 对话</h3>
            {selectedText && (
              <span className="text-sm text-gray-500">
                ({selectedText.length} 字符)
              </span>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </div>

        {/* 动作选择 */}
        <div className="flex gap-2 p-4 border-b bg-gray-50">
          {CHAT_ACTIONS.map((action) => (
            <Button
              key={action.id}
              variant={currentAction === action.id ? "default" : "outline"}
              size="sm"
              onClick={() => switchAction(action.id)}
              className="flex items-center gap-1"
            >
              {action.icon}
              {action.label}
            </Button>
          ))}
        </div>

        {/* 消息列表 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message, index) => (
            <div
              key={message.id}
              className={`flex gap-3 ${
                message.type === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              {message.type === 'assistant' && (
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <BotIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}
              
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.type === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                <div className="whitespace-pre-wrap text-sm">
                  {message.content}
                </div>
                
                {message.type === 'assistant' && (
                  <div className="flex items-center gap-2 mt-2 pt-2 border-t border-gray-200">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyMessage(message.content)}
                      className="h-6 px-2 text-xs"
                    >
                      <CopyIcon className="h-3 w-3 mr-1" />
                      复制
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => regenerateResponse(index)}
                      className="h-6 px-2 text-xs"
                      disabled={isLoading}
                    >
                      <RefreshCwIcon className="h-3 w-3 mr-1" />
                      重新生成
                    </Button>
                  </div>
                )}
              </div>
              
              {message.type === 'user' && (
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <UserIcon className="h-4 w-4 text-gray-600" />
                  </div>
                </div>
              )}
            </div>
          ))}
          
          {isLoading && (
            <div className="flex gap-3 justify-start">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <BotIcon className="h-4 w-4 text-blue-600" />
                </div>
              </div>
              <div className="bg-gray-100 rounded-lg p-3">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  AI 正在思考...
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* 输入区域 */}
        <div className="border-t p-4">
          <div className="flex gap-2">
            <textarea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入您的问题或想法..."
              className="flex-1 resize-none border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={2}
              disabled={isLoading}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              className="px-4"
            >
              <SendIcon className="h-4 w-4" />
            </Button>
          </div>
          <div className="text-xs text-gray-500 mt-2">
            按 Enter 发送，Shift + Enter 换行
          </div>
        </div>
      </div>
    </div>
  );
}