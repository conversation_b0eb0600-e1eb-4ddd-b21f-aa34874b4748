import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { ZodError } from 'zod';

/**
 * API 错误类型
 */
export class APIError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

/**
 * 认证中间件
 * 验证用户是否已登录
 */
export async function withAuth<T extends any[]>(
  handler: (request: NextRequest, session: any, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const session = await getServerSession(authOptions);
      
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: '未授权访问', code: 'UNAUTHORIZED' },
          { status: 401 }
        );
      }

      return await handler(request, session, ...args);
    } catch (error) {
      return handleAPIError(error);
    }
  };
}

/**
 * 错误处理中间件
 * 统一处理 API 错误
 */
export function handleAPIError(error: unknown): NextResponse {
  console.error('API 错误:', error);

  // Zod 验证错误
  if (error instanceof ZodError) {
    return NextResponse.json(
      {
        error: '输入参数无效',
        code: 'VALIDATION_ERROR',
        details: error.issues.map(issue => ({
          field: issue.path.join('.'),
          message: issue.message,
        })),
      },
      { status: 400 }
    );
  }

  // 自定义 API 错误
  if (error instanceof APIError) {
    return NextResponse.json(
      {
        error: error.message,
        code: error.code || 'API_ERROR',
      },
      { status: error.statusCode }
    );
  }

  // Prisma 错误
  if (error && typeof error === 'object' && 'code' in error) {
    const prismaError = error as any;
    
    switch (prismaError.code) {
      case 'P2002':
        return NextResponse.json(
          {
            error: '数据已存在，请检查唯一性约束',
            code: 'DUPLICATE_ERROR',
          },
          { status: 409 }
        );
      case 'P2025':
        return NextResponse.json(
          {
            error: '记录未找到',
            code: 'NOT_FOUND',
          },
          { status: 404 }
        );
      default:
        return NextResponse.json(
          {
            error: '数据库操作失败',
            code: 'DATABASE_ERROR',
          },
          { status: 500 }
        );
    }
  }

  // 默认服务器错误
  return NextResponse.json(
    {
      error: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR',
    },
    { status: 500 }
  );
}

/**
 * 请求验证中间件
 * 验证请求体和参数
 */
export function withValidation<T>(schema: any) {
  return function <U extends any[]>(
    handler: (request: NextRequest, validatedData: T, ...args: U) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, ...args: U): Promise<NextResponse> => {
      try {
        const body = await request.json();
        const validatedData = schema.parse(body);
        return await handler(request, validatedData, ...args);
      } catch (error) {
        return handleAPIError(error);
      }
    };
  };
}

/**
 * 速率限制中间件
 * 简单的内存速率限制实现
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function withRateLimit(
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15分钟
) {
  return function <T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
      try {
        // 获取客户端IP（简化版本）
        const clientIP = request.headers.get('x-forwarded-for') || 
                        request.headers.get('x-real-ip') || 
                        'unknown';

        const now = Date.now();
        const key = `rate_limit:${clientIP}`;
        
        const current = rateLimitMap.get(key);
        
        if (!current || now > current.resetTime) {
          // 重置计数器
          rateLimitMap.set(key, {
            count: 1,
            resetTime: now + windowMs,
          });
        } else if (current.count >= maxRequests) {
          // 超过限制
          return NextResponse.json(
            {
              error: '请求过于频繁，请稍后再试',
              code: 'RATE_LIMIT_EXCEEDED',
              retryAfter: Math.ceil((current.resetTime - now) / 1000),
            },
            { status: 429 }
          );
        } else {
          // 增加计数
          current.count++;
        }

        return await handler(request, ...args);
      } catch (error) {
        return handleAPIError(error);
      }
    };
  };
}

/**
 * 日志中间件
 * 记录 API 请求日志
 */
export function withLogging<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    const startTime = Date.now();
    const method = request.method;
    const url = request.url;
    
    try {
      const response = await handler(request, ...args);
      const duration = Date.now() - startTime;
      
      console.log(`${method} ${url} - ${response.status} - ${duration}ms`);
      
      return response;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`${method} ${url} - ERROR - ${duration}ms:`, error);
      throw error;
    }
  };
}

/**
 * 组合多个中间件
 */
export function compose<T extends any[]>(
  ...middlewares: Array<(handler: any) => any>
) {
  return (handler: (request: NextRequest, ...args: T) => Promise<NextResponse>) => {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), handler);
  };
}