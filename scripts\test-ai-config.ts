/**
 * AI 配置管理测试脚本
 * 测试 AI 配置的 CRUD 操作和验证功能
 */

import { prisma } from '../src/lib/db/prisma';
import { validateAIConfig } from '../src/lib/services/ai';
import type { AIServiceConfig } from '../src/types/ai.types';

async function testAIConfigManagement() {
  console.log('🧪 测试 AI 配置管理功能...\n');

  try {
    // 创建测试用户（如果不存在）
    let testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User'
        }
      });
      console.log('✅ 创建测试用户成功');
    } else {
      console.log('✅ 使用现有测试用户');
    }

    // 测试配置验证
    console.log('\n📋 测试配置验证:');
    
    const validConfig: AIServiceConfig = {
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      apiKey: 'test-key',
      maxTokens: 2000,
      temperature: 0.7
    };

    const validation = validateAIConfig(validConfig);
    console.log(`  有效配置验证: ${validation.valid ? '✅' : '❌'}`);
    if (!validation.valid) {
      console.log(`    错误: ${validation.errors.join(', ')}`);
    }

    // 测试创建配置
    console.log('\n➕ 测试创建 AI 配置:');
    
    const newConfig = await prisma.aIConfiguration.create({
      data: {
        userId: testUser.id,
        provider: 'openai',
        apiKey: 'test-openai-key',
        model: 'gpt-3.5-turbo',
        maxTokens: 2000,
        temperature: 0.7,
        isDefault: true
      }
    });
    console.log(`  ✅ 创建 OpenAI 配置成功: ${newConfig.id}`);

    const ollamaConfig = await prisma.aIConfiguration.create({
      data: {
        userId: testUser.id,
        provider: 'ollama',
        endpoint: 'http://localhost:11434',
        model: 'llama2',
        maxTokens: 2000,
        temperature: 0.7,
        isDefault: false
      }
    });
    console.log(`  ✅ 创建 Ollama 配置成功: ${ollamaConfig.id}`);

    // 测试查询配置
    console.log('\n📖 测试查询 AI 配置:');
    
    const userConfigs = await prisma.aIConfiguration.findMany({
      where: { userId: testUser.id },
      orderBy: [
        { isDefault: 'desc' },
        { createdAt: 'desc' }
      ]
    });
    console.log(`  ✅ 查询到 ${userConfigs.length} 个配置`);
    
    userConfigs.forEach(config => {
      console.log(`    - ${config.provider}/${config.model} ${config.isDefault ? '(默认)' : ''}`);
    });

    // 测试更新配置
    console.log('\n✏️ 测试更新 AI 配置:');
    
    const updatedConfig = await prisma.aIConfiguration.update({
      where: { id: newConfig.id },
      data: {
        maxTokens: 3000,
        temperature: 0.8
      }
    });
    console.log(`  ✅ 更新配置成功: maxTokens=${updatedConfig.maxTokens}, temperature=${updatedConfig.temperature}`);

    // 测试设置默认配置
    console.log('\n⭐ 测试设置默认配置:');
    
    await prisma.$transaction(async (tx) => {
      // 取消所有默认配置
      await tx.aIConfiguration.updateMany({
        where: {
          userId: testUser.id,
          isDefault: true
        },
        data: {
          isDefault: false
        }
      });

      // 设置新的默认配置
      await tx.aIConfiguration.update({
        where: { id: ollamaConfig.id },
        data: { isDefault: true }
      });
    });
    console.log('  ✅ 设置 Ollama 配置为默认成功');

    // 验证默认配置
    const defaultConfig = await prisma.aIConfiguration.findFirst({
      where: {
        userId: testUser.id,
        isDefault: true
      }
    });
    console.log(`  ✅ 当前默认配置: ${defaultConfig?.provider}/${defaultConfig?.model}`);

    // 测试删除配置
    console.log('\n🗑️ 测试删除 AI 配置:');
    
    await prisma.aIConfiguration.delete({
      where: { id: newConfig.id }
    });
    console.log('  ✅ 删除 OpenAI 配置成功');

    // 验证删除后的配置数量
    const remainingConfigs = await prisma.aIConfiguration.count({
      where: { userId: testUser.id }
    });
    console.log(`  ✅ 剩余配置数量: ${remainingConfigs}`);

    // 清理测试数据
    console.log('\n🧹 清理测试数据:');
    
    await prisma.aIConfiguration.deleteMany({
      where: { userId: testUser.id }
    });
    console.log('  ✅ 清理 AI 配置成功');

    await prisma.user.delete({
      where: { id: testUser.id }
    });
    console.log('  ✅ 清理测试用户成功');

    console.log('\n🎉 AI 配置管理功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testAIConfigManagement().catch(error => {
  console.error('测试执行失败:', error);
  process.exit(1);
});