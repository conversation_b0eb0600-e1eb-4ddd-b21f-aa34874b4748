/**
 * AI 服务缓存管理器
 * 提供智能缓存、批处理和性能优化功能
 */

import { AIRequest, AIResponse, AIProvider } from '@/types/ai.types';

interface CacheEntry {
  key: string;
  request: AIRequest;
  response: AIResponse;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  provider: AIProvider;
  expiresAt: number;
}

interface BatchRequest {
  id: string;
  request: AIRequest;
  resolve: (response: AIResponse) => void;
  reject: (error: Error) => void;
  timestamp: number;
  priority: 'high' | 'medium' | 'low';
}

interface CacheConfig {
  /** 最大缓存条目数 */
  maxEntries: number;
  /** 默认过期时间（毫秒） */
  defaultTTL: number;
  /** 清理间隔（毫秒） */
  cleanupInterval: number;
  /** 是否启用缓存 */
  enabled: boolean;
  /** 缓存命中率阈值 */
  hitRateThreshold: number;
}

interface BatchConfig {
  /** 批处理大小 */
  batchSize: number;
  /** 批处理等待时间（毫秒） */
  batchTimeout: number;
  /** 是否启用批处理 */
  enabled: boolean;
  /** 最大并发批次数 */
  maxConcurrentBatches: number;
}

/**
 * AI 缓存管理器
 */
export class AICacheManager {
  private cache = new Map<string, CacheEntry>();
  private batchQueue: BatchRequest[] = [];
  private activeBatches = new Set<string>();
  private cleanupTimer?: NodeJS.Timeout;
  private batchTimer?: NodeJS.Timeout;
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    batchesProcessed: 0,
    totalRequests: 0,
  };

  constructor(
    private cacheConfig: CacheConfig = {
      maxEntries: 1000,
      defaultTTL: 30 * 60 * 1000, // 30分钟
      cleanupInterval: 5 * 60 * 1000, // 5分钟
      enabled: true,
      hitRateThreshold: 0.7,
    },
    private batchConfig: BatchConfig = {
      batchSize: 5,
      batchTimeout: 100,
      enabled: true,
      maxConcurrentBatches: 3,
    }
  ) {
    this.startCleanupTimer();
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(request: AIRequest, provider: AIProvider): string {
    const keyData = {
      prompt: request.prompt,
      model: request.model,
      maxTokens: request.maxTokens,
      temperature: request.temperature,
      provider,
    };
    
    // 简单的哈希函数
    const str = JSON.stringify(keyData);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return `ai_cache_${Math.abs(hash).toString(36)}`;
  }

  /**
   * 从缓存获取响应
   */
  async get(request: AIRequest, provider: AIProvider): Promise<AIResponse | null> {
    if (!this.cacheConfig.enabled) {
      return null;
    }

    const key = this.generateCacheKey(request, provider);
    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // 检查是否过期
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    // 更新访问统计
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    this.stats.hits++;
    return { ...entry.response };
  }

  /**
   * 设置缓存
   */
  async set(
    request: AIRequest, 
    response: AIResponse, 
    provider: AIProvider,
    ttl?: number
  ): Promise<void> {
    if (!this.cacheConfig.enabled) {
      return;
    }

    const key = this.generateCacheKey(request, provider);
    const now = Date.now();
    const expiresAt = now + (ttl || this.cacheConfig.defaultTTL);

    const entry: CacheEntry = {
      key,
      request: { ...request },
      response: { ...response },
      timestamp: now,
      accessCount: 1,
      lastAccessed: now,
      provider,
      expiresAt,
    };

    // 检查缓存大小限制
    if (this.cache.size >= this.cacheConfig.maxEntries) {
      this.evictLeastUsed();
    }

    this.cache.set(key, entry);
  }

  /**
   * 批处理请求
   */
  async batchRequest(
    request: AIRequest,
    provider: AIProvider,
    executor: (requests: AIRequest[]) => Promise<AIResponse[]>,
    priority: 'high' | 'medium' | 'low' = 'medium'
  ): Promise<AIResponse> {
    this.stats.totalRequests++;

    // 如果不启用批处理，直接执行
    if (!this.batchConfig.enabled) {
      const responses = await executor([request]);
      return responses[0];
    }

    // 检查缓存
    const cachedResponse = await this.get(request, provider);
    if (cachedResponse) {
      return cachedResponse;
    }

    // 添加到批处理队列
    return new Promise<AIResponse>((resolve, reject) => {
      const batchRequest: BatchRequest = {
        id: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        request,
        resolve,
        reject,
        timestamp: Date.now(),
        priority,
      };

      this.batchQueue.push(batchRequest);
      this.scheduleBatchProcessing(provider, executor);
    });
  }

  /**
   * 调度批处理
   */
  private scheduleBatchProcessing(
    provider: AIProvider,
    executor: (requests: AIRequest[]) => Promise<AIResponse[]>
  ) {
    if (this.batchTimer) {
      return;
    }

    this.batchTimer = setTimeout(async () => {
      await this.processBatch(provider, executor);
      this.batchTimer = undefined;
    }, this.batchConfig.batchTimeout);

    // 如果队列已满，立即处理
    if (this.batchQueue.length >= this.batchConfig.batchSize) {
      clearTimeout(this.batchTimer);
      this.batchTimer = undefined;
      this.processBatch(provider, executor);
    }
  }

  /**
   * 处理批次
   */
  private async processBatch(
    provider: AIProvider,
    executor: (requests: AIRequest[]) => Promise<AIResponse[]>
  ) {
    if (this.batchQueue.length === 0) {
      return;
    }

    // 检查并发批次限制
    if (this.activeBatches.size >= this.batchConfig.maxConcurrentBatches) {
      // 延迟处理
      setTimeout(() => this.processBatch(provider, executor), 50);
      return;
    }

    // 按优先级排序
    this.batchQueue.sort((a, b) => {
      const priorityOrder = { high: 0, medium: 1, low: 2 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });

    // 提取批次
    const batchSize = Math.min(this.batchQueue.length, this.batchConfig.batchSize);
    const batch = this.batchQueue.splice(0, batchSize);
    
    if (batch.length === 0) {
      return;
    }

    const batchId = `batch_${Date.now()}`;
    this.activeBatches.add(batchId);

    try {
      // 执行批处理
      const requests = batch.map(item => item.request);
      const responses = await executor(requests);

      // 处理响应
      for (let i = 0; i < batch.length; i++) {
        const batchRequest = batch[i];
        const response = responses[i];

        if (response) {
          // 缓存响应
          await this.set(batchRequest.request, response, provider);
          batchRequest.resolve(response);
        } else {
          batchRequest.reject(new Error('批处理响应为空'));
        }
      }

      this.stats.batchesProcessed++;
    } catch (error) {
      // 处理错误
      batch.forEach(batchRequest => {
        batchRequest.reject(error instanceof Error ? error : new Error('批处理失败'));
      });
    } finally {
      this.activeBatches.delete(batchId);
      
      // 如果还有待处理的请求，继续处理
      if (this.batchQueue.length > 0) {
        setTimeout(() => this.processBatch(provider, executor), 10);
      }
    }
  }

  /**
   * 驱逐最少使用的条目
   */
  private evictLeastUsed() {
    let leastUsedKey: string | null = null;
    let leastUsedEntry: CacheEntry | null = null;

    for (const [key, entry] of this.cache) {
      if (!leastUsedEntry || 
          entry.accessCount < leastUsedEntry.accessCount ||
          (entry.accessCount === leastUsedEntry.accessCount && 
           entry.lastAccessed < leastUsedEntry.lastAccessed)) {
        leastUsedKey = key;
        leastUsedEntry = entry;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
      this.stats.evictions++;
    }
  }

  /**
   * 清理过期条目
   */
  private cleanup() {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache) {
      if (now > entry.expiresAt) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      this.cache.delete(key);
      this.stats.evictions++;
    });
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.cacheConfig.cleanupInterval);
  }

  /**
   * 获取缓存统计
   */
  getStats() {
    const hitRate = this.stats.totalRequests > 0 
      ? this.stats.hits / (this.stats.hits + this.stats.misses)
      : 0;

    return {
      ...this.stats,
      hitRate,
      cacheSize: this.cache.size,
      queueSize: this.batchQueue.length,
      activeBatches: this.activeBatches.size,
      isHealthy: hitRate >= this.cacheConfig.hitRateThreshold,
    };
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear();
    this.batchQueue.length = 0;
    this.activeBatches.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      batchesProcessed: 0,
      totalRequests: 0,
    };
  }

  /**
   * 预热缓存
   */
  async warmup(
    requests: Array<{ request: AIRequest; provider: AIProvider }>,
    executor: (provider: AIProvider, requests: AIRequest[]) => Promise<AIResponse[]>
  ) {
    const groupedRequests = new Map<AIProvider, AIRequest[]>();

    // 按提供商分组
    requests.forEach(({ request, provider }) => {
      if (!groupedRequests.has(provider)) {
        groupedRequests.set(provider, []);
      }
      groupedRequests.get(provider)!.push(request);
    });

    // 并行预热
    const warmupPromises = Array.from(groupedRequests.entries()).map(
      async ([provider, providerRequests]) => {
        try {
          const responses = await executor(provider, providerRequests);
          
          // 缓存响应
          for (let i = 0; i < providerRequests.length; i++) {
            if (responses[i]) {
              await this.set(providerRequests[i], responses[i], provider);
            }
          }
        } catch (error) {
          console.warn(`预热缓存失败 (${provider}):`, error);
        }
      }
    );

    await Promise.allSettled(warmupPromises);
  }

  /**
   * 销毁管理器
   */
  destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }
    this.clear();
  }
}

/**
 * 全局缓存管理器实例
 */
export const aiCacheManager = new AICacheManager();