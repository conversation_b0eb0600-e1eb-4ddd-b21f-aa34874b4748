import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create a demo user
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Demo User',
      subscription: 'free',
    },
  });

  console.log('👤 Created demo user:', demoUser.email);

  // Create some demo folders
  const workFolder = await prisma.folder.create({
    data: {
      name: 'Work Projects',
      userId: demoUser.id,
    },
  });

  const personalFolder = await prisma.folder.create({
    data: {
      name: 'Personal Notes',
      userId: demoUser.id,
    },
  });

  console.log('📁 Created demo folders');

  // Create some demo documents
  const welcomeDoc = await prisma.document.create({
    data: {
      title: 'Welcome to AI Document Editor',
      content: JSON.stringify({
        type: 'doc',
        content: [
          {
            type: 'heading',
            attrs: { level: 1 },
            content: [{ type: 'text', text: 'Welcome to AI Document Editor' }],
          },
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'This is your first document! You can start writing here and use AI features to enhance your content.',
              },
            ],
          },
          {
            type: 'heading',
            attrs: { level: 2 },
            content: [{ type: 'text', text: 'Features' }],
          },
          {
            type: 'bulletList',
            content: [
              {
                type: 'listItem',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      { type: 'text', text: 'AI-powered writing assistance' },
                    ],
                  },
                ],
              },
              {
                type: 'listItem',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      { type: 'text', text: 'Smart document organization' },
                    ],
                  },
                ],
              },
              {
                type: 'listItem',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      { type: 'text', text: 'Real-time collaboration' },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      }),
      userId: demoUser.id,
      wordCount: 25,
      charCount: 150,
    },
  });

  const projectDoc = await prisma.document.create({
    data: {
      title: 'Project Planning',
      content: JSON.stringify({
        type: 'doc',
        content: [
          {
            type: 'heading',
            attrs: { level: 1 },
            content: [{ type: 'text', text: 'Project Planning Document' }],
          },
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Use this document to plan your projects and track progress.',
              },
            ],
          },
        ],
      }),
      userId: demoUser.id,
      folderId: workFolder.id,
      wordCount: 12,
      charCount: 80,
    },
  });

  console.log('📄 Created demo documents');

  // Create a demo AI configuration
  await prisma.aIConfiguration.create({
    data: {
      userId: demoUser.id,
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      maxTokens: 2000,
      temperature: 0.7,
      isDefault: true,
    },
  });

  console.log('🤖 Created demo AI configuration');

  console.log('✅ Database seeded successfully!');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });