import { useState, useEffect, useCallback } from 'react';
import { FolderWithRelations } from '@/lib/services/folder-service';

interface UseFoldersReturn {
  folders: FolderWithRelations[];
  loading: boolean;
  error: string | null;
  createFolder: (name: string, parentId?: string) => Promise<FolderWithRelations | null>;
  updateFolder: (id: string, data: { name?: string; parentId?: string | null }) => Promise<FolderWithRelations | null>;
  deleteFolder: (id: string) => Promise<boolean>;
  refreshFolders: () => Promise<void>;
}

export function useFolders(): UseFoldersReturn {
  const [folders, setFolders] = useState<FolderWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFolders = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // 获取包含完整层级结构和文档的树形数据
      const response = await fetch('/api/folders/tree?includeDocuments=true');

      if (!response.ok) {
        throw new Error('Failed to fetch folders');
      }

      const data = await response.json();
      // tree API 返回的是 { tree: { folders: [...], documents: [...] } } 格式
      setFolders(data.tree?.folders || []);

      // 如果有根目录文档，也存储起来
      if (data.tree?.documents) {
        // 可以通过事件通知组件更新根目录文档
        window.dispatchEvent(new CustomEvent('root-documents-updated', {
          detail: { documents: data.tree.documents }
        }));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching folders:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const createFolder = useCallback(async (
    name: string,
    parentId?: string
  ): Promise<FolderWithRelations | null> => {
    try {
      setError(null);

      const response = await fetch('/api/folders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, parentId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create folder');
      }

      const data = await response.json();
      const newFolder = data.folder;

      // 重新获取完整的文件夹树结构
      await fetchFolders();

      return newFolder;
    } catch (err) {
      console.error('Error creating folder:', err);
      // 不设置全局 error 状态，让调用者处理错误
      throw err; // 重新抛出错误让调用者处理
    }
  }, [fetchFolders]);

  const updateFolder = useCallback(async (
    id: string,
    data: { name?: string; parentId?: string | null }
  ): Promise<FolderWithRelations | null> => {
    try {
      setError(null);

      const response = await fetch(`/api/folders/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update folder');
      }

      const responseData = await response.json();
      const updatedFolder = responseData.folder;

      // 重新获取完整的文件夹树结构，特别是在移动操作后
      await fetchFolders();

      return updatedFolder;
    } catch (err) {
      console.error('Error updating folder:', err);
      // 不设置全局 error 状态，让调用者处理错误
      throw err; // 重新抛出错误让调用者处理
    }
  }, [fetchFolders]);

  const deleteFolder = useCallback(async (id: string, force: boolean = false): Promise<boolean> => {
    try {
      setError(null);

      const url = `/api/folders/${id}${force ? '?force=true' : ''}`;
      const response = await fetch(url, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        // 创建包含响应数据的错误对象
        const error = new Error(errorData.error || 'Failed to delete folder') as any;
        error.response = { status: response.status, data: errorData };
        throw error;
      }

      // 重新获取完整的文件夹树结构
      await fetchFolders();

      return true;
    } catch (err: any) {
      const errorMessage = err.message || 'An error occurred';
      setError(errorMessage);
      console.error('Error deleting folder:', err);
      // 重新抛出错误以便上层处理
      throw err;
    }
  }, [fetchFolders]);

  const refreshFolders = useCallback(async () => {
    await fetchFolders();
  }, [fetchFolders]);

  useEffect(() => {
    fetchFolders();
  }, [fetchFolders]);

  return {
    folders,
    loading,
    error,
    createFolder,
    updateFolder,
    deleteFolder,
    refreshFolders,
  };
}

// Hook for getting a specific folder
export function useFolder(id: string | null) {
  const [folder, setFolder] = useState<FolderWithRelations | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchFolder = useCallback(async () => {
    if (!id) {
      setFolder(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/folders/${id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch folder');
      }

      const data = await response.json();
      setFolder(data.folder);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching folder:', err);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchFolder();
  }, [fetchFolder]);

  return {
    folder,
    loading,
    error,
    refetch: fetchFolder,
  };
}
