'use client';

import React, { useState } from 'react';
import { FolderTreeFixed } from '@/components/hierarchy/FolderTreeFixed';
import { useServerDocument } from '@/hooks/useServerDocuments';
import { Button } from '@/components/ui/Button';
import { FileText, Save, Share2, Settings } from 'lucide-react';

interface EnhancedDocumentManagerProps {
  userId?: string;
}

/**
 * 增强版文档管理器
 * 集成了文件夹管理和文件树功能
 */
export function EnhancedDocumentManager({ userId }: EnhancedDocumentManagerProps) {
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);

  const { document, loading: documentLoading, updateDocument } = useServerDocument(selectedDocumentId);

  const handleSelectFolder = (folderId: string | null) => {
    setSelectedFolderId(folderId);
    setSelectedDocumentId(null); // 清除文档选择
  };

  const handleSelectDocument = (documentId: string) => {
    setSelectedDocumentId(documentId);
    setSelectedFolderId(null); // 清除文件夹选择
  };

  const handleSaveDocument = async () => {
    if (document && selectedDocumentId) {
      await updateDocument({
        title: document.title,
        content: document.content
      });
    }
  };

  return (
    <div className="h-screen flex bg-gray-50">
      {/* 左侧文件树面板 */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div className="flex-1">
          <FolderTreeFixed
            onSelectFolder={handleSelectFolder}
            onSelectDocument={handleSelectDocument}
            selectedFolderId={selectedFolderId}
            selectedDocumentId={selectedDocumentId}
          />
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部工具栏 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {selectedDocumentId && document ? (
                <div className="flex items-center space-x-3">
                  <FileText className="w-5 h-5 text-gray-500" />
                  <div>
                    <h1 className="text-lg font-semibold text-gray-900">
                      {document.title}
                    </h1>
                    <p className="text-sm text-gray-500">
                      {document.wordCount} 词 • {document.charCount} 字符
                    </p>
                  </div>
                </div>
              ) : selectedFolderId ? (
                <div className="flex items-center space-x-3">
                  <div className="w-5 h-5 text-gray-500">📁</div>
                  <div>
                    <h1 className="text-lg font-semibold text-gray-900">文件夹视图</h1>
                    <p className="text-sm text-gray-500">文件夹 ID: {selectedFolderId}</p>
                  </div>
                </div>
              ) : (
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">文档编辑器</h1>
                  <p className="text-sm text-gray-500">从左侧选择文档或文件夹</p>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-3">
              {selectedDocumentId && (
                <>
                  <Button
                    onClick={handleSaveDocument}
                    variant="outline"
                    size="sm"
                    className="flex items-center space-x-2"
                  >
                    <Save className="w-4 h-4" />
                    <span>保存</span>
                  </Button>
                  <Button
                    size="sm"
                    className="flex items-center space-x-2"
                  >
                    <Share2 className="w-4 h-4" />
                    <span>分享</span>
                  </Button>
                </>
              )}
              <Button
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <Settings className="w-4 h-4" />
                <span>设置</span>
              </Button>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden">
          {documentLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-gray-500">加载文档中...</div>
            </div>
          ) : selectedDocumentId && document ? (
            <DocumentContentArea document={document} onUpdate={updateDocument} />
          ) : selectedFolderId ? (
            <FolderContentArea folderId={selectedFolderId} />
          ) : (
            <WelcomeArea />
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * 文档内容编辑区域
 */
interface DocumentContentAreaProps {
  document: any;
  onUpdate: (data: any) => Promise<any>;
}

function DocumentContentArea({ document, onUpdate }: DocumentContentAreaProps) {
  const [title, setTitle] = useState(document.title);
  const [content, setContent] = useState(document.content);

  const handleTitleChange = async (newTitle: string) => {
    setTitle(newTitle);
    await onUpdate({ title: newTitle });
  };

  const handleContentChange = async (newContent: string) => {
    setContent(newContent);
    await onUpdate({ content: newContent });
  };

  return (
    <div className="h-full flex flex-col p-6">
      {/* 文档标题编辑 */}
      <div className="mb-6">
        <input
          type="text"
          value={title}
          onChange={(e) => handleTitleChange(e.target.value)}
          className="w-full text-2xl font-bold border-none outline-none bg-transparent placeholder-gray-400"
          placeholder="文档标题"
        />
      </div>

      {/* 文档内容编辑 */}
      <div className="flex-1">
        <textarea
          value={content}
          onChange={(e) => handleContentChange(e.target.value)}
          className="w-full h-full p-4 border border-gray-200 rounded-lg resize-none outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="开始写作..."
        />
      </div>

      {/* 文档统计信息 */}
      <div className="mt-4 flex justify-between text-sm text-gray-500">
        <div>
          创建时间: {new Date(document.createdAt).toLocaleString()}
        </div>
        <div>
          最后更新: {new Date(document.updatedAt).toLocaleString()}
        </div>
      </div>
    </div>
  );
}

/**
 * 文件夹内容区域
 */
interface FolderContentAreaProps {
  folderId: string;
}

function FolderContentArea({ folderId }: FolderContentAreaProps) {
  return (
    <div className="h-full flex items-center justify-center">
      <div className="text-center">
        <div className="text-6xl mb-4">📁</div>
        <div className="text-xl font-semibold text-gray-700 mb-2">文件夹视图</div>
        <p className="text-gray-500 mb-4">
          这里可以显示文件夹的详细信息和管理选项
        </p>
        <div className="text-sm text-gray-400">
          文件夹 ID: {folderId}
        </div>
      </div>
    </div>
  );
}

/**
 * 欢迎页面
 */
function WelcomeArea() {
  return (
    <div className="h-full flex items-center justify-center">
      <div className="text-center max-w-md">
        <div className="text-6xl mb-6">📝</div>
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">
          欢迎使用文档编辑器
        </h2>
        <p className="text-gray-600 mb-6">
          从左侧文件树中选择一个文档开始编辑，或者创建新的文档和文件夹来组织您的内容。
        </p>
        <div className="space-y-2 text-sm text-gray-500">
          <p>💡 提示：</p>
          <ul className="text-left space-y-1">
            <li>• 点击文件夹可以展开/折叠</li>
            <li>• 右键点击可以访问快捷菜单</li>
            <li>• 拖拽文件和文件夹来重新组织</li>
            <li>• 使用 + 按钮创建新内容</li>
          </ul>
        </div>
      </div>
    </div>
  );
}