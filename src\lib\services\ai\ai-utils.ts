/**
 * AI 服务工具函数
 * 提供便捷的 AI 服务操作函数
 */

import { IAIService } from './base-ai-service';
import { AIServiceFactory, aiServiceManager } from './ai-service-factory';
import { aiEnvConfig } from '@/lib/config/env';
import {
  AIServiceConfig,
  AIProvider,
  AIServiceError,
  AIErrorType,
} from '@/types/ai.types';

/**
 * 创建 AI 服务实例
 * @param config AI 服务配置
 * @returns AI 服务实例
 */
export function createAIService(config: AIServiceConfig): IAIService {
  return AIServiceFactory.createService(config);
}

/**
 * 获取默认 AI 服务
 * @returns 默认 AI 服务实例
 */
export function getDefaultAIService(): IAIService {
  return aiServiceManager.getDefaultService();
}

/**
 * 验证 AI 配置
 * @param config AI 服务配置
 * @returns 验证结果
 */
export function validateAIConfig(config: AIServiceConfig): {
  valid: boolean;
  errors: string[];
} {
  return AIServiceFactory.validateConfig(config);
}

/**
 * 测试 AI 服务连接
 * @param config AI 服务配置
 * @returns 连接测试结果
 */
export async function testAIConnection(config: AIServiceConfig): Promise<{
  success: boolean;
  error?: string;
  responseTime?: number;
}> {
  const startTime = Date.now();

  try {
    // 直接创建服务实例进行测试，避免缓存干扰
    let service;

    switch (config.provider) {
      case 'openai':
        // 直接测试 OpenAI API
        const testResult = await testOpenAIDirectly(config);
        return {
          success: testResult.success,
          responseTime: Date.now() - startTime,
          error: testResult.error,
        };
      case 'gemini':
        // 直接测试 Gemini API
        const geminiResult = await testGeminiDirectly(config);
        return {
          success: geminiResult.success,
          responseTime: Date.now() - startTime,
          error: geminiResult.error,
        };
      default:
        // 对于其他服务，使用原有逻辑
        const service = AIServiceFactory.createService(config);
        const connected = await service.testConnection();

        return {
          success: connected,
          responseTime: Date.now() - startTime,
          error: connected ? undefined : '连接测试失败',
        };
    }
  } catch (error) {
    return {
      success: false,
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : '未知错误',
    };
  }
}

/**
 * 直接测试 OpenAI 连接
 */
async function testOpenAIDirectly(config: AIServiceConfig): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    console.log('测试 OpenAI 连接');
    console.log('代理配置:', config.endpoint);

    // 使用 OpenAI 服务进行测试
    const { OpenAIService } = await import('./openai-service');
    const openaiService = new OpenAIService(config);
    const connected = await openaiService.testConnection();

    if (connected) {
      console.log('OpenAI 连接测试成功');
      return {
        success: true
      };
    } else {
      console.error('OpenAI 连接测试失败');
      return {
        success: false,
        error: '连接测试失败，请检查配置和网络连接'
      };
    }
  } catch (error) {
    console.error('OpenAI 连接测试异常:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '连接失败'
    };
  }
}

/**
 * 直接测试 Gemini 连接
 */
async function testGeminiDirectly(config: AIServiceConfig): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    console.log('测试 Gemini 连接');

    // 使用 Gemini 服务进行测试
    const { GeminiService } = await import('./gemini-service');
    const geminiService = new GeminiService(config);
    const connected = await geminiService.testConnection();

    if (connected) {
      console.log('Gemini 连接测试成功');
      return {
        success: true
      };
    } else {
      console.error('Gemini 连接测试失败');
      return {
        success: false,
        error: '连接测试失败，请检查配置和网络连接'
      };
    }
  } catch (error) {
    console.error('Gemini 连接测试异常:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '连接失败'
    };
  }
}

/**
 * 获取提供商的推荐模型
 * @param provider AI 服务提供商
 * @returns 推荐模型列表
 */
export function getRecommendedModels(provider: AIProvider): string[] {
  switch (provider) {
    case 'openai':
      return [
        'gpt-4o',           // 最新的 GPT-4 Omni 模型
        'gpt-4o-mini',      // 轻量版 GPT-4 Omni
        'gpt-4-turbo',      // GPT-4 Turbo
        'gpt-4',            // 标准 GPT-4
        'gpt-3.5-turbo',    // GPT-3.5 Turbo
        'gpt-3.5-turbo-16k' // GPT-3.5 Turbo 长上下文版本
      ];
    case 'ollama':
      return [
        'llama3.2',         // 最新的 Llama 3.2
        'llama3.1',         // Llama 3.1
        'llama3.1:70b',     // Llama 3.1 70B
        'llama3',           // Llama 3
        'llama2',           // Llama 2
        'llama2:13b',       // Llama 2 13B
        'llama2:70b',       // Llama 2 70B
        'codellama',        // Code Llama
        'codellama:13b',    // Code Llama 13B
        'mistral',          // Mistral
        'mistral:7b',       // Mistral 7B
        'mixtral',          // Mixtral
        'neural-chat',      // Neural Chat
        'starling-lm',      // Starling LM
        'qwen2',            // Qwen 2
        'qwen2:7b',         // Qwen 2 7B
        'deepseek-coder',   // DeepSeek Coder
        'phi3',             // Phi-3
        'gemma',            // Gemma
        'gemma:7b'          // Gemma 7B
      ];
    case 'gemini':
      return [
        'gemini-1.5-pro',     // 最新的 Gemini 1.5 Pro
        'gemini-1.5-flash',   // Gemini 1.5 Flash (更快)
        'gemini-pro',         // Gemini Pro
        'gemini-pro-vision'   // Gemini Pro Vision (支持图像)
      ];
    default:
      return [];
  }
}

/**
 * 估算文本的令牌数量
 * @param text 文本内容
 * @returns 估算的令牌数
 */
export function estimateTokens(text: string): number {
  // 分离中文和英文
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishText = text.replace(/[\u4e00-\u9fff]/g, '');
  const englishWords = englishText
    .split(/\s+/)
    .filter((word) => word.length > 0).length;

  // 中文字符按 1:1 计算，英文单词按 1:1.3 计算
  return Math.ceil(chineseChars + englishWords * 1.3);
}

/**
 * 计算令牌成本（美元）
 * @param provider AI 服务提供商
 * @param model 模型名称
 * @param inputTokens 输入令牌数
 * @param outputTokens 输出令牌数
 * @returns 估算成本
 */
export function calculateTokenCost(
  provider: AIProvider,
  model: string,
  inputTokens: number,
  outputTokens: number
): number {
  // 价格表（每1000个令牌的价格，美元）- 2025年1月价格
  const pricing: Record<string, { input: number; output: number }> = {
    // OpenAI 最新价格
    'gpt-4o': { input: 0.005, output: 0.015 },           // GPT-4 Omni
    'gpt-4o-mini': { input: 0.00015, output: 0.0006 },   // GPT-4 Omni Mini
    'gpt-4-turbo': { input: 0.01, output: 0.03 },        // GPT-4 Turbo
    'gpt-4': { input: 0.03, output: 0.06 },              // GPT-4
    'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },   // GPT-3.5 Turbo
    'gpt-3.5-turbo-16k': { input: 0.003, output: 0.004 }, // GPT-3.5 Turbo 16K

    // Gemini 价格
    'gemini-1.5-pro': { input: 0.00125, output: 0.005 },   // Gemini 1.5 Pro
    'gemini-1.5-flash': { input: 0.000075, output: 0.0003 }, // Gemini 1.5 Flash
    'gemini-pro': { input: 0.00025, output: 0.0005 },      // Gemini Pro
    'gemini-pro-vision': { input: 0.00025, output: 0.0005 }, // Gemini Pro Vision

    // Ollama 本地运行，成本为0
    'llama3.2': { input: 0, output: 0 },
    'llama3.1': { input: 0, output: 0 },
    'llama3.1:70b': { input: 0, output: 0 },
    'llama3': { input: 0, output: 0 },
    'llama2': { input: 0, output: 0 },
    'llama2:13b': { input: 0, output: 0 },
    'llama2:70b': { input: 0, output: 0 },
    'codellama': { input: 0, output: 0 },
    'codellama:13b': { input: 0, output: 0 },
    'mistral': { input: 0, output: 0 },
    'mistral:7b': { input: 0, output: 0 },
    'mixtral': { input: 0, output: 0 },
    'neural-chat': { input: 0, output: 0 },
    'starling-lm': { input: 0, output: 0 },
    'qwen2': { input: 0, output: 0 },
    'qwen2:7b': { input: 0, output: 0 },
    'deepseek-coder': { input: 0, output: 0 },
    'phi3': { input: 0, output: 0 },
    'gemma': { input: 0, output: 0 },
    'gemma:7b': { input: 0, output: 0 },
  };

  const modelPricing = pricing[model];
  if (!modelPricing) {
    // 对于未知模型，根据提供商返回默认价格
    if (provider === 'openai') {
      return ((inputTokens / 1000) * 0.002) + ((outputTokens / 1000) * 0.002); // 默认价格
    } else if (provider === 'gemini') {
      return ((inputTokens / 1000) * 0.0005) + ((outputTokens / 1000) * 0.001); // 默认价格
    }
    return 0; // Ollama 或其他本地模型
  }

  const inputCost = (inputTokens / 1000) * modelPricing.input;
  const outputCost = (outputTokens / 1000) * modelPricing.output;

  return inputCost + outputCost;
}

/**
 * 格式化 AI 错误消息
 * @param error 错误对象
 * @returns 用户友好的错误消息
 */
export function formatAIError(error: unknown): string {
  if (error instanceof AIServiceError) {
    switch (error.type) {
      case AIErrorType.NETWORK_ERROR:
        return '网络连接失败，请检查网络设置';
      case AIErrorType.INVALID_API_KEY:
        return 'API 密钥无效，请检查配置';
      case AIErrorType.QUOTA_EXCEEDED:
        return '请求配额已用完，请稍后再试';
      case AIErrorType.TIMEOUT:
        return '请求超时，请稍后再试';
      case AIErrorType.SERVICE_UNAVAILABLE:
        return 'AI 服务暂时不可用，请稍后再试';
      case AIErrorType.CONTENT_FILTERED:
        return '内容被安全过滤器拦截，请修改后重试';
      case AIErrorType.INVALID_REQUEST:
        return `请求参数无效：${error.message}`;
      default:
        return `AI 服务错误：${error.message}`;
    }
  }

  if (error instanceof Error) {
    return error.message;
  }

  return '未知错误';
}

/**
 * 检查提供商是否需要 API 密钥
 * @param provider AI 服务提供商
 * @returns 是否需要 API 密钥
 */
export function requiresApiKey(provider: AIProvider): boolean {
  return provider === 'openai' || provider === 'gemini';
}

/**
 * 检查提供商是否显示端点配置字段
 * @param provider AI 服务提供商
 * @returns 是否显示端点配置字段
 */
export function requiresEndpoint(provider: AIProvider): boolean {
  // OpenAI 显示端点字段用于代理配置，但不是必需的
  // Ollama 必须配置端点
  return provider === 'ollama' || provider === 'openai';
}

/**
 * 获取提供商的默认端点
 * @param provider AI 服务提供商
 * @returns 默认端点 URL
 */
export function getDefaultEndpoint(provider: AIProvider): string | undefined {
  switch (provider) {
    case 'openai':
      // 对于 OpenAI，返回代理地址（如果配置了的话）
      return aiEnvConfig.getProxyUrl();
    case 'ollama':
      return aiEnvConfig.ollamaEndpoint;
    case 'gemini':
      return aiEnvConfig.geminiEndpoint;
    default:
      return undefined;
  }
}

/**
 * 生成系统提示模板
 * @param type 提示类型
 * @returns 系统提示文本
 */
export function getSystemPromptTemplate(type: string): string {
  const templates: Record<string, string> = {
    writing:
      '你是一个专业的写作助手，帮助用户改进文档内容。请提供准确、有用的建议。',
    translation: '你是一个专业的翻译助手，请提供准确、自然的翻译结果。',
    analysis: '你是一个文本分析专家，请提供详细、客观的分析结果。',
    explanation: '你是一个知识解释专家，请用简单易懂的语言解释复杂概念。',
    summarization: '你是一个文档摘要专家，请提供简洁、准确的内容摘要。',
    rewriting:
      '你是一个文本改写专家，请根据要求改写文本，保持原意的同时改进表达。',
  };

  return templates[type] || '你是一个有用的AI助手，请根据用户需求提供帮助。';
}
