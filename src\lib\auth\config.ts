import { NextAuthOptions } from "next-auth";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import GitHubProvider from "next-auth/providers/github";
import { prisma } from "@/lib/db/prisma";
import bcrypt from "bcryptjs";

/**
 * NextAuth.js 认证配置
 * 配置认证提供商、会话策略、回调函数等
 */
export const authOptions: NextAuthOptions = {
  // 使用 Prisma 适配器连接数据库
  adapter: PrismaAdapter(prisma),
  
  // 认证提供商配置
  providers: [
    // 邮箱密码认证提供商
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "邮箱", type: "email" },
        password: { label: "密码", type: "password" }
      },
      async authorize(credentials) {
        // 验证输入参数
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // 从数据库查找用户
        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          }
        });

        // 检查用户是否存在且有密码
        if (!user || !user.password) {
          return null;
        }

        // 验证密码
        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        );

        if (!isPasswordValid) {
          return null;
        }

        // 返回用户信息（不包含密码）
        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
        };
      }
    }),

    // Google OAuth 提供商（可选，需要配置环境变量）
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? [
      GoogleProvider({
        clientId: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      })
    ] : []),

    // GitHub OAuth 提供商（可选，需要配置环境变量）
    ...(process.env.GITHUB_ID && process.env.GITHUB_SECRET ? [
      GitHubProvider({
        clientId: process.env.GITHUB_ID,
        clientSecret: process.env.GITHUB_SECRET,
      })
    ] : []),
  ],
  
  // 会话配置
  session: {
    strategy: "jwt", // 使用 JWT 策略
    maxAge: 30 * 24 * 60 * 60, // 会话有效期：30天
  },
  
  // JWT 配置
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // JWT 有效期：30天
  },
  
  // 自定义页面路径
  pages: {
    signIn: "/auth/signin", // 登录页面路径
  },
  
  // 回调函数
  callbacks: {
    // JWT 回调：在创建 JWT 时调用
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    
    // 会话回调：在创建会话时调用
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
      }
      return session;
    },
  },
  
  // 事件处理
  events: {
    // 用户创建事件
    async createUser({ user }) {
      console.log(`新用户创建: ${user.email}`);
    },
    
    // 用户登录事件
    async signIn({ user }) {
      console.log(`用户登录: ${user.email}`);
    },
  },
};