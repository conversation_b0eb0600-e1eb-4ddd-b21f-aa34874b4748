'use client';

import React from 'react';
import { HierarchyDemo } from '@/components/hierarchy/HierarchyDemo';

export default function FileTreeDemoPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            增强型文件树组件演示
          </h1>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-semibold mb-4">已实现功能：</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-semibold text-blue-800 mb-2">
                  📁 可展开/折叠的树结构
                </h3>
                <p className="text-sm text-blue-600">
                  点击文件夹可以展开/折叠并查看其内容。
                  有子项的文件夹会显示箭头图标。
                </p>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-semibold text-green-800 mb-2">
                  🖱️ 拖放支持
                </h3>
                <p className="text-sm text-green-600">
                  拖动文件夹和文档可以在不同位置之间移动它们。
                  拖动到有效目标时，放置区域会高亮显示。
                </p>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="font-semibold text-purple-800 mb-2">
                  📋 右键菜单操作
                </h3>
                <p className="text-sm text-purple-600">
                  右键点击项目可以访问重命名、删除和复制选项。
                  文档还支持复制功能。
                </p>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
              <h3 className="font-semibold text-yellow-800 mb-2">
                🎯 如何测试：
              </h3>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• 点击文件夹展开/折叠它们</li>
                <li>• 右键点击文件夹或文档查看上下文菜单</li>
                <li>• 拖动项目在文件夹之间移动</li>
                <li>• 使用 + 按钮创建新的文件夹和文档</li>
                <li>• 鼠标悬停在项目上查看操作按钮</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <HierarchyDemo />
        </div>
      </div>
    </div>
  );
}