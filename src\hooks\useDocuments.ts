import { useState, useEffect, useCallback, useMemo } from 'react';
import { documentService, CreateDocumentData, UpdateDocumentData, DocumentFilter, DocumentStats } from '@/lib/storage/document-service';
import { LocalDocument } from '@/lib/storage/database';
import { AppError } from '@/types';

export interface UseDocumentsOptions {
  userId?: string;
  autoLoad?: boolean;
  filter?: DocumentFilter;
}

export interface UseDocumentsReturn {
  documents: LocalDocument[];
  loading: boolean;
  error: AppError | null;
  stats: DocumentStats | null;
  
  // CRUD operations
  createDocument: (data: CreateDocumentData) => Promise<LocalDocument | null>;
  getDocument: (id: string) => Promise<LocalDocument | null>;
  updateDocument: (id: string, data: UpdateDocumentData) => Promise<LocalDocument | null>;
  deleteDocument: (id: string) => Promise<boolean>;
  restoreDocument: (id: string) => Promise<LocalDocument | null>;
  duplicateDocument: (id: string, newTitle?: string) => Promise<LocalDocument | null>;
  
  // Utility operations
  searchDocuments: (query: string) => Promise<LocalDocument[]>;
  getRecentDocuments: (limit?: number) => Promise<LocalDocument[]>;
  refreshDocuments: () => Promise<void>;
  clearError: () => void;
}

export function useDocuments(options: UseDocumentsOptions = {}): UseDocumentsReturn {
  const { userId, autoLoad = true, filter } = options;
  
  // 使用 useMemo 来稳定 filter 对象的引用，避免无限循环
  const stableFilter = useMemo(() => filter || {}, [filter]);
  
  const [documents, setDocuments] = useState<LocalDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<AppError | null>(null);
  const [stats, setStats] = useState<DocumentStats | null>(null);

  // Load documents
  const loadDocuments = useCallback(async () => {
    if (!userId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const filterWithUserId = { ...stableFilter, userId };
      const docs = await documentService.getDocuments(filterWithUserId);
      setDocuments(docs);
      
      // Load stats
      const docStats = await documentService.getDocumentStats(userId);
      setStats(docStats);
    } catch (err) {
      setError(err as AppError);
    } finally {
      setLoading(false);
    }
  }, [userId, stableFilter]);

  // Auto-load documents when userId changes
  useEffect(() => {
    if (!autoLoad || !userId) return;
    
    const loadData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const filterWithUserId = { ...stableFilter, userId };
        const docs = await documentService.getDocuments(filterWithUserId);
        setDocuments(docs);
        
        // Load stats
        const docStats = await documentService.getDocumentStats(userId);
        setStats(docStats);
      } catch (err) {
        setError(err as AppError);
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [autoLoad, userId, stableFilter]); // 使用稳定的 stableFilter

  // Create document
  const createDocument = useCallback(async (data: CreateDocumentData): Promise<LocalDocument | null> => {
    setError(null);
    
    try {
      const newDocument = await documentService.createDocument(data);
      setDocuments(prev => [newDocument, ...prev]);
      
      // Update stats
      if (userId) {
        const docStats = await documentService.getDocumentStats(userId);
        setStats(docStats);
      }
      
      return newDocument;
    } catch (err) {
      setError(err as AppError);
      return null;
    }
  }, [userId]);

  // Get single document
  const getDocument = useCallback(async (id: string): Promise<LocalDocument | null> => {
    setError(null);
    
    try {
      return await documentService.getDocument(id);
    } catch (err) {
      setError(err as AppError);
      return null;
    }
  }, []);

  // Update document
  const updateDocument = useCallback(async (id: string, data: UpdateDocumentData): Promise<LocalDocument | null> => {
    setError(null);
    
    try {
      const updatedDocument = await documentService.updateDocument(id, data);
      
      // Update local state
      setDocuments(prev => 
        prev.map(doc => doc.id === id ? updatedDocument : doc)
      );
      
      // Update stats
      if (userId) {
        const docStats = await documentService.getDocumentStats(userId);
        setStats(docStats);
      }
      
      return updatedDocument;
    } catch (err) {
      setError(err as AppError);
      return null;
    }
  }, [userId]);

  // Delete document
  const deleteDocument = useCallback(async (id: string): Promise<boolean> => {
    setError(null);
    
    try {
      await documentService.deleteDocument(id);
      
      // Update local state
      setDocuments(prev => prev.filter(doc => doc.id !== id));
      
      // Update stats
      if (userId) {
        const docStats = await documentService.getDocumentStats(userId);
        setStats(docStats);
      }
      
      return true;
    } catch (err) {
      setError(err as AppError);
      return false;
    }
  }, [userId]);

  // Restore document
  const restoreDocument = useCallback(async (id: string): Promise<LocalDocument | null> => {
    setError(null);
    
    try {
      const restoredDocument = await documentService.restoreDocument(id);
      
      // Add back to local state
      setDocuments(prev => [restoredDocument, ...prev]);
      
      // Update stats
      if (userId) {
        const docStats = await documentService.getDocumentStats(userId);
        setStats(docStats);
      }
      
      return restoredDocument;
    } catch (err) {
      setError(err as AppError);
      return null;
    }
  }, [userId]);

  // Duplicate document
  const duplicateDocument = useCallback(async (id: string, newTitle?: string): Promise<LocalDocument | null> => {
    setError(null);
    
    try {
      const duplicatedDocument = await documentService.duplicateDocument(id, newTitle);
      
      // Add to local state
      setDocuments(prev => [duplicatedDocument, ...prev]);
      
      // Update stats
      if (userId) {
        const docStats = await documentService.getDocumentStats(userId);
        setStats(docStats);
      }
      
      return duplicatedDocument;
    } catch (err) {
      setError(err as AppError);
      return null;
    }
  }, [userId]);

  // Search documents
  const searchDocuments = useCallback(async (query: string): Promise<LocalDocument[]> => {
    if (!userId) return [];
    
    setError(null);
    
    try {
      return await documentService.searchDocuments(userId, query);
    } catch (err) {
      setError(err as AppError);
      return [];
    }
  }, [userId]);

  // Get recent documents
  const getRecentDocuments = useCallback(async (limit: number = 10): Promise<LocalDocument[]> => {
    if (!userId) return [];
    
    setError(null);
    
    try {
      return await documentService.getRecentDocuments(userId, limit);
    } catch (err) {
      setError(err as AppError);
      return [];
    }
  }, [userId]);

  // Refresh documents
  const refreshDocuments = useCallback(async () => {
    if (!userId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const filterWithUserId = { ...stableFilter, userId };
      const docs = await documentService.getDocuments(filterWithUserId);
      setDocuments(docs);
      
      // Load stats
      const docStats = await documentService.getDocumentStats(userId);
      setStats(docStats);
    } catch (err) {
      setError(err as AppError);
    } finally {
      setLoading(false);
    }
  }, [userId, stableFilter]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    documents,
    loading,
    error,
    stats,
    createDocument,
    getDocument,
    updateDocument,
    deleteDocument,
    restoreDocument,
    duplicateDocument,
    searchDocuments,
    getRecentDocuments,
    refreshDocuments,
    clearError
  };
}

// Hook for managing a single document
export interface UseDocumentOptions {
  documentId?: string;
  autoLoad?: boolean;
}

export interface UseDocumentReturn {
  document: LocalDocument | null;
  loading: boolean;
  error: AppError | null;
  
  updateDocument: (data: UpdateDocumentData) => Promise<boolean>;
  deleteDocument: () => Promise<boolean>;
  duplicateDocument: (newTitle?: string) => Promise<LocalDocument | null>;
  refreshDocument: () => Promise<void>;
  clearError: () => void;
}

export function useDocument(options: UseDocumentOptions = {}): UseDocumentReturn {
  const { documentId, autoLoad = true } = options;
  
  const [document, setDocument] = useState<LocalDocument | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<AppError | null>(null);

  // Load document
  const loadDocument = useCallback(async () => {
    if (!documentId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const doc = await documentService.getDocument(documentId);
      setDocument(doc);
    } catch (err) {
      setError(err as AppError);
    } finally {
      setLoading(false);
    }
  }, [documentId]);

  // Auto-load document when documentId changes
  useEffect(() => {
    if (!autoLoad || !documentId) return;
    
    const loadData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const doc = await documentService.getDocument(documentId);
        setDocument(doc);
      } catch (err) {
        setError(err as AppError);
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [autoLoad, documentId]); // 直接依赖原始值，避免函数依赖

  // Update document
  const updateDocument = useCallback(async (data: UpdateDocumentData): Promise<boolean> => {
    if (!documentId) return false;
    
    setError(null);
    
    try {
      const updatedDocument = await documentService.updateDocument(documentId, data);
      setDocument(updatedDocument);
      return true;
    } catch (err) {
      setError(err as AppError);
      return false;
    }
  }, [documentId]);

  // Delete document
  const deleteDocument = useCallback(async (): Promise<boolean> => {
    if (!documentId) return false;
    
    setError(null);
    
    try {
      await documentService.deleteDocument(documentId);
      setDocument(null);
      return true;
    } catch (err) {
      setError(err as AppError);
      return false;
    }
  }, [documentId]);

  // Duplicate document
  const duplicateDocument = useCallback(async (newTitle?: string): Promise<LocalDocument | null> => {
    if (!documentId) return null;
    
    setError(null);
    
    try {
      return await documentService.duplicateDocument(documentId, newTitle);
    } catch (err) {
      setError(err as AppError);
      return null;
    }
  }, [documentId]);

  // Refresh document
  const refreshDocument = useCallback(async () => {
    if (!documentId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const doc = await documentService.getDocument(documentId);
      setDocument(doc);
    } catch (err) {
      setError(err as AppError);
    } finally {
      setLoading(false);
    }
  }, [documentId]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    document,
    loading,
    error,
    updateDocument,
    deleteDocument,
    duplicateDocument,
    refreshDocument,
    clearError
  };
}