/**
 * AI 交互历史记录 API 路由
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { AIInteractionHistoryService } from '@/lib/services/ai-interaction-history';
import type { HistoryQueryParams } from '@/lib/services/ai-interaction-history';

/**
 * 获取 AI 交互历史记录
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // 解析查询参数
    const params: HistoryQueryParams = {
      userId: session.user.id,
      documentId: searchParams.get('documentId') || undefined,
      type: searchParams.get('type') || undefined,
      provider: searchParams.get('provider') || undefined,
      search: searchParams.get('search') || undefined,
      dateFrom: searchParams.get('dateFrom') 
        ? new Date(searchParams.get('dateFrom')!) 
        : undefined,
      dateTo: searchParams.get('dateTo') 
        ? new Date(searchParams.get('dateTo')!) 
        : undefined,
      page: searchParams.get('page') 
        ? parseInt(searchParams.get('page')!) 
        : 1,
      limit: searchParams.get('limit') 
        ? parseInt(searchParams.get('limit')!) 
        : 20,
      sortBy: (searchParams.get('sortBy') as any) || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as any) || 'desc',
    };

    const result = await AIInteractionHistoryService.getInteractionHistory(params);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('获取 AI 交互历史记录失败:', error);
    return NextResponse.json(
      { error: '获取历史记录失败' },
      { status: 500 }
    );
  }
}

/**
 * 创建新的 AI 交互记录
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      documentId,
      type,
      input,
      output,
      provider,
      model,
      tokens,
    } = body;

    // 验证必需字段
    if (!documentId || !type || !input || !output || !provider || !model) {
      return NextResponse.json(
        { error: '缺少必需字段' },
        { status: 400 }
      );
    }

    const interaction = await AIInteractionHistoryService.createInteraction({
      documentId,
      userId: session.user.id,
      type,
      input,
      output,
      provider,
      model,
      tokens: tokens || 0,
    });

    return NextResponse.json(interaction);
  } catch (error) {
    console.error('创建 AI 交互记录失败:', error);
    return NextResponse.json(
      { error: '创建交互记录失败' },
      { status: 500 }
    );
  }
}

/**
 * 批量删除 AI 交互记录
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { interactionIds, clearAll } = body;

    let deletedCount = 0;

    if (clearAll) {
      // 清空所有记录
      deletedCount = await AIInteractionHistoryService.clearAllInteractions(
        session.user.id
      );
    } else if (interactionIds && Array.isArray(interactionIds)) {
      // 批量删除指定记录
      deletedCount = await AIInteractionHistoryService.deleteInteractions(
        interactionIds,
        session.user.id
      );
    } else {
      return NextResponse.json(
        { error: '无效的删除参数' },
        { status: 400 }
      );
    }

    return NextResponse.json({ deletedCount });
  } catch (error) {
    console.error('删除 AI 交互记录失败:', error);
    return NextResponse.json(
      { error: '删除记录失败' },
      { status: 500 }
    );
  }
}
