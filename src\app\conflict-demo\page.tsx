'use client';

import React, { useState, useEffect } from 'react';
import { useVersionHistory } from '@/hooks/useVersionHistory';
import { 
  AdvancedConflictResolver,
  ConflictStatusIndicator,
  VersionHistory,
  VersionComparison
} from '@/components/sync';
import { DetailedConflict, ConflictSeverity } from '@/lib/services/conflict-detection';
import { DocumentVersion, VersionComparison as VersionComparisonType } from '@/lib/services/version-history';
import { 
  AlertTriangle, 
  History, 
  GitBranch,
  FileText,
  Clock,
  RefreshCw
} from 'lucide-react';

/**
 * 冲突检测和版本历史演示页面
 */
export default function ConflictDemoPage() {
  const [activeTab, setActiveTab] = useState<'conflicts' | 'versions' | 'comparison'>('conflicts');
  const [selectedDocument, setSelectedDocument] = useState('demo-doc-1');
  const [mockConflicts, setMockConflicts] = useState<DetailedConflict[]>([]);
  const [selectedComparison, setSelectedComparison] = useState<{
    version1: DocumentVersion;
    version2: DocumentVersion;
    comparison: VersionComparisonType;
  } | null>(null);

  const {
    versions,
    currentVersion,
    isLoading,
    error,
    refreshVersions,
    restoreVersion,
    compareVersions,
    stats
  } = useVersionHistory({
    documentId: selectedDocument,
    autoRefresh: false
  });

  // 创建模拟冲突数据
  useEffect(() => {
    const mockConflictData: DetailedConflict[] = [
      {
        id: 'conflict_1',
        documentId: 'demo-doc-1',
        localVersion: {
          id: 'local-1',
          title: '项目计划文档',
          content: { type: 'doc', content: [] },
          updatedAt: new Date('2023-12-01T10:30:00'),
          metadata: { wordCount: 150, characterCount: 800, tags: ['项目', '计划'], isPublic: false }
        },
        remoteVersion: {
          id: 'remote-1',
          title: '项目计划文档 - 已更新',
          content: { type: 'doc', content: [] },
          updatedAt: new Date('2023-12-01T10:35:00'),
          metadata: { wordCount: 180, characterCount: 950, tags: ['项目', '计划', '更新'], isPublic: false }
        },
        conflictType: 'title',
        timestamp: new Date(),
        resolved: false,
        severity: ConflictSeverity.MEDIUM,
        affectedSections: ['title', 'content', 'metadata'],
        suggestedResolution: 'merge',
        autoMergeable: true,
        conflictDetails: [
          {
            type: 'modification',
            section: 'title',
            localValue: '项目计划文档',
            remoteValue: '项目计划文档 - 已更新',
            description: '标题发生变化'
          },
          {
            type: 'addition',
            section: 'tags',
            localValue: ['项目', '计划'],
            remoteValue: ['项目', '计划', '更新'],
            description: '新增标签'
          }
        ]
      },
      {
        id: 'conflict_2',
        documentId: 'demo-doc-2',
        localVersion: {
          id: 'local-2',
          title: '会议纪要',
          content: { type: 'doc', content: [] },
          updatedAt: new Date('2023-12-01T14:20:00'),
          metadata: { wordCount: 300, characterCount: 1500, tags: ['会议'], isPublic: true }
        },
        remoteVersion: {
          id: 'remote-2',
          title: '会议纪要',
          content: { type: 'doc', content: [] },
          updatedAt: new Date('2023-12-01T14:22:00'),
          metadata: { wordCount: 320, characterCount: 1600, tags: ['会议'], isPublic: false }
        },
        conflictType: 'content',
        timestamp: new Date(),
        resolved: false,
        severity: ConflictSeverity.HIGH,
        affectedSections: ['content', 'metadata'],
        suggestedResolution: 'manual',
        autoMergeable: false,
        conflictDetails: [
          {
            type: 'modification',
            section: 'content',
            localValue: '本地内容...',
            remoteValue: '远程内容...',
            description: '内容发生重大变化'
          },
          {
            type: 'modification',
            section: 'isPublic',
            localValue: true,
            remoteValue: false,
            description: '公开状态不一致'
          }
        ]
      }
    ];

    setMockConflicts(mockConflictData);
  }, []);

  const handleResolveConflict = async (
    conflictId: string,
    resolution: 'local' | 'remote' | 'merge',
    mergedData?: any
  ) => {
    console.log('解决冲突:', { conflictId, resolution, mergedData });
    
    // 模拟解决冲突
    setMockConflicts(prev => prev.filter(c => c.id !== conflictId));
    
    // 实际应用中这里会调用同步服务的解决冲突方法
    alert(`冲突已解决：${resolution}`);
  };

  const handleViewVersion = (version: DocumentVersion) => {
    console.log('查看版本:', version);
    alert(`查看版本 ${version.version}: ${version.title}`);
  };

  const handleCompareVersions = async (version1: DocumentVersion, version2: DocumentVersion) => {
    const comparison = await compareVersions(version1, version2);
    if (comparison) {
      setSelectedComparison({
        version1,
        version2,
        comparison
      });
      setActiveTab('comparison');
    }
  };

  const handleRestoreVersion = async (version: DocumentVersion) => {
    try {
      await restoreVersion(version);
      alert(`已恢复到版本 ${version.version}`);
    } catch (error) {
      alert('恢复版本失败');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                冲突检测和版本历史演示
              </h1>
              <p className="text-gray-600 mt-1">
                展示高级冲突检测、解决和文档版本历史功能
              </p>
            </div>
            <div className="flex items-center gap-4">
              <select
                value={selectedDocument}
                onChange={(e) => setSelectedDocument(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="demo-doc-1">项目计划文档</option>
                <option value="demo-doc-2">会议纪要</option>
                <option value="demo-doc-3">技术文档</option>
              </select>
              <button
                onClick={refreshVersions}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <RefreshCw className="h-4 w-4" />
                刷新
              </button>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="text-2xl font-bold text-red-600">
              {mockConflicts.length}
            </div>
            <div className="text-sm text-gray-600">活跃冲突</div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="text-2xl font-bold text-blue-600">
              {stats.totalVersions}
            </div>
            <div className="text-sm text-gray-600">版本总数</div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="text-2xl font-bold text-green-600">
              {stats.changeTypes.user || 0}
            </div>
            <div className="text-sm text-gray-600">用户编辑</div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="text-2xl font-bold text-purple-600">
              {stats.changeTypes.merge || 0}
            </div>
            <div className="text-sm text-gray-600">合并操作</div>
          </div>
        </div>

        {/* 选项卡 */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('conflicts')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'conflicts'
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  冲突管理
                  {mockConflicts.length > 0 && (
                    <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">
                      {mockConflicts.length}
                    </span>
                  )}
                </div>
              </button>
              <button
                onClick={() => setActiveTab('versions')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'versions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <div className="flex items-center gap-2">
                  <History className="h-4 w-4" />
                  版本历史
                </div>
              </button>
              <button
                onClick={() => setActiveTab('comparison')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'comparison'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <div className="flex items-center gap-2">
                  <GitBranch className="h-4 w-4" />
                  版本比较
                </div>
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'conflicts' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  冲突检测和解决
                </h3>
                
                {mockConflicts.length === 0 ? (
                  <div className="text-center py-12 text-gray-500">
                    <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>没有检测到冲突</p>
                    <p className="text-sm">所有文档都已成功同步</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {mockConflicts.map((conflict) => (
                      <div key={conflict.id}>
                        <div className="mb-4">
                          <ConflictStatusIndicator conflict={conflict} />
                        </div>
                        <AdvancedConflictResolver
                          conflict={conflict}
                          versionHistory={versions.slice(0, 5)}
                          onResolve={(resolution, mergedData) => 
                            handleResolveConflict(conflict.id, resolution, mergedData)
                          }
                          onViewVersion={handleViewVersion}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'versions' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  文档版本历史
                </h3>
                
                {isLoading ? (
                  <div className="text-center py-12">
                    <RefreshCw className="h-8 w-8 mx-auto mb-4 text-gray-400 animate-spin" />
                    <p className="text-gray-500">加载版本历史...</p>
                  </div>
                ) : error ? (
                  <div className="text-center py-12 text-red-500">
                    <AlertTriangle className="h-8 w-8 mx-auto mb-4" />
                    <p>加载失败: {error}</p>
                  </div>
                ) : (
                  <VersionHistory
                    documentId={selectedDocument}
                    versions={versions}
                    currentVersion={currentVersion}
                    onRestoreVersion={handleRestoreVersion}
                    onViewVersion={handleViewVersion}
                    onCompareVersions={handleCompareVersions}
                  />
                )}
              </div>
            )}

            {activeTab === 'comparison' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  版本比较
                </h3>
                
                {selectedComparison ? (
                  <VersionComparison
                    version1={selectedComparison.version1}
                    version2={selectedComparison.version2}
                    comparison={selectedComparison.comparison}
                    onClose={() => setSelectedComparison(null)}
                  />
                ) : (
                  <div className="text-center py-12 text-gray-500">
                    <GitBranch className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>选择两个版本进行比较</p>
                    <p className="text-sm">在版本历史页面中选择版本进行比较</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 调试信息 */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            调试信息
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-700 mb-2">冲突统计</h4>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify({
                  totalConflicts: mockConflicts.length,
                  severityBreakdown: mockConflicts.reduce((acc, conflict) => {
                    acc[conflict.severity] = (acc[conflict.severity] || 0) + 1;
                    return acc;
                  }, {} as Record<string, number>),
                  autoMergeableCount: mockConflicts.filter(c => c.autoMergeable).length
                }, null, 2)}
              </pre>
            </div>
            <div>
              <h4 className="font-medium text-gray-700 mb-2">版本统计</h4>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(stats, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}