/**
 * AI 配置页面测试脚本
 * 测试 AI 配置页面的基本功能和无限循环修复
 */

const { chromium } = require('playwright');

async function testAIConfigPage() {
  console.log('🚀 开始测试 AI 配置页面...');

  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 监听控制台错误
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.error('❌ 控制台错误:', msg.text());
      }
    });

    // 监听页面错误
    page.on('pageerror', error => {
      console.error('❌ 页面错误:', error.message);
    });

    // 1. 访问 AI 配置页面
    console.log('📍 访问 AI 配置页面...');
    await page.goto('http://localhost:4501/ai-config');

    // 等待页面加载
    await page.waitForTimeout(2000);

    // 检查是否有无限循环错误
    const hasError = await page.locator('text=Maximum update depth exceeded').count() > 0;
    if (hasError) {
      console.error('❌ 发现无限循环错误！');
      return false;
    } else {
      console.log('✅ 页面加载正常，无无限循环错误');
    }

    // 2. 测试添加配置按钮
    console.log('📍 测试添加配置功能...');
    const addButton = page.locator('text=添加配置').first();
    if (await addButton.count() > 0) {
      await addButton.click();
      await page.waitForTimeout(1000);

      // 检查表单是否出现
      const form = page.locator('form');
      if (await form.count() > 0) {
        console.log('✅ 添加配置表单正常显示');

        // 测试提供商选择
        const providerSelect = page.locator('[role="combobox"]').first();
        if (await providerSelect.count() > 0) {
          await providerSelect.click();
          await page.waitForTimeout(500);

          // 选择 OpenAI
          const openaiOption = page.locator('text=OpenAI');
          if (await openaiOption.count() > 0) {
            await openaiOption.click();
            console.log('✅ 提供商选择功能正常');
          }
        }

        // 返回列表页面
        const cancelButton = page.locator('text=取消');
        if (await cancelButton.count() > 0) {
          await cancelButton.click();
          await page.waitForTimeout(1000);
        }
      }
    }

    // 3. 检查页面稳定性（等待5秒看是否有错误）
    console.log('📍 检查页面稳定性...');
    await page.waitForTimeout(5000);

    console.log('✅ AI 配置页面测试完成！');
    return true;

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
    return false;
  } finally {
    await browser.close();
  }
}

// 运行测试
testAIConfigPage().then(success => {
  if (success) {
    console.log('🎉 所有测试通过！');
    process.exit(0);
  } else {
    console.log('💥 测试失败！');
    process.exit(1);
  }
}).catch(error => {
  console.error('💥 测试运行失败:', error);
  process.exit(1);
});