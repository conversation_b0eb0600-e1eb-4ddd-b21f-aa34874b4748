/**
 * AI 交互历史记录组件
 * 显示和管理用户的 AI 交互历史记录
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Calendar, 
  Trash2, 
  RefreshCw, 
  ChevronLeft, 
  ChevronRight,
  MessageSquare,
  Bot,
  Clock,
  FileText,
  BarChart3,
  Settings,
  X,
  Check,
  AlertTriangle
} from 'lucide-react';
import { useAIInteractionHistory } from '@/hooks/use-ai-interaction-history';
import type { AIInteractionWithDetails } from '@/lib/services/ai-interaction-history';

/**
 * 过滤器状态
 */
interface FilterState {
  search: string;
  type: string;
  provider: string;
  dateFrom: string;
  dateTo: string;
}

/**
 * AI 交互历史记录组件
 */
export function AIInteractionHistory() {
  const {
    history,
    stats,
    fetchHistory,
    fetchStats,
    deleteInteractions,
    clearAllInteractions,
    searchInteractions,
    filterByType,
    filterByProvider,
    filterByDateRange,
    goToPage,
    changePageSize,
  } = useAIInteractionHistory();

  const [filters, setFilters] = useState<FilterState>({
    search: '',
    type: '',
    provider: '',
    dateFrom: '',
    dateTo: '',
  });

  const [selectedInteractions, setSelectedInteractions] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showClearAllConfirm, setShowClearAllConfirm] = useState(false);

  // 初始化数据
  useEffect(() => {
    fetchHistory();
    fetchStats();
  }, [fetchHistory, fetchStats]);

  /**
   * 处理搜索
   */
  const handleSearch = async (searchTerm: string) => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
    if (searchTerm.trim()) {
      await searchInteractions(searchTerm);
    } else {
      await fetchHistory();
    }
  };

  /**
   * 处理类型过滤
   */
  const handleTypeFilter = async (type: string) => {
    setFilters(prev => ({ ...prev, type }));
    if (type) {
      await filterByType(type);
    } else {
      await fetchHistory();
    }
  };

  /**
   * 处理提供商过滤
   */
  const handleProviderFilter = async (provider: string) => {
    setFilters(prev => ({ ...prev, provider }));
    if (provider) {
      await filterByProvider(provider);
    } else {
      await fetchHistory();
    }
  };

  /**
   * 处理日期范围过滤
   */
  const handleDateRangeFilter = async () => {
    const { dateFrom, dateTo } = filters;
    const fromDate = dateFrom ? new Date(dateFrom) : undefined;
    const toDate = dateTo ? new Date(dateTo) : undefined;
    
    await filterByDateRange(fromDate, toDate);
  };

  /**
   * 清除所有过滤器
   */
  const clearFilters = async () => {
    setFilters({
      search: '',
      type: '',
      provider: '',
      dateFrom: '',
      dateTo: '',
    });
    await fetchHistory();
  };

  /**
   * 选择/取消选择交互记录
   */
  const toggleInteractionSelection = (interactionId: string) => {
    setSelectedInteractions(prev => 
      prev.includes(interactionId)
        ? prev.filter(id => id !== interactionId)
        : [...prev, interactionId]
    );
  };

  /**
   * 全选/取消全选
   */
  const toggleSelectAll = () => {
    if (selectedInteractions.length === history.interactions.length) {
      setSelectedInteractions([]);
    } else {
      setSelectedInteractions(history.interactions.map(i => i.id));
    }
  };

  /**
   * 删除选中的交互记录
   */
  const handleDeleteSelected = async () => {
    if (selectedInteractions.length === 0) return;
    
    try {
      await deleteInteractions(selectedInteractions);
      setSelectedInteractions([]);
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error('删除记录失败:', error);
    }
  };

  /**
   * 清空所有记录
   */
  const handleClearAll = async () => {
    try {
      await clearAllInteractions();
      setSelectedInteractions([]);
      setShowClearAllConfirm(false);
    } catch (error) {
      console.error('清空记录失败:', error);
    }
  };

  /**
   * 格式化交互类型
   */
  const formatInteractionType = (type: string) => {
    const typeMap: Record<string, string> = {
      generate: '生成',
      rewrite: '改写',
      summarize: '总结',
      analyze: '分析',
      translate: '翻译',
      explain: '解释',
    };
    return typeMap[type] || type;
  };

  /**
   * 格式化提供商名称
   */
  const formatProvider = (provider: string) => {
    const providerMap: Record<string, string> = {
      openai: 'OpenAI',
      ollama: 'Ollama',
      gemini: 'Gemini',
    };
    return providerMap[provider] || provider;
  };

  /**
   * 格式化日期
   */
  const formatDate = (date: string) => {
    return new Date(date).toLocaleString('zh-CN');
  };

  /**
   * 截断文本
   */
  const truncateText = (text: string, maxLength = 100) => {
    return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* 头部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <MessageSquare className="w-5 h-5 mr-2" />
            AI 交互历史
          </h2>
          
          {history.total > 0 && (
            <span className="text-sm text-gray-500">
              共 {history.total} 条记录
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* 统计按钮 */}
          <button
            onClick={() => setShowStats(!showStats)}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            title="查看统计"
          >
            <BarChart3 className="w-4 h-4" />
          </button>

          {/* 过滤器按钮 */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            title="过滤器"
          >
            <Filter className="w-4 h-4" />
          </button>

          {/* 刷新按钮 */}
          <button
            onClick={() => fetchHistory()}
            disabled={history.loading}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
            title="刷新"
          >
            <RefreshCw className={`w-4 h-4 ${history.loading ? 'animate-spin' : ''}`} />
          </button>

          {/* 删除选中 */}
          {selectedInteractions.length > 0 && (
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
              title="删除选中"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          )}

          {/* 清空所有 */}
          {history.total > 0 && (
            <button
              onClick={() => setShowClearAllConfirm(true)}
              className="px-3 py-1 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
            >
              清空所有
            </button>
          )}
        </div>
      </div>

      {/* 统计面板 */}
      {showStats && stats.stats && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stats.stats.totalInteractions}
              </div>
              <div className="text-sm text-gray-600">总交互次数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {stats.stats.totalTokens.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">总令牌数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Object.keys(stats.stats.byType).length}
              </div>
              <div className="text-sm text-gray-600">功能类型</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {Object.keys(stats.stats.byProvider).length}
              </div>
              <div className="text-sm text-gray-600">AI 提供商</div>
            </div>
          </div>
        </div>
      )}

      {/* 过滤器面板 */}
      {showFilters && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* 搜索 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                搜索
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  placeholder="搜索内容..."
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* 类型过滤 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                功能类型
              </label>
              <select
                value={filters.type}
                onChange={(e) => handleTypeFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部类型</option>
                <option value="generate">生成</option>
                <option value="rewrite">改写</option>
                <option value="summarize">总结</option>
                <option value="analyze">分析</option>
                <option value="translate">翻译</option>
                <option value="explain">解释</option>
              </select>
            </div>

            {/* 提供商过滤 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                AI 提供商
              </label>
              <select
                value={filters.provider}
                onChange={(e) => handleProviderFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部提供商</option>
                <option value="openai">OpenAI</option>
                <option value="ollama">Ollama</option>
                <option value="gemini">Gemini</option>
              </select>
            </div>

            {/* 开始日期 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                开始日期
              </label>
              <input
                type="date"
                value={filters.dateFrom}
                onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* 结束日期 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                结束日期
              </label>
              <input
                type="date"
                value={filters.dateTo}
                onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div className="flex items-center justify-between mt-4">
            <button
              onClick={handleDateRangeFilter}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              应用日期过滤
            </button>
            
            <button
              onClick={clearFilters}
              className="px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              清除过滤器
            </button>
          </div>
        </div>
      )}

      {/* 交互记录列表 */}
      <div className="flex-1 overflow-auto">
        {history.loading ? (
          <div className="flex items-center justify-center h-32">
            <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-600">加载中...</span>
          </div>
        ) : history.error ? (
          <div className="flex items-center justify-center h-32 text-red-600">
            <AlertTriangle className="w-6 h-6 mr-2" />
            {history.error}
          </div>
        ) : history.interactions.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-gray-500">
            <MessageSquare className="w-12 h-12 mb-2 opacity-50" />
            <p>暂无 AI 交互记录</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {/* 全选控制 */}
            {history.interactions.length > 0 && (
              <div className="flex items-center px-4 py-2 bg-gray-50">
                <input
                  type="checkbox"
                  checked={selectedInteractions.length === history.interactions.length}
                  onChange={toggleSelectAll}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label className="ml-2 text-sm text-gray-600">
                  {selectedInteractions.length > 0 
                    ? `已选择 ${selectedInteractions.length} 条记录`
                    : '全选'
                  }
                </label>
              </div>
            )}

            {/* 交互记录项 */}
            {history.interactions.map((interaction) => (
              <InteractionItem
                key={interaction.id}
                interaction={interaction}
                selected={selectedInteractions.includes(interaction.id)}
                onToggleSelect={() => toggleInteractionSelection(interaction.id)}
                formatInteractionType={formatInteractionType}
                formatProvider={formatProvider}
                formatDate={formatDate}
                truncateText={truncateText}
              />
            ))}
          </div>
        )}
      </div>

      {/* 分页控制 */}
      {history.totalPages > 1 && (
        <div className="flex items-center justify-between px-4 py-3 border-t border-gray-200">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">每页显示</span>
            <select
              value={history.limit}
              onChange={(e) => changePageSize(parseInt(e.target.value))}
              className="px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
            <span className="text-sm text-gray-600">条记录</span>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => goToPage(history.page - 1)}
              disabled={history.page <= 1}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>

            <span className="text-sm text-gray-600">
              第 {history.page} 页，共 {history.totalPages} 页
            </span>

            <button
              onClick={() => goToPage(history.page + 1)}
              disabled={history.page >= history.totalPages}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <AlertTriangle className="w-6 h-6 text-red-600 mr-2" />
              <h3 className="text-lg font-semibold">确认删除</h3>
            </div>
            <p className="text-gray-600 mb-6">
              确定要删除选中的 {selectedInteractions.length} 条交互记录吗？此操作不可撤销。
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleDeleteSelected}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 清空所有确认对话框 */}
      {showClearAllConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <AlertTriangle className="w-6 h-6 text-red-600 mr-2" />
              <h3 className="text-lg font-semibold">确认清空</h3>
            </div>
            <p className="text-gray-600 mb-6">
              确定要清空所有 AI 交互记录吗？此操作将删除所有历史记录，不可撤销。
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowClearAllConfirm(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleClearAll}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                清空所有
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * 交互记录项组件
 */
interface InteractionItemProps {
  interaction: AIInteractionWithDetails;
  selected: boolean;
  onToggleSelect: () => void;
  formatInteractionType: (type: string) => string;
  formatProvider: (provider: string) => string;
  formatDate: (date: string) => string;
  truncateText: (text: string, maxLength?: number) => string;
}

function InteractionItem({
  interaction,
  selected,
  onToggleSelect,
  formatInteractionType,
  formatProvider,
  formatDate,
  truncateText,
}: InteractionItemProps) {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className={`p-4 hover:bg-gray-50 transition-colors ${selected ? 'bg-blue-50' : ''}`}>
      <div className="flex items-start space-x-3">
        {/* 选择框 */}
        <input
          type="checkbox"
          checked={selected}
          onChange={onToggleSelect}
          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1"
        />

        {/* 交互图标 */}
        <div className="flex-shrink-0">
          <Bot className="w-8 h-8 text-blue-600 bg-blue-100 rounded-full p-1.5" />
        </div>

        {/* 交互内容 */}
        <div className="flex-1 min-w-0">
          {/* 头部信息 */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {formatInteractionType(interaction.type)}
              </span>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                {formatProvider(interaction.provider)}
              </span>
              <span className="text-xs text-gray-500">
                {interaction.model}
              </span>
            </div>
            
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <Clock className="w-3 h-3" />
              {formatDate(interaction.createdAt.toISOString())}
            </div>
          </div>

          {/* 文档信息 */}
          <div className="flex items-center mb-2 text-sm text-gray-600">
            <FileText className="w-4 h-4 mr-1" />
            <span className="truncate">{interaction.document.title}</span>
          </div>

          {/* 输入内容 */}
          <div className="mb-2">
            <div className="text-sm font-medium text-gray-700 mb-1">输入：</div>
            <div className="text-sm text-gray-600 bg-gray-50 rounded p-2">
              {expanded ? interaction.input : truncateText(interaction.input)}
              {interaction.input.length > 100 && (
                <button
                  onClick={() => setExpanded(!expanded)}
                  className="ml-2 text-blue-600 hover:text-blue-700 text-xs"
                >
                  {expanded ? '收起' : '展开'}
                </button>
              )}
            </div>
          </div>

          {/* 输出内容 */}
          <div className="mb-2">
            <div className="text-sm font-medium text-gray-700 mb-1">输出：</div>
            <div className="text-sm text-gray-600 bg-green-50 rounded p-2">
              {expanded ? interaction.output : truncateText(interaction.output)}
              {interaction.output.length > 100 && (
                <button
                  onClick={() => setExpanded(!expanded)}
                  className="ml-2 text-blue-600 hover:text-blue-700 text-xs"
                >
                  {expanded ? '收起' : '展开'}
                </button>
              )}
            </div>
          </div>

          {/* 令牌信息 */}
          {interaction.tokens > 0 && (
            <div className="text-xs text-gray-500">
              消耗令牌：{interaction.tokens.toLocaleString()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default AIInteractionHistory;