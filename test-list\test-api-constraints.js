/**
 * API 约束测试用例
 *
 * 文件作用：
 * 1. 通过 HTTP API 测试数据库约束是否生效
 * 2. 验证前端会收到什么样的错误响应
 * 3. 测试实际用户操作场景
 *
 * 测试方法：
 * - 使用 fetch API 直接调用后端接口
 * - 模拟用户在前端的操作
 * - 验证错误处理和响应格式
 */

// 使用 Node.js 18+ 内置的 fetch API

const BASE_URL = 'http://localhost:3000';

/**
 * 模拟登录获取 session（简化版）
 */
async function getAuthHeaders() {
  // 在实际测试中，这里应该是真实的认证流程
  // 现在我们直接使用 API，依赖服务器端的 session 处理
  return {
    'Content-Type': 'application/json',
    // 注意：实际应用中需要正确的认证 headers
  };
}

/**
 * 测试文档创建约束
 */
async function testDocumentConstraints() {
  console.log('\n📄 测试文档创建约束');

  const headers = await getAuthHeaders();
  const testTitle = 'API测试文档';

  try {
    // 测试1: 创建第一个文档
    console.log('   测试1: 创建第一个文档');
    const response1 = await fetch(`${BASE_URL}/api/documents`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        title: testTitle,
        content: '这是第一个测试文档',
        folderId: null // 根目录
      })
    });

    const result1 = await response1.json();
    console.log(`   状态: ${response1.status}`);

    if (response1.ok) {
      console.log(`   ✅ 第一个文档创建成功: ${result1.document?.id?.slice(-8) || 'unknown'}...`);

      // 测试2: 尝试创建同名文档
      console.log('   测试2: 尝试创建同名文档（应该失败）');
      const response2 = await fetch(`${BASE_URL}/api/documents`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          title: testTitle,
          content: '这是第二个测试文档',
          folderId: null // 同样在根目录
        })
      });

      const result2 = await response2.json();
      console.log(`   状态: ${response2.status}`);

      if (response2.ok) {
        console.log(`   ❌ 意外成功: 约束未生效`);
        console.log(`   创建的文档ID: ${result2.document?.id?.slice(-8) || 'unknown'}...`);
      } else {
        console.log(`   ✅ 正确失败: ${result2.error || '未知错误'}`);
        console.log(`   错误详情:`, result2);
      }

    } else {
      console.log(`   ❌ 第一个文档创建失败: ${result1.error || '未知错误'}`);
      console.log(`   错误详情:`, result1);
    }

  } catch (error) {
    console.log(`   ❌ 请求失败: ${error.message}`);
  }
}

/**
 * 测试文件夹创建约束
 */
async function testFolderConstraints() {
  console.log('\n📁 测试文件夹创建约束');

  const headers = await getAuthHeaders();
  const testName = 'API测试文件夹';

  try {
    // 测试1: 创建第一个文件夹
    console.log('   测试1: 创建第一个文件夹');
    const response1 = await fetch(`${BASE_URL}/api/folders`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        name: testName,
        parentId: null // 根目录
      })
    });

    const result1 = await response1.json();
    console.log(`   状态: ${response1.status}`);

    if (response1.ok) {
      console.log(`   ✅ 第一个文件夹创建成功: ${result1.folder?.id?.slice(-8) || 'unknown'}...`);

      // 测试2: 尝试创建同名文件夹
      console.log('   测试2: 尝试创建同名文件夹（应该失败）');
      const response2 = await fetch(`${BASE_URL}/api/folders`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          name: testName,
          parentId: null // 同样在根目录
        })
      });

      const result2 = await response2.json();
      console.log(`   状态: ${response2.status}`);

      if (response2.ok) {
        console.log(`   ❌ 意外成功: 约束未生效`);
        console.log(`   创建的文件夹ID: ${result2.folder?.id?.slice(-8) || 'unknown'}...`);
      } else {
        console.log(`   ✅ 正确失败: ${result2.error || '未知错误'}`);
        console.log(`   错误详情:`, result2);
      }

    } else {
      console.log(`   ❌ 第一个文件夹创建失败: ${result1.error || '未知错误'}`);
      console.log(`   错误详情:`, result1);
    }

  } catch (error) {
    console.log(`   ❌ 请求失败: ${error.message}`);
  }
}

/**
 * 测试跨位置同名创建（应该成功）
 */
async function testCrossLocationSameName() {
  console.log('\n🌍 测试跨位置同名创建');

  const headers = await getAuthHeaders();

  try {
    // 先获取现有文件夹
    const foldersResponse = await fetch(`${BASE_URL}/api/folders/tree`, {
      method: 'GET',
      headers
    });

    if (!foldersResponse.ok) {
      console.log('   ❌ 无法获取文件夹列表');
      return;
    }

    const foldersData = await foldersResponse.json();
    const folders = foldersData.folders || [];

    if (folders.length === 0) {
      console.log('   📝 没有现有文件夹，跳过跨位置测试');
      return;
    }

    const targetFolder = folders[0];
    console.log(`   使用目标文件夹: ${targetFolder.name} (${targetFolder.id.slice(-8)}...)`);

    // 测试在不同文件夹下创建同名项目
    const testName = 'CrossLocationTest';

    // 在根目录创建
    console.log('   在根目录创建文件夹');
    const rootResponse = await fetch(`${BASE_URL}/api/folders`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        name: testName,
        parentId: null
      })
    });

    console.log(`   根目录创建状态: ${rootResponse.status}`);

    // 在子文件夹创建同名
    console.log('   在子文件夹创建同名文件夹');
    const subResponse = await fetch(`${BASE_URL}/api/folders`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        name: testName,
        parentId: targetFolder.id
      })
    });

    console.log(`   子文件夹创建状态: ${subResponse.status}`);

    if (rootResponse.ok && subResponse.ok) {
      console.log('   ✅ 跨位置同名创建成功（符合预期）');
    } else {
      console.log('   ⚠️  跨位置同名创建部分失败');
      if (!rootResponse.ok) {
        const rootError = await rootResponse.json();
        console.log(`   根目录错误: ${rootError.error}`);
      }
      if (!subResponse.ok) {
        const subError = await subResponse.json();
        console.log(`   子文件夹错误: ${subError.error}`);
      }
    }

  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`);
  }
}

/**
 * 检查服务器状态
 */
async function checkServerStatus() {
  try {
    const response = await fetch(`${BASE_URL}/api/folders/tree`);
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * 主测试函数
 */
async function testAPIConstraints() {
  console.log('🚀 开始 API 约束测试');
  console.log('=' .repeat(60));

  // 检查服务器状态
  console.log('🔍 检查服务器状态...');
  const serverOk = await checkServerStatus();

  if (!serverOk) {
    console.log('❌ 服务器未响应，请确保开发服务器正在运行');
    console.log('   运行命令: npm run dev');
    return;
  }

  console.log('✅ 服务器正常运行');

  // 执行测试
  await testDocumentConstraints();
  await testFolderConstraints();
  await testCrossLocationSameName();

  console.log('\n🎯 API 测试总结:');
  console.log('   📝 测试了通过 HTTP API 创建重复项目的行为');
  console.log('   🔍 验证了数据库约束在 API 层面的表现');
  console.log('   💡 可以根据测试结果改进前端错误处理');

  console.log('\n💡 下一步建议:');
  console.log('   1. 改进 API 错误响应格式，提供更友好的错误消息');
  console.log('   2. 在前端添加约束错误的特殊处理');
  console.log('   3. 考虑在创建前进行客户端验证');
}

// 执行测试
console.log('🚀 开始执行 API 约束测试...');
testAPIConstraints().catch(console.error);
