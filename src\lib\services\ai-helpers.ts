/**
 * AI 服务辅助函数
 * 提供各种 AI 服务的配置和验证功能
 */

export type AIProvider = 'openai' | 'ollama' | 'gemini';

/**
 * 获取推荐的模型列表
 */
export function getRecommendedModels(provider: AIProvider): string[] {
  switch (provider) {
    case 'openai':
      return [
        'gpt-4o', // 最新的 GPT-4 Omni 模型
        'gpt-4o-mini', // 轻量版 GPT-4 Omni
        'gpt-4-turbo', // GPT-4 Turbo
        'gpt-4', // 标准 GPT-4
        'gpt-3.5-turbo', // GPT-3.5 Turbo
        'gpt-3.5-turbo-16k', // GPT-3.5 Turbo 长上下文版本
      ];
    case 'ollama':
      return [
        'mistral:latest',
        'gemma3:27b',
        'gemma3:27b-it-q8_0',
        'huihui_ai/deepseek-r1-abliterated:32b-qwen-distill-q6_K',
        'qwen2.5-coder:14b-instruct-q6_K',
        'huihui_ai/deepseek-r1-abliterated:32b',
        'mistral-small:22b-instruct-2409-q6_K',
        'qwen2.5:32b-instruct-q6_K',
        'qwen3:32b-q8_0',
        'mistral-nemo:12b-instruct-2407-q6_K',
        'qwen3:14b-fp16',
        'mychen76/openhands_32b-cline-roocode:Q6',
        'deepseek-r1:32b',
        'llama3.2-vision:11b-instruct-q8_0',
      ];
    case 'gemini':
      return [
        'gemini-1.5-pro', // 最新的 Gemini 1.5 Pro
        'gemini-1.5-flash', // Gemini 1.5 Flash (更快)
        'gemini-pro', // Gemini Pro
        'gemini-pro-vision', // Gemini Pro Vision (支持图像)
        'gemini-2.5-flash-lite',
      ];
    default:
      return [];
  }
}

/**
 * 获取默认端点
 */
export function getDefaultEndpoint(provider: AIProvider): string | undefined {
  switch (provider) {
    case 'openai':
      return undefined; // OpenAI 使用默认端点
    case 'ollama':
      return 'http://localhost:11454';
    case 'gemini':
      return undefined; // Gemini 使用默认端点
    default:
      return undefined;
  }
}

/**
 * 检查是否需要 API 密钥
 */
export function requiresApiKey(provider: AIProvider): boolean {
  switch (provider) {
    case 'openai':
      return true;
    case 'ollama':
      return false; // Ollama 本地运行，不需要 API 密钥
    case 'gemini':
      return true;
    default:
      return false;
  }
}

/**
 * 检查是否需要自定义端点
 */
export function requiresEndpoint(provider: AIProvider): boolean {
  switch (provider) {
    case 'openai':
      return true; // OpenAI 显示端点字段用于代理配置
    case 'ollama':
      return true; // Ollama 需要指定本地端点
    case 'gemini':
      return true; // Gemini 显示端点字段用于代理配置
    default:
      return false;
  }
}

/**
 * 获取提供商显示名称
 */
export function getProviderDisplayName(provider: AIProvider): string {
  switch (provider) {
    case 'openai':
      return 'OpenAI';
    case 'ollama':
      return 'Ollama';
    case 'gemini':
      return 'Gemini';
    default:
      return provider;
  }
}

/**
 * 获取提供商描述
 */
export function getProviderDescription(provider: AIProvider): string {
  switch (provider) {
    case 'openai':
      return 'OpenAI GPT 系列模型，需要 API 密钥';
    case 'ollama':
      return '本地运行的开源大语言模型';
    case 'gemini':
      return 'Google Gemini 模型，需要 API 密钥';
    default:
      return '';
  }
}

/**
 * 验证配置
 */
export function validateAIConfig(provider: AIProvider, config: any): string[] {
  const errors: string[] = [];

  if (!provider) {
    errors.push('请选择服务提供商');
  }

  if (!config.model) {
    errors.push('请选择模型');
  }

  if (requiresApiKey(provider) && !config.apiKey) {
    errors.push('API 密钥是必需的');
  }

  if (requiresEndpoint(provider) && !config.endpoint) {
    errors.push('服务端点是必需的');
  }

  if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 100000)) {
    errors.push('最大令牌数必须在 1-100000 之间');
  }

  if (
    config.temperature &&
    (config.temperature < 0 || config.temperature > 2)
  ) {
    errors.push('温度参数必须在 0-2 之间');
  }

  return errors;
}

/**
 * 获取默认配置
 */
export function getDefaultConfig(provider: AIProvider) {
  const models = getRecommendedModels(provider);
  return {
    provider,
    model: models[0] || '',
    endpoint: getDefaultEndpoint(provider),
    maxTokens: 2000,
    temperature: 0.7,
    isDefault: false,
  };
}
