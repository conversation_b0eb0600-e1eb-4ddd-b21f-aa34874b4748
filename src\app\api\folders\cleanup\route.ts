import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { FolderService } from '@/lib/services/folder-service';

/**
 * POST /api/folders/cleanup - 清理空文件夹
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const dryRun = searchParams.get('dryRun') !== 'false'; // 默认为预览模式

    const result = await FolderService.cleanupEmptyFolders(session.user.id, dryRun);

    return NextResponse.json({
      message: dryRun ? '空文件夹预览完成' : '空文件夹清理完成',
      result,
      preview: dryRun,
    });
  } catch (error) {
    console.error('清理空文件夹失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/folders/cleanup - 预览空文件夹
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const result = await FolderService.cleanupEmptyFolders(session.user.id, true);

    return NextResponse.json({
      message: '空文件夹预览',
      result,
    });
  } catch (error) {
    console.error('预览空文件夹失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}