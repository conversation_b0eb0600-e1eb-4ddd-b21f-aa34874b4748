import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';

/**
 * GET /api/documents/[id]/history - 获取文档历史记录
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    // 验证文档是否存在且属于当前用户
    const document = await prisma.document.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!document) {
      return NextResponse.json({ error: '文档未找到' }, { status: 404 });
    }

    // 获取文档历史记录
    const history = await prisma.documentHistory.findMany({
      where: {
        documentId: params.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 50, // 限制返回最近50条记录
    });

    return NextResponse.json({ history });
  } catch (error) {
    console.error('获取文档历史记录失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/documents/[id]/history - 创建文档历史记录
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const body = await request.json();
    const { content, changeType = 'user' } = body;

    if (!content) {
      return NextResponse.json({ error: '内容不能为空' }, { status: 400 });
    }

    // 验证文档是否存在且属于当前用户
    const document = await prisma.document.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!document) {
      return NextResponse.json({ error: '文档未找到' }, { status: 404 });
    }

    // 获取当前最大版本号
    const lastHistory = await prisma.documentHistory.findFirst({
      where: {
        documentId: params.id,
      },
      orderBy: {
        version: 'desc',
      },
    });

    const nextVersion = (lastHistory?.version || 0) + 1;

    // 创建历史记录
    const historyRecord = await prisma.documentHistory.create({
      data: {
        documentId: params.id,
        version: nextVersion,
        content,
        changeType,
      },
    });

    return NextResponse.json({ history: historyRecord }, { status: 201 });
  } catch (error) {
    console.error('创建文档历史记录失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}