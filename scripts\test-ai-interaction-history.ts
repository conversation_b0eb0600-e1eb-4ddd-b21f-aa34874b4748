/**
 * AI 交互历史记录功能测试脚本
 */

import { prisma } from '../src/lib/db';
import { AIInteractionHistoryService } from '../src/lib/services/ai-interaction-history';

/**
 * 创建测试数据
 */
async function createTestData() {
  console.log('创建测试数据...');

  // 创建测试用户
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '测试用户',
      subscription: 'free',
    },
  });

  // 创建测试文档
  const testDocument = await prisma.document.upsert({
    where: { id: 'test-doc-1' },
    update: {},
    create: {
      id: 'test-doc-1',
      title: '测试文档',
      content: '这是一个测试文档的内容',
      userId: testUser.id,
      wordCount: 10,
      charCount: 50,
    },
  });

  // 创建测试 AI 交互记录
  const interactions = [
    {
      documentId: testDocument.id,
      userId: testUser.id,
      type: 'generate',
      input: '请帮我写一段关于人工智能的介绍',
      output: '人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。',
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      tokens: 150,
    },
    {
      documentId: testDocument.id,
      userId: testUser.id,
      type: 'rewrite',
      input: '这段文字写得不够正式，请帮我改写',
      output: '经过改写后的正式版本文字内容',
      provider: 'openai',
      model: 'gpt-4',
      tokens: 120,
    },
    {
      documentId: testDocument.id,
      userId: testUser.id,
      type: 'summarize',
      input: '请总结这篇文章的主要内容',
      output: '文章主要讨论了人工智能的发展历程和应用前景',
      provider: 'gemini',
      model: 'gemini-pro',
      tokens: 80,
    },
    {
      documentId: testDocument.id,
      userId: testUser.id,
      type: 'translate',
      input: '请将这段中文翻译成英文',
      output: 'Please translate this Chinese text into English',
      provider: 'ollama',
      model: 'llama2',
      tokens: 60,
    },
    {
      documentId: testDocument.id,
      userId: testUser.id,
      type: 'explain',
      input: '请解释一下机器学习的概念',
      output: '机器学习是人工智能的一个子领域，它使计算机能够在没有明确编程的情况下学习和改进。',
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      tokens: 100,
    },
  ];

  for (const interaction of interactions) {
    await AIInteractionHistoryService.createInteraction(interaction);
  }

  console.log(`✅ 创建了 ${interactions.length} 条测试交互记录`);
  return { testUser, testDocument };
}

/**
 * 测试查询功能
 */
async function testQueryFunctions(userId: string) {
  console.log('\n测试查询功能...');

  // 测试获取历史记录
  console.log('1. 测试获取历史记录');
  const history = await AIInteractionHistoryService.getInteractionHistory({
    userId,
    page: 1,
    limit: 10,
  });
  console.log(`   - 总记录数: ${history.total}`);
  console.log(`   - 当前页记录数: ${history.interactions.length}`);

  // 测试搜索功能
  console.log('2. 测试搜索功能');
  const searchResult = await AIInteractionHistoryService.getInteractionHistory({
    userId,
    search: '人工智能',
    page: 1,
    limit: 10,
  });
  console.log(`   - 搜索结果数: ${searchResult.total}`);

  // 测试类型过滤
  console.log('3. 测试类型过滤');
  const typeFilterResult = await AIInteractionHistoryService.getInteractionHistory({
    userId,
    type: 'generate',
    page: 1,
    limit: 10,
  });
  console.log(`   - 生成类型记录数: ${typeFilterResult.total}`);

  // 测试提供商过滤
  console.log('4. 测试提供商过滤');
  const providerFilterResult = await AIInteractionHistoryService.getInteractionHistory({
    userId,
    provider: 'openai',
    page: 1,
    limit: 10,
  });
  console.log(`   - OpenAI 记录数: ${providerFilterResult.total}`);

  // 测试获取统计信息
  console.log('5. 测试统计信息');
  const stats = await AIInteractionHistoryService.getInteractionStats(userId);
  console.log(`   - 总交互次数: ${stats.totalInteractions}`);
  console.log(`   - 总令牌数: ${stats.totalTokens}`);
  console.log(`   - 按类型统计:`, stats.byType);
  console.log(`   - 按提供商统计:`, stats.byProvider);

  // 测试获取最近记录
  console.log('6. 测试获取最近记录');
  const recentInteractions = await AIInteractionHistoryService.getRecentInteractions(userId, 3);
  console.log(`   - 最近 3 条记录数: ${recentInteractions.length}`);
}

/**
 * 测试删除功能
 */
async function testDeleteFunctions(userId: string) {
  console.log('\n测试删除功能...');

  // 获取一些记录用于删除测试
  const history = await AIInteractionHistoryService.getInteractionHistory({
    userId,
    page: 1,
    limit: 2,
  });

  if (history.interactions.length > 0) {
    const interactionIds = history.interactions.map(i => i.id);
    
    console.log('1. 测试批量删除');
    const deletedCount = await AIInteractionHistoryService.deleteInteractions(
      interactionIds,
      userId
    );
    console.log(`   - 删除了 ${deletedCount} 条记录`);
  }

  // 测试清空所有记录（注释掉以保留测试数据）
  // console.log('2. 测试清空所有记录');
  // const clearedCount = await AIInteractionHistoryService.clearAllInteractions(userId);
  // console.log(`   - 清空了 ${clearedCount} 条记录`);
}

/**
 * 主测试函数
 */
async function main() {
  try {
    console.log('🚀 开始测试 AI 交互历史记录功能\n');

    // 创建测试数据
    const { testUser } = await createTestData();

    // 测试查询功能
    await testQueryFunctions(testUser.id);

    // 测试删除功能
    await testDeleteFunctions(testUser.id);

    console.log('\n✅ 所有测试完成！');
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
if (require.main === module) {
  main();
}