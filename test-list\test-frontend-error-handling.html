<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端错误处理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .error {
            color: red;
            background: #fee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: green;
            background: #efe;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
    </style>
</head>
<body>
    <h1>前端错误处理测试</h1>
    <p>这个页面用于测试前端的错误处理是否正常工作。</p>

    <div class="test-section">
        <h2>测试文件夹创建错误处理</h2>
        <div>
            <input type="text" id="folderName" placeholder="文件夹名称" value="test1234">
            <button onclick="testCreateFolder()">创建文件夹</button>
        </div>
        <div id="folderResult"></div>
    </div>

    <div class="test-section">
        <h2>测试文档创建错误处理</h2>
        <div>
            <input type="text" id="documentTitle" placeholder="文档标题" value="a2">
            <button onclick="testCreateDocument()">创建文档</button>
        </div>
        <div id="documentResult"></div>
    </div>

    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>首先创建一个文件夹/文档（应该成功）</li>
            <li>再次创建同名文件夹/文档（应该失败并显示友好错误）</li>
            <li>检查错误消息是否友好且具体</li>
        </ol>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001';

        async function testCreateFolder() {
            const name = document.getElementById('folderName').value;
            const resultDiv = document.getElementById('folderResult');
            
            if (!name.trim()) {
                resultDiv.innerHTML = '<div class="error">请输入文件夹名称</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div>创建中...</div>';
                
                const response = await fetch(`${API_BASE}/api/folders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: name.trim(),
                        parentId: null
                    }),
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ 文件夹创建成功！<br>ID: ${data.folder.id}<br>名称: ${data.folder.name}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 创建失败<br>状态码: ${response.status}<br>错误信息: ${data.error || '未知错误'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        async function testCreateDocument() {
            const title = document.getElementById('documentTitle').value;
            const resultDiv = document.getElementById('documentResult');
            
            if (!title.trim()) {
                resultDiv.innerHTML = '<div class="error">请输入文档标题</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div>创建中...</div>';
                
                const response = await fetch(`${API_BASE}/api/documents`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title.trim(),
                        content: '',
                        folderId: null
                    }),
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ 文档创建成功！<br>ID: ${data.document.id}<br>标题: ${data.document.title}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 创建失败<br>状态码: ${response.status}<br>错误信息: ${data.error || '未知错误'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        // 页面加载时显示当前 API 地址
        document.addEventListener('DOMContentLoaded', function() {
            console.log('API Base URL:', API_BASE);
        });
    </script>
</body>
</html>
