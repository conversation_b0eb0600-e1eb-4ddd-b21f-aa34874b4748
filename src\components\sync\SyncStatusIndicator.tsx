'use client';

import React from 'react';
import { useSyncStatus } from '@/hooks/useSync';
import { 
  Cloud, 
  CloudOff, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle,
  Wifi,
  WifiOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SyncStatusIndicatorProps {
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * 同步状态指示器组件
 * 显示当前的网络连接状态和同步状态
 */
export function SyncStatusIndicator({ 
  className, 
  showText = true, 
  size = 'md' 
}: SyncStatusIndicatorProps) {
  const { isOnline, isSyncing, lastSyncAt } = useSyncStatus();

  const getStatusInfo = () => {
    if (!isOnline) {
      return {
        icon: WifiOff,
        text: '离线',
        color: 'text-red-500',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
      };
    }

    if (isSyncing) {
      return {
        icon: RefreshCw,
        text: '同步中...',
        color: 'text-blue-500',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
        animate: true,
      };
    }

    if (lastSyncAt) {
      const timeDiff = Date.now() - lastSyncAt.getTime();
      const minutes = Math.floor(timeDiff / 60000);
      
      if (minutes < 1) {
        return {
          icon: CheckCircle,
          text: '已同步',
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
        };
      } else if (minutes < 5) {
        return {
          icon: CheckCircle,
          text: `${minutes}分钟前同步`,
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
        };
      } else {
        return {
          icon: AlertCircle,
          text: `${minutes}分钟前同步`,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
        };
      }
    }

    return {
      icon: Cloud,
      text: '在线',
      color: 'text-gray-500',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200',
    };
  };

  const statusInfo = getStatusInfo();
  const Icon = statusInfo.icon;

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  return (
    <div
      className={cn(
        'inline-flex items-center gap-2 px-2 py-1 rounded-md border',
        statusInfo.bgColor,
        statusInfo.borderColor,
        className
      )}
      title={statusInfo.text}
    >
      <Icon
        className={cn(
          sizeClasses[size],
          statusInfo.color,
          statusInfo.animate && 'animate-spin'
        )}
      />
      {showText && (
        <span className={cn(statusInfo.color, textSizeClasses[size])}>
          {statusInfo.text}
        </span>
      )}
    </div>
  );
}

/**
 * 简化版同步状态指示器，只显示图标
 */
export function SyncStatusIcon({ 
  className, 
  size = 'md' 
}: Omit<SyncStatusIndicatorProps, 'showText'>) {
  return (
    <SyncStatusIndicator 
      className={className} 
      showText={false} 
      size={size} 
    />
  );
}

/**
 * 详细的同步状态面板
 */
export function SyncStatusPanel({ className }: { className?: string }) {
  const { isOnline, isSyncing, lastSyncAt } = useSyncStatus();

  const formatLastSync = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    return `${days}天前`;
  };

  return (
    <div className={cn('p-4 bg-white border rounded-lg shadow-sm', className)}>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">同步状态</span>
          <SyncStatusIcon size="sm" />
        </div>

        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center justify-between">
            <span>网络连接</span>
            <div className="flex items-center gap-1">
              {isOnline ? (
                <>
                  <Wifi className="h-4 w-4 text-green-500" />
                  <span className="text-green-600">在线</span>
                </>
              ) : (
                <>
                  <WifiOff className="h-4 w-4 text-red-500" />
                  <span className="text-red-600">离线</span>
                </>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span>同步状态</span>
            <div className="flex items-center gap-1">
              {isSyncing ? (
                <>
                  <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
                  <span className="text-blue-600">同步中</span>
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-green-600">已同步</span>
                </>
              )}
            </div>
          </div>

          {lastSyncAt && (
            <div className="flex items-center justify-between">
              <span>上次同步</span>
              <span className="text-gray-500">
                {formatLastSync(lastSyncAt)}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}