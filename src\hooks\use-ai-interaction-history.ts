/**
 * AI 交互历史记录管理 Hook
 */

import { useState, useEffect, useCallback } from 'react';
import type {
  AIInteractionWithDetails,
  HistoryQueryParams,
  HistoryQueryResult,
  HistoryStats,
} from '@/lib/services/ai-interaction-history';

/**
 * 历史记录查询状态
 */
interface HistoryState {
  /** 交互记录列表 */
  interactions: AIInteractionWithDetails[];
  /** 总记录数 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 总页数 */
  totalPages: number;
  /** 加载状态 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
}

/**
 * 统计信息状态
 */
interface StatsState {
  /** 统计数据 */
  stats: HistoryStats | null;
  /** 加载状态 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
}

/**
 * AI 交互历史记录管理 Hook
 */
export function useAIInteractionHistory() {
  const [historyState, setHistoryState] = useState<HistoryState>({
    interactions: [],
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0,
    loading: false,
    error: null,
  });

  const [statsState, setStatsState] = useState<StatsState>({
    stats: null,
    loading: false,
    error: null,
  });

  /**
   * 获取交互历史记录
   */
  const fetchHistory = useCallback(async (params: Partial<HistoryQueryParams> = {}) => {
    setHistoryState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const searchParams = new URLSearchParams();
      
      // 构建查询参数
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (value instanceof Date) {
            searchParams.append(key, value.toISOString());
          } else {
            searchParams.append(key, String(value));
          }
        }
      });

      const response = await fetch(`/api/ai-interactions?${searchParams}`);
      
      if (!response.ok) {
        throw new Error('获取历史记录失败');
      }

      const result: HistoryQueryResult = await response.json();
      
      setHistoryState(prev => ({
        ...prev,
        interactions: result.interactions,
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        loading: false,
      }));
    } catch (error) {
      setHistoryState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '获取历史记录失败',
      }));
    }
  }, []);

  /**
   * 获取统计信息
   */
  const fetchStats = useCallback(async () => {
    setStatsState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/ai-interactions/stats');
      
      if (!response.ok) {
        throw new Error('获取统计信息失败');
      }

      const stats: HistoryStats = await response.json();
      
      setStatsState({
        stats,
        loading: false,
        error: null,
      });
    } catch (error) {
      setStatsState({
        stats: null,
        loading: false,
        error: error instanceof Error ? error.message : '获取统计信息失败',
      });
    }
  }, []);

  /**
   * 获取最近的交互记录
   */
  const fetchRecentInteractions = useCallback(async (limit = 5) => {
    try {
      const response = await fetch(`/api/ai-interactions/recent?limit=${limit}`);
      
      if (!response.ok) {
        throw new Error('获取最近记录失败');
      }

      const interactions: AIInteractionWithDetails[] = await response.json();
      return interactions;
    } catch (error) {
      console.error('获取最近交互记录失败:', error);
      return [];
    }
  }, []);

  /**
   * 创建新的交互记录
   */
  const createInteraction = useCallback(async (data: {
    documentId: string;
    type: string;
    input: string;
    output: string;
    provider: string;
    model: string;
    tokens?: number;
  }) => {
    try {
      const response = await fetch('/api/ai-interactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('创建交互记录失败');
      }

      const interaction = await response.json();
      
      // 刷新历史记录
      await fetchHistory({ page: historyState.page, limit: historyState.limit });
      
      return interaction;
    } catch (error) {
      console.error('创建交互记录失败:', error);
      throw error;
    }
  }, [fetchHistory, historyState.page, historyState.limit]);

  /**
   * 删除交互记录
   */
  const deleteInteractions = useCallback(async (interactionIds: string[]) => {
    try {
      const response = await fetch('/api/ai-interactions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ interactionIds }),
      });

      if (!response.ok) {
        throw new Error('删除记录失败');
      }

      const result = await response.json();
      
      // 刷新历史记录
      await fetchHistory({ page: historyState.page, limit: historyState.limit });
      
      return result.deletedCount;
    } catch (error) {
      console.error('删除交互记录失败:', error);
      throw error;
    }
  }, [fetchHistory, historyState.page, historyState.limit]);

  /**
   * 清空所有交互记录
   */
  const clearAllInteractions = useCallback(async () => {
    try {
      const response = await fetch('/api/ai-interactions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ clearAll: true }),
      });

      if (!response.ok) {
        throw new Error('清空记录失败');
      }

      const result = await response.json();
      
      // 刷新历史记录和统计信息
      await Promise.all([
        fetchHistory({ page: 1, limit: historyState.limit }),
        fetchStats(),
      ]);
      
      return result.deletedCount;
    } catch (error) {
      console.error('清空交互记录失败:', error);
      throw error;
    }
  }, [fetchHistory, fetchStats, historyState.limit]);

  /**
   * 搜索交互记录
   */
  const searchInteractions = useCallback(async (searchTerm: string) => {
    await fetchHistory({ 
      search: searchTerm, 
      page: 1, 
      limit: historyState.limit 
    });
  }, [fetchHistory, historyState.limit]);

  /**
   * 按类型过滤
   */
  const filterByType = useCallback(async (type: string) => {
    await fetchHistory({ 
      type, 
      page: 1, 
      limit: historyState.limit 
    });
  }, [fetchHistory, historyState.limit]);

  /**
   * 按提供商过滤
   */
  const filterByProvider = useCallback(async (provider: string) => {
    await fetchHistory({ 
      provider, 
      page: 1, 
      limit: historyState.limit 
    });
  }, [fetchHistory, historyState.limit]);

  /**
   * 按日期范围过滤
   */
  const filterByDateRange = useCallback(async (dateFrom?: Date, dateTo?: Date) => {
    await fetchHistory({ 
      dateFrom, 
      dateTo, 
      page: 1, 
      limit: historyState.limit 
    });
  }, [fetchHistory, historyState.limit]);

  /**
   * 翻页
   */
  const goToPage = useCallback(async (page: number) => {
    await fetchHistory({ page, limit: historyState.limit });
  }, [fetchHistory, historyState.limit]);

  /**
   * 改变每页数量
   */
  const changePageSize = useCallback(async (limit: number) => {
    await fetchHistory({ page: 1, limit });
  }, [fetchHistory]);

  return {
    // 历史记录状态
    history: historyState,
    
    // 统计信息状态
    stats: statsState,
    
    // 操作方法
    fetchHistory,
    fetchStats,
    fetchRecentInteractions,
    createInteraction,
    deleteInteractions,
    clearAllInteractions,
    searchInteractions,
    filterByType,
    filterByProvider,
    filterByDateRange,
    goToPage,
    changePageSize,
  };
}