'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import { useCallback, useEffect } from 'react';
import { SlashCommandExtension } from '@/lib/editor/slash-command-extension';
import { SlashCommandMenu } from './SlashCommandMenu';
import { TableToolbar } from './TableToolbar';
import { MediaInsertManager } from './MediaInsertManager';
import { AITextGenerationManager } from './AITextGenerationManager';
import { AITextRewriteManager } from './AITextRewriteManager';
import { AIRewriteSelectionMenu } from './AIRewriteSelectionMenu';
import { AIDocumentAnalysisManager } from './AIDocumentAnalysisManager';
import { AITranslationExplanationManager } from './AITranslationExplanationManager';
import { EnhancedSelectionMenu } from './EnhancedSelectionMenu';
import { SLASH_COMMANDS } from '@/lib/editor/slash-commands';

interface EditorProps {
  content?: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  editable?: boolean;
  className?: string;
  /** 是否启用 AI 功能 */
  enableAI?: boolean;
}

export function Editor({
  content = '',
  placeholder = '开始写作...',
  onChange,
  editable = true,
  className = '',
  enableAI = true,
}: EditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Configure built-in extensions
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
        codeBlock: {
          HTMLAttributes: {
            class: 'code-block',
          },
        },
        // 禁用 StarterKit 中的 HorizontalRule，使用单独的扩展
        horizontalRule: false,
      }),
      Placeholder.configure({
        placeholder: `${placeholder} (输入 "/" 查看命令)`,
        emptyEditorClass: 'is-editor-empty',
      }),
      CharacterCount.configure({
        limit: null,
      }),
      // 表格扩展
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'editor-table',
        },
      }),
      TableRow,
      TableHeader,
      TableCell,
      // 分割线扩展
      HorizontalRule.configure({
        HTMLAttributes: {
          class: 'editor-hr',
        },
      }),
      // 任务列表扩展
      TaskList.configure({
        HTMLAttributes: {
          class: 'task-list',
        },
      }),
      TaskItem.configure({
        nested: true,
        HTMLAttributes: {
          class: 'task-item',
        },
      }),
      // 图片扩展
      Image.configure({
        inline: true,
        allowBase64: true,
        HTMLAttributes: {
          class: 'editor-image',
        },
      }),
      // 链接扩展
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'editor-link',
          rel: 'noopener noreferrer',
          target: '_blank',
        },
      }),
      // 添加斜杠命令扩展
      SlashCommandExtension.configure({
        commands: SLASH_COMMANDS,
        trigger: '/',
        allowSpaces: false,
        startOfLine: false,
        char: '/',
      }),
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange?.(html);
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none touch-manipulation',
      },
    },
  });

  // Update content when prop changes
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  // Update editable state
  useEffect(() => {
    if (editor) {
      editor.setEditable(editable);
    }
  }, [editable, editor]);

  const getWordCount = useCallback(() => {
    if (!editor) return 0;
    return editor.storage.characterCount.words();
  }, [editor]);

  const getCharacterCount = useCallback(() => {
    if (!editor) return 0;
    return editor.storage.characterCount.characters();
  }, [editor]);

  if (!editor) {
    return (
      <div className={`min-h-[400px] w-full ${className}`}>
        <div className="flex items-center justify-center h-full">
          <div className="text-muted-foreground">加载编辑器...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      <div className="relative">
        <EditorContent 
          editor={editor} 
          className="min-h-[400px] w-full px-3 sm:px-4 py-4 sm:py-6 focus-within:outline-none touch-manipulation"
        />
        
        {/* 斜杠命令菜单 */}
        <SlashCommandMenu editor={editor} />
        
        {/* 表格工具栏 */}
        {editor.isActive('table') && (
          <div className="absolute top-4 left-4 z-10">
            <TableToolbar editor={editor} />
          </div>
        )}
        
        {/* 媒体插入管理器 */}
        <MediaInsertManager editor={editor} />
        
        {/* AI 文本生成管理器 */}
        <AITextGenerationManager editor={editor} enabled={enableAI} />
        
        {/* AI 文本改写管理器 */}
        <AITextRewriteManager editor={editor} enabled={enableAI} />
        
        {/* AI 改写选择菜单 */}
        <AIRewriteSelectionMenu editor={editor} enabled={enableAI} />
        
        {/* AI 文档分析管理器 */}
        <AIDocumentAnalysisManager editor={editor} enabled={enableAI} />
        
        {/* AI 翻译和解释管理器 */}
        <AITranslationExplanationManager editor={editor} enabled={enableAI} />
        
        {/* 增强版文本选择菜单 */}
        <EnhancedSelectionMenu 
          editor={editor} 
          enabled={true}
          enableAI={enableAI}
          minSelectionLength={2}
        />
        
        {/* Word count display - 移动端优化 */}
        <div className="absolute bottom-2 right-3 sm:right-4 text-xs text-muted-foreground">
          <span className="hidden sm:inline">{getWordCount()} 词 · {getCharacterCount()} 字符</span>
          <span className="sm:hidden">{getWordCount()}词</span>
        </div>
      </div>
    </div>
  );
}

export { useEditor };