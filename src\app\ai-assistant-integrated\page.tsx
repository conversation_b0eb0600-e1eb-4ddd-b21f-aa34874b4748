'use client';

import React, { useState, useCallback } from 'react';
import { AIAssistantManager } from '@/components/ai/AIAssistantManager';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  SparklesIcon,
  FileTextIcon,
  EditIcon,
  SaveIcon,
  RefreshCwIcon
} from 'lucide-react';
import type { AIAssistantAction, AIAssistantActionData } from '@/hooks/useAIAssistant';

/**
 * AI 助手集成演示页面
 * 展示 AI 助手面板与文档编辑器的完整集成
 */
export default function AIAssistantIntegratedPage() {
  const [content, setContent] = useState(`# AI 文档编辑器演示

欢迎使用 AI 增强的文档编辑器！这个编辑器集成了强大的 AI 助手面板，可以帮助您：

## 写作助手功能
- **AI 续写**: 基于上下文智能续写内容
- **AI 改写**: 优化和改进现有文本
- **创意写作**: 生成创意内容和想法

## 文档分析功能
- **文档摘要**: 自动生成内容摘要
- **关键词提取**: 识别重要概念和术语
- **大纲生成**: 创建结构化大纲

## 语言工具
- **AI 翻译**: 多语言翻译支持
- **AI 解释**: 解释复杂概念
- **语法检查**: 检查和修正语法错误

请选择文本并使用右侧的 AI 助手面板来体验这些功能。您也可以直接在文档中输入内容，AI 会根据上下文提供智能建议。

---

*提示: 点击右侧的 AI 按钮打开助手面板，选择文本后可以使用相关的 AI 功能。*`);

  const [isProcessing, setIsProcessing] = useState(false);
  const [processingAction, setProcessingAction] = useState<string>('');
  const [aiResults, setAiResults] = useState<Array<{
    id: string;
    action: string;
    input: string;
    output: string;
    timestamp: Date;
  }>>([]);

  /**
   * 处理 AI 操作
   */
  const handleAIAction = useCallback(async (action: AIAssistantAction, data?: AIAssistantActionData) => {
    setIsProcessing(true);
    setProcessingAction(action);

    try {
      // 模拟 AI 处理延迟
      await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2000));

      // 模拟 AI 响应
      const mockResponse = generateMockAIResponse(action, data);
      
      // 记录结果
      const result = {
        id: Math.random().toString(36).substr(2, 9),
        action,
        input: data?.text || content.substring(0, 100) + '...',
        output: mockResponse,
        timestamp: new Date()
      };
      
      setAiResults(prev => [result, ...prev.slice(0, 4)]);

      // 对于某些操作，直接更新内容
      if (action === 'ai-continue' && data?.text) {
        const newContent = content + '\n\n' + mockResponse;
        setContent(newContent);
      } else if (action === 'ai-rewrite' && data?.text) {
        const updatedContent = content.replace(data.text, mockResponse);
        setContent(updatedContent);
      }

    } catch (error) {
      console.error('AI action failed:', error);
    } finally {
      setIsProcessing(false);
      setProcessingAction('');
    }
  }, [content]);

  /**
   * 生成模拟 AI 响应
   */
  const generateMockAIResponse = (action: AIAssistantAction, data?: AIAssistantActionData): string => {
    const responses: Record<AIAssistantAction, string> = {
      'ai-continue': '这是 AI 生成的续写内容。基于前面的上下文，我们可以继续探讨这个话题的更多细节和相关观点。',
      'ai-rewrite': '这是经过 AI 优化的文本版本，语言更加流畅，表达更加清晰准确。',
      'ai-summarize': '文档摘要：本文档介绍了 AI 文档编辑器的主要功能，包括写作助手、文档分析和语言工具等核心特性。',
      'ai-translate': 'This is the AI-translated version of the selected text, maintaining the original meaning while adapting to the target language.',
      'ai-explain': '这个概念的解释：这是一个复杂的技术概念，涉及多个相关领域的知识，需要从不同角度来理解。',
      'ai-keywords': '关键词：AI助手, 文档编辑, 智能写作, 自然语言处理, 用户体验',
      'ai-outline': '# 文档大纲\n1. 引言\n2. 主要功能\n   - 写作助手\n   - 文档分析\n   - 语言工具\n3. 使用方法\n4. 总结',
      'ai-analysis': '内容分析：文档结构清晰，语言专业，适合技术文档的风格。建议增加更多实例说明。',
      'ai-creative': '创意内容：在数字化时代的浪潮中，AI 技术如同一位智慧的伙伴，默默地协助我们完成各种创作任务...',
      'ai-custom': '根据您的自定义指令，AI 已经处理了相关内容并生成了符合要求的结果。',
      'ai-chat': '我是您的 AI 助手，很高兴为您提供帮助。请告诉我您需要什么协助？',
      'ai-grammar': '语法检查完成：发现 2 处语法问题，已提供修正建议。整体语言质量良好。',
      'ai-expand': '扩展内容：基于原始文本，我添加了更多详细信息、背景知识和相关例子，使内容更加丰富完整。',
      'ai-settings': '设置页面已打开，您可以配置 AI 服务参数。'
    };

    return responses[action] || '这是 AI 生成的响应内容。';
  };

  /**
   * 获取操作显示名称
   */
  const getActionDisplayName = (action: string): string => {
    const names: Record<string, string> = {
      'ai-continue': 'AI 续写',
      'ai-rewrite': 'AI 改写',
      'ai-summarize': '文档摘要',
      'ai-translate': 'AI 翻译',
      'ai-explain': 'AI 解释',
      'ai-keywords': '关键词提取',
      'ai-outline': '生成大纲',
      'ai-analysis': '内容分析',
      'ai-creative': '创意写作',
      'ai-custom': '自定义指令',
      'ai-chat': 'AI 对话',
      'ai-grammar': '语法检查',
      'ai-expand': '内容扩展',
      'ai-settings': 'AI 设置'
    };
    return names[action] || action;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-4 space-y-6">
        {/* 页面标题 */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-2">
            <SparklesIcon className="h-8 w-8 text-blue-600" />
            AI 助手集成演示
          </h1>
          <p className="text-gray-600">
            完整的 AI 助手面板与文档编辑器集成示例
          </p>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          {/* 编辑器区域 */}
          <div className="xl:col-span-3 space-y-6">
            {/* 工具栏 */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileTextIcon className="h-5 w-5 text-gray-600" />
                    <span className="font-medium text-gray-900">文档编辑器</span>
                    {isProcessing && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <RefreshCwIcon className="h-3 w-3 animate-spin" />
                        {getActionDisplayName(processingAction)}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <SaveIcon className="h-4 w-4 mr-1" />
                      保存
                    </Button>
                    <Button variant="outline" size="sm">
                      <EditIcon className="h-4 w-4 mr-1" />
                      格式
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 编辑器 */}
            <Card>
              <CardContent className="p-0">
                <Textarea
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  className="min-h-[600px] border-0 resize-none focus:ring-0 text-base leading-relaxed"
                  placeholder="开始输入您的文档内容..."
                />
              </CardContent>
            </Card>

            {/* 使用提示 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">使用提示</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">📝 文本选择</h4>
                    <p className="text-sm text-gray-600">
                      选择编辑器中的文本，然后使用 AI 助手面板中的相关功能
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">⚡ 快捷键</h4>
                    <p className="text-sm text-gray-600">
                      使用 Ctrl+Shift+A 快速打开/关闭 AI 助手面板
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">🎯 智能建议</h4>
                    <p className="text-sm text-gray-600">
                      AI 会根据文档内容和选中文本提供相关功能建议
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">📱 响应式</h4>
                    <p className="text-sm text-gray-600">
                      面板会自动适配不同屏幕尺寸，提供最佳体验
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* AI 结果面板 */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SparklesIcon className="h-5 w-5 text-purple-600" />
                  AI 处理结果
                </CardTitle>
              </CardHeader>
              <CardContent>
                {aiResults.length === 0 ? (
                  <div className="text-center py-8">
                    <SparklesIcon className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-sm text-gray-500">
                      使用 AI 功能后，结果将显示在这里
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {aiResults.map((result) => (
                      <div
                        key={result.id}
                        className="p-3 bg-gray-50 rounded-lg border space-y-2"
                      >
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs">
                            {getActionDisplayName(result.action)}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {result.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        
                        {result.input && (
                          <div className="text-xs text-gray-600">
                            <strong>输入:</strong> {result.input.substring(0, 80)}...
                          </div>
                        )}
                        
                        <div className="text-sm text-gray-800">
                          <strong>结果:</strong>
                          <p className="mt-1 text-gray-700 leading-relaxed">
                            {result.output}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 统计信息 */}
            <Card>
              <CardHeader>
                <CardTitle>文档统计</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">字符数:</span>
                  <Badge variant="outline">
                    {content.length.toLocaleString()}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">单词数:</span>
                  <Badge variant="outline">
                    {content.split(/\s+/).filter(word => word.length > 0).length}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">段落数:</span>
                  <Badge variant="outline">
                    {content.split(/\n\s*\n/).filter(para => para.trim().length > 0).length}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">AI 操作:</span>
                  <Badge variant="secondary">
                    {aiResults.length}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* AI 助手管理器 */}
      <AIAssistantManager
        position="right"
        width={350}
        defaultOpen={false}
        onAIAction={handleAIAction}
      />
    </div>
  );
}