version: '3.8'

services:
  # Adminer - 轻量级数据库管理工具
  adminer:
    image: adminer:latest
    container_name: kiro-editor-adminer
    restart: always
    ports:
      - "8083:8080"
    environment:
      ADMINER_DEFAULT_SERVER: sqlite
      ADMINER_DESIGN: pepa-linha-dark  # 深色主题
    volumes:
      - ../prisma/dev.db:/tmp/dev.db:ro
    networks:
      - kiro-editor-network

  # SQLiteWeb - SQLite 专用管理工具
  sqliteweb:
    image: coleifer/sqlite-web:latest
    container_name: kiro-editor-sqliteweb
    restart: always
    ports:
      - "8084:8080"
    volumes:
      - ../prisma:/data
    command: sqlite_web /data/dev.db --host 0.0.0.0 --port 8080 --read-only
    networks:
      - kiro-editor-network

networks:
  kiro-editor-network:
    driver: bridge