/**
 * AI 配置同步功能测试脚本
 * 测试加密、同步和冲突解决功能
 */

import { encrypt, decrypt, hash, verifyHash, generateToken } from '../src/lib/utils/encryption';
import { aiConfigSyncService } from '../src/lib/services/ai-config-sync';

async function testEncryption() {
  console.log('🔐 测试加密功能...\n');

  try {
    // 测试基本加密解密
    const originalText = 'sk-1234567890abcdef';
    console.log(`原始文本: ${originalText}`);

    const encrypted = encrypt(originalText);
    console.log(`加密后: ${encrypted.substring(0, 50)}...`);

    const decrypted = decrypt(encrypted);
    console.log(`解密后: ${decrypted}`);

    if (originalText === decrypted) {
      console.log('✅ 基本加密解密测试通过');
    } else {
      console.log('❌ 基本加密解密测试失败');
    }

    // 测试空字符串
    const emptyEncrypted = encrypt('');
    const emptyDecrypted = decrypt(emptyEncrypted);
    console.log(`✅ 空字符串处理: "${emptyDecrypted}"`);

    // 测试哈希功能
    const testData = 'test data for hashing';
    const hashValue = hash(testData);
    console.log(`✅ 哈希值: ${hashValue.substring(0, 16)}...`);

    const isValid = verifyHash(testData, hashValue);
    console.log(`✅ 哈希验证: ${isValid ? '通过' : '失败'}`);

    // 测试令牌生成
    const token = generateToken(16);
    console.log(`✅ 随机令牌: ${token}`);

    console.log('\n🎉 加密功能测试完成！\n');
  } catch (error) {
    console.error('❌ 加密功能测试失败:', error);
  }
}

async function testSyncService() {
  console.log('🔄 测试同步服务...\n');

  try {
    const testUserId = 'test-user-123';
    const testDeviceId = 'test-device-456';

    // 测试设备管理
    console.log('📱 测试设备管理:');
    const devices = await aiConfigSyncService.getUserDevices(testUserId);
    console.log(`  ✅ 获取设备列表: ${devices.length} 个设备`);

    const newDeviceId = await aiConfigSyncService.registerDevice(testUserId, {
      name: 'Test Device',
      type: 'desktop',
      platform: 'Test Platform'
    });
    console.log(`  ✅ 注册设备成功: ${newDeviceId}`);

    // 测试配置同步数据准备
    console.log('\n⚙️ 测试配置同步:');
    
    // 模拟配置数据（实际应该从数据库获取）
    const mockConfigId = 'test-config-789';
    console.log(`  ✅ 准备同步配置: ${mockConfigId}`);

    // 测试同步状态
    const syncStatus = await aiConfigSyncService.getConfigSyncStatus(mockConfigId, testUserId);
    console.log(`  ✅ 获取同步状态: 已同步设备 ${syncStatus.syncedDevices.length} 个`);

    // 测试冲突检测
    console.log('\n⚠️ 测试冲突检测:');
    const conflicts = await aiConfigSyncService.detectConfigConflicts(testUserId);
    console.log(`  ✅ 检测到冲突: ${conflicts.length} 个`);

    // 测试冲突解决
    if (conflicts.length > 0) {
      const resolved = await aiConfigSyncService.resolveConfigConflict(
        conflicts[0].configId,
        testUserId,
        'local'
      );
      console.log(`  ✅ 解决冲突: ${resolved ? '成功' : '失败'}`);
    }

    // 测试批量同步
    console.log('\n🔄 测试批量同步:');
    const syncResult = await aiConfigSyncService.syncAllConfigs(testUserId, testDeviceId);
    console.log(`  ✅ 同步结果: 成功 ${syncResult.syncedCount} 个，失败 ${syncResult.failedCount} 个`);

    // 测试同步历史
    console.log('\n📜 测试同步历史:');
    const history = await aiConfigSyncService.getSyncHistory(testUserId, 10);
    console.log(`  ✅ 获取历史记录: ${history.length} 条`);

    // 测试清理历史
    const cleanedCount = await aiConfigSyncService.cleanupSyncHistory(testUserId, 30);
    console.log(`  ✅ 清理历史记录: ${cleanedCount} 条`);

    console.log('\n🎉 同步服务测试完成！\n');
  } catch (error) {
    console.error('❌ 同步服务测试失败:', error);
  }
}

async function testConfigSyncData() {
  console.log('📊 测试配置同步数据处理...\n');

  try {
    // 模拟配置数据
    const mockConfig = {
      id: 'config-123',
      provider: 'openai',
      model: 'gpt-4',
      apiKey: 'sk-test-key-123456789',
      maxTokens: 2000,
      temperature: 0.7,
      isDefault: true
    };

    console.log('原始配置:');
    console.log(`  提供商: ${mockConfig.provider}`);
    console.log(`  模型: ${mockConfig.model}`);
    console.log(`  API密钥: ${mockConfig.apiKey.substring(0, 10)}...`);

    // 测试敏感数据加密
    const encryptedApiKey = encrypt(mockConfig.apiKey);
    console.log(`\n✅ API密钥加密: ${encryptedApiKey.substring(0, 20)}...`);

    const decryptedApiKey = decrypt(encryptedApiKey);
    console.log(`✅ API密钥解密: ${decryptedApiKey === mockConfig.apiKey ? '成功' : '失败'}`);

    // 测试数据完整性校验
    const configForChecksum = JSON.stringify({
      provider: mockConfig.provider,
      model: mockConfig.model,
      apiKey: mockConfig.apiKey,
      maxTokens: mockConfig.maxTokens,
      temperature: mockConfig.temperature,
      isDefault: mockConfig.isDefault
    });

    const checksum = hash(configForChecksum);
    console.log(`✅ 数据校验和: ${checksum.substring(0, 16)}...`);

    // 验证校验和
    const isValid = verifyHash(configForChecksum, checksum);
    console.log(`✅ 校验和验证: ${isValid ? '通过' : '失败'}`);

    // 模拟同步数据结构
    const syncData = {
      id: mockConfig.id,
      provider: mockConfig.provider,
      model: mockConfig.model,
      maxTokens: mockConfig.maxTokens,
      temperature: mockConfig.temperature,
      isDefault: mockConfig.isDefault,
      encryptedApiKey: encryptedApiKey,
      checksum: checksum,
      version: 1,
      lastModified: new Date()
    };

    console.log('\n同步数据结构:');
    console.log(`  配置ID: ${syncData.id}`);
    console.log(`  版本: ${syncData.version}`);
    console.log(`  校验和: ${syncData.checksum.substring(0, 16)}...`);
    console.log(`  加密密钥: ${syncData.encryptedApiKey ? '已加密' : '无'}`);

    console.log('\n🎉 配置同步数据处理测试完成！\n');
  } catch (error) {
    console.error('❌ 配置同步数据处理测试失败:', error);
  }
}

async function runAllTests() {
  console.log('🧪 开始 AI 配置同步功能测试...\n');

  await testEncryption();
  await testSyncService();
  await testConfigSyncData();

  console.log('🎉 所有测试完成！');
}

// 运行测试
runAllTests().catch(error => {
  console.error('测试执行失败:', error);
  process.exit(1);
});