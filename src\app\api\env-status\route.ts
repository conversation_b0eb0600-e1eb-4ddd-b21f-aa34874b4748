/**
 * 环境变量状态 API
 * 返回当前环境变量的配置状态
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { getEnvConfigSummary } from '@/lib/config/env';

/**
 * 获取环境变量配置状态
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取环境变量配置摘要
    const envSummary = getEnvConfigSummary();

    return NextResponse.json(envSummary);

  } catch (error) {
    console.error('获取环境变量状态失败:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : '获取环境变量状态失败'
    }, { status: 500 });
  }
}