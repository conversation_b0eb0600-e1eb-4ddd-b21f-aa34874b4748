"use client";

import { useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { LoginForm, RegisterForm } from "@/components/auth";
import Link from "next/link";

export default function AuthDemoPage() {
  const [activeTab, setActiveTab] = useState<"login" | "register">("login");
  const { user, isAuthenticated, isLoading, logout } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>正在验证身份...</p>
        </div>
      </div>
    );
  }

  if (isAuthenticated && user) {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow p-6">
            <h1 className="text-2xl font-bold mb-6">认证成功！</h1>
            
            <div className="bg-green-50 border border-green-200 p-4 rounded mb-6">
              <div className="flex items-center">
                <div className="text-green-600 text-xl mr-3">✅</div>
                <div>
                  <p className="text-green-800 font-medium">登录成功</p>
                  <p className="text-green-700 text-sm">您已成功通过身份验证</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-3 mb-6">
              <div className="flex justify-between">
                <span className="font-medium">用户ID:</span>
                <span className="text-gray-600">{user.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">姓名:</span>
                <span className="text-gray-600">{user.name || "未设置"}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">邮箱:</span>
                <span className="text-gray-600">{user.email}</span>
              </div>
            </div>
            
            <div className="flex space-x-4">
              <Link
                href="/dashboard"
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                进入仪表板
              </Link>
              <button
                onClick={logout}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-center mb-6">认证系统演示</h1>
          
          {/* 标签切换 */}
          <div className="flex mb-6 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setActiveTab("login")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === "login"
                  ? "bg-white text-blue-600 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              登录
            </button>
            <button
              onClick={() => setActiveTab("register")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === "register"
                  ? "bg-white text-blue-600 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              注册
            </button>
          </div>
          
          {/* 表单内容 */}
          {activeTab === "login" ? (
            <LoginForm onSuccess={() => {
              // 登录成功后页面会自动更新显示用户信息
            }} />
          ) : (
            <RegisterForm onSuccess={() => {
              // 注册成功后页面会自动更新显示用户信息
            }} />
          )}
          
          <div className="mt-6 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-600 text-center">
              <Link href="/" className="text-blue-600 hover:text-blue-800">
                ← 返回首页
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}