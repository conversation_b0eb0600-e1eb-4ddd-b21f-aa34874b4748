services:
  # SQLite 数据库管理工具 - Adminer
  adminer:
    image: adminer:latest
    container_name: kiro-editor-adminer
    restart: always
    ports:
      - "8083:8080"
    environment:
      ADMINER_DEFAULT_SERVER: sqlite
    volumes:
      - ./prisma/dev.db:/var/www/html/dev.db:ro  # 只读挂载数据库文件
    networks:
      - kiro-editor-network

  # SQLite Web 管理工具
  sqliteweb:
    image: coleifer/sqlite-web:latest
    container_name: kiro-editor-sqliteweb
    restart: always
    ports:
      - "8084:8080"
    volumes:
      - ./prisma:/data  # 挂载整个 prisma 目录
    command: sqlite_web /data/dev.db --host 0.0.0.0 --port 8080
    networks:
      - kiro-editor-network

networks:
  kiro-editor-network:
    driver: bridge