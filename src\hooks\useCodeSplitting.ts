import { useState, useEffect, useCallback, useRef } from 'react';
import { ComponentType } from 'react';

interface CodeSplittingOptions {
  /** 预加载延迟时间（毫秒） */
  preloadDelay?: number;
  /** 是否在鼠标悬停时预加载 */
  preloadOnHover?: boolean;
  /** 是否在组件可见时预加载 */
  preloadOnVisible?: boolean;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 重试延迟时间（毫秒） */
  retryDelay?: number;
}

interface CodeSplittingState<T> {
  component: ComponentType<T> | null;
  isLoading: boolean;
  isPreloading: boolean;
  error: Error | null;
  retryCount: number;
}

/**
 * 代码分割和懒加载 Hook
 * 提供智能的组件加载策略
 */
export function useCodeSplitting<T extends object>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  options: CodeSplittingOptions = {}
) {
  const {
    preloadDelay = 1000,
    preloadOnHover = true,
    preloadOnVisible = true,
    maxRetries = 3,
    retryDelay = 1000,
  } = options;

  const [state, setState] = useState<CodeSplittingState<T>>({
    component: null,
    isLoading: false,
    isPreloading: false,
    error: null,
    retryCount: 0,
  });

  const preloadTimerRef = useRef<NodeJS.Timeout>();
  const retryTimerRef = useRef<NodeJS.Timeout>();
  const intersectionObserverRef = useRef<IntersectionObserver>();

  // 加载组件
  const loadComponent = useCallback(async (isPreload = false) => {
    if (state.component || state.isLoading) {
      return;
    }

    setState(prev => ({
      ...prev,
      isLoading: !isPreload,
      isPreloading: isPreload,
      error: null,
    }));

    try {
      const module = await importFn();
      setState(prev => ({
        ...prev,
        component: module.default,
        isLoading: false,
        isPreloading: false,
        retryCount: 0,
      }));
    } catch (error) {
      const err = error instanceof Error ? error : new Error('组件加载失败');
      
      setState(prev => ({
        ...prev,
        error: err,
        isLoading: false,
        isPreloading: false,
        retryCount: prev.retryCount + 1,
      }));

      // 自动重试
      if (state.retryCount < maxRetries) {
        retryTimerRef.current = setTimeout(() => {
          loadComponent(isPreload);
        }, retryDelay * Math.pow(2, state.retryCount)); // 指数退避
      }
    }
  }, [importFn, state.component, state.isLoading, state.retryCount, maxRetries, retryDelay]);

  // 预加载组件
  const preloadComponent = useCallback(() => {
    if (preloadTimerRef.current) {
      clearTimeout(preloadTimerRef.current);
    }

    preloadTimerRef.current = setTimeout(() => {
      loadComponent(true);
    }, preloadDelay);
  }, [loadComponent, preloadDelay]);

  // 立即加载组件
  const loadComponentNow = useCallback(() => {
    loadComponent(false);
  }, [loadComponent]);

  // 重试加载
  const retryLoad = useCallback(() => {
    setState(prev => ({ ...prev, retryCount: 0, error: null }));
    loadComponent(false);
  }, [loadComponent]);

  // 鼠标悬停事件处理
  const handleMouseEnter = useCallback(() => {
    if (preloadOnHover && !state.component && !state.isLoading) {
      preloadComponent();
    }
  }, [preloadOnHover, state.component, state.isLoading, preloadComponent]);

  const handleMouseLeave = useCallback(() => {
    if (preloadTimerRef.current) {
      clearTimeout(preloadTimerRef.current);
    }
  }, []);

  // 可见性观察器
  const observeVisibility = useCallback((element: HTMLElement | null) => {
    if (!preloadOnVisible || !element) {
      return;
    }

    if (intersectionObserverRef.current) {
      intersectionObserverRef.current.disconnect();
    }

    intersectionObserverRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !state.component && !state.isLoading) {
            preloadComponent();
          }
        });
      },
      { threshold: 0.1 }
    );

    intersectionObserverRef.current.observe(element);
  }, [preloadOnVisible, state.component, state.isLoading, preloadComponent]);

  // 清理定时器和观察器
  useEffect(() => {
    return () => {
      if (preloadTimerRef.current) {
        clearTimeout(preloadTimerRef.current);
      }
      if (retryTimerRef.current) {
        clearTimeout(retryTimerRef.current);
      }
      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }
    };
  }, []);

  return {
    // 组件状态
    Component: state.component,
    isLoading: state.isLoading,
    isPreloading: state.isPreloading,
    error: state.error,
    retryCount: state.retryCount,
    
    // 控制方法
    loadComponent: loadComponentNow,
    preloadComponent,
    retryLoad,
    
    // 事件处理器
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
    observeVisibility,
    
    // 状态检查
    isReady: !!state.component,
    canRetry: state.error && state.retryCount < maxRetries,
    hasMaxRetries: state.retryCount >= maxRetries,
  };
}

/**
 * 批量代码分割管理器
 */
export class CodeSplittingManager {
  private static instance: CodeSplittingManager;
  private loadedModules = new Map<string, any>();
  private loadingPromises = new Map<string, Promise<any>>();
  private preloadQueue: string[] = [];
  private isProcessingQueue = false;

  static getInstance(): CodeSplittingManager {
    if (!CodeSplittingManager.instance) {
      CodeSplittingManager.instance = new CodeSplittingManager();
    }
    return CodeSplittingManager.instance;
  }

  /**
   * 注册模块
   */
  registerModule(
    key: string, 
    importFn: () => Promise<any>,
    priority: 'high' | 'medium' | 'low' = 'medium'
  ) {
    if (this.loadedModules.has(key)) {
      return;
    }

    // 根据优先级添加到预加载队列
    if (priority === 'high') {
      this.preloadQueue.unshift(key);
    } else {
      this.preloadQueue.push(key);
    }

    // 存储导入函数
    this.loadingPromises.set(key, importFn());
    
    // 开始处理队列
    this.processPreloadQueue();
  }

  /**
   * 获取模块
   */
  async getModule(key: string): Promise<any> {
    // 如果已加载，直接返回
    if (this.loadedModules.has(key)) {
      return this.loadedModules.get(key);
    }

    // 如果正在加载，等待加载完成
    const importFn = this.loadingPromises.get(key);
    if (!importFn) {
      throw new Error(`模块 ${key} 未注册`);
    }

    try {
      const module = await (importFn as any)();
      this.loadedModules.set(key, module);
      return module;
    } catch (error) {
      console.error(`加载模块 ${key} 失败:`, error);
      throw error;
    }
  }

  /**
   * 预加载模块
   */
  async preloadModule(key: string): Promise<void> {
    try {
      await this.getModule(key);
    } catch (error) {
      console.warn(`预加载模块 ${key} 失败:`, error);
    }
  }

  /**
   * 处理预加载队列
   */
  private async processPreloadQueue() {
    if (this.isProcessingQueue || this.preloadQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.preloadQueue.length > 0) {
      const key = this.preloadQueue.shift()!;
      
      // 如果模块已加载，跳过
      if (this.loadedModules.has(key)) {
        continue;
      }

      try {
        await this.preloadModule(key);
        // 添加小延迟，避免阻塞主线程
        await new Promise(resolve => setTimeout(resolve, 10));
      } catch (error) {
        console.warn(`预加载队列处理失败 ${key}:`, error);
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      loadedModules: this.loadedModules.size,
      pendingModules: this.preloadQueue.length,
      registeredModules: this.loadingPromises.size,
    };
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.loadedModules.clear();
    this.loadingPromises.clear();
    this.preloadQueue.length = 0;
  }
}

/**
 * 使用代码分割管理器的 Hook
 */
export function useCodeSplittingManager() {
  const manager = CodeSplittingManager.getInstance();
  
  const registerModule = useCallback((
    key: string,
    importFn: () => Promise<any>,
    priority?: 'high' | 'medium' | 'low'
  ) => {
    manager.registerModule(key, importFn, priority);
  }, [manager]);

  const getModule = useCallback((key: string) => {
    return manager.getModule(key);
  }, [manager]);

  const preloadModule = useCallback((key: string) => {
    return manager.preloadModule(key);
  }, [manager]);

  return {
    registerModule,
    getModule,
    preloadModule,
    getStats: () => manager.getStats(),
    clearCache: () => manager.clearCache(),
  };
}