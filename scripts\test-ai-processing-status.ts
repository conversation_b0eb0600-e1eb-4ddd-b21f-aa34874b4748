/**
 * AI 处理状态功能测试脚本
 */

import { aiProcessingManager } from '../src/lib/services/ai-processing-manager';
import type { AIProcessingContext } from '../src/types/ai-status.types';

/**
 * 测试单个处理流程
 */
async function testSingleProcessing() {
  console.log('🧪 测试单个处理流程...');

  const context: AIProcessingContext = {
    id: 'test-single-1',
    documentId: 'test-doc-1',
    userId: 'test-user-1',
    type: 'generate',
    input: '请帮我写一段关于人工智能的介绍',
    options: {
      showDetailedProgress: true,
      showTokenCount: true,
      showTimeEstimate: true,
      allowCancel: true,
      autoHideDelay: 3000,
    },
    startTime: new Date(),
    cancelToken: new AbortController(),
  };

  // 订阅状态变化
  const unsubscribe = aiProcessingManager.subscribe(context.id, (progress) => {
    console.log(`  📊 进度更新: ${progress.status} - ${Math.round(progress.overallProgress)}%`);
    if (progress.currentStage) {
      console.log(`     当前阶段: ${progress.currentStage}`);
    }
    if (progress.error) {
      console.log(`     ❌ 错误: ${progress.error}`);
    }
  });

  // 监听完成事件
  aiProcessingManager.once(`complete:${context.id}`, (event) => {
    console.log('  ✅ 处理完成');
    console.log(`     总耗时: ${Date.now() - context.startTime.getTime()}ms`);
  });

  try {
    // 开始处理
    await aiProcessingManager.startProcessing(context);
    
    // 模拟处理结果
    setTimeout(() => {
      aiProcessingManager.completeProcessing(context.id, {
        id: context.id,
        type: context.type,
        input: context.input,
        output: '人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。',
        provider: 'openai',
        model: 'gpt-4',
        tokensUsed: 150,
        processingTime: Date.now() - context.startTime.getTime(),
        createdAt: new Date(),
      });
    }, 6000); // 6秒后完成

    // 等待处理完成
    await new Promise(resolve => {
      aiProcessingManager.once(`complete:${context.id}`, resolve);
      // 超时保护
      setTimeout(resolve, 8000);
    });

  } catch (error) {
    console.error('  ❌ 处理失败:', error);
  } finally {
    unsubscribe();
  }

  console.log('✅ 单个处理流程测试完成\n');
}

/**
 * 测试多个并发处理
 */
async function testMultipleProcessing() {
  console.log('🧪 测试多个并发处理...');

  const tasks = [
    { type: 'generate', input: '生成一段关于机器学习的介绍' },
    { type: 'rewrite', input: '改写这段文字使其更正式' },
    { type: 'summarize', input: '总结这篇文章的主要内容' },
    { type: 'translate', input: '将这段中文翻译成英文' },
  ];

  const contexts: AIProcessingContext[] = tasks.map((task, index) => ({
    id: `test-multi-${index + 1}`,
    documentId: 'test-doc-1',
    userId: 'test-user-1',
    type: task.type,
    input: task.input,
    options: {
      showDetailedProgress: true,
      allowCancel: true,
      autoHideDelay: 2000,
    },
    startTime: new Date(),
    cancelToken: new AbortController(),
  }));

  // 订阅所有任务的状态变化
  const unsubscribes = contexts.map(context => 
    aiProcessingManager.subscribe(context.id, (progress) => {
      console.log(`  📊 [${context.id}] ${progress.status} - ${Math.round(progress.overallProgress)}%`);
    })
  );

  try {
    // 并发开始所有处理
    const processingPromises = contexts.map(async (context, index) => {
      // 稍微错开开始时间
      await new Promise(resolve => setTimeout(resolve, index * 200));
      
      await aiProcessingManager.startProcessing(context);
      
      // 模拟不同的处理时间
      const processingTime = 3000 + Math.random() * 4000;
      setTimeout(() => {
        aiProcessingManager.completeProcessing(context.id, {
          id: context.id,
          type: context.type,
          input: context.input,
          output: `${context.type} 处理结果: ${context.input}`,
          provider: 'openai',
          model: 'gpt-3.5-turbo',
          tokensUsed: Math.floor(50 + Math.random() * 100),
          processingTime: Date.now() - context.startTime.getTime(),
          createdAt: new Date(),
        });
      }, processingTime);
    });

    // 等待所有处理完成
    await Promise.all(processingPromises);

    // 等待所有完成事件
    await Promise.all(contexts.map(context => 
      new Promise(resolve => {
        aiProcessingManager.once(`complete:${context.id}`, resolve);
      })
    ));

  } catch (error) {
    console.error('  ❌ 多个处理失败:', error);
  } finally {
    unsubscribes.forEach(unsubscribe => unsubscribe());
  }

  console.log('✅ 多个并发处理测试完成\n');
}

/**
 * 测试处理取消
 */
async function testProcessingCancellation() {
  console.log('🧪 测试处理取消...');

  const context: AIProcessingContext = {
    id: 'test-cancel-1',
    documentId: 'test-doc-1',
    userId: 'test-user-1',
    type: 'generate',
    input: '这是一个会被取消的处理任务',
    options: {
      allowCancel: true,
      autoHideDelay: 1000,
    },
    startTime: new Date(),
    cancelToken: new AbortController(),
  };

  // 订阅状态变化
  const unsubscribe = aiProcessingManager.subscribe(context.id, (progress) => {
    console.log(`  📊 进度更新: ${progress.status} - ${Math.round(progress.overallProgress)}%`);
  });

  // 监听取消事件
  aiProcessingManager.once(`cancel:${context.id}`, () => {
    console.log('  🛑 处理已取消');
  });

  try {
    // 开始处理
    await aiProcessingManager.startProcessing(context);
    
    // 2秒后取消处理
    setTimeout(() => {
      console.log('  🛑 正在取消处理...');
      aiProcessingManager.cancelProcessing(context.id);
    }, 2000);

    // 等待取消事件
    await new Promise(resolve => {
      aiProcessingManager.once(`cancel:${context.id}`, resolve);
    });

  } catch (error) {
    console.error('  ❌ 取消测试失败:', error);
  } finally {
    unsubscribe();
  }

  console.log('✅ 处理取消测试完成\n');
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('🧪 测试错误处理...');

  const context: AIProcessingContext = {
    id: 'test-error-1',
    documentId: 'test-doc-1',
    userId: 'test-user-1',
    type: 'generate',
    input: '这是一个会出错的处理任务',
    options: {
      autoHideDelay: 2000,
    },
    startTime: new Date(),
    cancelToken: new AbortController(),
  };

  // 订阅状态变化
  const unsubscribe = aiProcessingManager.subscribe(context.id, (progress) => {
    console.log(`  📊 进度更新: ${progress.status} - ${Math.round(progress.overallProgress)}%`);
    if (progress.error) {
      console.log(`     ❌ 错误信息: ${progress.error}`);
    }
  });

  // 监听错误事件
  aiProcessingManager.once(`error:${context.id}`, (event) => {
    console.log('  ❌ 处理出错');
  });

  try {
    // 开始处理
    await aiProcessingManager.startProcessing(context);
    
    // 2秒后模拟错误
    setTimeout(() => {
      console.log('  ❌ 模拟处理错误...');
      aiProcessingManager.handleError(context.id, new Error('模拟的处理错误'));
    }, 2000);

    // 等待错误事件
    await new Promise(resolve => {
      aiProcessingManager.once(`error:${context.id}`, resolve);
    });

  } catch (error) {
    console.error('  ❌ 错误处理测试失败:', error);
  } finally {
    unsubscribe();
  }

  console.log('✅ 错误处理测试完成\n');
}

/**
 * 测试统计信息
 */
async function testStatistics() {
  console.log('🧪 测试统计信息...');

  // 获取统计信息
  const stats = aiProcessingManager.getStats();
  
  console.log('  📈 当前统计信息:');
  console.log(`     总处理数: ${stats.totalProcessed}`);
  console.log(`     成功数: ${stats.successCount}`);
  console.log(`     失败数: ${stats.errorCount}`);
  console.log(`     取消数: ${stats.cancelledCount}`);
  console.log(`     平均处理时间: ${stats.averageProcessingTime}ms`);
  console.log(`     总令牌数: ${stats.totalTokensUsed}`);
  console.log(`     按类型统计:`, stats.byType);
  console.log(`     按提供商统计:`, stats.byProvider);

  console.log('✅ 统计信息测试完成\n');
}

/**
 * 主测试函数
 */
async function main() {
  try {
    console.log('🚀 开始测试 AI 处理状态功能\n');

    // 测试单个处理流程
    await testSingleProcessing();

    // 测试多个并发处理
    await testMultipleProcessing();

    // 测试处理取消
    await testProcessingCancellation();

    // 测试错误处理
    await testErrorHandling();

    // 测试统计信息
    await testStatistics();

    console.log('✅ 所有测试完成！');
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  main();
}