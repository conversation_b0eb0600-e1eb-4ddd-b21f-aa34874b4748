/**
 * AI 服务测试脚本
 * 测试 AI 服务抽象层的基本功能
 */

import {
  AIServiceFactory,
  validateAIConfig,
  getRecommendedModels,
  estimateTokens,
  formatAIError
} from '../src/lib/services/ai';
import type { AIServiceConfig } from '../src/types/ai.types';

async function testAIServiceFactory() {
  console.log('🧪 测试 AI 服务工厂...\n');

  // 测试支持的提供商
  console.log('📋 支持的提供商:');
  const providers = AIServiceFactory.getSupportedProviders();
  providers.forEach(provider => {
    console.log(`  - ${provider}`);
  });
  console.log();

  // 测试默认配置
  console.log('⚙️ 默认配置:');
  providers.forEach(provider => {
    const defaultConfig = AIServiceFactory.getDefaultConfig(provider);
    console.log(`  ${provider}:`, JSON.stringify(defaultConfig, null, 2));
  });
  console.log();

  // 测试配置验证
  console.log('✅ 配置验证测试:');
  
  // 有效配置
  const validConfig: AIServiceConfig = {
    provider: 'openai',
    model: 'gpt-3.5-turbo',
    apiKey: 'test-key',
    maxTokens: 1000,
    temperature: 0.7
  };
  
  const validResult = validateAIConfig(validConfig);
  console.log(`  有效配置: ${validResult.valid ? '✅' : '❌'}`);
  if (!validResult.valid) {
    console.log(`    错误: ${validResult.errors.join(', ')}`);
  }

  // 无效配置
  const invalidConfig = {
    provider: 'openai',
    model: '',
    maxTokens: -1
  } as AIServiceConfig;
  
  const invalidResult = validateAIConfig(invalidConfig);
  console.log(`  无效配置: ${invalidResult.valid ? '❌' : '✅'}`);
  console.log(`    错误: ${invalidResult.errors.join(', ')}`);
  console.log();

  // 测试推荐模型
  console.log('🤖 推荐模型:');
  providers.forEach(provider => {
    const models = getRecommendedModels(provider);
    console.log(`  ${provider}: ${models.join(', ')}`);
  });
  console.log();

  // 测试令牌估算
  console.log('🔢 令牌估算测试:');
  const testTexts = [
    '你好，世界！',
    'Hello, world!',
    '这是一个测试文本，用来估算令牌数量。This is a test text for token estimation.',
    '人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。'
  ];
  
  testTexts.forEach(text => {
    const tokens = estimateTokens(text);
    console.log(`  "${text.substring(0, 30)}..." -> ${tokens} 令牌`);
  });
  console.log();

  // 测试服务创建（不实际调用 API）
  console.log('🏭 服务创建测试:');
  try {
    // OpenAI 服务
    const openaiConfig: AIServiceConfig = {
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      apiKey: 'test-key'
    };
    const openaiService = AIServiceFactory.createService(openaiConfig);
    console.log(`  ✅ OpenAI 服务创建成功: ${openaiService.provider}`);

    // Ollama 服务
    const ollamaConfig: AIServiceConfig = {
      provider: 'ollama',
      model: 'llama2',
      endpoint: 'http://localhost:11434'
    };
    const ollamaService = AIServiceFactory.createService(ollamaConfig);
    console.log(`  ✅ Ollama 服务创建成功: ${ollamaService.provider}`);

    // Gemini 服务
    const geminiConfig: AIServiceConfig = {
      provider: 'gemini',
      model: 'gemini-pro',
      apiKey: 'test-key'
    };
    const geminiService = AIServiceFactory.createService(geminiConfig);
    console.log(`  ✅ Gemini 服务创建成功: ${geminiService.provider}`);

    // 测试缓存
    const openaiService2 = AIServiceFactory.createService(openaiConfig);
    console.log(`  ✅ 服务缓存测试: ${openaiService === openaiService2 ? '相同实例' : '不同实例'}`);

  } catch (error) {
    console.log(`  ❌ 服务创建失败: ${formatAIError(error)}`);
  }
  console.log();

  console.log('🎉 AI 服务抽象层测试完成！');
}

// 运行测试
testAIServiceFactory().catch(error => {
  console.error('❌ 测试失败:', formatAIError(error));
  process.exit(1);
});