/**
 * AI 生成内容的样式定义
 * 用于区分 AI 生成的内容和用户输入的内容
 */

/* AI 生成内容容器 */
.ai-generated-content {
  /* 使用 CSS 变量以便主题切换 */
  --ai-primary-color: #3b82f6;
  --ai-secondary-color: #8b5cf6;
  --ai-background: linear-gradient(135deg, #eff6ff 0%, #f3e8ff 100%);
  --ai-border: #bfdbfe;
  --ai-text: #1e40af;
  --ai-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .ai-generated-content {
    --ai-primary-color: #60a5fa;
    --ai-secondary-color: #a78bfa;
    --ai-background: linear-gradient(135deg, #1e3a8a 0%, #581c87 100%);
    --ai-border: #3730a3;
    --ai-text: #bfdbfe;
    --ai-shadow: 0 10px 25px -5px rgba(96, 165, 250, 0.2), 0 4px 6px -2px rgba(96, 165, 250, 0.1);
  }
}

/* AI 生成文本的特殊样式 */
.ai-generated-text {
  position: relative;
  font-family: inherit;
  line-height: 1.6;
  color: var(--ai-text);
}

/* AI 生成文本的动画效果 */
.ai-generated-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: -4px;
  width: 2px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    var(--ai-primary-color),
    var(--ai-secondary-color)
  );
  border-radius: 1px;
  opacity: 0.6;
}

/* 打字机效果动画 */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* 应用打字机效果的类 */
.ai-typewriter-effect {
  overflow: hidden;
  white-space: nowrap;
  border-right: 2px solid var(--ai-primary-color);
  animation: 
    typewriter 2s steps(40, end),
    blink 0.75s step-end infinite;
}

/* AI 生成内容的高亮效果 */
.ai-content-highlight {
  background: linear-gradient(
    120deg,
    transparent 0%,
    rgba(59, 130, 246, 0.1) 50%,
    transparent 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 选项按钮样式 */
.ai-option-button {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.ai-option-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.ai-option-button:hover::before {
  left: 100%;
}

/* 操作按钮的特殊效果 */
.ai-action-button {
  position: relative;
  overflow: hidden;
  transform: translateY(0);
  transition: all 0.2s ease-in-out;
}

.ai-action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ai-action-button:active {
  transform: translateY(0);
}

/* 接受按钮的特殊样式 */
.ai-accept-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.ai-accept-button:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 拒绝按钮的特殊样式 */
.ai-reject-button {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  box-shadow: 0 2px 4px rgba(107, 114, 128, 0.2);
}

.ai-reject-button:hover {
  background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

/* 重新生成按钮的特殊样式 */
.ai-regenerate-button {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.ai-regenerate-button:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 加载状态的脉冲效果 */
.ai-loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 错误状态样式 */
.ai-error-state {
  background: linear-gradient(135deg, #fef2f2 0%, #fde8e8 100%);
  border-color: #fca5a5;
  color: #dc2626;
}

/* 成功状态样式 */
.ai-success-state {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-color: #86efac;
  color: #16a34a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-generated-content {
    min-width: 280px;
    max-width: 90vw;
    padding: 12px;
  }
  
  .ai-action-button {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .ai-option-button {
    padding: 4px 8px;
    font-size: 10px;
  }
}

/* 可访问性增强 */
@media (prefers-reduced-motion: reduce) {
  .ai-generated-content,
  .ai-action-button,
  .ai-option-button {
    animation: none;
    transition: none;
  }
  
  .ai-typewriter-effect {
    animation: none;
    border-right: none;
    white-space: normal;
    overflow: visible;
  }
  
  .ai-content-highlight {
    animation: none;
    background: rgba(59, 130, 246, 0.1);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .ai-generated-content {
    --ai-border: #000000;
    --ai-text: #000000;
    border-width: 2px;
  }
  
  .ai-generated-text::before {
    background: #000000;
    opacity: 1;
  }
}

/* 打印样式 */
@media print {
  .ai-generated-content {
    background: white !important;
    border: 1px solid #000000 !important;
    box-shadow: none !important;
    color: #000000 !important;
  }
  
  .ai-action-button,
  .ai-option-button {
    display: none !important;
  }
  
  .ai-generated-text::before {
    display: none !important;
  }
}