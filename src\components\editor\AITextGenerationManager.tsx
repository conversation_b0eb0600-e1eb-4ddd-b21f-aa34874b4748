'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Editor } from '@tiptap/react';
import { AIGeneratedContent, AIGenerationLoading } from './AIGeneratedContent';
import { 
  TextGenerationService, 
  TextGenerationRequest, 
  TextGenerationResult 
} from '@/lib/services/ai/text-generation-service';
import { aiServiceManager } from '@/lib/services/ai/ai-service-factory';
import { AIServiceError } from '@/types/ai.types';

/**
 * AI 文本生成管理器的属性
 */
interface AITextGenerationManagerProps {
  /** TipTap 编辑器实例 */
  editor: Editor;
  /** 是否启用 AI 功能 */
  enabled?: boolean;
}

/**
 * 生成状态
 */
interface GenerationState {
  /** 是否正在生成 */
  isGenerating: boolean;
  /** 是否显示结果 */
  showResult: boolean;
  /** 生成结果 */
  result?: TextGenerationResult;
  /** 错误信息 */
  error?: string;
  /** 生成位置 */
  position?: { top: number; left: number };
  /** 插入位置 */
  insertPosition?: number;
}

/**
 * AI 文本生成管理器组件
 * 负责管理 AI 文本生成的整个流程
 */
export function AITextGenerationManager({ 
  editor, 
  enabled = true 
}: AITextGenerationManagerProps) {
  const [state, setState] = useState<GenerationState>({
    isGenerating: false,
    showResult: false
  });
  
  const textGenerationService = useRef<TextGenerationService>();
  const abortController = useRef<AbortController>();

  // 初始化文本生成服务
  useEffect(() => {
    try {
      const aiService = aiServiceManager.getDefaultService();
      textGenerationService.current = new TextGenerationService(aiService);
    } catch (error) {
      console.warn('AI 服务未配置，AI 文本生成功能将不可用');
    }
  }, []);

  /**
   * 开始生成文本
   */
  const startGeneration = useCallback(async (
    type: TextGenerationRequest['type'],
    options: Partial<TextGenerationRequest> = {}
  ) => {
    if (!enabled || !textGenerationService.current || state.isGenerating) {
      return;
    }

    try {
      // 获取当前编辑器状态
      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);
      const beforeCursor = editor.state.doc.textBetween(0, from);
      const afterCursor = editor.state.doc.textBetween(to, editor.state.doc.content.size);
      const fullContent = editor.getText();

      // 计算显示位置
      const position = calculatePosition(editor, from);

      // 构建生成请求
      const request: TextGenerationRequest = {
        context: fullContent,
        selectedText: selectedText || undefined,
        beforeCursor,
        afterCursor,
        type,
        ...options
      };

      // 创建取消控制器
      abortController.current = new AbortController();

      // 更新状态为生成中
      setState({
        isGenerating: true,
        showResult: false,
        error: undefined,
        position,
        insertPosition: type === 'continue' ? from : to
      });

      // 执行生成
      const result = await textGenerationService.current.generateText(request);

      // 更新状态为显示结果
      setState(prev => ({
        ...prev,
        isGenerating: false,
        showResult: true,
        result,
        error: undefined
      }));

    } catch (error) {
      console.error('AI 文本生成失败:', error);
      
      let errorMessage = '生成失败，请稍后重试';
      if (error instanceof AIServiceError) {
        errorMessage = error.message;
      }

      setState(prev => ({
        ...prev,
        isGenerating: false,
        showResult: false,
        error: errorMessage
      }));
    }
  }, [editor, enabled, state.isGenerating]);

  /**
   * 接受生成的内容
   */
  const acceptContent = useCallback((content: string) => {
    if (!state.insertPosition === undefined) return;

    // 在指定位置插入内容
    const insertPos = state.insertPosition || editor.state.selection.from;
    
    editor
      .chain()
      .focus()
      .setTextSelection(insertPos)
      .insertContent(content)
      .run();

    // 清除状态
    setState({
      isGenerating: false,
      showResult: false
    });
  }, [editor, state.insertPosition]);

  /**
   * 拒绝生成的内容
   */
  const rejectContent = useCallback(() => {
    setState({
      isGenerating: false,
      showResult: false
    });
  }, []);

  /**
   * 重新生成内容
   */
  const regenerateContent = useCallback(async () => {
    if (!state.result) return;

    // 使用相同的参数重新生成
    const { type } = state.result;
    await startGeneration(type);
  }, [state.result, startGeneration]);

  /**
   * 取消生成
   */
  const cancelGeneration = useCallback(() => {
    if (abortController.current) {
      abortController.current.abort();
    }
    
    setState({
      isGenerating: false,
      showResult: false
    });
  }, []);

  /**
   * 续写文本
   */
  const continueText = useCallback((options?: Partial<TextGenerationRequest>) => {
    return startGeneration('continue', options);
  }, [startGeneration]);

  /**
   * 补全文本
   */
  const completeText = useCallback((options?: Partial<TextGenerationRequest>) => {
    return startGeneration('complete', options);
  }, [startGeneration]);

  /**
   * 扩展文本
   */
  const expandText = useCallback((options?: Partial<TextGenerationRequest>) => {
    return startGeneration('expand', options);
  }, [startGeneration]);

  // 暴露方法给父组件
  useEffect(() => {
    if (editor) {
      // 将方法添加到编辑器实例上，方便其他组件调用
      (editor as any).aiTextGeneration = {
        continueText,
        completeText,
        expandText,
        isGenerating: state.isGenerating
      };
    }
  }, [editor, continueText, completeText, expandText, state.isGenerating]);

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + G: 续写文本
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'G') {
        event.preventDefault();
        continueText();
      }
      
      // Ctrl/Cmd + Shift + E: 扩展文本
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'E') {
        event.preventDefault();
        const { from, to } = editor.state.selection;
        if (from !== to) { // 有选中文本时才扩展
          expandText();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [continueText, expandText, editor]);

  if (!enabled || !textGenerationService.current) {
    return null;
  }

  return (
    <>
      {/* 生成中的加载状态 */}
      <AIGenerationLoading
        visible={state.isGenerating}
        onCancel={cancelGeneration}
        message="AI 正在生成内容..."
        position={state.position}
      />

      {/* 生成结果显示 */}
      {state.result && (
        <AIGeneratedContent
          result={state.result}
          visible={state.showResult}
          onAccept={acceptContent}
          onReject={rejectContent}
          onRegenerate={regenerateContent}
          isRegenerating={state.isGenerating}
          position={state.position}
        />
      )}

      {/* 错误提示 */}
      {state.error && (
        <div
          className="
            bg-red-50 border border-red-200 rounded-lg p-3
            text-sm text-red-700
            animate-in slide-in-from-bottom-2 fade-in duration-200
          "
          style={state.position ? {
            position: 'absolute',
            top: state.position.top,
            left: state.position.left,
            zIndex: 50
          } : {}}
        >
          <div className="flex items-center justify-between">
            <span>{state.error}</span>
            <button
              onClick={() => setState(prev => ({ ...prev, error: undefined }))}
              className="ml-2 text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </>
  );
}

/**
 * 计算显示位置
 */
function calculatePosition(editor: Editor, position: number): { top: number; left: number } {
  try {
    // 获取编辑器 DOM 元素
    const editorElement = editor.view.dom;
    const editorRect = editorElement.getBoundingClientRect();
    
    // 获取光标位置
    const coords = editor.view.coordsAtPos(position);
    
    return {
      top: coords.top - editorRect.top + 30, // 在光标下方显示
      left: coords.left - editorRect.left
    };
  } catch (error) {
    // 如果无法获取精确位置，返回默认位置
    return { top: 100, left: 100 };
  }
}

/**
 * 扩展编辑器类型定义
 */
declare module '@tiptap/react' {
  interface Editor {
    aiTextGeneration?: {
      continueText: (options?: Partial<TextGenerationRequest>) => Promise<void>;
      completeText: (options?: Partial<TextGenerationRequest>) => Promise<void>;
      expandText: (options?: Partial<TextGenerationRequest>) => Promise<void>;
      isGenerating: boolean;
    };
  }
}

export default AITextGenerationManager;