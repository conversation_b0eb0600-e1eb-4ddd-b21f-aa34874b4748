'use client';

/**
 * 环境变量配置状态显示组件
 * 显示当前的环境变量配置状态
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/Button';
import { RefreshCw, Eye, EyeOff } from 'lucide-react';

interface EnvStatus {
  ai: {
    hasOpenAIKey: boolean;
    hasGeminiKey: boolean;
    hasProxy: boolean;
    proxyUrl: string;
  };
  database: {
    url: string;
  };
  app: {
    environment: string;
    authUrl: string;
  };
}

export function EnvConfigStatus() {
  const [envStatus, setEnvStatus] = useState<EnvStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [showDetails, setShowDetails] = useState(false);

  const fetchEnvStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/env-status');
      if (response.ok) {
        const data = await response.json();
        setEnvStatus(data);
      }
    } catch (error) {
      console.error('获取环境变量状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEnvStatus();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            加载环境配置...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (!envStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>环境配置状态</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">无法获取环境配置状态</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          环境配置状态
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? (
                <>
                  <EyeOff className="w-4 h-4 mr-1" />
                  隐藏详情
                </>
              ) : (
                <>
                  <Eye className="w-4 h-4 mr-1" />
                  显示详情
                </>
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchEnvStatus}
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* AI 配置状态 */}
        <div>
          <h4 className="font-medium mb-2">AI 服务配置</h4>
          <div className="flex flex-wrap gap-2">
            <Badge variant={envStatus.ai.hasOpenAIKey ? 'default' : 'secondary'}>
              OpenAI API Key: {envStatus.ai.hasOpenAIKey ? '已配置' : '未配置'}
            </Badge>
            <Badge variant={envStatus.ai.hasGeminiKey ? 'default' : 'secondary'}>
              Gemini API Key: {envStatus.ai.hasGeminiKey ? '已配置' : '未配置'}
            </Badge>
            <Badge variant={envStatus.ai.hasProxy ? 'default' : 'secondary'}>
              HTTP 代理: {envStatus.ai.proxyUrl}
            </Badge>
          </div>
        </div>

        {showDetails && (
          <>
            {/* 数据库配置 */}
            <div>
              <h4 className="font-medium mb-2">数据库配置</h4>
              <div className="text-sm text-muted-foreground">
                <p>数据库 URL: {envStatus.database.url}</p>
              </div>
            </div>

            {/* 应用配置 */}
            <div>
              <h4 className="font-medium mb-2">应用配置</h4>
              <div className="flex flex-wrap gap-2">
                <Badge variant={envStatus.app.environment === 'production' ? 'destructive' : 'default'}>
                  环境: {envStatus.app.environment}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground mt-2">
                <p>认证 URL: {envStatus.app.authUrl}</p>
              </div>
            </div>
          </>
        )}

        {/* 使用说明 */}
        <div className="mt-4 p-3 bg-muted rounded-lg">
          <h5 className="font-medium text-sm mb-1">配置优先级</h5>
          <p className="text-xs text-muted-foreground">
            页面配置 &gt; 环境变量 (.env) &gt; 默认值
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            在 AI 配置页面中设置的值会覆盖环境变量配置
          </p>
        </div>
      </CardContent>
    </Card>
  );
}