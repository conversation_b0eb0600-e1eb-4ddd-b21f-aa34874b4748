'use client';

/**
 * 完整的 AI 配置管理页面
 * 包含所有配置功能，包括代理设置
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AIConfigList } from '@/components/ai/AIConfigList';
import { AIConfigForm } from '@/components/ai/AIConfigForm';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, AlertCircle, Settings, Shield, Cloud, Zap, CheckCircle } from 'lucide-react';

import { useAIConfig } from '@/hooks/useAIConfig';
import type { AIConfigData, FullAIConfigData } from '@/hooks/useAIConfig';

type ViewMode = 'list' | 'add' | 'edit';

export default function AIConfigCompletePage() {
  const router = useRouter();
  const {
    configs,
    loading,
    error,
    createConfig,
    updateConfig,
    deleteConfig,
    setDefaultConfig,
    testConnection,
    getConfig,
    clearError
  } = useAIConfig();

  const [activeTab, setActiveTab] = useState('configs');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [editingConfig, setEditingConfig] = useState<FullAIConfigData | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  // 代理配置状态
  const [proxyConfig, setProxyConfig] = useState({
    enabled: false,
    host: '',
    port: '',
    username: '',
    password: '',
    protocol: 'http' as 'http' | 'https' | 'socks5'
  });

  /**
   * 处理添加配置
   */
  const handleAdd = () => {
    setViewMode('add');
    setEditingConfig(null);
    setFormError(null);
    clearError();
  };

  /**
   * 处理编辑配置
   */
  const handleEdit = async (config: AIConfigData) => {
    try {
      const fullConfig = await getConfig(config.id);
      if (fullConfig) {
        setEditingConfig(fullConfig);
        setViewMode('edit');
        setFormError(null);
        clearError();
      } else {
        setFormError('获取配置详情失败');
      }
    } catch (err) {
      setFormError('获取配置详情失败');
    }
  };

  /**
   * 处理表单提交
   */
  const handleFormSubmit = async (data: Omit<FullAIConfigData, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> => {
    try {
      let success = false;

      if (viewMode === 'add') {
        success = await createConfig(data);
      } else if (viewMode === 'edit' && editingConfig) {
        success = await updateConfig(editingConfig.id, data);
      }

      if (success) {
        setViewMode('list');
        setEditingConfig(null);
        setFormError(null);
        return true;
      }

      return false;
    } catch (err) {
      setFormError(err instanceof Error ? err.message : '操作失败');
      return false;
    }
  };

  /**
   * 处理取消操作
   */
  const handleCancel = () => {
    setViewMode('list');
    setEditingConfig(null);
    setFormError(null);
    clearError();
  };

  /**
   * 处理连接测试（从列表页面）
   */
  const handleTestFromList = async (config: AIConfigData) => {
    try {
      const fullConfig = await getConfig(config.id);
      if (fullConfig) {
        const result = await testConnection({
          provider: fullConfig.provider as any,
          apiKey: fullConfig.apiKey,
          endpoint: fullConfig.endpoint,
          model: fullConfig.model,
          maxTokens: fullConfig.maxTokens,
          temperature: fullConfig.temperature
        });

        // 显示测试结果
        if (result.success) {
          alert(`连接测试成功！\n响应时间: ${result.responseTime}ms`);
        } else {
          alert(`连接测试失败：\n${result.error}`);
        }
      }
    } catch (err) {
      alert('连接测试失败：获取配置详情失败');
    }
  };

  /**
   * 处理代理配置保存
   */
  const handleProxySave = async () => {
    try {
      // 这里应该调用 API 保存代理配置
      // 暂时使用 localStorage 模拟
      localStorage.setItem('ai-proxy-config', JSON.stringify(proxyConfig));
      alert('代理配置已保存');
    } catch (err) {
      alert('保存代理配置失败');
    }
  };

  /**
   * 加载代理配置
   */
  useEffect(() => {
    try {
      const saved = localStorage.getItem('ai-proxy-config');
      if (saved) {
        setProxyConfig(JSON.parse(saved));
      }
    } catch (err) {
      console.error('加载代理配置失败:', err);
    }
  }, []);

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* 页面头部 */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">AI 配置中心</h1>
            <p className="text-gray-600">管理您的 AI 服务配置、代理设置和高级选项</p>
          </div>
        </div>

        {viewMode !== 'list' && (
          <Button
            variant="ghost"
            onClick={handleCancel}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回列表
          </Button>
        )}
      </div>

      {/* 错误提示 */}
      {(error || formError) && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || formError}
          </AlertDescription>
        </Alert>
      )}

      {/* 主要内容 */}
      {viewMode === 'list' ? (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="configs" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              AI 配置
            </TabsTrigger>
            <TabsTrigger value="proxy" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              代理设置
            </TabsTrigger>
            <TabsTrigger value="sync" className="flex items-center gap-2">
              <Cloud className="h-4 w-4" />
              云端同步
            </TabsTrigger>
            <TabsTrigger value="advanced" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              高级选项
            </TabsTrigger>
          </TabsList>

          <TabsContent value="configs">
            <AIConfigList
              configs={configs}
              loading={loading}
              error={error}
              onAdd={handleAdd}
              onEdit={handleEdit}
              onDelete={deleteConfig}
              onSetDefault={setDefaultConfig}
              onTestConnection={handleTestFromList}
            />
          </TabsContent>

          <TabsContent value="proxy">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  网络代理配置
                </CardTitle>
                <CardDescription>
                  配置代理服务器以访问 OpenAI 等需要代理的 AI 服务
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 启用代理 */}
                <div className="flex items-center space-x-2">
                  <input
                    id="proxy-enabled"
                    type="checkbox"
                    checked={proxyConfig.enabled}
                    onChange={(e) => setProxyConfig(prev => ({ ...prev, enabled: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="proxy-enabled" className="font-medium">
                    启用代理服务器
                  </label>
                </div>

                {proxyConfig.enabled && (
                  <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                    {/* 代理协议 */}
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">
                        代理协议
                      </label>
                      <select
                        value={proxyConfig.protocol}
                        onChange={(e) => setProxyConfig(prev => ({
                          ...prev,
                          protocol: e.target.value as 'http' | 'https' | 'socks5'
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="http">HTTP</option>
                        <option value="https">HTTPS</option>
                        <option value="socks5">SOCKS5</option>
                      </select>
                    </div>

                    {/* 代理地址和端口 */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          代理地址
                        </label>
                        <input
                          type="text"
                          value={proxyConfig.host}
                          onChange={(e) => setProxyConfig(prev => ({ ...prev, host: e.target.value }))}
                          placeholder="127.0.0.1"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          端口
                        </label>
                        <input
                          type="text"
                          value={proxyConfig.port}
                          onChange={(e) => setProxyConfig(prev => ({ ...prev, port: e.target.value }))}
                          placeholder="7890"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>

                    {/* 认证信息 */}
                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900">认证信息（可选）</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            用户名
                          </label>
                          <input
                            type="text"
                            value={proxyConfig.username}
                            onChange={(e) => setProxyConfig(prev => ({ ...prev, username: e.target.value }))}
                            placeholder="可选"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            密码
                          </label>
                          <input
                            type="password"
                            value={proxyConfig.password}
                            onChange={(e) => setProxyConfig(prev => ({ ...prev, password: e.target.value }))}
                            placeholder="可选"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                      </div>
                    </div>

                    {/* 测试连接 */}
                    <div className="flex items-center gap-4">
                      <Button
                        onClick={() => {
                          // 测试代理连接
                          alert('代理连接测试功能开发中...');
                        }}
                        variant="outline"
                      >
                        测试连接
                      </Button>
                      <Button onClick={handleProxySave}>
                        保存配置
                      </Button>
                    </div>
                  </div>
                )}

                {/* 常用代理配置说明 */}
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">常用代理配置</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <p><strong>Clash:</strong> HTTP: 127.0.0.1:7890, SOCKS5: 127.0.0.1:7891</p>
                    <p><strong>V2Ray:</strong> HTTP: 127.0.0.1:10809, SOCKS5: 127.0.0.1:10808</p>
                    <p><strong>Shadowsocks:</strong> SOCKS5: 127.0.0.1:1080</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sync">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Cloud className="h-5 w-5" />
                  云端同步
                </CardTitle>
                <CardDescription>
                  将您的 AI 配置同步到云端，在多个设备间共享
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-900">同步已启用</span>
                  </div>
                  <p className="text-sm text-green-700">
                    您的配置已自动同步到云端，最后同步时间：{new Date().toLocaleString()}
                  </p>
                </div>

                <div className="space-y-3">
                  <Button variant="outline" className="w-full">
                    立即同步
                  </Button>
                  <Button variant="outline" className="w-full">
                    查看同步历史
                  </Button>
                  <Button variant="outline" className="w-full">
                    管理同步设备
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  高级选项
                </CardTitle>
                <CardDescription>
                  高级配置和实验性功能
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 请求超时 */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    请求超时时间（秒）
                  </label>
                  <input
                    type="number"
                    defaultValue={30}
                    min={5}
                    max={300}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* 重试次数 */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    失败重试次数
                  </label>
                  <input
                    type="number"
                    defaultValue={3}
                    min={0}
                    max={10}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* 并发限制 */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    并发请求限制
                  </label>
                  <input
                    type="number"
                    defaultValue={5}
                    min={1}
                    max={20}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* 实验性功能 */}
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">实验性功能</h4>

                  <div className="flex items-center space-x-2">
                    <input
                      id="enable-streaming"
                      type="checkbox"
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="enable-streaming">
                      启用流式响应（实时显示生成内容）
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      id="enable-cache"
                      type="checkbox"
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="enable-cache">
                      启用响应缓存（提高重复请求速度）
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      id="enable-analytics"
                      type="checkbox"
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="enable-analytics">
                      启用使用分析（帮助改进服务）
                    </label>
                  </div>
                </div>

                <Button className="w-full">
                  保存高级设置
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      ) : (
        <div className="flex justify-center">
          <AIConfigForm
            initialData={editingConfig || undefined}
            onSubmit={handleFormSubmit}
            onTestConnection={testConnection}
            onCancel={handleCancel}
            isEditing={viewMode === 'edit'}
          />
        </div>
      )}
    </div>
  );
}