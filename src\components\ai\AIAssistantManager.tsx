'use client';

import React, { useCallback, useEffect } from 'react';
import { AIAssistantPanel, AIAssistantToggle } from './AIAssistantPanel';
import { useAIAssistant, useAIAssistantResponsive } from '@/hooks/useAIAssistant';
import type { AIAssistantAction, AIAssistantActionData } from '@/hooks/useAIAssistant';

/**
 * AI 助手管理器属性
 */
interface AIAssistantManagerProps {
  /** 面板位置 */
  position?: 'left' | 'right';
  /** 面板宽度 */
  width?: number;
  /** 初始打开状态 */
  defaultOpen?: boolean;
  /** 自定义类名 */
  className?: string;
  /** AI 操作处理器 */
  onAIAction?: (action: AIAssistantAction, data?: AIAssistantActionData) => Promise<void>;
  /** 文本选择变化回调 */
  onTextSelectionChange?: (text: string) => void;
  /** 面板状态变化回调 */
  onPanelStateChange?: (isOpen: boolean) => void;
}

/**
 * AI 助手管理器组件
 * 集成了面板状态管理、响应式检测和操作处理
 */
export function AIAssistantManager({
  position = 'right',
  width = 320,
  defaultOpen = false,
  className = '',
  onAIAction,
  onTextSelectionChange,
  onPanelStateChange
}: AIAssistantManagerProps) {
  // 使用 AI 助手 Hook
  const {
    state,
    togglePanel,
    openPanel,
    closePanel,
    setSelectedText,
    executeAction,
    isActionAvailable
  } = useAIAssistant({
    defaultOpen,
    persistState: true,
    storageKey: 'ai-assistant-manager-state'
  });

  // 响应式检测
  const { isMobile } = useAIAssistantResponsive();

  /**
   * 处理 AI 操作
   */
  const handleAIAction = useCallback(async (actionId: string, data?: any) => {
    try {
      const action = actionId as AIAssistantAction;
      await executeAction(action, data, onAIAction);
    } catch (error) {
      console.error('AI action failed:', error);
      // 这里可以添加错误提示
    }
  }, [executeAction, onAIAction]);

  /**
   * 处理文本选择变化
   */
  const handleTextSelectionChange = useCallback((text: string) => {
    setSelectedText(text);
    if (onTextSelectionChange) {
      onTextSelectionChange(text);
    }
  }, [setSelectedText, onTextSelectionChange]);

  /**
   * 监听面板状态变化
   */
  useEffect(() => {
    if (onPanelStateChange) {
      onPanelStateChange(state.isOpen);
    }
  }, [state.isOpen, onPanelStateChange]);

  /**
   * 监听文档中的文本选择
   */
  useEffect(() => {
    const handleSelectionChange = () => {
      const selection = window.getSelection();
      const selectedText = selection?.toString().trim() || '';
      
      if (selectedText !== state.selectedText) {
        handleTextSelectionChange(selectedText);
      }
    };

    // 监听选择变化
    document.addEventListener('selectionchange', handleSelectionChange);
    
    // 监听鼠标释放（用于处理拖拽选择）
    document.addEventListener('mouseup', handleSelectionChange);
    
    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('mouseup', handleSelectionChange);
    };
  }, [state.selectedText, handleTextSelectionChange]);

  return (
    <>
      {/* AI 助手面板 */}
      <AIAssistantPanel
        isOpen={state.isOpen}
        onToggle={togglePanel}
        position={position}
        width={width}
        isMobile={isMobile}
        className={className}
        onAIAction={handleAIAction}
        selectedText={state.selectedText}
        isProcessing={state.isProcessing}
        processingStatus={state.processingStatus}
      />
      
      {/* 切换按钮 */}
      <AIAssistantToggle
        isOpen={state.isOpen}
        onToggle={togglePanel}
        position={position}
      />
    </>
  );
}

/**
 * AI 助手管理器的简化版本
 * 用于只需要基本功能的场景
 */
interface SimpleAIAssistantProps {
  /** 是否显示 */
  show?: boolean;
  /** AI 操作处理器 */
  onAIAction?: (action: AIAssistantAction, data?: AIAssistantActionData) => Promise<void>;
}

export function SimpleAIAssistant({
  show = true,
  onAIAction
}: SimpleAIAssistantProps) {
  if (!show) return null;

  return (
    <AIAssistantManager
      position="right"
      width={300}
      defaultOpen={false}
      onAIAction={onAIAction}
    />
  );
}

/**
 * 用于编辑器集成的 AI 助手组件
 */
interface EditorAIAssistantProps {
  /** 编辑器实例或内容 */
  editor?: any;
  /** 当前文档内容 */
  content?: string;
  /** AI 操作处理器 */
  onAIAction?: (action: AIAssistantAction, data?: AIAssistantActionData) => Promise<void>;
  /** 内容更新回调 */
  onContentUpdate?: (content: string) => void;
}

export function EditorAIAssistant({
  editor,
  content,
  onAIAction,
  onContentUpdate
}: EditorAIAssistantProps) {
  /**
   * 处理编辑器相关的 AI 操作
   */
  const handleEditorAIAction = useCallback(async (actionId: string, data?: any) => {
    const action = actionId as AIAssistantAction;
    
    // 如果有编辑器实例，可以直接操作编辑器
    if (editor) {
      const selectedText = editor.state.doc.textBetween(
        editor.state.selection.from,
        editor.state.selection.to
      );
      
      // 更新数据中的文本信息
      const enhancedData = {
        ...data,
        text: data?.text || selectedText,
        editorContent: content,
        selection: {
          from: editor.state.selection.from,
          to: editor.state.selection.to
        }
      };
      
      if (onAIAction) {
        await onAIAction(action, enhancedData);
      }
    } else {
      // 没有编辑器实例时的处理
      if (onAIAction) {
        await onAIAction(action, data);
      }
    }
  }, [editor, content, onAIAction]);

  return (
    <AIAssistantManager
      position="right"
      width={320}
      defaultOpen={false}
      onAIAction={handleEditorAIAction}
    />
  );
}

export default AIAssistantManager;