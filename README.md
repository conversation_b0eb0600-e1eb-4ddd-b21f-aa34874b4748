# AI Document Editor

A modern document editor built with Next.js 14, featuring AI-powered writing assistance and intelligent document management.

## Features

- 🚀 Modern document editing with TipTap editor
- 🤖 AI-powered writing assistance (OpenAI, Ollama, Gemini)
- 📁 Hierarchical folder and document management
- 🔄 Real-time sync between devices
- 📱 Responsive design for all devices
- ⚡ Slash commands for quick formatting
- 🎨 Beautiful, modern UI with Tailwind CSS

## Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Editor**: TipTap (ProseMirror-based)
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Database**: SQLite (dev) / PostgreSQL (prod)
- **ORM**: Prisma
- **Authentication**: NextAuth.js
- **AI Integration**: OpenAI, Ollama, Gemini APIs

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Git

### Installation

1. Clone the repository:
\`\`\`bash
git clone <repository-url>
cd nextjs-document-editor
\`\`\`

2. Install dependencies:
\`\`\`bash
npm install
\`\`\`

3. Set up environment variables:
\`\`\`bash
cp .env.local.example .env.local
\`\`\`

4. Initialize the database:
\`\`\`bash
npm run db:generate
npm run db:push
\`\`\`

5. Start the development server:
\`\`\`bash
npm run dev
\`\`\`

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Development

### Available Scripts

- \`npm run dev\` - Start development server
- \`npm run build\` - Build for production
- \`npm run start\` - Start production server
- \`npm run lint\` - Run ESLint
- \`npm run type-check\` - Run TypeScript type checking
- \`npm run db:generate\` - Generate Prisma client
- \`npm run db:push\` - Push schema to database
- \`npm run db:migrate\` - Run database migrations
- \`npm run db:studio\` - Open Prisma Studio

### Project Structure

\`\`\`
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── api/               # API routes
│   ├── dashboard/         # Dashboard pages
│   ├── editor/            # Editor pages
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── auth/              # Authentication components
│   ├── editor/            # Editor components
│   ├── ai/                # AI feature components
│   ├── file-manager/      # File management components
│   └── ui/                # Reusable UI components
├── lib/                   # Core libraries
│   ├── ai/               # AI service integrations
│   ├── auth/             # Authentication utilities
│   ├── storage/          # Storage services
│   └── utils/            # Utility functions
├── stores/               # Zustand stores
├── types/                # TypeScript type definitions
└── hooks/                # Custom React hooks
\`\`\`

## Configuration

### AI Services

The application supports multiple AI providers:

1. **OpenAI**: Set \`OPENAI_API_KEY\` in your environment
2. **Ollama**: Set \`OLLAMA_ENDPOINT\` (default: http://localhost:11434)
3. **Gemini**: Set \`GEMINI_API_KEY\` in your environment

Users can also configure these services directly in the application settings.

### Database

- Development: SQLite (file-based)
- Production: PostgreSQL (recommended)

Update the \`DATABASE_URL\` in your \`.env.local\` file accordingly.

## Contributing

1. Fork the repository
2. Create a feature branch: \`git checkout -b feature/amazing-feature\`
3. Commit your changes: \`git commit -m 'Add amazing feature'\`
4. Push to the branch: \`git push origin feature/amazing-feature\`
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [TipTap](https://tiptap.dev/) for the excellent editor framework
- [Next.js](https://nextjs.org/) for the amazing React framework
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Prisma](https://prisma.io/) for the database toolkit