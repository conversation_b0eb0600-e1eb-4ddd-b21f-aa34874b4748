'use client';

import React from 'react';
import { SyncProgress as SyncProgressType } from '@/types/sync';
import { 
  RefreshCw, 
  Upload, 
  Download, 
  GitMerge, 
  CheckCircle,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SyncProgressProps {
  progress: SyncProgressType;
  className?: string;
}

/**
 * 同步进度显示组件
 */
export function SyncProgress({ progress, className }: SyncProgressProps) {
  const getPhaseInfo = (phase: string) => {
    switch (phase) {
      case 'preparing':
        return {
          icon: Loader2,
          text: '准备同步...',
          color: 'text-blue-500',
        };
      case 'uploading':
        return {
          icon: Upload,
          text: '上传文档...',
          color: 'text-blue-500',
        };
      case 'downloading':
        return {
          icon: Download,
          text: '下载更新...',
          color: 'text-green-500',
        };
      case 'resolving':
        return {
          icon: GitMerge,
          text: '解决冲突...',
          color: 'text-yellow-500',
        };
      case 'completed':
        return {
          icon: CheckCircle,
          text: '同步完成',
          color: 'text-green-500',
        };
      default:
        return {
          icon: RefreshCw,
          text: '同步中...',
          color: 'text-blue-500',
        };
    }
  };

  const phaseInfo = getPhaseInfo(progress.phase);
  const Icon = phaseInfo.icon;
  const percentage = progress.total > 0 ? (progress.completed / progress.total) * 100 : 0;

  return (
    <div className={cn('bg-white border rounded-lg p-4 shadow-sm', className)}>
      <div className="space-y-3">
        {/* 阶段信息 */}
        <div className="flex items-center gap-3">
          <Icon 
            className={cn(
              'h-5 w-5',
              phaseInfo.color,
              (progress.phase === 'preparing' || progress.phase === 'uploading') && 'animate-spin'
            )} 
          />
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <span className="font-medium text-gray-900">
                {phaseInfo.text}
              </span>
              <span className="text-sm text-gray-500">
                {progress.completed} / {progress.total}
              </span>
            </div>
            {progress.current && (
              <div className="text-sm text-gray-600 mt-1">
                当前: {progress.current}
              </div>
            )}
          </div>
        </div>

        {/* 进度条 */}
        <div className="space-y-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={cn(
                'h-2 rounded-full transition-all duration-300',
                progress.phase === 'completed' ? 'bg-green-500' : 'bg-blue-500'
              )}
              style={{ width: `${percentage}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500">
            <span>0%</span>
            <span>{Math.round(percentage)}%</span>
            <span>100%</span>
          </div>
        </div>

        {/* 详细信息 */}
        {progress.phase !== 'preparing' && (
          <div className="text-xs text-gray-500 space-y-1">
            <div>已完成: {progress.completed} 项</div>
            <div>剩余: {progress.total - progress.completed} 项</div>
            {percentage > 0 && (
              <div>
                预计完成时间: {
                  Math.round((progress.total - progress.completed) / (progress.completed / 1)) 
                } 秒
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * 简化版同步进度条
 */
interface SyncProgressBarProps {
  progress: SyncProgressType;
  className?: string;
  showText?: boolean;
}

export function SyncProgressBar({ 
  progress, 
  className, 
  showText = true 
}: SyncProgressBarProps) {
  const percentage = progress.total > 0 ? (progress.completed / progress.total) * 100 : 0;
  const phaseInfo = getPhaseInfo(progress.phase);

  return (
    <div className={cn('space-y-2', className)}>
      {showText && (
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-700">{phaseInfo.text}</span>
          <span className="text-gray-500">
            {progress.completed} / {progress.total}
          </span>
        </div>
      )}
      <div className="w-full bg-gray-200 rounded-full h-1.5">
        <div
          className={cn(
            'h-1.5 rounded-full transition-all duration-300',
            progress.phase === 'completed' ? 'bg-green-500' : 'bg-blue-500'
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );

  function getPhaseInfo(phase: string) {
    switch (phase) {
      case 'preparing':
        return { text: '准备中...' };
      case 'uploading':
        return { text: '上传中...' };
      case 'downloading':
        return { text: '下载中...' };
      case 'resolving':
        return { text: '解决冲突中...' };
      case 'completed':
        return { text: '完成' };
      default:
        return { text: '同步中...' };
    }
  }
}

/**
 * 同步进度通知组件
 */
interface SyncProgressNotificationProps {
  progress: SyncProgressType;
  onClose?: () => void;
  className?: string;
}

export function SyncProgressNotification({ 
  progress, 
  onClose, 
  className 
}: SyncProgressNotificationProps) {
  const phaseInfo = getPhaseInfo(progress.phase);
  const Icon = phaseInfo.icon;

  return (
    <div className={cn(
      'fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 min-w-80',
      className
    )}>
      <div className="flex items-start gap-3">
        <Icon 
          className={cn(
            'h-5 w-5 mt-0.5',
            phaseInfo.color,
            (progress.phase === 'preparing' || progress.phase === 'uploading') && 'animate-spin'
          )} 
        />
        <div className="flex-1 space-y-2">
          <div className="flex items-center justify-between">
            <span className="font-medium text-gray-900">
              {phaseInfo.text}
            </span>
            {onClose && progress.phase === 'completed' && (
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            )}
          </div>
          
          <SyncProgressBar 
            progress={progress} 
            showText={false}
          />
          
          <div className="text-xs text-gray-500">
            {progress.completed} / {progress.total} 项已完成
          </div>
        </div>
      </div>
    </div>
  );

  function getPhaseInfo(phase: string) {
    switch (phase) {
      case 'preparing':
        return {
          icon: Loader2,
          text: '准备同步',
          color: 'text-blue-500',
        };
      case 'uploading':
        return {
          icon: Upload,
          text: '上传文档',
          color: 'text-blue-500',
        };
      case 'downloading':
        return {
          icon: Download,
          text: '下载更新',
          color: 'text-green-500',
        };
      case 'resolving':
        return {
          icon: GitMerge,
          text: '解决冲突',
          color: 'text-yellow-500',
        };
      case 'completed':
        return {
          icon: CheckCircle,
          text: '同步完成',
          color: 'text-green-500',
        };
      default:
        return {
          icon: RefreshCw,
          text: '同步中',
          color: 'text-blue-500',
        };
    }
  }
}