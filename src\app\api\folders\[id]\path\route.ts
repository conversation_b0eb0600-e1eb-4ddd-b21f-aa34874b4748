import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { FolderService } from '@/lib/services/folder-service';

/**
 * GET /api/folders/[id]/path - 获取文件夹的完整路径
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    // 验证文件夹是否存在且属于当前用户
    const folder = await FolderService.validateFolderAccess(params.id, session.user.id);
    
    if (!folder) {
      return NextResponse.json({ error: '文件夹未找到' }, { status: 404 });
    }

    // 获取完整路径
    const path = await FolderService.getFolderPath(params.id);
    
    // 获取文件夹统计信息
    const stats = await FolderService.getFolderStats(params.id, session.user.id);

    return NextResponse.json({
      folder: {
        id: folder.id,
        name: folder.name,
      },
      path,
      pathString: path.join(' / '),
      breadcrumbs: path.map((name, index) => ({
        name,
        isLast: index === path.length - 1,
      })),
      stats,
    });
  } catch (error) {
    console.error('获取文件夹路径失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}