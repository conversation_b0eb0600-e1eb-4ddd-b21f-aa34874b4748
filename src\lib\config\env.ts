/**
 * 环境变量配置管理
 * 提供统一的环境变量访问接口
 */

/**
 * AI 服务相关的环境变量配置
 */
export const aiEnvConfig = {
  // API 密钥
  openaiApiKey: process.env.OPENAI_API_KEY || '',
  geminiApiKey: process.env.GEMINI_API_KEY || '',

  // 代理配置
  httpProxy: process.env.HTTP_PROXY || '',
  httpsProxy: process.env.HTTPS_PROXY || '',

  // 服务端点
  openaiEndpoint: process.env.OPENAI_ENDPOINT || '',
  ollamaEndpoint: process.env.OLLAMA_ENDPOINT || 'http://localhost:11454',
  geminiEndpoint:
    process.env.GEMINI_ENDPOINT ||
    'https://generativelanguage.googleapis.com/v1beta',

  // 获取代理地址（优先 HTTPS_PROXY，然后 HTTP_PROXY）
  getProxyUrl(): string {
    return this.httpsProxy || this.httpProxy || '';
  },

  // 检查是否配置了代理
  hasProxy(): boolean {
    return !!(this.httpsProxy || this.httpProxy);
  },

  // 获取 OpenAI 的默认配置
  getOpenAIDefaults() {
    return {
      apiKey: this.openaiApiKey,
      endpoint: this.openaiEndpoint,
      proxy: this.getProxyUrl(),
    };
  },

  // 获取 Gemini 的默认配置
  getGeminiDefaults() {
    return {
      apiKey: this.geminiApiKey,
      endpoint: this.geminiEndpoint,
    };
  },

  // 获取 Ollama 的默认配置
  getOllamaDefaults() {
    return {
      endpoint: this.ollamaEndpoint,
    };
  },
};

/**
 * 数据库相关的环境变量配置
 */
export const dbEnvConfig = {
  databaseUrl: process.env.DATABASE_URL || 'file:./dev.db',
};

/**
 * 认证相关的环境变量配置
 */
export const authEnvConfig = {
  nextAuthUrl: process.env.NEXTAUTH_URL || 'http://localhost:3000',
  nextAuthSecret:
    process.env.NEXTAUTH_SECRET ||
    'your-secret-key-here-change-in-production-12345678',
};

/**
 * 应用相关的环境变量配置
 */
export const appEnvConfig = {
  nodeEnv: process.env.NODE_ENV || 'development',
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
};

/**
 * 获取所有环境变量配置的摘要
 */
export function getEnvConfigSummary() {
  return {
    ai: {
      hasOpenAIKey: !!aiEnvConfig.openaiApiKey,
      hasGeminiKey: !!aiEnvConfig.geminiApiKey,
      hasProxy: aiEnvConfig.hasProxy(),
      proxyUrl: aiEnvConfig.getProxyUrl() ? '已配置' : '未配置',
    },
    database: {
      url: dbEnvConfig.databaseUrl,
    },
    app: {
      environment: appEnvConfig.nodeEnv,
      authUrl: authEnvConfig.nextAuthUrl,
    },
  };
}
