import Dexie, { Table } from 'dexie';
import { Document, DocumentMetadata } from '@/types';
import { JSONContent } from '@tiptap/react';

// Local storage document interface (slightly different from the main Document type)
export interface LocalDocument {
    id: string;
    title: string;
    content: JSONContent;
    folderId?: string;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
    lastSyncAt?: Date;
    metadata: DocumentMetadata;
    // Local-only fields
    isDirty: boolean; // Indicates if document has unsaved changes
    isDeleted: boolean; // Soft delete flag
}

// Database schema
export class DocumentDatabase extends Dexie {
    documents!: Table<LocalDocument>;

    constructor() {
        super('DocumentEditorDB');

        this.version(1).stores({
            documents: '++id, title, userId, folderId, createdAt, updatedAt, lastSyncAt, isDirty, isDeleted'
        });

        // Add hooks for automatic metadata updates
        this.documents.hook('creating', (primKey, obj, trans) => {
            const now = new Date();
            obj.createdAt = now;
            obj.updatedAt = now;
            obj.isDirty = true;
            obj.isDeleted = false;

            // Calculate initial metadata
            obj.metadata = {
                ...obj.metadata,
                wordCount: this.calculateWordCount(obj.content),
                characterCount: this.calculateCharacterCount(obj.content),
            };
        });

        this.documents.hook('updating', (modifications, primKey, obj, trans) => {
            (modifications as any).updatedAt = new Date();
            (modifications as any).isDirty = true;

            // Update metadata if content changed
            if ((modifications as any).content) {
                (modifications as any).metadata = {
                    ...obj.metadata,
                    ...(modifications as any).metadata,
                    wordCount: this.calculateWordCount((modifications as any).content),
                    characterCount: this.calculateCharacterCount((modifications as any).content),
                };
            }
        });
    }

    private calculateWordCount(content: JSONContent): number {
        const text = this.extractTextFromContent(content);
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    }

    private calculateCharacterCount(content: JSONContent): number {
        const text = this.extractTextFromContent(content);
        return text.length;
    }

    private extractTextFromContent(content: JSONContent): string {
        if (!content) return '';

        let text = '';

        if (content.text) {
            text += content.text;
        }

        if (content.content && Array.isArray(content.content)) {
            for (const child of content.content) {
                text += this.extractTextFromContent(child);
            }
        }

        return text;
    }
}

// Create and export database instance
export const db = new DocumentDatabase();
