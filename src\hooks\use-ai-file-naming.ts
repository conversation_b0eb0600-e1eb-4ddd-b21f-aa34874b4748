/**
 * AI 文件命名功能的自定义 Hook
 * 提供文件命名建议、文档摘要和重命名建议的状态管理
 */

import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import type {
  DocumentNamingSuggestion,
  DocumentSummary,
  SmartRenameSuggestion,
  FileNameValidation,
  NamingOptions,
  SummaryOptions,
  BatchNamingResult
} from '@/types/ai-naming.types';
import { NamingStyle
} from '@/types/ai-naming.types';

interface UseAIFileNamingOptions {
  /** 是否自动加载建议 */
  autoLoad?: boolean;
  /** 默认命名选项 */
  defaultNamingOptions?: Partial<NamingOptions>;
  /** 默认摘要选项 */
  defaultSummaryOptions?: Partial<SummaryOptions>;
}

interface UseAIFileNamingReturn {
  // 状态
  isLoading: boolean;
  isGenerating: boolean;
  isValidating: boolean;
  
  // 数据
  namingSuggestion: DocumentNamingSuggestion | null;
  documentSummary: DocumentSummary | null;
  smartSuggestions: SmartRenameSuggestion[];
  fileNameValidation: FileNameValidation | null;
  batchResult: BatchNamingResult | null;
  
  // 选项
  namingOptions: NamingOptions;
  summaryOptions: SummaryOptions;
  
  // 操作方法
  generateNamingSuggestions: (documentId: string, options?: Partial<NamingOptions>) => Promise<void>;
  batchGenerateNamingSuggestions: (documentIds: string[], options?: Partial<NamingOptions>) => Promise<void>;
  generateDocumentSummary: (documentId: string, options?: Partial<SummaryOptions>) => Promise<void>;
  loadSmartRenameSuggestions: () => Promise<void>;
  validateFileName: (fileName: string, content?: string) => Promise<void>;
  updateNamingOptions: (options: Partial<NamingOptions>) => void;
  updateSummaryOptions: (options: Partial<SummaryOptions>) => void;
  reset: () => void;
}

/**
 * AI 文件命名功能 Hook
 */
export function useAIFileNaming(options: UseAIFileNamingOptions = {}): UseAIFileNamingReturn {
  const { toast } = useToast();
  const { autoLoad = false, defaultNamingOptions = {}, defaultSummaryOptions = {} } = options;

  // 状态定义
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // 数据状态
  const [namingSuggestion, setNamingSuggestion] = useState<DocumentNamingSuggestion | null>(null);
  const [documentSummary, setDocumentSummary] = useState<DocumentSummary | null>(null);
  const [smartSuggestions, setSmartSuggestions] = useState<SmartRenameSuggestion[]>([]);
  const [fileNameValidation, setFileNameValidation] = useState<FileNameValidation | null>(null);
  const [batchResult, setBatchResult] = useState<BatchNamingResult | null>(null);

  // 选项状态
  const [namingOptions, setNamingOptions] = useState<NamingOptions>({
    style: NamingStyle.DESCRIPTIVE,
    maxLength: 50,
    includeDate: false,
    dateFormat: 'YYYY-MM-DD',
    includeTypePrefix: false,
    language: 'zh',
    excludeWords: ['文档', '新建', '未命名'],
    suggestionCount: 5,
    ...defaultNamingOptions
  });

  const [summaryOptions, setSummaryOptions] = useState<SummaryOptions>({
    length: 'medium',
    type: 'overview',
    language: 'zh',
    includeKeywords: true,
    maxWords: 100,
    ...defaultSummaryOptions
  });

  // 自动加载
  useEffect(() => {
    if (autoLoad) {
      loadSmartRenameSuggestions();
    }
  }, [autoLoad]);

  /**
   * 生成命名建议
   */
  const generateNamingSuggestions = useCallback(async (
    documentId: string,
    options?: Partial<NamingOptions>
  ) => {
    setIsGenerating(true);
    setNamingSuggestion(null);

    try {
      const response = await fetch('/api/ai/file-naming', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentId,
          options: { ...namingOptions, ...options }
        })
      });

      if (!response.ok) {
        throw new Error('生成命名建议失败');
      }

      const data = await response.json();
      setNamingSuggestion(data.data);

      toast({
        title: '建议生成完成',
        description: `已生成 ${data.data.suggestions.length} 个命名建议`
      });
    } catch (error) {
      console.error('生成命名建议失败:', error);
      toast({
        title: '生成失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      });
    } finally {
      setIsGenerating(false);
    }
  }, [namingOptions, toast]);

  /**
   * 批量生成命名建议
   */
  const batchGenerateNamingSuggestions = useCallback(async (
    documentIds: string[],
    options?: Partial<NamingOptions>
  ) => {
    setIsGenerating(true);
    setBatchResult(null);

    try {
      const response = await fetch('/api/ai/file-naming', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentIds,
          options: { ...namingOptions, ...options }
        })
      });

      if (!response.ok) {
        throw new Error('批量生成命名建议失败');
      }

      const data = await response.json();
      setBatchResult(data.data);

      toast({
        title: '批量生成完成',
        description: `成功处理 ${data.data.processedDocuments} 个文档`
      });
    } catch (error) {
      console.error('批量生成失败:', error);
      toast({
        title: '生成失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      });
    } finally {
      setIsGenerating(false);
    }
  }, [namingOptions, toast]);

  /**
   * 生成文档摘要
   */
  const generateDocumentSummary = useCallback(async (
    documentId: string,
    options?: Partial<SummaryOptions>
  ) => {
    setIsLoading(true);

    try {
      const response = await fetch('/api/ai/file-naming', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentId,
          options: { ...summaryOptions, ...options }
        })
      });

      if (!response.ok) {
        throw new Error('生成文档摘要失败');
      }

      const data = await response.json();
      setDocumentSummary(data.data);

      toast({
        title: '摘要生成完成',
        description: '文档摘要已生成'
      });
    } catch (error) {
      console.error('生成摘要失败:', error);
      toast({
        title: '生成失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  }, [summaryOptions, toast]);

  /**
   * 加载智能重命名建议
   */
  const loadSmartRenameSuggestions = useCallback(async () => {
    try {
      const response = await fetch('/api/ai/file-naming/smart-rename');
      if (response.ok) {
        const data = await response.json();
        setSmartSuggestions(data.data || []);
      }
    } catch (error) {
      console.error('加载智能建议失败:', error);
      toast({
        title: '加载失败',
        description: '无法加载智能重命名建议',
        variant: 'destructive'
      });
    }
  }, [toast]);

  /**
   * 验证文件名
   */
  const validateFileName = useCallback(async (fileName: string, content?: string) => {
    if (!fileName.trim()) {
      setFileNameValidation(null);
      return;
    }

    setIsValidating(true);

    try {
      const response = await fetch('/api/ai/file-naming/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileName,
          content
        })
      });

      if (response.ok) {
        const data = await response.json();
        setFileNameValidation(data.data);
      }
    } catch (error) {
      console.error('验证文件名失败:', error);
      toast({
        title: '验证失败',
        description: '无法验证文件名质量',
        variant: 'destructive'
      });
    } finally {
      setIsValidating(false);
    }
  }, [toast]);

  /**
   * 更新命名选项
   */
  const updateNamingOptions = useCallback((options: Partial<NamingOptions>) => {
    setNamingOptions(prev => ({ ...prev, ...options }));
  }, []);

  /**
   * 更新摘要选项
   */
  const updateSummaryOptions = useCallback((options: Partial<SummaryOptions>) => {
    setSummaryOptions(prev => ({ ...prev, ...options }));
  }, []);

  /**
   * 重置所有状态
   */
  const reset = useCallback(() => {
    setNamingSuggestion(null);
    setDocumentSummary(null);
    setSmartSuggestions([]);
    setFileNameValidation(null);
    setBatchResult(null);
  }, []);

  return {
    // 状态
    isLoading,
    isGenerating,
    isValidating,
    
    // 数据
    namingSuggestion,
    documentSummary,
    smartSuggestions,
    fileNameValidation,
    batchResult,
    
    // 选项
    namingOptions,
    summaryOptions,
    
    // 操作方法
    generateNamingSuggestions,
    batchGenerateNamingSuggestions,
    generateDocumentSummary,
    loadSmartRenameSuggestions,
    validateFileName,
    updateNamingOptions,
    updateSummaryOptions,
    reset
  };
}