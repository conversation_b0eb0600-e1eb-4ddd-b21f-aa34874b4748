'use client';

import { Suspense, lazy, ComponentType, ReactNode } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

interface LazyLoaderProps {
  /** 加载状态组件 */
  fallback?: ReactNode;
  /** 错误状态组件 */
  errorFallback?: ComponentType<{ error: Error; resetErrorBoundary: () => void }>;
  /** 是否延迟加载 */
  delay?: number;
  /** 组件名称（用于调试） */
  componentName?: string;
}

/**
 * 默认加载状态组件
 */
const DefaultLoadingFallback = ({ componentName }: { componentName?: string }) => (
  <div className="flex items-center justify-center p-8">
    <div className="flex items-center space-x-3">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      <span className="text-sm text-gray-600">
        {componentName ? `加载 ${componentName}...` : '加载中...'}
      </span>
    </div>
  </div>
);

/**
 * 默认错误状态组件
 */
const DefaultErrorFallback = ({
  error,
  resetErrorBoundary,
  componentName
}: {
  error: Error;
  resetErrorBoundary: () => void;
  componentName?: string;
}) => (
  <div className="flex flex-col items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg">
    <div className="text-red-600 mb-2">❌</div>
    <h3 className="text-lg font-medium text-red-800 mb-2">
      {componentName ? `${componentName} 加载失败` : '组件加载失败'}
    </h3>
    <p className="text-sm text-red-600 mb-4 text-center max-w-md">
      {error.message || '发生未知错误'}
    </p>
    <button
      onClick={resetErrorBoundary}
      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
    >
      重试
    </button>
  </div>
);

/**
 * 懒加载组件包装器
 */
export function LazyLoader({
  fallback,
  errorFallback: ErrorFallback,
  delay = 0,
  componentName,
}: LazyLoaderProps) {
  return function LazyComponent<P extends object>(
    LazyComponent: ComponentType<P>
  ) {
    return function WrappedComponent(props: P) {
      const LoadingFallback = fallback || <DefaultLoadingFallback componentName={componentName} />;
      const ErrorFallbackComponent = ErrorFallback || DefaultErrorFallback;

      return (
        <ErrorBoundary
          FallbackComponent={(errorProps) => (
            <ErrorFallbackComponent {...errorProps} componentName={componentName} />
          )}
          onError={(error) => {
            console.error(`懒加载组件错误 ${componentName || 'Unknown'}:`, error);
          }}
        >
          <Suspense fallback={LoadingFallback}>
            <DelayedComponent delay={delay}>
              <LazyComponent {...props} />
            </DelayedComponent>
          </Suspense>
        </ErrorBoundary>
      );
    };
  };
}

/**
 * 延迟组件包装器
 */
function DelayedComponent({
  children,
  delay
}: {
  children: ReactNode;
  delay: number;
}) {
  if (delay <= 0) {
    return <>{children}</>;
  }

  // 简单的延迟实现
  const [showComponent, setShowComponent] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowComponent(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  if (!showComponent) {
    return <DefaultLoadingFallback />;
  }

  return <>{children}</>;
}

/**
 * 创建懒加载组件的工厂函数
 */
export function createLazyComponent<P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  options: LazyLoaderProps = {}
) {
  const LazyComponent = lazy(importFn);
  return LazyLoader(options)(LazyComponent);
}

/**
 * 预定义的懒加载组件
 */

// AI 相关组件
export const LazyAIPanel = createLazyComponent(
  () => import('@/components/ai/MobileAIPanel'),
  { componentName: 'AI助手面板', delay: 100 }
);

export const LazyAIInteractionHistory = createLazyComponent(
  () => import('@/components/ai/AIInteractionHistory'),
  { componentName: 'AI交互历史', delay: 150 }
);

// 编辑器相关组件
export const LazySlashCommandMenu = createLazyComponent(
  () => import('@/components/editor/SlashCommandMenu'),
  { componentName: '斜杠命令菜单', delay: 50 }
);

export const LazyTableToolbar = createLazyComponent(
  () => import('@/components/editor/TableToolbar'),
  { componentName: '表格工具栏', delay: 100 }
);

export const LazyMediaInsertManager = createLazyComponent(
  () => import('@/components/editor/MediaInsertManager'),
  { componentName: '媒体插入管理器', delay: 150 }
);

// 文件管理相关组件
export const LazyFolderTree = createLazyComponent(
  () => import('@/components/hierarchy/FolderTreeFixed'),
  { componentName: '文件夹树', delay: 100 }
);

// 仪表板相关组件
export const LazyDashboardStats = createLazyComponent(
  () => import('@/components/dashboard/DashboardStats'),
  { componentName: '仪表板统计', delay: 100 }
);

export const LazyRecentDocuments = createLazyComponent(
  () => import('@/components/dashboard/RecentDocuments'),
  { componentName: '最近文档', delay: 150 }
);

/**
 * 条件懒加载 Hook
 * 根据条件决定是否懒加载组件
 */
export function useConditionalLazyLoad<P extends object>(
  condition: boolean,
  importFn: () => Promise<{ default: ComponentType<P> }>,
  options: LazyLoaderProps = {}
) {
  const [LazyComponent, setLazyComponent] = useState<ComponentType<P> | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (condition && !LazyComponent && !isLoading) {
      setIsLoading(true);
      setError(null);

      importFn()
        .then((module) => {
          setLazyComponent(() => module.default);
        })
        .catch((err) => {
          setError(err instanceof Error ? err : new Error('组件加载失败'));
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [condition, LazyComponent, isLoading, importFn]);

  return {
    Component: LazyComponent,
    isLoading,
    error,
    isReady: !!LazyComponent,
  };
}

// 导入必要的 React hooks
import { useState, useEffect } from 'react';