/**
 * AI 服务相关的类型定义
 */

/**
 * AI 服务提供商类型
 */
export type AIProvider = 'openai' | 'ollama' | 'gemini';

/**
 * AI 服务配置接口
 */
export interface AIServiceConfig {
  /** 服务提供商 */
  provider: AIProvider;
  /** API 密钥（OpenAI 和 Gemini 需要） */
  apiKey?: string;
  /** 服务端点 URL（Ollama 和自定义端点） */
  endpoint?: string;
  /** 使用的模型名称 */
  model: string;
  /** 最大令牌数 */
  maxTokens?: number;
  /** 温度参数（0-1） */
  temperature?: number;
  /** 请求超时时间（毫秒） */
  timeout?: number;
}

/**
 * AI 请求参数
 */
export interface AIRequest {
  /** 用户提示 */
  prompt: string;
  /** 上下文内容 */
  context?: string;
  /** 系统提示 */
  systemPrompt?: string;
  /** 最大令牌数（覆盖配置） */
  maxTokens?: number;
  /** 温度参数（覆盖配置） */
  temperature?: number;
}

/**
 * AI 响应结果
 */
export interface AIResponse {
  /** 生成的文本内容 */
  content: string;
  /** 使用的令牌数 */
  tokensUsed: number;
  /** 响应时间（毫秒） */
  responseTime: number;
  /** 使用的模型 */
  model: string;
  /** 服务提供商 */
  provider: AIProvider;
}

/**
 * 文本改写选项
 */
export interface RewriteOptions {
  /** 改写风格 */
  style?: 'formal' | 'informal' | 'academic' | 'creative' | 'concise' | 'detailed';
  /** 目标语调 */
  tone?: 'professional' | 'friendly' | 'neutral' | 'persuasive';
  /** 目标长度 */
  length?: 'shorter' | 'longer' | 'same';
}

/**
 * 文本分析结果
 */
export interface TextAnalysis {
  /** 情感分析 */
  sentiment: 'positive' | 'negative' | 'neutral';
  /** 关键词 */
  keywords: string[];
  /** 主题 */
  topics: string[];
  /** 可读性评分（0-100） */
  readabilityScore: number;
  /** 改进建议 */
  suggestions: string[];
  /** 字数统计 */
  wordCount: number;
  /** 字符数统计 */
  characterCount: number;
}

/**
 * AI 服务错误类型
 */
export enum AIErrorType {
  /** 网络错误 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** API 密钥无效 */
  INVALID_API_KEY = 'INVALID_API_KEY',
  /** 配额超限 */
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  /** 请求超时 */
  TIMEOUT = 'TIMEOUT',
  /** 服务不可用 */
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  /** 无效请求 */
  INVALID_REQUEST = 'INVALID_REQUEST',
  /** 内容被过滤 */
  CONTENT_FILTERED = 'CONTENT_FILTERED',
  /** 未知错误 */
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * AI 服务错误
 */
export class AIServiceError extends Error {
  constructor(
    public type: AIErrorType,
    message: string,
    public provider: AIProvider,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

/**
 * 重试配置
 */
export interface RetryConfig {
  /** 最大重试次数 */
  maxRetries: number;
  /** 初始延迟时间（毫秒） */
  initialDelay: number;
  /** 延迟倍数（指数退避） */
  backoffMultiplier: number;
  /** 最大延迟时间（毫秒） */
  maxDelay: number;
  /** 可重试的错误类型 */
  retryableErrors: AIErrorType[];
}