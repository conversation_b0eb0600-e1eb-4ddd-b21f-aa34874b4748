/**
 * AI 文件名验�?API 路由
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { aiFileNaming } from '@/lib/services/ai-file-naming';
import { z } from 'zod';

// 请求参数验证模式
const validateNameSchema = z.object({
  fileName: z.string().min(1, '文件名不能为空'),
  content: z.string().optional()
});

/**
 * POST /api/ai/file-naming/validate
 * 验证文件名质量
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { fileName, content } = validateNameSchema.parse(body);

    // 验证文件名
    const validation = await aiFileNaming.validateFileName(fileName, content);

    return NextResponse.json({
      success: true,
      data: validation
    });

  } catch (error) {
    console.error('文件名验证失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '验证失败' },
      { status: 500 }
    );
  }
}
