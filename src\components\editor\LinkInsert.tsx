'use client';

import { useState, useCallback, useEffect } from 'react';
import { Editor } from '@tiptap/react';
import { Link as LinkIcon, X, Check, ExternalLink, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/Button';

interface LinkInsertProps {
  editor: Editor | null;
  onClose: () => void;
  className?: string;
}

interface LinkData {
  url: string;
  text: string;
  title?: string;
  target: '_blank' | '_self';
}

/**
 * 链接插入组件
 * 支持链接插入、编辑和预览
 */
export function LinkInsert({ editor, onClose, className }: LinkInsertProps) {
  const [linkData, setLinkData] = useState<LinkData>({
    url: '',
    text: '',
    title: '',
    target: '_blank',
  });
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  /**
   * 初始化链接数据
   */
  useEffect(() => {
    if (!editor) return;

    const { from, to } = editor.state.selection;
    const selectedText = editor.state.doc.textBetween(from, to);
    
    // 检查是否在编辑现有链接
    const linkAttributes = editor.getAttributes('link');
    if (linkAttributes.href) {
      setLinkData({
        url: linkAttributes.href,
        text: selectedText || linkAttributes.href,
        title: linkAttributes.title || '',
        target: linkAttributes.target || '_blank',
      });
      setIsEditing(true);
    } else {
      setLinkData(prev => ({
        ...prev,
        text: selectedText,
      }));
    }
  }, [editor]);

  /**
   * 验证 URL
   */
  const validateUrl = useCallback((url: string): boolean => {
    if (!url.trim()) return false;
    
    try {
      // 如果没有协议，自动添加 https://
      const urlToValidate = url.startsWith('http://') || url.startsWith('https://') 
        ? url 
        : `https://${url}`;
      
      new URL(urlToValidate);
      return true;
    } catch {
      return false;
    }
  }, []);

  /**
   * 格式化 URL
   */
  const formatUrl = useCallback((url: string): string => {
    if (!url.trim()) return '';
    
    // 如果没有协议，自动添加 https://
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return `https://${url}`;
    }
    
    return url;
  }, []);

  /**
   * 处理输入变化
   */
  const handleInputChange = useCallback((field: keyof LinkData, value: string) => {
    setLinkData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // 清除错误
    if (error) {
      setError(null);
    }
  }, [error]);

  /**
   * 插入或更新链接
   */
  const handleInsertLink = useCallback(() => {
    if (!editor) return;

    const { url, text, title, target } = linkData;

    // 验证 URL
    if (!validateUrl(url)) {
      setError('请输入有效的 URL');
      return;
    }

    // 验证文本
    if (!text.trim()) {
      setError('请输入链接文本');
      return;
    }

    const formattedUrl = formatUrl(url);

    if (isEditing) {
      // 更新现有链接
      editor
        .chain()
        .focus()
        .extendMarkRange('link')
        .setLink({
          href: formattedUrl,
          target,
        })
        .insertContent(text)
        .run();
    } else {
      // 插入新链接
      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);
      
      if (selectedText) {
        // 如果有选中文本，将其转换为链接
        editor
          .chain()
          .focus()
          .setLink({
            href: formattedUrl,
            target,
          })
          .run();
      } else {
        // 插入新的链接文本
        editor
          .chain()
          .focus()
          .insertContent(`<a href="${formattedUrl}" title="${title || ''}" target="${target}">${text}</a>`)
          .run();
      }
    }

    onClose();
  }, [editor, linkData, isEditing, validateUrl, formatUrl, onClose]);

  /**
   * 移除链接
   */
  const handleRemoveLink = useCallback(() => {
    if (!editor) return;

    editor
      .chain()
      .focus()
      .extendMarkRange('link')
      .unsetLink()
      .run();

    onClose();
  }, [editor, onClose]);

  /**
   * 在新窗口中打开链接预览
   */
  const handlePreviewLink = useCallback(() => {
    const formattedUrl = formatUrl(linkData.url);
    if (validateUrl(linkData.url)) {
      window.open(formattedUrl, '_blank', 'noopener,noreferrer');
    }
  }, [linkData.url, formatUrl, validateUrl]);

  return (
    <div className={`bg-background border border-border rounded-lg shadow-lg p-6 w-96 ${className}`}>
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <LinkIcon className="h-5 w-5" />
          {isEditing ? '编辑链接' : '插入链接'}
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* 表单 */}
      <div className="space-y-4">
        {/* URL 输入 */}
        <div>
          <label className="block text-sm font-medium mb-1">链接地址</label>
          <div className="flex gap-2">
            <input
              type="url"
              value={linkData.url}
              onChange={(e) => handleInputChange('url', e.target.value)}
              placeholder="https://example.com"
              className="flex-1 px-3 py-2 border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            />
            {linkData.url && validateUrl(linkData.url) && (
              <Button
                variant="outline"
                size="sm"
                onClick={handlePreviewLink}
                title="在新窗口中预览"
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* 链接文本 */}
        <div>
          <label className="block text-sm font-medium mb-1">显示文本</label>
          <input
            type="text"
            value={linkData.text}
            onChange={(e) => handleInputChange('text', e.target.value)}
            placeholder="链接文本"
            className="w-full px-3 py-2 border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
          />
        </div>

        {/* 标题 (可选) */}
        <div>
          <label className="block text-sm font-medium mb-1">标题 (可选)</label>
          <input
            type="text"
            value={linkData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="鼠标悬停时显示的提示文本"
            className="w-full px-3 py-2 border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
          />
        </div>

        {/* 打开方式 */}
        <div>
          <label className="block text-sm font-medium mb-1">打开方式</label>
          <div className="flex gap-4">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="target"
                value="_blank"
                checked={linkData.target === '_blank'}
                onChange={(e) => handleInputChange('target', e.target.value)}
                className="w-4 h-4 text-primary"
              />
              <span className="text-sm">新窗口</span>
            </label>
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="target"
                value="_self"
                checked={linkData.target === '_self'}
                onChange={(e) => handleInputChange('target', e.target.value)}
                className="w-4 h-4 text-primary"
              />
              <span className="text-sm">当前窗口</span>
            </label>
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-destructive/10 text-destructive rounded-md mt-4">
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span className="text-sm">{error}</span>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex gap-2 justify-end mt-6">
        {isEditing && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleRemoveLink}
            className="text-destructive hover:text-destructive"
          >
            移除链接
          </Button>
        )}
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
        >
          取消
        </Button>
        <Button
          size="sm"
          onClick={handleInsertLink}
          disabled={!linkData.url.trim() || !linkData.text.trim()}
          className="flex items-center gap-2"
        >
          <Check className="h-4 w-4" />
          {isEditing ? '更新链接' : '插入链接'}
        </Button>
      </div>
    </div>
  );
}