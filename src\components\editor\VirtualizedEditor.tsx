'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import { useCallback, useEffect, useState, useMemo, useRef } from 'react';
import { FixedSizeList as List } from 'react-window';
import { SlashCommandExtension } from '@/lib/editor/slash-command-extension';
import { SLASH_COMMANDS } from '@/lib/editor/slash-commands';

interface VirtualizedEditorProps {
  content?: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  editable?: boolean;
  className?: string;
  /** 是否启用虚拟滚动 */
  enableVirtualization?: boolean;
  /** 每页显示的行数 */
  itemsPerPage?: number;
  /** 每行的高度 */
  itemHeight?: number;
}

interface DocumentChunk {
  id: string;
  content: string;
  startLine: number;
  endLine: number;
}

/**
 * 虚拟化编辑器组件
 * 用于处理大文档的性能优化
 */
export function VirtualizedEditor({
  content = '',
  placeholder = '开始写作...',
  onChange,
  editable = true,
  className = '',
  enableVirtualization = false,
  itemsPerPage = 50,
  itemHeight = 24,
}: VirtualizedEditorProps) {
  const [documentChunks, setDocumentChunks] = useState<DocumentChunk[]>([]);
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: itemsPerPage });
  const [totalLines, setTotalLines] = useState(0);
  const listRef = useRef<List>(null);
  const editorRef = useRef<HTMLDivElement>(null);

  // 将文档内容分割成块
  const chunkDocument = useCallback((content: string): DocumentChunk[] => {
    const lines = content.split('\n');
    const chunks: DocumentChunk[] = [];
    
    for (let i = 0; i < lines.length; i += itemsPerPage) {
      const chunkLines = lines.slice(i, i + itemsPerPage);
      chunks.push({
        id: `chunk-${i}`,
        content: chunkLines.join('\n'),
        startLine: i,
        endLine: Math.min(i + itemsPerPage - 1, lines.length - 1),
      });
    }
    
    return chunks;
  }, [itemsPerPage]);

  // 计算文档总行数
  const calculateTotalLines = useCallback((content: string): number => {
    return content.split('\n').length;
  }, []);

  // 更新文档块
  useEffect(() => {
    if (enableVirtualization && content) {
      const chunks = chunkDocument(content);
      const lines = calculateTotalLines(content);
      
      setDocumentChunks(chunks);
      setTotalLines(lines);
    }
  }, [content, enableVirtualization, chunkDocument, calculateTotalLines]);

  // 标准编辑器配置
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: { levels: [1, 2, 3] },
        bulletList: { keepMarks: true, keepAttributes: false },
        orderedList: { keepMarks: true, keepAttributes: false },
        codeBlock: { HTMLAttributes: { class: 'code-block' } },
        horizontalRule: false,
      }),
      Placeholder.configure({
        placeholder: `${placeholder} (输入 "/" 查看命令)`,
        emptyEditorClass: 'is-editor-empty',
      }),
      CharacterCount.configure({ limit: null }),
      SlashCommandExtension.configure({
        commands: SLASH_COMMANDS,
        trigger: '/',
        allowSpaces: false,
        startOfLine: false,
        char: '/',
      }),
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange?.(html);
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
      },
    },
  });

  // 虚拟化列表项渲染器
  const VirtualizedItem = useCallback(({ index, style }: { index: number; style: any }) => {
    const chunk = documentChunks[index];
    if (!chunk) return null;

    return (
      <div style={style} className="border-b border-gray-100">
        <div className="p-2 text-sm font-mono text-gray-600">
          行 {chunk.startLine + 1} - {chunk.endLine + 1}
        </div>
        <div className="p-4">
          <pre className="whitespace-pre-wrap text-sm">{chunk.content}</pre>
        </div>
      </div>
    );
  }, [documentChunks]);

  // 处理滚动事件
  const handleScroll = useCallback((scrollTop: number) => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(startIndex + itemsPerPage, documentChunks.length - 1);
    
    setVisibleRange({ start: startIndex, end: endIndex });
  }, [itemHeight, itemsPerPage, documentChunks.length]);

  // 获取字数统计
  const getWordCount = useCallback(() => {
    if (!editor) return 0;
    return editor.storage.characterCount.words();
  }, [editor]);

  const getCharacterCount = useCallback(() => {
    if (!editor) return 0;
    return editor.storage.characterCount.characters();
  }, [editor]);

  // 如果不启用虚拟化或文档较小，使用标准编辑器
  if (!enableVirtualization || totalLines < 1000) {
    if (!editor) {
      return (
        <div className={`min-h-[400px] w-full ${className}`}>
          <div className="flex items-center justify-center h-full">
            <div className="text-muted-foreground">加载编辑器...</div>
          </div>
        </div>
      );
    }

    return (
      <div className={`w-full ${className}`}>
        <div className="relative">
          <EditorContent 
            editor={editor} 
            className="min-h-[400px] w-full px-3 sm:px-4 py-4 sm:py-6 focus-within:outline-none"
          />
          
          <div className="absolute bottom-2 right-3 sm:right-4 text-xs text-muted-foreground">
            <span className="hidden sm:inline">{getWordCount()} 词 · {getCharacterCount()} 字符</span>
            <span className="sm:hidden">{getWordCount()}词</span>
          </div>
        </div>
      </div>
    );
  }

  // 虚拟化编辑器渲染
  return (
    <div className={`w-full ${className}`}>
      <div className="relative">
        {/* 虚拟化提示 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div className="flex items-center space-x-2">
            <div className="text-blue-600">⚡</div>
            <div className="text-sm text-blue-800">
              大文档模式已启用 - 总计 {totalLines.toLocaleString()} 行
            </div>
          </div>
        </div>

        {/* 虚拟化列表 */}
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <List
            ref={listRef}
            height={600}
            width="100%"
            itemCount={documentChunks.length}
            itemSize={itemHeight * itemsPerPage + 60} // 包含标题和内边距
            onScroll={({ scrollOffset }) => handleScroll(scrollOffset)}
            className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
          >
            {VirtualizedItem}
          </List>
        </div>

        {/* 统计信息 */}
        <div className="mt-4 flex justify-between items-center text-sm text-gray-600">
          <div>
            显示块 {visibleRange.start + 1} - {visibleRange.end + 1} / {documentChunks.length}
          </div>
          <div>
            {totalLines.toLocaleString()} 行 · {getCharacterCount().toLocaleString()} 字符
          </div>
        </div>

        {/* 编辑模式切换 */}
        <div className="mt-4 text-center">
          <button
            onClick={() => {
              // 切换到标准编辑模式
              window.location.href = window.location.href + '&mode=edit';
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            切换到编辑模式
          </button>
        </div>
      </div>
    </div>
  );
}