import { useState, useEffect, useCallback } from 'react';
import { DocumentWithRelations } from '@/lib/services/document-service';

interface UseServerDocumentsOptions {
  folderId?: string | null;
  autoLoad?: boolean;
}

interface UseServerDocumentsReturn {
  documents: DocumentWithRelations[];
  loading: boolean;
  error: string | null;
  createDocument: (title: string, content?: string, folderId?: string) => Promise<DocumentWithRelations | null>;
  updateDocument: (id: string, data: { title?: string; content?: string; folderId?: string | null }) => Promise<DocumentWithRelations | null>;
  deleteDocument: (id: string) => Promise<boolean>;
  moveDocument: (id: string, targetFolderId: string | null) => Promise<DocumentWithRelations | null>;
  duplicateDocument: (id: string, newTitle?: string) => Promise<DocumentWithRelations | null>;
  searchDocuments: (query: string) => Promise<DocumentWithRelations[]>;
  refreshDocuments: () => Promise<void>;
}

export function useServerDocuments(options: UseServerDocumentsOptions = {}): UseServerDocumentsReturn {
  const { folderId, autoLoad = true } = options;

  const [documents, setDocuments] = useState<DocumentWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDocuments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (folderId !== undefined) {
        if (folderId === null) {
          params.append('rootOnly', 'true');
        } else {
          params.append('folderId', folderId);
        }
      }

      const response = await fetch(`/api/documents?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch documents');
      }

      const data = await response.json();
      setDocuments(data.documents || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching documents:', err);
    } finally {
      setLoading(false);
    }
  }, [folderId]);

  const createDocument = useCallback(async (
    title: string,
    content?: string,
    targetFolderId?: string
  ): Promise<DocumentWithRelations | null> => {
    try {
      setError(null);

      const response = await fetch('/api/documents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          content: content || '',
          folderId: targetFolderId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create document');
      }

      const data = await response.json();
      const newDocument = data.document;

      // Add the new document to the state if it belongs to the current folder view
      if (targetFolderId === folderId || (!targetFolderId && folderId === null)) {
        setDocuments(prev => [newDocument, ...prev]);
      }

      return newDocument;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      console.error('Error creating document:', err);
      return null;
    }
  }, [folderId]);

  const updateDocument = useCallback(async (
    id: string,
    data: { title?: string; content?: string; folderId?: string | null }
  ): Promise<DocumentWithRelations | null> => {
    try {
      setError(null);

      const response = await fetch(`/api/documents/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update document');
      }

      const responseData = await response.json();
      const updatedDocument = responseData.document;

      // Update the document in the state or remove it if moved to different folder
      setDocuments(prev => {
        const filtered = prev.filter(doc => doc.id !== id);

        // Add back if it still belongs to current folder view
        if (updatedDocument.folderId === folderId ||
            (!updatedDocument.folderId && folderId === null)) {
          return [updatedDocument, ...filtered];
        }

        return filtered;
      });

      return updatedDocument;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      console.error('Error updating document:', err);
      return null;
    }
  }, [folderId]);

  const deleteDocument = useCallback(async (id: string): Promise<boolean> => {
    try {
      setError(null);

      const response = await fetch(`/api/documents/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete document');
      }

      // Remove the document from the state
      setDocuments(prev => prev.filter(doc => doc.id !== id));

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      console.error('Error deleting document:', err);
      return false;
    }
  }, []);

  const moveDocument = useCallback(async (
    id: string,
    targetFolderId: string | null
  ): Promise<DocumentWithRelations | null> => {
    try {
      setError(null);

      const response = await fetch(`/api/documents/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ folderId: targetFolderId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to move document');
      }

      const responseData = await response.json();
      const updatedDocument = responseData.document;

      // 移除文档从当前视图（无论移动到哪里）
      setDocuments(prev => prev.filter(doc => doc.id !== id));

      // 通知其他相关的hooks刷新数据
      // 这里可以通过事件或者全局状态管理来通知文件树刷新
      window.dispatchEvent(new CustomEvent('document-moved', {
        detail: { documentId: id, targetFolderId }
      }));

      return updatedDocument;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      console.error('Error moving document:', err);
      return null;
    }
  }, []);

  const duplicateDocument = useCallback(async (
    id: string,
    newTitle?: string
  ): Promise<DocumentWithRelations | null> => {
    try {
      setError(null);

      // First get the original document
      const response = await fetch(`/api/documents/${id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch original document');
      }

      const data = await response.json();
      const originalDocument = data.document;

      // Create a new document with the same content
      const title = newTitle || `${originalDocument.title} (Copy)`;
      return await createDocument(title, originalDocument.content, originalDocument.folderId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      console.error('Error duplicating document:', err);
      return null;
    }
  }, [createDocument]);

  const searchDocuments = useCallback(async (query: string): Promise<DocumentWithRelations[]> => {
    try {
      setError(null);

      const params = new URLSearchParams({ q: query });
      if (folderId) {
        params.append('folderId', folderId);
      }

      const response = await fetch(`/api/documents/search?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to search documents');
      }

      const data = await response.json();
      return data.documents || [];
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      console.error('Error searching documents:', err);
      return [];
    }
  }, [folderId]);

  const refreshDocuments = useCallback(async () => {
    await fetchDocuments();
  }, [fetchDocuments]);

  // 使用 useEffect 来处理 folderId 和 autoLoad 的变化，避免无限循环
  useEffect(() => {
    if (!autoLoad) return;

    const loadDocuments = async () => {
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (folderId !== undefined) {
          if (folderId === null) {
            params.append('rootOnly', 'true');
          } else {
            params.append('folderId', folderId);
          }
        }

        const response = await fetch(`/api/documents?${params.toString()}`);

        if (!response.ok) {
          throw new Error('Failed to fetch documents');
        }

        const data = await response.json();
        setDocuments(data.documents || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        console.error('Error fetching documents:', err);
      } finally {
        setLoading(false);
      }
    };

    loadDocuments();
  }, [folderId, autoLoad]); // 直接依赖 folderId 和 autoLoad

  return {
    documents,
    loading,
    error,
    createDocument,
    updateDocument,
    deleteDocument,
    moveDocument,
    duplicateDocument,
    searchDocuments,
    refreshDocuments,
  };
}

// Hook for getting a specific document
export function useServerDocument(id: string | null) {
  const [document, setDocument] = useState<DocumentWithRelations | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDocument = useCallback(async () => {
    if (!id) {
      setDocument(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/documents/${id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch document');
      }

      const data = await response.json();
      setDocument(data.document);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching document:', err);
    } finally {
      setLoading(false);
    }
  }, [id]);

  const updateDocument = useCallback(async (
    data: { title?: string; content?: string; folderId?: string | null }
  ): Promise<DocumentWithRelations | null> => {
    if (!id) return null;

    try {
      setError(null);

      const response = await fetch(`/api/documents/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update document');
      }

      const responseData = await response.json();
      const updatedDocument = responseData.document;

      setDocument(updatedDocument);
      return updatedDocument;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      console.error('Error updating document:', err);
      return null;
    }
  }, [id]);

  useEffect(() => {
    fetchDocument();
  }, [fetchDocument]);

  return {
    document,
    loading,
    error,
    updateDocument,
    refetch: fetchDocument,
  };
}