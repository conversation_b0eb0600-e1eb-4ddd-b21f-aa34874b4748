'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Editor } from '@tiptap/react';
import { AIRewriteComparison, AIRewriteLoading } from './AIRewriteComparison';
import { 
  TextRewriteService, 
  TextRewriteRequest, 
  TextRewriteResult,
  RewriteStyle,
  RewriteType
} from '@/lib/services/ai/text-rewrite-service';
import { aiServiceManager } from '@/lib/services/ai/ai-service-factory';
import { AIServiceError } from '@/types/ai.types';

/**
 * AI 文本改写管理器的属性
 */
interface AITextRewriteManagerProps {
  /** TipTap 编辑器实例 */
  editor: Editor;
  /** 是否启用 AI 功能 */
  enabled?: boolean;
}

/**
 * 改写状态
 */
interface RewriteState {
  /** 是否正在改写 */
  isRewriting: boolean;
  /** 是否显示结果 */
  showResult: boolean;
  /** 改写结果 */
  result?: TextRewriteResult;
  /** 错误信息 */
  error?: string;
  /** 显示位置 */
  position?: { top: number; left: number };
  /** 原始选择范围 */
  originalSelection?: { from: number; to: number };
}

/**
 * 快速改写选项
 */
interface QuickRewriteOption {
  id: string;
  label: string;
  type: RewriteType;
  style?: RewriteStyle;
  description: string;
  icon: string;
}

/**
 * 预定义的快速改写选项
 */
const QUICK_REWRITE_OPTIONS: QuickRewriteOption[] = [
  {
    id: 'improve',
    label: '改进优化',
    type: 'improve',
    description: '提升文本质量和表达效果',
    icon: '✨'
  },
  {
    id: 'grammar',
    label: '语法检查',
    type: 'grammar',
    description: '修正语法错误和用词问题',
    icon: '📝'
  },
  {
    id: 'formal',
    label: '正式化',
    type: 'style',
    style: 'formal',
    description: '调整为正式、严谨的风格',
    icon: '🎩'
  },
  {
    id: 'casual',
    label: '口语化',
    type: 'style',
    style: 'casual',
    description: '调整为轻松、随意的风格',
    icon: '😊'
  },
  {
    id: 'simplify',
    label: '简化',
    type: 'simplify',
    description: '使文本更简洁明了',
    icon: '🎯'
  },
  {
    id: 'expand',
    label: '扩展',
    type: 'expand',
    description: '增加细节和说明',
    icon: '📈'
  },
  {
    id: 'academic',
    label: '学术化',
    type: 'style',
    style: 'academic',
    description: '调整为学术、专业的风格',
    icon: '🎓'
  },
  {
    id: 'creative',
    label: '创意化',
    type: 'style',
    style: 'creative',
    description: '增加创意和生动性',
    icon: '🎨'
  }
];

/**
 * AI 文本改写管理器组件
 * 负责管理 AI 文本改写的整个流程
 */
export function AITextRewriteManager({ 
  editor, 
  enabled = true 
}: AITextRewriteManagerProps) {
  const [state, setState] = useState<RewriteState>({
    isRewriting: false,
    showResult: false
  });
  
  const textRewriteService = useRef<TextRewriteService>();
  const abortController = useRef<AbortController>();

  // 初始化文本改写服务
  useEffect(() => {
    try {
      const aiService = aiServiceManager.getDefaultService();
      textRewriteService.current = new TextRewriteService(aiService);
    } catch (error) {
      console.warn('AI 服务未配置，AI 文本改写功能将不可用');
    }
  }, []);

  /**
   * 开始改写文本
   */
  const startRewrite = useCallback(async (
    request: Omit<TextRewriteRequest, 'originalText'>
  ) => {
    if (!enabled || !textRewriteService.current || state.isRewriting) {
      return;
    }

    try {
      // 获取选中的文本
      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);
      
      if (!selectedText.trim()) {
        setState(prev => ({ 
          ...prev, 
          error: '请先选择要改写的文本' 
        }));
        return;
      }

      // 获取上下文
      const fullContent = editor.getText();
      const beforeText = editor.state.doc.textBetween(0, from);
      const afterText = editor.state.doc.textBetween(to, editor.state.doc.content.size);
      const context = `${beforeText.slice(-200)}${selectedText}${afterText.slice(0, 200)}`;

      // 计算显示位置
      const position = calculatePosition(editor, from);

      // 构建完整的改写请求
      const fullRequest: TextRewriteRequest = {
        originalText: selectedText,
        context,
        ...request
      };

      // 创建取消控制器
      abortController.current = new AbortController();

      // 更新状态为改写中
      setState({
        isRewriting: true,
        showResult: false,
        error: undefined,
        position,
        originalSelection: { from, to }
      });

      // 执行改写
      const result = await textRewriteService.current.rewriteText(fullRequest);

      // 更新状态为显示结果
      setState(prev => ({
        ...prev,
        isRewriting: false,
        showResult: true,
        result,
        error: undefined
      }));

    } catch (error) {
      console.error('AI 文本改写失败:', error);
      
      let errorMessage = '改写失败，请稍后重试';
      if (error instanceof AIServiceError) {
        errorMessage = error.message;
      }

      setState(prev => ({
        ...prev,
        isRewriting: false,
        showResult: false,
        error: errorMessage
      }));
    }
  }, [editor, enabled, state.isRewriting]);

  /**
   * 接受改写的内容
   */
  const acceptRewrite = useCallback((rewrittenText: string) => {
    if (!state.originalSelection) return;

    const { from, to } = state.originalSelection;
    
    // 替换选中的文本
    editor
      .chain()
      .focus()
      .setTextSelection({ from, to })
      .insertContent(rewrittenText)
      .run();

    // 清除状态
    setState({
      isRewriting: false,
      showResult: false
    });
  }, [editor, state.originalSelection]);

  /**
   * 拒绝改写的内容
   */
  const rejectRewrite = useCallback(() => {
    setState({
      isRewriting: false,
      showResult: false
    });
  }, []);

  /**
   * 重新生成改写
   */
  const regenerateRewrite = useCallback(async () => {
    if (!state.result || !state.originalSelection) return;

    // 重新构建请求
    const request: Omit<TextRewriteRequest, 'originalText'> = {
      type: state.result.type,
      style: state.result.style,
      generateMultiple: true
    };

    await startRewrite(request);
  }, [state.result, state.originalSelection, startRewrite]);

  /**
   * 取消改写
   */
  const cancelRewrite = useCallback(() => {
    if (abortController.current) {
      abortController.current.abort();
    }
    
    setState({
      isRewriting: false,
      showResult: false
    });
  }, []);

  /**
   * 快速改写方法
   */
  const quickRewrite = useCallback((optionId: string) => {
    const option = QUICK_REWRITE_OPTIONS.find(opt => opt.id === optionId);
    if (!option) return;

    return startRewrite({
      type: option.type,
      style: option.style,
      generateMultiple: true
    });
  }, [startRewrite]);

  // 暴露方法给父组件
  useEffect(() => {
    if (editor) {
      // 将方法添加到编辑器实例上，方便其他组件调用
      (editor as any).aiTextRewrite = {
        // 快速改写方法
        improve: () => quickRewrite('improve'),
        checkGrammar: () => quickRewrite('grammar'),
        makeFormal: () => quickRewrite('formal'),
        makeCasual: () => quickRewrite('casual'),
        simplify: () => quickRewrite('simplify'),
        expand: () => quickRewrite('expand'),
        makeAcademic: () => quickRewrite('academic'),
        makeCreative: () => quickRewrite('creative'),
        
        // 自定义改写
        rewrite: (request: Omit<TextRewriteRequest, 'originalText'>) => startRewrite(request),
        
        // 状态
        isRewriting: state.isRewriting,
        
        // 快速选项
        quickOptions: QUICK_REWRITE_OPTIONS
      };
    }
  }, [editor, quickRewrite, startRewrite, state.isRewriting]);

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + R: 改进优化
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'R') {
        event.preventDefault();
        quickRewrite('improve');
      }
      
      // Ctrl/Cmd + Shift + G: 语法检查
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'G') {
        event.preventDefault();
        quickRewrite('grammar');
      }
      
      // Ctrl/Cmd + Shift + F: 正式化
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'F') {
        event.preventDefault();
        quickRewrite('formal');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [quickRewrite]);

  if (!enabled || !textRewriteService.current) {
    return null;
  }

  return (
    <>
      {/* 改写中的加载状态 */}
      <AIRewriteLoading
        visible={state.isRewriting}
        onCancel={cancelRewrite}
        message="AI 正在改写文本..."
        position={state.position}
      />

      {/* 改写结果对比 */}
      {state.result && (
        <AIRewriteComparison
          result={state.result}
          visible={state.showResult}
          onAccept={acceptRewrite}
          onReject={rejectRewrite}
          onRegenerate={regenerateRewrite}
          isRegenerating={state.isRewriting}
          position={state.position}
        />
      )}

      {/* 错误提示 */}
      {state.error && (
        <div
          className="
            fixed bg-red-50 border border-red-200 rounded-lg p-3 z-50
            text-sm text-red-700
            animate-in slide-in-from-bottom-2 fade-in duration-200
          "
          style={state.position ? {
            top: state.position.top,
            left: state.position.left,
          } : {
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)'
          }}
        >
          <div className="flex items-center justify-between">
            <span>{state.error}</span>
            <button
              onClick={() => setState(prev => ({ ...prev, error: undefined }))}
              className="ml-2 text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </>
  );
}

/**
 * 计算显示位置
 */
function calculatePosition(editor: Editor, position: number): { top: number; left: number } {
  try {
    // 获取编辑器 DOM 元素
    const editorElement = editor.view.dom;
    const editorRect = editorElement.getBoundingClientRect();
    
    // 获取光标位置
    const coords = editor.view.coordsAtPos(position);
    
    return {
      top: coords.top - editorRect.top + 30, // 在光标下方显示
      left: coords.left - editorRect.left
    };
  } catch (error) {
    // 如果无法获取精确位置，返回默认位置
    return { top: 100, left: 100 };
  }
}

/**
 * 扩展编辑器类型定义
 */
declare module '@tiptap/react' {
  interface Editor {
    aiTextRewrite?: {
      // 快速改写方法
      improve: () => Promise<void>;
      checkGrammar: () => Promise<void>;
      makeFormal: () => Promise<void>;
      makeCasual: () => Promise<void>;
      simplify: () => Promise<void>;
      expand: () => Promise<void>;
      makeAcademic: () => Promise<void>;
      makeCreative: () => Promise<void>;
      
      // 自定义改写
      rewrite: (request: Omit<TextRewriteRequest, 'originalText'>) => Promise<void>;
      
      // 状态
      isRewriting: boolean;
      
      // 快速选项
      quickOptions: QuickRewriteOption[];
    };
  }
}
export default AITextRewriteManager;