# AI 交互历史记录功能

## 功能概述

AI 交互历史记录功能为用户提供了完整的 AI 交互记录管理系统，包括记录存储、查询、搜索、过滤和统计分析等功能。

## 主要特性

### 📝 完整记录存储
- 自动记录所有 AI 交互的详细信息
- 包含输入内容、输出结果、交互类型、AI 提供商、模型信息等
- 记录时间戳和令牌消耗情况
- 关联到具体的文档和用户

### 🔍 智能搜索和过滤
- **全文搜索**：支持在输入、输出内容和文档标题中搜索
- **类型过滤**：按交互类型过滤（生成、改写、总结、分析、翻译、解释）
- **提供商过滤**：按 AI 服务提供商过滤（OpenAI、Ollama、Gemini）
- **日期范围过滤**：按时间范围筛选记录
- **组合过滤**：支持多个条件同时过滤

### 📊 统计分析
- **总体统计**：显示总交互次数和令牌消耗
- **分类统计**：按功能类型和提供商分组统计
- **趋势分析**：显示最近30天的使用趋势
- **可视化展示**：直观的数据图表展示

### 🗂️ 批量管理
- **批量选择**：支持多选交互记录
- **批量删除**：一次性删除多条记录
- **清空功能**：清空所有历史记录
- **权限控制**：只能管理自己的记录

### 📄 分页浏览
- **灵活分页**：支持自定义每页显示数量
- **快速导航**：页码跳转和上下页导航
- **性能优化**：大量数据的高效加载

## 技术实现

### 后端架构

#### 数据模型
```typescript
// AI 交互记录模型
interface AIInteraction {
  id: string;
  documentId: string;  // 关联文档
  userId: string;      // 关联用户
  type: string;        // 交互类型
  input: string;       // 输入内容
  output: string;      // 输出结果
  provider: string;    // AI 提供商
  model: string;       // 使用的模型
  tokens: number;      // 消耗的令牌数
  createdAt: Date;     // 创建时间
}
```

#### 服务层
- `AIInteractionHistoryService`：核心业务逻辑
- 支持复杂查询和统计分析
- 数据库操作优化和索引设计
- 用户权限验证和数据隔离

#### API 接口
- `GET /api/ai-interactions`：查询历史记录
- `POST /api/ai-interactions`：创建新记录
- `DELETE /api/ai-interactions`：删除记录
- `GET /api/ai-interactions/stats`：获取统计信息
- `GET /api/ai-interactions/recent`：获取最近记录

### 前端架构

#### React 组件
- `AIInteractionHistory`：完整的历史记录管理界面
- `AIHistoryPanel`：简化的面板组件（用于 AI 助手面板）
- `InteractionItem`：单个交互记录的显示组件

#### 状态管理
- `useAIInteractionHistory` Hook：统一的状态管理
- 支持异步数据加载和错误处理
- 实时搜索和过滤功能
- 分页和排序控制

#### 用户体验
- 响应式设计，支持各种屏幕尺寸
- 加载状态和错误提示
- 直观的操作反馈
- 键盘快捷键支持

## 使用方法

### 1. 基本使用

```tsx
import { AIInteractionHistory } from '@/components/ai/AIInteractionHistory';

// 完整的历史记录管理界面
function HistoryPage() {
  return (
    <div className="h-screen">
      <AIInteractionHistory />
    </div>
  );
}
```

### 2. 面板模式

```tsx
import { AIHistoryPanel } from '@/components/ai/AIHistoryPanel';

// 在 AI 助手面板中使用
function AIAssistantPanel() {
  return (
    <div className="w-80 h-96">
      <AIHistoryPanel
        limit={10}
        showSearch={true}
        showStats={true}
        onInteractionClick={(interaction) => {
          console.log('点击了交互记录:', interaction);
        }}
        onViewAll={() => {
          // 跳转到完整历史页面
        }}
      />
    </div>
  );
}
```

### 3. 编程式使用

```tsx
import { useAIInteractionHistory } from '@/hooks/use-ai-interaction-history';

function MyComponent() {
  const {
    history,
    stats,
    fetchHistory,
    createInteraction,
    deleteInteractions,
    searchInteractions,
  } = useAIInteractionHistory();

  // 创建新的交互记录
  const handleCreateInteraction = async () => {
    await createInteraction({
      documentId: 'doc-123',
      type: 'generate',
      input: '用户输入',
      output: 'AI 输出',
      provider: 'openai',
      model: 'gpt-4',
      tokens: 150,
    });
  };

  // 搜索记录
  const handleSearch = async (term: string) => {
    await searchInteractions(term);
  };

  return (
    <div>
      {/* 使用历史记录数据 */}
    </div>
  );
}
```

## 配置选项

### 环境变量
```env
# 数据库连接
DATABASE_URL="your-database-url"

# 认证配置
NEXTAUTH_SECRET="your-secret"
NEXTAUTH_URL="http://localhost:3000"
```

### 数据库迁移
```bash
# 生成 Prisma 客户端
npm run db:generate

# 推送数据库模式
npm run db:push

# 运行迁移
npm run db:migrate
```

## 测试

### 运行测试脚本
```bash
# 测试后端功能
npx tsx scripts/test-ai-interaction-history.ts

# 启动开发服务器
npm run dev

# 访问演示页面
http://localhost:3000/ai-history-demo
```

### 测试功能
1. **数据创建**：测试交互记录的创建和存储
2. **查询功能**：测试各种查询和过滤条件
3. **搜索功能**：测试全文搜索的准确性
4. **统计功能**：验证统计数据的正确性
5. **删除功能**：测试单个和批量删除
6. **分页功能**：测试大量数据的分页显示

## 性能优化

### 数据库优化
- 为常用查询字段添加索引
- 使用分页查询减少内存占用
- 优化复杂统计查询的性能

### 前端优化
- 虚拟滚动处理大量数据
- 防抖搜索减少 API 调用
- 缓存查询结果提升响应速度
- 懒加载非关键组件

## 扩展功能

### 计划中的功能
- **导出功能**：支持导出历史记录为 CSV/JSON
- **高级分析**：更详细的使用分析和报告
- **标签系统**：为交互记录添加自定义标签
- **收藏功能**：收藏重要的交互记录
- **分享功能**：分享有用的 AI 对话记录

### 集成建议
- 与文档编辑器深度集成
- 在 AI 功能使用时自动记录
- 提供快速重用历史结果的功能
- 支持从历史记录中学习用户偏好

## 故障排除

### 常见问题

1. **记录不显示**
   - 检查用户认证状态
   - 确认数据库连接正常
   - 验证 API 路由配置

2. **搜索不工作**
   - 检查搜索关键词是否正确
   - 确认数据库中有匹配的记录
   - 验证搜索 API 的响应

3. **统计数据不准确**
   - 检查数据库中的记录完整性
   - 确认统计查询的逻辑
   - 验证日期范围设置

4. **删除功能失败**
   - 确认用户有删除权限
   - 检查记录 ID 是否正确
   - 验证数据库约束设置

### 调试建议
- 查看浏览器控制台的错误信息
- 检查网络请求的响应状态
- 使用数据库管理工具验证数据
- 启用详细的日志记录

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个功能！

### 开发环境设置
1. 克隆项目并安装依赖
2. 配置环境变量
3. 运行数据库迁移
4. 启动开发服务器
5. 运行测试确保功能正常

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 和 Prettier 规则
- 添加适当的注释和文档
- 编写单元测试覆盖新功能