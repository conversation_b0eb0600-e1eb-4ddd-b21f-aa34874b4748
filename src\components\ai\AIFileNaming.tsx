/**
 * AI 文件命名建议组件
 * 提供智能文件命名、重命名建议和文档摘要功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Sparkles, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  RefreshCw,
  Copy,
  Edit3,
  Eye,
  Settings,
  Lightbulb,
  Target,
  Clock
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { AIProcessingIndicator } from './AIProcessingIndicator';
import type { 
  DocumentNamingSuggestion,
  FileNamingSuggestion,
  DocumentSummary,
  SmartRenameSuggestion,
  NamingOptions,
  SummaryOptions,
  NamingStyle,
  FileNameValidation
} from '@/types/ai-naming.types';

interface AIFileNamingProps {
  /** 文档ID */
  documentId?: string;
  /** 文档ID列表（批量处理时使用） */
  documentIds?: string[];
  /** 当前文件名 */
  currentFileName?: string;
  /** 文档内容（用于验证） */
  documentContent?: string;
  /** 命名完成回调 */
  onNamingComplete?: (suggestion: DocumentNamingSuggestion) => void;
  /** 重命名应用回调 */
  onRenameApplied?: (oldName: string, newName: string) => void;
  /** 是否显示高级选项 */
  showAdvancedOptions?: boolean;
}

/**
 * AI 文件命名组件
 */
export function AIFileNaming({
  documentId,
  documentIds,
  currentFileName,
  documentContent,
  onNamingComplete,
  onRenameApplied,
  showAdvancedOptions = false
}: AIFileNamingProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [namingSuggestion, setNamingSuggestion] = useState<DocumentNamingSuggestion | null>(null);
  const [documentSummary, setDocumentSummary] = useState<DocumentSummary | null>(null);
  const [smartSuggestions, setSmartSuggestions] = useState<SmartRenameSuggestion[]>([]);
  const [fileNameValidation, setFileNameValidation] = useState<FileNameValidation | null>(null);
  const [testFileName, setTestFileName] = useState(currentFileName || '');

  const [namingOptions, setNamingOptions] = useState<NamingOptions>({
    style: 'descriptive' as NamingStyle,
    maxLength: 50,
    includeDate: false,
    dateFormat: 'YYYY-MM-DD',
    includeTypePrefix: false,
    language: 'zh',
    excludeWords: ['文档', '新建', '未命名'],
    suggestionCount: 5
  });

  const [summaryOptions, setSummaryOptions] = useState<SummaryOptions>({
    length: 'medium',
    type: 'overview',
    language: 'zh',
    includeKeywords: true,
    maxWords: 100
  });

  const isBatchMode = documentIds && documentIds.length > 1;

  // 自动加载智能建议
  useEffect(() => {
    loadSmartRenameSuggestions();
  }, []);

  // 当文件名改变时验证
  useEffect(() => {
    if (testFileName) {
      validateFileName(testFileName);
    }
  }, [testFileName]);

  /**
   * 生成命名建议
   */
  const handleGenerateNaming = async () => {
    if (!documentId) {
      toast({
        title: '错误',
        description: '请选择要命名的文档',
        variant: 'destructive'
      });
      return;
    }

    setIsGenerating(true);
    setNamingSuggestion(null);

    try {
      const response = await fetch('/api/ai/file-naming', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentId,
          options: namingOptions
        })
      });

      if (!response.ok) {
        throw new Error('生成命名建议失败');
      }

      const data = await response.json();
      setNamingSuggestion(data.data);
      onNamingComplete?.(data.data);

      toast({
        title: '建议生成完成',
        description: `已生成 ${data.data.suggestions.length} 个命名建议`
      });
    } catch (error) {
      console.error('生成命名建议失败:', error);
      toast({
        title: '生成失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  /**
   * 生成文档摘要
   */
  const handleGenerateSummary = async () => {
    if (!documentId) {
      toast({
        title: '错误',
        description: '请选择要生成摘要的文档',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/ai/file-naming', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentId,
          options: summaryOptions
        })
      });

      if (!response.ok) {
        throw new Error('生成文档摘要失败');
      }

      const data = await response.json();
      setDocumentSummary(data.data);

      toast({
        title: '摘要生成完成',
        description: '文档摘要已生成'
      });
    } catch (error) {
      console.error('生成摘要失败:', error);
      toast({
        title: '生成失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 加载智能重命名建议
   */
  const loadSmartRenameSuggestions = async () => {
    try {
      const response = await fetch('/api/ai/file-naming/smart-rename');
      if (response.ok) {
        const data = await response.json();
        setSmartSuggestions(data.data || []);
      }
    } catch (error) {
      console.error('加载智能建议失败:', error);
    }
  };

  /**
   * 验证文件名
   */
  const validateFileName = async (fileName: string) => {
    try {
      const response = await fetch('/api/ai/file-naming/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileName,
          content: documentContent
        })
      });

      if (response.ok) {
        const data = await response.json();
        setFileNameValidation(data.data);
      }
    } catch (error) {
      console.error('验证文件名失败:', error);
    }
  };

  /**
   * 复制建议名称
   */
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: '已复制',
      description: '建议名称已复制到剪贴板'
    });
  };

  /**
   * 应用建议名称
   */
  const applySuggestion = (suggestion: FileNamingSuggestion) => {
    setTestFileName(suggestion.suggestedName);
    onRenameApplied?.(currentFileName || '', suggestion.suggestedName);
    toast({
      title: '建议已应用',
      description: `文件名已更新为: ${suggestion.suggestedName}`
    });
  };

  /**
   * 获取置信度颜色
   */
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  /**
   * 获取质量评分颜色
   */
  const getQualityColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  /**
   * 渲染命名建议列表
   */
  const renderNamingSuggestions = () => {
    if (!namingSuggestion) return null;

    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              最佳建议
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex-1">
                <h3 className="font-medium">{namingSuggestion.bestSuggestion.suggestedName}</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {namingSuggestion.bestSuggestion.reason}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={getConfidenceColor(namingSuggestion.bestSuggestion.confidence)}>
                  {Math.round(namingSuggestion.bestSuggestion.confidence * 100)}%
                </Badge>
                <Button
                  size="sm"
                  onClick={() => applySuggestion(namingSuggestion.bestSuggestion)}
                >
                  应用
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>所有建议</CardTitle>
            <CardDescription>共 {namingSuggestion.suggestions.length} 个建议</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {namingSuggestion.suggestions.map((suggestion, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium">{suggestion.suggestedName}</h4>
                    <p className="text-sm text-gray-600">{suggestion.reason}</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline" className="text-xs">
                        {suggestion.type}
                      </Badge>
                      <Badge className={getConfidenceColor(suggestion.confidence)}>
                        {Math.round(suggestion.confidence * 100)}%
                      </Badge>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(suggestion.suggestedName)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => applySuggestion(suggestion)}
                    >
                      应用
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  /**
   * 渲染文档摘要
   */
  const renderDocumentSummary = () => {
    if (!documentSummary) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            文档摘要
          </CardTitle>
          <CardDescription>
            置信度: {Math.round(documentSummary.confidence * 100)}% | 
            字数: {documentSummary.wordCount}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">摘要内容</h4>
            <p className="text-gray-700 leading-relaxed">{documentSummary.summary}</p>
          </div>

          {documentSummary.keywords.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">关键词</h4>
              <div className="flex flex-wrap gap-2">
                {documentSummary.keywords.map((keyword, index) => (
                  <Badge key={index} variant="secondary">
                    {keyword}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {documentSummary.mainTopics.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">主要主题</h4>
              <div className="flex flex-wrap gap-2">
                {documentSummary.mainTopics.map((topic, index) => (
                  <Badge key={index} variant="outline">
                    {topic}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  /**
   * 渲染智能重命名建议
   */
  const renderSmartRenameSuggestions = () => {
    if (smartSuggestions.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <Lightbulb className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>暂无智能重命名建议</p>
          <p className="text-sm">当发现可以改进的文件名时，会在这里显示建议</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {smartSuggestions.map((suggestion) => (
          <Card key={suggestion.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant={
                      suggestion.priority === 'high' ? 'destructive' :
                      suggestion.priority === 'medium' ? 'default' : 'secondary'
                    }>
                      {suggestion.priority === 'high' ? '高优先级' :
                       suggestion.priority === 'medium' ? '中优先级' : '低优先级'}
                    </Badge>
                    <Badge variant="outline">
                      质量评分: {suggestion.qualityScore}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm text-gray-600">当前名称:</span>
                      <p className="font-medium">{suggestion.currentName}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">建议名称:</span>
                      <p className="font-medium text-blue-600">{suggestion.suggestedName}</p>
                    </div>
                    <p className="text-sm text-gray-600">{suggestion.improvementReason}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2 ml-4">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => copyToClipboard(suggestion.suggestedName)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => {
                      onRenameApplied?.(suggestion.currentName, suggestion.suggestedName);
                      toast({
                        title: '重命名建议已应用',
                        description: `文件已重命名为: ${suggestion.suggestedName}`
                      });
                    }}
                    disabled={suggestion.applied}
                  >
                    {suggestion.applied ? '已应用' : '应用'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  /**
   * 渲染文件名验证
   */
  const renderFileNameValidation = () => {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            文件名验证
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">测试文件名</label>
            <Input
              value={testFileName}
              onChange={(e) => setTestFileName(e.target.value)}
              placeholder="输入要验证的文件名"
            />
          </div>

          {fileNameValidation && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">质量评分:</span>
                <span className={`font-bold ${getQualityColor(fileNameValidation.qualityScore)}`}>
                  {fileNameValidation.qualityScore}/100
                </span>
                {fileNameValidation.isValid ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500" />
                )}
              </div>

              {fileNameValidation.errors.length > 0 && (
                <div className="p-3 bg-red-50 rounded-lg">
                  <h4 className="font-medium text-red-800 mb-2">错误</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {fileNameValidation.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {fileNameValidation.warnings.length > 0 && (
                <div className="p-3 bg-yellow-50 rounded-lg">
                  <h4 className="font-medium text-yellow-800 mb-2">警告</h4>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {fileNameValidation.warnings.map((warning, index) => (
                      <li key={index}>• {warning}</li>
                    ))}
                  </ul>
                </div>
              )}

              {fileNameValidation.suggestions.length > 0 && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-800 mb-2">建议</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    {fileNameValidation.suggestions.map((suggestion, index) => (
                      <li key={index}>• {suggestion}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  /**
   * 渲染高级选项
   */
  const renderAdvancedOptions = () => {
    if (!showAdvancedOptions) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            高级选项
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Tabs defaultValue="naming" className="w-full">
            <TabsList>
              <TabsTrigger value="naming">命名选项</TabsTrigger>
              <TabsTrigger value="summary">摘要选项</TabsTrigger>
            </TabsList>

            <TabsContent value="naming" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">命名风格</label>
                  <select
                    value={namingOptions.style}
                    onChange={(e) => setNamingOptions(prev => ({
                      ...prev,
                      style: e.target.value as NamingStyle
                    }))}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="concise">简洁</option>
                    <option value="descriptive">描述性</option>
                    <option value="formal">正式</option>
                    <option value="technical">技术性</option>
                    <option value="creative">创意</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">最大长度</label>
                  <Input
                    type="number"
                    min="10"
                    max="100"
                    value={namingOptions.maxLength}
                    onChange={(e) => setNamingOptions(prev => ({
                      ...prev,
                      maxLength: parseInt(e.target.value)
                    }))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">语言偏好</label>
                  <select
                    value={namingOptions.language}
                    onChange={(e) => setNamingOptions(prev => ({
                      ...prev,
                      language: e.target.value as 'zh' | 'en' | 'auto'
                    }))}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="zh">中文</option>
                    <option value="en">英文</option>
                    <option value="auto">自动检测</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">建议数量</label>
                  <Input
                    type="number"
                    min="1"
                    max="10"
                    value={namingOptions.suggestionCount}
                    onChange={(e) => setNamingOptions(prev => ({
                      ...prev,
                      suggestionCount: parseInt(e.target.value)
                    }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={namingOptions.includeDate}
                    onChange={(e) => setNamingOptions(prev => ({
                      ...prev,
                      includeDate: e.target.checked
                    }))}
                  />
                  <span className="text-sm">包含日期</span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={namingOptions.includeTypePrefix}
                    onChange={(e) => setNamingOptions(prev => ({
                      ...prev,
                      includeTypePrefix: e.target.checked
                    }))}
                  />
                  <span className="text-sm">包含类型前缀</span>
                </label>
              </div>
            </TabsContent>

            <TabsContent value="summary" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">摘要长度</label>
                  <select
                    value={summaryOptions.length}
                    onChange={(e) => setSummaryOptions(prev => ({
                      ...prev,
                      length: e.target.value as 'short' | 'medium' | 'long'
                    }))}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="short">简短</option>
                    <option value="medium">中等</option>
                    <option value="long">详细</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">摘要类型</label>
                  <select
                    value={summaryOptions.type}
                    onChange={(e) => setSummaryOptions(prev => ({
                      ...prev,
                      type: e.target.value as 'overview' | 'key_points' | 'abstract'
                    }))}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="overview">概述</option>
                    <option value="key_points">要点</option>
                    <option value="abstract">摘要</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">最大字数</label>
                  <Input
                    type="number"
                    min="10"
                    max="500"
                    value={summaryOptions.maxWords}
                    onChange={(e) => setSummaryOptions(prev => ({
                      ...prev,
                      maxWords: parseInt(e.target.value)
                    }))}
                  />
                </div>
              </div>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={summaryOptions.includeKeywords}
                  onChange={(e) => setSummaryOptions(prev => ({
                    ...prev,
                    includeKeywords: e.target.checked
                  }))}
                />
                <span className="text-sm">包含关键词</span>
              </label>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* 主要操作区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            AI 文件命名助手
          </CardTitle>
          <CardDescription>
            智能分析文档内容，生成最适合的文件名建议
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button
              onClick={handleGenerateNaming}
              disabled={isGenerating || !documentId}
              className="flex items-center gap-2"
            >
              {isGenerating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Edit3 className="h-4 w-4" />
              )}
              生成命名建议
            </Button>

            <Button
              variant="outline"
              onClick={handleGenerateSummary}
              disabled={isLoading || !documentId}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <FileText className="h-4 w-4" />
              )}
              生成摘要
            </Button>

            <Button
              variant="outline"
              onClick={loadSmartRenameSuggestions}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              刷新建议
            </Button>
          </div>

          {/* AI处理状态指示器 */}
          {(isGenerating || isLoading) && (
            <div className="mt-4">
              <AIProcessingIndicator
                processingId={`naming_${documentId || 'batch'}_${Date.now()}`}
                showProgress={true}
                showTokenCount={false}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* 高级选项 */}
      {renderAdvancedOptions()}

      {/* 功能标签页 */}
      <Tabs defaultValue="suggestions" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="suggestions">命名建议</TabsTrigger>
          <TabsTrigger value="summary">文档摘要</TabsTrigger>
          <TabsTrigger value="smart">智能重命名</TabsTrigger>
          <TabsTrigger value="validate">名称验证</TabsTrigger>
        </TabsList>

        <TabsContent value="suggestions" className="space-y-4">
          {renderNamingSuggestions()}
        </TabsContent>

        <TabsContent value="summary" className="space-y-4">
          {renderDocumentSummary()}
        </TabsContent>

        <TabsContent value="smart" className="space-y-4">
          {renderSmartRenameSuggestions()}
        </TabsContent>

        <TabsContent value="validate" className="space-y-4">
          {renderFileNameValidation()}
        </TabsContent>
      </Tabs>
    </div>
  );
}