"use client";

import { SessionProvider as NextAuthSessionProvider } from "next-auth/react";
import { ReactNode } from "react";

/**
 * 会话提供者组件属性接口
 */
interface SessionProviderProps {
  children: ReactNode; // 子组件
}

/**
 * 会话提供者组件
 * 封装 NextAuth.js 的 SessionProvider，为整个应用提供认证上下文
 * 必须在应用的根组件中使用，以便子组件能够访问认证状态
 * 
 * @param children 需要访问认证状态的子组件
 */
export function SessionProvider({ children }: SessionProviderProps) {
  return (
    <NextAuthSessionProvider>
      {children}
    </NextAuthSessionProvider>
  );
}