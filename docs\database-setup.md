# Database Setup Guide

This document explains how to set up and manage the database for the Next.js Document Editor application.

## Database Configuration

### Development (SQLite)

The application uses SQLite for development, which is configured in:
- **Schema**: `prisma/schema.prisma`
- **Database file**: `prisma/dev.db`
- **Environment**: `.env` or `.env.local`

```env
DATABASE_URL="file:./dev.db"
```

### Production (PostgreSQL)

For production deployment, use PostgreSQL:
- **Schema**: `prisma/schema.production.prisma`
- **Environment**: Set `DATABASE_URL` to your PostgreSQL connection string

```env
DATABASE_URL="postgresql://username:password@localhost:5432/nextjs_document_editor"
```

## Database Schema

The database includes the following main entities:

### Users
- User authentication and profile information
- Subscription management (free, pro, enterprise)

### Folders & Documents
- Hierarchical folder structure
- Document content stored as JSON (TipTap format)
- Word count and character count tracking
- Document sharing capabilities

### AI Configuration
- Multiple AI service configurations per user
- Support for OpenAI, Ollama, and Gemini
- Default configuration selection

### AI Interactions
- History of AI interactions
- Token usage tracking
- Input/output logging for debugging

## Available Commands

### Development Commands

```bash
# Generate Prisma client
npm run db:generate

# Push schema changes to database (development)
npm run db:push

# Create and run migrations
npm run db:migrate

# Open Prisma Studio (database GUI)
npm run db:studio

# Seed database with demo data
npm run db:seed

# Reset database (WARNING: deletes all data)
npm run db:reset
```

### Production Deployment

1. **Switch to PostgreSQL schema**:
   ```bash
   cp prisma/schema.production.prisma prisma/schema.prisma
   ```

2. **Set environment variables**:
   ```env
   DATABASE_URL="postgresql://username:password@host:port/database"
   ```

3. **Deploy migrations**:
   ```bash
   npx prisma migrate deploy
   ```

4. **Generate client**:
   ```bash
   npx prisma generate
   ```

## Database Utilities

The application provides database utility functions in `src/lib/db/`:

- `users.ts` - User management functions
- `folders.ts` - Folder operations
- `documents.ts` - Document CRUD operations
- `ai-config.ts` - AI configuration management
- `prisma.ts` - Prisma client configuration

### Example Usage

```typescript
import { createDocument, getDocumentById } from '@/lib/db/documents'
import { createUser } from '@/lib/db/users'

// Create a new user
const user = await createUser({
  email: '<EMAIL>',
  name: 'John Doe'
})

// Create a new document
const document = await createDocument({
  title: 'My First Document',
  content: { type: 'doc', content: [] },
  userId: user.id
})
```

## Troubleshooting

### Common Issues

1. **"Environment variable not found: DATABASE_URL"**
   - Ensure `.env` file exists with `DATABASE_URL` set
   - Check that the file is in the project root

2. **Migration errors**
   - Reset database: `npm run db:reset`
   - Regenerate client: `npm run db:generate`

3. **Production deployment issues**
   - Ensure PostgreSQL is running and accessible
   - Verify connection string format
   - Check firewall and network settings

### Database Inspection

Use Prisma Studio to inspect and modify database contents:

```bash
npm run db:studio
```

This opens a web interface at `http://localhost:5555` where you can view and edit data.

## Security Considerations

- **API Keys**: AI service API keys are stored encrypted in the database
- **User Isolation**: All queries include user ID filtering to prevent data leaks
- **Soft Deletes**: Consider implementing soft deletes for important data
- **Backups**: Set up regular database backups for production

## Performance Optimization

- **Indexing**: Key fields are indexed for optimal query performance
- **Connection Pooling**: Prisma handles connection pooling automatically
- **Query Optimization**: Use `include` and `select` to fetch only needed data

```typescript
// Good: Only fetch needed fields
const user = await prisma.user.findUnique({
  where: { id },
  select: { id: true, name: true, email: true }
})

// Better: Include related data in single query
const userWithDocs = await prisma.user.findUnique({
  where: { id },
  include: { documents: true }
})
```