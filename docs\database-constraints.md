# 数据库约束说明

## 概述

本项目使用了自定义 SQL 索引来实现支持软删除的唯一约束。由于 Prisma 的 `@@unique` 不支持 `WHERE` 条件，我们通过手动 SQL 索引来实现。

## 约束列表

### 1. 文档标题唯一约束

**约束名称：** `unique_active_document_title`

**约束逻辑：** 同一用户在同一文件夹下不能有重复标题的活跃文档

**SQL 定义：**
```sql
CREATE UNIQUE INDEX unique_active_document_title
ON documents(userId, title, COALESCE(folderId, ''))
WHERE isDeleted = false;
```

**字段说明：**
- `userId`: 确保用户隔离
- `title`: 文档标题
- `COALESCE(folderId, '')`: 处理 NULL 值，根目录用空字符串表示
- `WHERE isDeleted = false`: 只对活跃文档生效

### 2. 文件夹名称唯一约束

**约束名称：** `unique_active_folder_name`

**约束逻辑：** 同一用户在同一父文件夹下不能有重复名称的活跃文件夹

**SQL 定义：**
```sql
CREATE UNIQUE INDEX unique_active_folder_name
ON folders(userId, name, COALESCE(parentId, ''))
WHERE isDeleted = false;
```

**字段说明：**
- `userId`: 确保用户隔离
- `name`: 文件夹名称
- `COALESCE(parentId, '')`: 处理 NULL 值，根目录用空字符串表示
- `WHERE isDeleted = false`: 只对活跃文件夹生效

## 约束行为

### ✅ 允许的操作

1. **跨用户同名**：不同用户可以创建同名项目
   ```
   用户A: 文档 "README"
   用户B: 文档 "README"  ✅ 允许
   ```

2. **跨位置同名**：同一用户在不同位置可以创建同名项目
   ```
   用户A/根目录: 文件夹 "项目"
   用户A/工作: 文件夹 "项目"  ✅ 允许
   ```

3. **软删除后重建**：删除项目后可以重新创建同名项目
   ```
   用户A: 创建文档 "草稿" → 删除 → 重新创建 "草稿"  ✅ 允许
   ```

### ❌ 禁止的操作

1. **同位置重复**：同一用户在同一位置不能创建重复名称的活跃项目
   ```
   用户A/根目录: 文档 "README"
   用户A/根目录: 文档 "README"  ❌ 禁止
   ```

## 错误处理

当违反约束时，数据库会抛出错误：

```
UNIQUE constraint failed on the fields: (`index 'unique_active_document_title'`)
```

应用层应该捕获这些错误并提供友好的用户提示。

## 迁移历史

- **20250727065639_add_custom_unique_constraints**: 添加支持软删除的唯一约束（正确的 Prisma 迁移方式）

## 验证约束

可以通过以下 SQL 查看当前约束：

```sql
-- 查看所有唯一索引
SELECT name, sql FROM sqlite_master
WHERE type='index'
AND name LIKE '%unique%'
AND name NOT LIKE 'sqlite_%';
```

## 注意事项

1. **Prisma Schema 同步**：由于使用了自定义 SQL，Prisma Schema 中只有注释说明，没有实际约束定义

2. **迁移管理**：自定义约束需要手动管理迁移文件

3. **测试覆盖**：需要确保测试用例覆盖约束场景

4. **错误处理**：前端需要正确处理约束违反错误

## 相关文件

- `prisma/schema.prisma`: Schema 定义和注释
- `prisma/migrations/20250727065639_add_custom_unique_constraints/migration.sql`: 约束迁移
- `test-list/test-database-constraints.js`: 约束测试用例
