import { withAuth } from "next-auth/middleware";

/**
 * NextAuth.js 中间件
 * 用于保护需要认证的路由
 */
export default withAuth(
  function middleware() {
    // 在这里可以添加额外的中间件逻辑
    // 例如：日志记录、权限检查等
  },
  {
    callbacks: {
      /**
       * 授权回调函数
       * 决定用户是否有权限访问特定路由
       */
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;
        
        // 定义不需要认证的公开路由
        const publicRoutes = [
          "/",                    // 首页
          "/auth/signin",         // 登录页面
          "/auth/signup",         // 注册页面
          "/api/auth/register",   // 注册API
        ];
        
        // 允许访问公开路由
        if (publicRoutes.some(route => pathname.startsWith(route))) {
          return true;
        }
        
        // 其他路由需要认证（检查是否有有效的令牌）
        return !!token;
      },
    },
  }
);

/**
 * 中间件配置
 * 定义哪些路径需要经过中间件处理
 */
export const config = {
  matcher: [
    /*
     * 匹配所有请求路径，除了以下开头的路径：
     * - api/auth (NextAuth.js 认证路由)
     * - _next/static (静态文件)
     * - _next/image (图片优化文件)
     * - favicon.ico (网站图标)
     * - public (公共文件夹)
     */
    "/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)",
  ],
};