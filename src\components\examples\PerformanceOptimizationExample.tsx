'use client';

import { useState } from 'react';
import { OptimizedEditor } from '@/components/editor/OptimizedEditor';
import { OptimizedDocumentList } from '@/components/dashboard/OptimizedDocumentList';
import { PerformanceMonitor } from '@/components/common/PerformanceMonitor';
import { useDocuments } from '@/hooks/useDocuments';

/**
 * 性能优化功能演示组件
 * 展示各种性能优化技术的使用
 */
export function PerformanceOptimizationExample() {
  const [activeTab, setActiveTab] = useState<'editor' | 'list' | 'monitor'>('editor');
  const [editorContent, setEditorContent] = useState('');
  const [enableVirtualization, setEnableVirtualization] = useState(true);
  const [enableAI, setEnableAI] = useState(true);
  const [enableLazyLoading, setEnableLazyLoading] = useState(true);

  // 模拟文档数据
  const { documents, loading, error, refreshDocuments } = useDocuments({
    userId: 'demo-user',
    autoLoad: true,
  });

  // 生成大文档内容用于测试
  const generateLargeContent = () => {
    const paragraphs = [];
    for (let i = 0; i < 1000; i++) {
      paragraphs.push(
        `这是第 ${i + 1} 段内容。Lorem ipsum dolor sit amet, consectetur adipiscing elit. ` +
        `Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, ` +
        `quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. ` +
        `Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.`
      );
    }
    return paragraphs.join('\n\n');
  };

  const tabs = [
    { id: 'editor', label: '优化编辑器', icon: '📝' },
    { id: 'list', label: '虚拟列表', icon: '📋' },
    { id: 'monitor', label: '性能监控', icon: '⚡' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 头部 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            性能优化功能演示
          </h1>
          <p className="text-gray-600">
            展示虚拟化、懒加载、缓存等性能优化技术的实际应用
          </p>
        </div>

        {/* 选项卡 */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`
                    py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* 选项卡内容 */}
          <div className="p-6">
            {activeTab === 'editor' && (
              <div className="space-y-6">
                {/* 控制面板 */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">编辑器配置</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={enableVirtualization}
                        onChange={(e) => setEnableVirtualization(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">启用虚拟化</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={enableAI}
                        onChange={(e) => setEnableAI(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">启用 AI 功能</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={enableLazyLoading}
                        onChange={(e) => setEnableLazyLoading(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">启用懒加载</span>
                    </label>
                  </div>
                  
                  <div className="mt-4 flex space-x-2">
                    <button
                      onClick={() => setEditorContent('')}
                      className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                    >
                      清空内容
                    </button>
                    <button
                      onClick={() => setEditorContent('这是一个简单的测试文档。')}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      加载小文档
                    </button>
                    <button
                      onClick={() => setEditorContent(generateLargeContent())}
                      className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
                    >
                      加载大文档 (测试虚拟化)
                    </button>
                  </div>
                </div>

                {/* 编辑器 */}
                <div className="border border-gray-200 rounded-lg">
                  <OptimizedEditor
                    content={editorContent}
                    onChange={setEditorContent}
                    enableAI={enableAI}
                    enableVirtualization={enableVirtualization}
                    enableLazyLoading={enableLazyLoading}
                    enablePerformanceMonitor={true}
                    virtualizationThreshold={10000} // 10KB 阈值用于演示
                    placeholder="在这里开始写作..."
                  />
                </div>

                {/* 统计信息 */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">文档统计</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-blue-700">字符数:</span>
                      <span className="ml-1 font-medium">{editorContent.length.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="text-blue-700">大小:</span>
                      <span className="ml-1 font-medium">{(editorContent.length / 1024).toFixed(1)} KB</span>
                    </div>
                    <div>
                      <span className="text-blue-700">虚拟化:</span>
                      <span className="ml-1 font-medium">
                        {enableVirtualization && editorContent.length > 10000 ? '已启用' : '未启用'}
                      </span>
                    </div>
                    <div>
                      <span className="text-blue-700">模式:</span>
                      <span className="ml-1 font-medium">
                        {editorContent.length > 10000 ? '大文档' : '标准'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'list' && (
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">虚拟列表演示</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    当文档数量超过 50 个时，自动启用虚拟滚动以提高性能
                  </p>
                  <div className="flex space-x-2">
                    <button
                      onClick={refreshDocuments}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      刷新列表
                    </button>
                  </div>
                </div>

                <OptimizedDocumentList
                  documents={documents}
                  loading={loading}
                  error={error}
                  onRefresh={refreshDocuments}
                  enableVirtualization={true}
                  virtualizationThreshold={10} // 降低阈值用于演示
                  itemsPerRow={3}
                  rowHeight={200}
                />
              </div>
            )}

            {activeTab === 'monitor' && (
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">性能监控</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    实时监控应用性能指标，包括内存使用、AI 服务性能、代码分割等
                  </p>
                </div>

                {/* 性能监控组件 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-3">监控功能</h4>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• 内存使用情况监控</li>
                      <li>• AI 服务缓存命中率</li>
                      <li>• 批处理队列状态</li>
                      <li>• 代码分割加载状态</li>
                      <li>• 页面性能指标</li>
                      <li>• 性能警告和建议</li>
                    </ul>
                  </div>

                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-3">优化建议</h4>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• 大文档使用虚拟化编辑器</li>
                      <li>• 启用 AI 服务缓存</li>
                      <li>• 使用批处理优化 API 调用</li>
                      <li>• 懒加载非关键组件</li>
                      <li>• 监控内存使用情况</li>
                      <li>• 定期清理缓存数据</li>
                    </ul>
                  </div>
                </div>

                {/* 性能监控器会自动显示在右下角 */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <div className="text-yellow-600">💡</div>
                    <div className="text-sm text-yellow-800">
                      性能监控器已在右下角显示，点击可查看详细信息
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 性能监控器 */}
        <PerformanceMonitor 
          showDetails={true}
          updateInterval={3000}
          showInDevelopment={true}
        />
      </div>
    </div>
  );
}