'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/Button';
import { 
  MessageCircleIcon, 
  EditIcon, 
  BoldIcon,
  ItalicIcon,
  UnderlineIcon,
  HighlighterIcon,
  LinkIcon,
  CopyIcon
} from 'lucide-react';

/**
 * 选择菜单动作类型
 */
export type SelectionActionType = 'chat' | 'edit' | 'format' | 'utility';

/**
 * 选择菜单动作接口
 */
export interface SelectionMenuAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  type: SelectionActionType;
  description?: string;
  shortcut?: string;
  action: (selectedText: string, editor: Editor, range: { from: number; to: number }) => void;
  visible?: (selectedText: string, editor: Editor) => boolean;
}

/**
 * 选择菜单属性
 */
interface SelectionMenuProps {
  /** TipTap 编辑器实例 */
  editor: Editor;
  /** 是否启用 */
  enabled?: boolean;
  /** 自定义动作列表 */
  actions?: SelectionMenuAction[];
  /** 最小选择文本长度 */
  minSelectionLength?: number;
  /** 菜单显示延迟（毫秒） */
  showDelay?: number;
  /** 菜单隐藏延迟（毫秒） */
  hideDelay?: number;
  /** 是否显示默认格式化动作 */
  showFormatActions?: boolean;
  /** 是否显示实用工具动作 */
  showUtilityActions?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 菜单状态
 */
interface MenuState {
  visible: boolean;
  position: { x: number; y: number };
  selectedText: string;
  selectionRange: { from: number; to: number };
  actions: SelectionMenuAction[];
}

/**
 * 默认格式化动作
 */
const DEFAULT_FORMAT_ACTIONS: SelectionMenuAction[] = [
  {
    id: 'bold',
    label: '加粗',
    icon: <BoldIcon className="h-4 w-4" />,
    type: 'format',
    shortcut: '⌘B',
    action: (_, editor) => editor.chain().focus().toggleBold().run(),
    visible: (_, editor) => editor.can().toggleBold()
  },
  {
    id: 'italic',
    label: '斜体',
    icon: <ItalicIcon className="h-4 w-4" />,
    type: 'format',
    shortcut: '⌘I',
    action: (_, editor) => editor.chain().focus().toggleItalic().run(),
    visible: (_, editor) => editor.can().toggleItalic()
  }
];

/**
 * 默认实用工具动作
 */
const DEFAULT_UTILITY_ACTIONS: SelectionMenuAction[] = [
  {
    id: 'copy',
    label: '复制',
    icon: <CopyIcon className="h-4 w-4" />,
    type: 'utility',
    shortcut: '⌘C',
    action: (selectedText) => {
      navigator.clipboard.writeText(selectedText);
    }
  },
  {
    id: 'link',
    label: '添加链接',
    icon: <LinkIcon className="h-4 w-4" />,
    type: 'utility',
    action: (_, editor) => {
      const url = window.prompt('请输入链接地址:');
      if (url) {
        try {
          editor.chain().focus().setLink({ href: url }).run();
        } catch (error) {
          console.warn('设置链接失败:', error);
        }
      }
    }
  }
];

/**
 * 通用文本选择菜单组件
 * 当用户选择文本时显示可用的操作选项
 */
export function SelectionMenu({
  editor,
  enabled = true,
  actions = [],
  minSelectionLength = 1,
  showDelay = 100,
  hideDelay = 300,
  showFormatActions = true,
  showUtilityActions = true,
  className = ''
}: SelectionMenuProps) {
  const [menuState, setMenuState] = useState<MenuState>({
    visible: false,
    position: { x: 0, y: 0 },
    selectedText: '',
    selectionRange: { from: 0, to: 0 },
    actions: []
  });

  const showTimeoutRef = useRef<NodeJS.Timeout>();
  const hideTimeoutRef = useRef<NodeJS.Timeout>();
  const menuRef = useRef<HTMLDivElement>(null);

  /**
   * 获取所有可用的动作
   */
  const getAllActions = useCallback((): SelectionMenuAction[] => {
    const allActions: SelectionMenuAction[] = [...actions];
    
    if (showFormatActions) {
      allActions.push(...DEFAULT_FORMAT_ACTIONS);
    }
    
    if (showUtilityActions) {
      allActions.push(...DEFAULT_UTILITY_ACTIONS);
    }
    
    return allActions;
  }, [actions, showFormatActions, showUtilityActions]);

  /**
   * 过滤可见的动作
   */
  const getVisibleActions = useCallback((selectedText: string, editor: Editor): SelectionMenuAction[] => {
    const allActions = getAllActions();
    return allActions.filter(action => {
      if (action.visible) {
        return action.visible(selectedText, editor);
      }
      return true;
    });
  }, [getAllActions]);

  /**
   * 计算菜单的最佳显示位置
   */
  const calculateMenuPosition = useCallback((selectionCoords: { left: number; top: number; bottom: number }) => {
    const editorElement = editor.view.dom;
    const editorRect = editorElement.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // 基础位置：选择文本下方
    let x = selectionCoords.left - editorRect.left;
    let y = selectionCoords.bottom - editorRect.top + 8;
    
    // 估算菜单尺寸（基于动作数量）
    const estimatedMenuWidth = 280;
    const estimatedMenuHeight = 60 + (menuState.actions.length * 40);
    
    // 防止菜单超出右边界
    if (x + estimatedMenuWidth > viewportWidth - 20) {
      x = Math.max(10, viewportWidth - estimatedMenuWidth - 20);
    }
    
    // 防止菜单超出下边界，显示在选择文本上方
    if (selectionCoords.bottom + estimatedMenuHeight > viewportHeight - 20) {
      y = selectionCoords.top - editorRect.top - estimatedMenuHeight - 8;
    }
    
    // 确保菜单不会超出左边界和上边界
    x = Math.max(10, x);
    y = Math.max(10, y);
    
    return { x, y };
  }, [editor, menuState.actions.length]);

  /**
   * 显示菜单
   */
  const showMenu = useCallback((selectedText: string, range: { from: number; to: number }) => {
    // 清除隐藏定时器
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = undefined;
    }

    // 获取可见的动作
    const visibleActions = getVisibleActions(selectedText, editor);
    
    if (visibleActions.length === 0) {
      return;
    }

    // 计算菜单位置
    try {
      const coords = editor.view.coordsAtPos(range.to);
      const position = calculateMenuPosition(coords);

      setMenuState({
        visible: true,
        position,
        selectedText,
        selectionRange: range,
        actions: visibleActions
      });
    } catch (error) {
      console.warn('无法计算菜单位置:', error);
    }
  }, [editor, getVisibleActions, calculateMenuPosition]);

  /**
   * 隐藏菜单
   */
  const hideMenu = useCallback(() => {
    // 清除显示定时器
    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current);
      showTimeoutRef.current = undefined;
    }

    setMenuState(prev => ({ ...prev, visible: false }));
  }, []);

  /**
   * 延迟隐藏菜单
   */
  const delayedHideMenu = useCallback(() => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }
    
    hideTimeoutRef.current = setTimeout(() => {
      hideMenu();
    }, hideDelay);
  }, [hideMenu, hideDelay]);

  /**
   * 处理文本选择变化
   */
  const handleSelectionChange = useCallback(() => {
    if (!enabled) return;

    const { from, to } = editor.state.selection;
    const selectedText = editor.state.doc.textBetween(from, to);

    // 如果没有选中文本或文本太短，隐藏菜单
    if (!selectedText.trim() || selectedText.length < minSelectionLength) {
      delayedHideMenu();
      return;
    }

    // 延迟显示菜单，避免频繁触发
    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current);
    }

    showTimeoutRef.current = setTimeout(() => {
      showMenu(selectedText, { from, to });
    }, showDelay);
  }, [editor, enabled, minSelectionLength, showDelay, showMenu, delayedHideMenu]);

  /**
   * 执行动作
   */
  const executeAction = useCallback((action: SelectionMenuAction) => {
    const { selectedText, selectionRange } = menuState;
    
    // 确保选择范围正确
    editor.chain().focus().setTextSelection(selectionRange).run();
    
    // 执行动作
    action.action(selectedText, editor, selectionRange);
    
    // 隐藏菜单
    hideMenu();
  }, [editor, menuState, hideMenu]);

  // 监听编辑器选择变化
  useEffect(() => {
    if (!editor) return;

    const handleUpdate = () => {
      // 延迟处理，确保选择状态已更新
      setTimeout(handleSelectionChange, 10);
    };

    editor.on('selectionUpdate', handleUpdate);
    
    return () => {
      editor.off('selectionUpdate', handleUpdate);
    };
  }, [editor, handleSelectionChange]);

  // 监听鼠标事件
  useEffect(() => {
    const handleMouseDown = (event: MouseEvent) => {
      const target = event.target as Element;
      
      // 如果点击在菜单内部，不隐藏菜单
      if (menuRef.current?.contains(target)) {
        return;
      }
      
      // 如果点击在编辑器内部，延迟隐藏菜单（可能是新的选择）
      if (editor.view.dom.contains(target)) {
        delayedHideMenu();
        return;
      }
      
      // 点击在外部，立即隐藏菜单
      hideMenu();
    };

    const handleMouseUp = () => {
      // 鼠标释放后检查选择状态
      setTimeout(handleSelectionChange, 10);
    };

    if (menuState.visible) {
      document.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousedown', handleMouseDown);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [menuState.visible, editor, handleSelectionChange, delayedHideMenu, hideMenu]);

  // 监听键盘事件
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!menuState.visible) return;

      // ESC 键隐藏菜单
      if (event.key === 'Escape') {
        event.preventDefault();
        hideMenu();
        return;
      }

      // 方向键或其他导航键隐藏菜单
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(event.key)) {
        delayedHideMenu();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [menuState.visible, hideMenu, delayedHideMenu]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (showTimeoutRef.current) {
        clearTimeout(showTimeoutRef.current);
      }
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, []);

  if (!enabled || !menuState.visible || menuState.actions.length === 0) {
    return null;
  }

  // 按类型分组动作
  const actionsByType = menuState.actions.reduce((acc, action) => {
    if (!acc[action.type]) {
      acc[action.type] = [];
    }
    acc[action.type].push(action);
    return acc;
  }, {} as Record<SelectionActionType, SelectionMenuAction[]>);

  return (
    <div
      ref={menuRef}
      className={`
        absolute bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-50
        animate-in slide-in-from-bottom-2 fade-in duration-200
        max-w-xs min-w-[200px]
        ${className}
      `}
      style={{
        left: menuState.position.x,
        top: menuState.position.y,
      }}
      onMouseEnter={() => {
        // 鼠标进入菜单时，清除隐藏定时器
        if (hideTimeoutRef.current) {
          clearTimeout(hideTimeoutRef.current);
          hideTimeoutRef.current = undefined;
        }
      }}
      onMouseLeave={() => {
        // 鼠标离开菜单时，延迟隐藏
        delayedHideMenu();
      }}
    >
      {/* 头部信息 */}
      <div className="flex items-center gap-2 px-2 py-1 mb-2 border-b">
        <span className="text-sm font-medium text-gray-700">文本操作</span>
        <span className="text-xs text-gray-500 ml-auto">
          {menuState.selectedText.length} 字符
        </span>
      </div>

      {/* 动作按钮 */}
      <div className="space-y-1">
        {/* Chat 和 Edit 动作 */}
        {(actionsByType.chat || actionsByType.edit) && (
          <div className="space-y-1">
            {actionsByType.chat?.map((action) => (
              <ActionButton
                key={action.id}
                action={action}
                onClick={() => executeAction(action)}
              />
            ))}
            {actionsByType.edit?.map((action) => (
              <ActionButton
                key={action.id}
                action={action}
                onClick={() => executeAction(action)}
              />
            ))}
            {(actionsByType.format || actionsByType.utility) && (
              <div className="border-t my-1" />
            )}
          </div>
        )}

        {/* 格式化动作 */}
        {actionsByType.format && (
          <div className="space-y-1">
            <div className="text-xs text-gray-500 px-2 py-1">格式化</div>
            {actionsByType.format.map((action) => (
              <ActionButton
                key={action.id}
                action={action}
                onClick={() => executeAction(action)}
                compact
              />
            ))}
            {actionsByType.utility && <div className="border-t my-1" />}
          </div>
        )}

        {/* 实用工具动作 */}
        {actionsByType.utility && (
          <div className="space-y-1">
            <div className="text-xs text-gray-500 px-2 py-1">工具</div>
            {actionsByType.utility.map((action) => (
              <ActionButton
                key={action.id}
                action={action}
                onClick={() => executeAction(action)}
                compact
              />
            ))}
          </div>
        )}
      </div>

      {/* 提示信息 */}
      <div className="mt-2 pt-2 border-t">
        <p className="text-xs text-gray-400 text-center">
          选择操作或按 ESC 取消
        </p>
      </div>
    </div>
  );
}

/**
 * 动作按钮组件
 */
interface ActionButtonProps {
  action: SelectionMenuAction;
  onClick: () => void;
  compact?: boolean;
}

function ActionButton({ action, onClick, compact = false }: ActionButtonProps) {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      className={`
        w-full justify-start text-left group
        hover:bg-blue-50 hover:text-blue-700
        ${compact ? 'h-8 p-2' : 'h-auto p-2'}
      `}
    >
      <div className="flex items-start gap-2 w-full">
        <div className="text-blue-600 group-hover:text-blue-700 mt-0.5">
          {action.icon}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <span className={`font-medium ${compact ? 'text-xs' : 'text-sm'}`}>
              {action.label}
            </span>
            {action.shortcut && (
              <span className="text-xs text-gray-400 ml-2">
                {action.shortcut}
              </span>
            )}
          </div>
          {!compact && action.description && (
            <p className="text-xs text-gray-500 mt-0.5 leading-tight">
              {action.description}
            </p>
          )}
        </div>
      </div>
    </Button>
  );
}