'use client';

import { useState, useCallback, useEffect } from 'react';
import { Editor } from '@tiptap/react';
import { SlashCommand, SlashCommandMenuState } from '@/types/slash-command.types';
import { filterSlashCommands, findSlashCommand } from '@/lib/editor/slash-commands';

/**
 * 斜杠命令管理 Hook
 * 提供斜杠命令的状态管理和操作方法
 */
export function useSlashCommand(editor: Editor | null) {
  const [menuState, setMenuState] = useState<SlashCommandMenuState>({
    isOpen: false,
    position: { x: 0, y: 0 },
    range: null,
    query: '',
    selectedIndex: 0,
    filteredCommands: [],
  });

  /**
   * 计算菜单位置
   */
  const calculatePosition = useCallback((range: { from: number; to: number }) => {
    if (!editor) return { x: 0, y: 0 };

    const { view } = editor;
    const coords = view.coordsAtPos(range.from);
    const editorRect = view.dom.getBoundingClientRect();
    
    return {
      x: coords.left - editorRect.left,
      y: coords.bottom - editorRect.top + 8,
    };
  }, [editor]);

  /**
   * 打开斜杠命令菜单
   */
  const openMenu = useCallback((query: string, range: { from: number; to: number }) => {
    const filteredCommands = filterSlashCommands(query);
    const position = calculatePosition(range);
    
    setMenuState({
      isOpen: true,
      position,
      range,
      query,
      selectedIndex: 0,
      filteredCommands,
    });
  }, [calculatePosition]);

  /**
   * 关闭斜杠命令菜单
   */
  const closeMenu = useCallback(() => {
    setMenuState(prev => ({
      ...prev,
      isOpen: false,
      range: null,
      selectedIndex: 0,
      filteredCommands: [],
    }));
  }, []);

  /**
   * 更新查询字符串
   */
  const updateQuery = useCallback((query: string, range: { from: number; to: number }) => {
    const filteredCommands = filterSlashCommands(query);
    const position = calculatePosition(range);
    
    setMenuState(prev => ({
      ...prev,
      query,
      position,
      range,
      selectedIndex: 0,
      filteredCommands,
    }));
  }, [calculatePosition]);

  /**
   * 导航选择（上/下箭头键）
   */
  const navigate = useCallback((direction: 'up' | 'down') => {
    setMenuState(prev => {
      const { filteredCommands, selectedIndex } = prev;
      let newIndex = selectedIndex;
      
      if (direction === 'up') {
        newIndex = selectedIndex > 0 ? selectedIndex - 1 : filteredCommands.length - 1;
      } else {
        newIndex = selectedIndex < filteredCommands.length - 1 ? selectedIndex + 1 : 0;
      }
      
      return {
        ...prev,
        selectedIndex: newIndex,
      };
    });
  }, []);

  /**
   * 选择指定索引的命令
   */
  const selectCommand = useCallback((index: number) => {
    setMenuState(prev => ({
      ...prev,
      selectedIndex: Math.max(0, Math.min(index, prev.filteredCommands.length - 1)),
    }));
  }, []);

  /**
   * 执行选中的命令
   */
  const executeSelectedCommand = useCallback(async () => {
    const { filteredCommands, selectedIndex, range } = menuState;
    
    if (!editor || !range || filteredCommands.length === 0) {
      return false;
    }
    
    const selectedCommand = filteredCommands[selectedIndex];
    if (!selectedCommand) {
      return false;
    }

    try {
      // 关闭菜单
      closeMenu();
      
      // 执行命令
      await selectedCommand.action(editor, range);
      
      // 聚焦编辑器
      editor.commands.focus();
      
      return true;
    } catch (error) {
      console.error('执行斜杠命令时出错:', error);
      return false;
    }
  }, [editor, menuState, closeMenu]);

  /**
   * 执行指定的命令
   */
  const executeCommand = useCallback(async (commandId: string) => {
    const command = findSlashCommand(commandId);
    const { range } = menuState;
    
    if (!editor || !command || !range) {
      return false;
    }

    try {
      // 关闭菜单
      closeMenu();
      
      // 执行命令
      await command.action(editor, range);
      
      // 聚焦编辑器
      editor.commands.focus();
      
      return true;
    } catch (error) {
      console.error('执行斜杠命令时出错:', error);
      return false;
    }
  }, [editor, menuState, closeMenu]);

  /**
   * 获取当前选中的命令
   */
  const getSelectedCommand = useCallback((): SlashCommand | null => {
    const { filteredCommands, selectedIndex } = menuState;
    return filteredCommands[selectedIndex] || null;
  }, [menuState]);

  /**
   * 检查菜单是否应该显示
   */
  const shouldShowMenu = useCallback((): boolean => {
    return menuState.isOpen && menuState.filteredCommands.length > 0;
  }, [menuState]);

  /**
   * 重置菜单状态
   */
  const resetMenu = useCallback(() => {
    setMenuState({
      isOpen: false,
      position: { x: 0, y: 0 },
      range: null,
      query: '',
      selectedIndex: 0,
      filteredCommands: [],
    });
  }, []);

  /**
   * 监听编辑器状态变化
   */
  useEffect(() => {
    if (!editor) return;

    // 监听编辑器失去焦点时关闭菜单
    const handleBlur = () => {
      setTimeout(() => {
        closeMenu();
      }, 150); // 延迟关闭，避免点击菜单项时立即关闭
    };

    const editorElement = editor.view.dom;
    editorElement.addEventListener('blur', handleBlur);

    return () => {
      editorElement.removeEventListener('blur', handleBlur);
    };
  }, [editor, closeMenu]);

  return {
    // 状态
    menuState,
    isOpen: menuState.isOpen,
    filteredCommands: menuState.filteredCommands,
    selectedIndex: menuState.selectedIndex,
    selectedCommand: getSelectedCommand(),
    
    // 操作方法
    openMenu,
    closeMenu,
    updateQuery,
    navigate,
    selectCommand,
    executeSelectedCommand,
    executeCommand,
    
    // 工具方法
    shouldShowMenu,
    resetMenu,
  };
}