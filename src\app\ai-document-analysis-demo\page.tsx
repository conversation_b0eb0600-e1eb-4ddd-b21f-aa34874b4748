'use client';

import React, { useState } from 'react';
import { Editor } from '@/components/editor/Editor';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

/**
 * AI 文档分析功能演示页面
 */
export default function AIDocumentAnalysisDemoPage() {
  const [content, setContent] = useState(`
    <h1>人工智能技术的发展与应用</h1>
    
    <p>人工智能（Artificial Intelligence，简称AI）是计算机科学的一个重要分支，旨在创建能够模拟人类智能行为的系统。近年来，随着计算能力的提升和大数据技术的发展，人工智能技术取得了突破性进展。</p>
    
    <h2>技术发展历程</h2>
    
    <p>人工智能的发展可以追溯到20世纪50年代。从早期的符号主义方法，到后来的专家系统，再到现在的深度学习和神经网络，AI技术经历了多个发展阶段。</p>
    
    <p>深度学习作为机器学习的一个子集，通过构建多层神经网络来模拟人脑的学习过程。这种方法在图像识别、自然语言处理、语音识别等领域取得了显著成果。</p>
    
    <h2>主要应用领域</h2>
    
    <p>目前，人工智能技术已经广泛应用于各个行业：</p>
    
    <ul>
      <li><strong>医疗健康</strong>：AI辅助诊断、药物研发、个性化治疗方案</li>
      <li><strong>金融服务</strong>：风险评估、算法交易、反欺诈检测</li>
      <li><strong>交通运输</strong>：自动驾驶、智能交通管理、路径优化</li>
      <li><strong>教育培训</strong>：个性化学习、智能辅导、教学评估</li>
      <li><strong>制造业</strong>：智能制造、质量控制、预测性维护</li>
    </ul>
    
    <h2>技术挑战与机遇</h2>
    
    <p>尽管AI技术发展迅速，但仍面临诸多挑战。数据隐私和安全问题、算法的可解释性、技术伦理等都是需要解决的重要问题。同时，AI技术的发展也带来了巨大的机遇，包括提高生产效率、创造新的商业模式、改善人类生活质量等。</p>
    
    <h2>未来展望</h2>
    
    <p>展望未来，人工智能技术将继续快速发展。通用人工智能（AGI）的实现、量子计算与AI的结合、边缘计算的普及等都将推动AI技术进入新的发展阶段。我们有理由相信，人工智能将在未来发挥更加重要的作用，为人类社会的进步做出更大贡献。</p>
    
    <blockquote>
      <p>"人工智能是我们这个时代最重要的技术之一，它将重新定义我们的工作方式、生活方式和思考方式。" —— 科技专家</p>
    </blockquote>
    
    <p>总的来说，人工智能技术的发展前景广阔，但也需要我们以负责任的态度来推进其发展和应用，确保技术进步能够真正造福人类社会。</p>
  `);

  const [selectedDemo, setSelectedDemo] = useState('tech');

  const demoTexts = {
    tech: {
      title: '人工智能技术的发展与应用',
      content: `
        <h1>人工智能技术的发展与应用</h1>
        
        <p>人工智能（Artificial Intelligence，简称AI）是计算机科学的一个重要分支，旨在创建能够模拟人类智能行为的系统。近年来，随着计算能力的提升和大数据技术的发展，人工智能技术取得了突破性进展。</p>
        
        <h2>技术发展历程</h2>
        
        <p>人工智能的发展可以追溯到20世纪50年代。从早期的符号主义方法，到后来的专家系统，再到现在的深度学习和神经网络，AI技术经历了多个发展阶段。</p>
        
        <p>深度学习作为机器学习的一个子集，通过构建多层神经网络来模拟人脑的学习过程。这种方法在图像识别、自然语言处理、语音识别等领域取得了显著成果。</p>
        
        <h2>主要应用领域</h2>
        
        <p>目前，人工智能技术已经广泛应用于各个行业：</p>
        
        <ul>
          <li><strong>医疗健康</strong>：AI辅助诊断、药物研发、个性化治疗方案</li>
          <li><strong>金融服务</strong>：风险评估、算法交易、反欺诈检测</li>
          <li><strong>交通运输</strong>：自动驾驶、智能交通管理、路径优化</li>
          <li><strong>教育培训</strong>：个性化学习、智能辅导、教学评估</li>
          <li><strong>制造业</strong>：智能制造、质量控制、预测性维护</li>
        </ul>
        
        <h2>技术挑战与机遇</h2>
        
        <p>尽管AI技术发展迅速，但仍面临诸多挑战。数据隐私和安全问题、算法的可解释性、技术伦理等都是需要解决的重要问题。同时，AI技术的发展也带来了巨大的机遇，包括提高生产效率、创造新的商业模式、改善人类生活质量等。</p>
        
        <h2>未来展望</h2>
        
        <p>展望未来，人工智能技术将继续快速发展。通用人工智能（AGI）的实现、量子计算与AI的结合、边缘计算的普及等都将推动AI技术进入新的发展阶段。我们有理由相信，人工智能将在未来发挥更加重要的作用，为人类社会的进步做出更大贡献。</p>
        
        <blockquote>
          <p>"人工智能是我们这个时代最重要的技术之一，它将重新定义我们的工作方式、生活方式和思考方式。" —— 科技专家</p>
        </blockquote>
        
        <p>总的来说，人工智能技术的发展前景广阔，但也需要我们以负责任的态度来推进其发展和应用，确保技术进步能够真正造福人类社会。</p>
      `
    },
    business: {
      title: '数字化转型战略报告',
      content: `
        <h1>数字化转型战略报告</h1>
        
        <h2>执行摘要</h2>
        
        <p>本报告分析了当前企业数字化转型的趋势、挑战和机遇。通过对多个行业案例的研究，我们发现数字化转型已成为企业保持竞争优势的关键因素。</p>
        
        <h2>市场现状分析</h2>
        
        <p>根据最新的市场调研数据，超过80%的企业已经启动或计划启动数字化转型项目。主要驱动因素包括：</p>
        
        <ol>
          <li>客户需求的变化</li>
          <li>技术成本的降低</li>
          <li>竞争压力的增加</li>
          <li>监管环境的变化</li>
        </ol>
        
        <h2>关键技术趋势</h2>
        
        <p>云计算、人工智能、物联网和区块链等技术正在重塑商业模式。企业需要制定综合性的技术战略，以充分利用这些新兴技术的潜力。</p>
        
        <h2>实施建议</h2>
        
        <p>基于我们的分析，我们建议企业采取以下步骤：</p>
        
        <ul>
          <li>建立数字化转型领导团队</li>
          <li>制定清晰的转型路线图</li>
          <li>投资员工培训和技能提升</li>
          <li>建立敏捷的组织文化</li>
          <li>持续监测和优化转型进程</li>
        </ul>
        
        <h2>结论</h2>
        
        <p>数字化转型不仅是技术升级，更是组织变革的过程。成功的转型需要领导层的坚定承诺、全员的积极参与和持续的投入。</p>
      `
    },
    academic: {
      title: '机器学习在医疗诊断中的应用研究',
      content: `
        <h1>机器学习在医疗诊断中的应用研究</h1>
        
        <h2>摘要</h2>
        
        <p>本研究探讨了机器学习技术在医疗诊断领域的应用现状和发展前景。通过文献综述和案例分析，我们发现机器学习在提高诊断准确性、降低医疗成本和改善患者体验方面具有显著优势。</p>
        
        <h2>引言</h2>
        
        <p>随着医疗数据的爆炸式增长和计算能力的提升，机器学习技术在医疗领域的应用日益广泛。从影像诊断到基因分析，从药物发现到个性化治疗，机器学习正在革命性地改变医疗实践。</p>
        
        <h2>文献综述</h2>
        
        <p>近年来，大量研究表明机器学习在医疗诊断中的有效性。深度学习模型在皮肤癌检测、眼底病变识别、肺部结节分析等方面的表现已经达到或超过专业医生的水平。</p>
        
        <h2>方法论</h2>
        
        <p>本研究采用系统性文献综述的方法，检索了2018-2023年间发表的相关研究论文。我们重点关注以下几个方面：</p>
        
        <ul>
          <li>算法性能评估</li>
          <li>临床应用效果</li>
          <li>实施挑战和解决方案</li>
          <li>伦理和法律考量</li>
        </ul>
        
        <h2>研究发现</h2>
        
        <p>我们的分析显示，机器学习在医疗诊断中的应用主要集中在以下领域：</p>
        
        <ol>
          <li><strong>医学影像分析</strong>：包括X光、CT、MRI等影像的自动分析和诊断</li>
          <li><strong>病理学诊断</strong>：通过分析组织切片来辅助病理诊断</li>
          <li><strong>基因组学分析</strong>：利用基因数据进行疾病风险预测</li>
          <li><strong>临床决策支持</strong>：基于患者数据提供治疗建议</li>
        </ol>
        
        <h2>讨论</h2>
        
        <p>尽管机器学习在医疗诊断中显示出巨大潜力，但仍面临数据质量、算法可解释性、监管审批等挑战。未来的研究需要在技术创新和临床实用性之间找到平衡。</p>
        
        <h2>结论</h2>
        
        <p>机器学习技术在医疗诊断领域的应用前景广阔，但需要医疗专业人员、技术开发者和监管机构的共同努力，以确保这些技术能够安全、有效地服务于患者。</p>
        
        <h2>参考文献</h2>
        
        <p>[此处应包含相关的学术参考文献]</p>
      `
    }
  };

  const insertDemoText = (demo: keyof typeof demoTexts) => {
    setContent(demoTexts[demo].content);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AI 文档分析功能演示
          </h1>
          <p className="text-gray-600">
            体验强大的 AI 文档分析功能，包括摘要生成、关键词提取、大纲生成和内容分析
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 侧边栏 - 功能说明和示例文档 */}
          <div className="lg:col-span-1 space-y-4">
            <Card className="p-4">
              <h3 className="font-semibold mb-3">分析功能</h3>
              <ul className="text-sm space-y-2 text-gray-600">
                <li>📄 文档摘要生成</li>
                <li>🏷️ 关键词提取</li>
                <li>📋 结构化大纲生成</li>
                <li>📊 内容质量分析</li>
                <li>🎯 主题识别</li>
                <li>📈 可读性评估</li>
              </ul>
            </Card>

            <Card className="p-4">
              <h3 className="font-semibold mb-3">使用方法</h3>
              <ol className="text-sm space-y-2 text-gray-600">
                <li>1. 输入或选择文档内容</li>
                <li>2. 使用斜杠命令或快捷键</li>
                <li>3. 查看分析结果</li>
                <li>4. 复制或应用结果</li>
              </ol>
            </Card>

            <Card className="p-4">
              <h3 className="font-semibold mb-3">示例文档</h3>
              <Tabs value={selectedDemo} onValueChange={setSelectedDemo}>
                <TabsList className="grid w-full grid-cols-1">
                  <TabsTrigger value="tech">技术文章</TabsTrigger>
                </TabsList>
                <TabsContent value="tech" className="space-y-2">
                  <div className="text-xs text-gray-500 mb-2">点击加载示例文档：</div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText('tech')}
                  >
                    <div className="text-xs">
                      <div className="font-medium">AI技术发展</div>
                      <div className="text-gray-500">技术类长文档</div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText('business')}
                  >
                    <div className="text-xs">
                      <div className="font-medium">数字化转型</div>
                      <div className="text-gray-500">商业报告</div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-left h-auto p-2"
                    onClick={() => insertDemoText('academic')}
                  >
                    <div className="text-xs">
                      <div className="font-medium">学术研究</div>
                      <div className="text-gray-500">学术论文</div>
                    </div>
                  </Button>
                </TabsContent>
              </Tabs>
            </Card>

            <Card className="p-4">
              <h3 className="font-semibold mb-3">快捷键</h3>
              <div className="text-sm space-y-1 text-gray-600">
                <div><kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">⌘⇧S</kbd> 生成摘要</div>
                <div><kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">⌘⇧K</kbd> 提取关键词</div>
                <div><kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">⌘⇧O</kbd> 生成大纲</div>
                <div><kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">⌘⇧A</kbd> 内容分析</div>
              </div>
            </Card>
          </div>

          {/* 主编辑区域 */}
          <div className="lg:col-span-3">
            <Card className="p-6">
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-xl font-semibold">文档编辑器</h2>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <span>使用斜杠命令或快捷键进行分析</span>
                </div>
              </div>
              
              <div className="border rounded-lg overflow-hidden">
                <Editor
                  content={content}
                  onChange={setContent}
                  placeholder="输入文档内容或加载示例文档..."
                  enableAI={true}
                  className="min-h-[600px]"
                />
              </div>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">💡 使用提示</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 使用斜杠命令 "/" 可以快速访问 AI 分析功能</li>
                  <li>• 支持键盘快捷键快速分析文档</li>
                  <li>• 分析结果支持复制和导出</li>
                  <li>• 可以对同一文档进行多种类型的分析</li>
                </ul>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}