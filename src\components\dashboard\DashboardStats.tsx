'use client';

import { DocumentStats } from '@/lib/storage/document-service';

interface DashboardStatsProps {
  stats: DocumentStats | null;
  loading: boolean;
}

export function DashboardStats({ stats, loading }: DashboardStatsProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white p-6 rounded-lg shadow animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-8 bg-gray-200 rounded mb-1"></div>
            <div className="h-3 bg-gray-200 rounded w-16"></div>
          </div>
        ))}
      </div>
    );
  }

  const statCards = [
    {
      title: '我的文档',
      value: stats?.totalDocuments || 0,
      unit: '个文档',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      icon: '📄'
    },
    {
      title: '总字数',
      value: stats?.totalWords || 0,
      unit: '个字',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      icon: '✍️',
      format: true
    },
    {
      title: '总字符数',
      value: stats?.totalCharacters || 0,
      unit: '个字符',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      icon: '📝',
      format: true
    },
    {
      title: '平均字数',
      value: stats?.totalDocuments ? Math.round((stats.totalWords || 0) / stats.totalDocuments) : 0,
      unit: '字/文档',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      icon: '📊',
      format: true
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {statCards.map((card, index) => (
        <div key={index} className={`bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow ${card.bgColor} border-l-4 border-l-current`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-600 mb-1">
                {card.title}
              </h3>
              <p className={`text-2xl font-bold ${card.color}`}>
                {card.format ? card.value.toLocaleString() : card.value}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {card.unit}
              </p>
            </div>
            <div className="text-2xl opacity-60">
              {card.icon}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export default DashboardStats;