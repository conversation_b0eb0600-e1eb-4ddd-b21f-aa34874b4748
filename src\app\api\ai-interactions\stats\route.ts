/**
 * AI 交互统计信息 API 路由
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { AIInteractionHistoryService } from '@/lib/services/ai-interaction-history';

/**
 * 获取 AI 交互统计信息
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const stats = await AIInteractionHistoryService.getInteractionStats(
      session.user.id
    );
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error('获取 AI 交互统计信息失败:', error);
    return NextResponse.json(
      { error: '获取统计信息失败' },
      { status: 500 }
    );
  }
}
