/* 斜杠命令相关样式 */

/* 斜杠命令查询高亮 */
.slash-command-query {
  @apply bg-primary/10 text-primary rounded px-1;
  position: relative;
}

.slash-command-query::before {
  content: '';
  @apply absolute inset-0 bg-primary/20 rounded;
  animation: pulse 1.5s ease-in-out infinite;
}

/* 斜杠命令菜单动画 */
.slash-command-menu {
  animation: slideIn 0.15s ease-out;
  transform-origin: top left;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-8px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 命令项悬停效果 */
.slash-command-item {
  @apply transition-all duration-150 ease-in-out;
}

.slash-command-item:hover {
  @apply transform translate-x-1;
}

.slash-command-item.selected {
  @apply transform translate-x-1;
}

/* 命令图标样式 */
.slash-command-icon {
  @apply flex-shrink-0 w-8 h-8 flex items-center justify-center;
  @apply bg-muted rounded text-sm font-medium;
  @apply transition-colors duration-150;
}

.slash-command-item:hover .slash-command-icon,
.slash-command-item.selected .slash-command-icon {
  @apply bg-primary text-primary-foreground;
}

/* 快捷键显示样式 */
.slash-command-shortcut {
  @apply text-xs text-muted-foreground font-mono;
  @apply px-2 py-1 bg-muted rounded;
}

/* 分类标题样式 */
.slash-command-category {
  @apply text-xs font-semibold text-muted-foreground uppercase tracking-wide;
  @apply px-3 py-2 border-b border-border;
}

/* 无结果提示样式 */
.slash-command-empty {
  @apply text-center text-sm text-muted-foreground py-8;
}

.slash-command-empty::before {
  content: '🔍';
  @apply block text-2xl mb-2 opacity-50;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .slash-command-menu {
    @apply w-72 max-h-64;
  }
  
  .slash-command-shortcut {
    @apply hidden;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .slash-command-query {
    @apply bg-primary/20 text-primary-foreground;
  }
  
  .slash-command-query::before {
    @apply bg-primary/30;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .slash-command-query {
    @apply border border-primary;
  }
  
  .slash-command-item {
    @apply border-b border-border/50;
  }
  
  .slash-command-item:last-child {
    @apply border-b-0;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .slash-command-menu {
    animation: none;
  }
  
  .slash-command-query::before {
    animation: none;
  }
  
  .slash-command-item {
    @apply transition-none;
  }
  
  .slash-command-item:hover,
  .slash-command-item.selected {
    @apply transform-none;
  }
}

/* 表格样式 */
.editor-table {
  @apply border-collapse border border-border rounded-lg overflow-hidden;
  margin: 1.5rem 0;
  width: 100%;
}

.editor-table td,
.editor-table th {
  @apply border border-border px-3 py-2 text-left;
  min-width: 100px;
  position: relative;
}

.editor-table th {
  @apply bg-muted font-semibold;
}

.editor-table td {
  @apply bg-background;
}

.editor-table .selectedCell {
  @apply bg-primary/10;
}

/* 分割线样式 */
.editor-hr {
  @apply border-none border-t-2 border-border my-6;
  height: 2px;
  background: linear-gradient(90deg, transparent, hsl(var(--border)), transparent);
}

/* 任务列表样式 */
.task-list {
  @apply list-none pl-0;
}

.task-item {
  @apply flex items-start gap-2 my-2;
}

.task-item > label {
  @apply flex items-center gap-2 cursor-pointer;
}

.task-item > label > input[type="checkbox"] {
  @apply w-4 h-4 rounded border-2 border-border;
}

.task-item > label > input[type="checkbox"]:checked {
  @apply bg-primary border-primary;
}

.task-item > div {
  @apply flex-1;
}

.task-item[data-checked="true"] > div {
  @apply line-through text-muted-foreground;
}

/* 提示框样式 */
.callout {
  @apply flex gap-3 p-4 my-4 rounded-lg border-l-4;
}

.callout-icon {
  @apply flex-shrink-0 text-lg;
}

.callout-content {
  @apply flex-1;
}

.callout-content p {
  @apply m-0;
}

.callout-info {
  @apply bg-blue-50 border-blue-500 text-blue-900;
}

.callout-warning {
  @apply bg-yellow-50 border-yellow-500 text-yellow-900;
}

.callout-success {
  @apply bg-green-50 border-green-500 text-green-900;
}

.callout-error {
  @apply bg-red-50 border-red-500 text-red-900;
}

/* 深色模式下的提示框样式 */
@media (prefers-color-scheme: dark) {
  .callout-info {
    @apply bg-blue-950 border-blue-400 text-blue-100;
  }

  .callout-warning {
    @apply bg-yellow-950 border-yellow-400 text-yellow-100;
  }

  .callout-success {
    @apply bg-green-950 border-green-400 text-green-100;
  }

  .callout-error {
    @apply bg-red-950 border-red-400 text-red-100;
  }
}

/* 图片样式 */
.editor-image {
  @apply max-w-full h-auto rounded-lg shadow-sm;
  margin: 1rem 0;
}

.editor-image:hover {
  @apply shadow-md;
}

/* 链接样式 */
.editor-link {
  @apply text-primary underline decoration-primary/30 hover:decoration-primary;
  text-underline-offset: 2px;
  transition: all 0.2s ease;
}

.editor-link:hover {
  @apply decoration-2;
}

/* PDF 链接样式 */
.pdf-link {
  @apply block my-4 p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors;
}

.pdf-link-content {
  @apply flex items-center gap-3 no-underline;
}

.pdf-link-icon {
  @apply text-2xl flex-shrink-0;
}

.pdf-link-info {
  @apply flex-1 min-w-0;
}

.pdf-link-title {
  @apply font-medium text-foreground text-sm;
}

.pdf-link-description {
  @apply text-xs text-muted-foreground mt-1;
}

.pdf-link-url {
  @apply text-xs text-muted-foreground mt-1 truncate;
}

.pdf-link-arrow {
  @apply text-muted-foreground flex-shrink-0;
}

/* PDF 嵌入样式 */
.pdf-embed {
  @apply my-4 border border-border rounded-lg overflow-hidden;
}

.pdf-embed-header {
  @apply flex items-center gap-3 p-4 bg-muted/30 border-b border-border;
}

.pdf-embed-icon {
  @apply text-2xl flex-shrink-0;
}

.pdf-embed-info {
  @apply flex-1 min-w-0;
}

.pdf-embed-title {
  @apply font-medium text-foreground text-sm;
}

.pdf-embed-description {
  @apply text-xs text-muted-foreground mt-1;
}

.pdf-embed-link {
  @apply text-muted-foreground hover:text-foreground transition-colors p-1 rounded;
}

.pdf-embed-preview {
  @apply bg-background;
}

.pdf-embed-preview iframe {
  @apply w-full border-0;
}

/* 媒体插入弹窗动画 */
.media-insert-overlay {
  animation: fadeIn 0.2s ease-out;
}

.media-insert-content {
  animation: slideIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 拖拽上传区域样式 */
.drag-drop-area {
  @apply transition-all duration-200;
}

.drag-drop-area.drag-over {
  @apply border-primary bg-primary/5;
}

/* 上传进度条样式 */
.upload-progress {
  @apply relative overflow-hidden;
}

.upload-progress::after {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 响应式调整 */
@media (max-width: 640px) {
  .pdf-link {
    @apply p-3;
  }
  
  .pdf-embed-header {
    @apply p-3;
  }
  
  .pdf-embed-preview iframe {
    height: 300px;
  }
  
  .editor-image {
    @apply max-w-full;
  }
}