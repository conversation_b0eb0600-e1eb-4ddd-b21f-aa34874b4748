'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Editor } from '@tiptap/react';
import { SlashCommand, SlashCommandCategory } from '@/types/slash-command.types';
import { SLASH_COMMANDS, filterSlashCommands, findSlashCommand } from '@/lib/editor/slash-commands';
import { cn } from '@/lib/utils';

interface SlashCommandMenuProps {
  editor: Editor | null;
  className?: string;
}

interface MenuState {
  isOpen: boolean;
  position: { x: number; y: number };
  query: string;
  selectedIndex: number;
  filteredCommands: SlashCommand[];
  range: { from: number; to: number } | null;
}

/**
 * 斜杠命令菜单组件
 * 显示可用的斜杠命令并处理用户交互
 */
export function SlashCommandMenu({ editor, className }: SlashCommandMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuState, setMenuState] = useState<MenuState>({
    isOpen: false,
    position: { x: 0, y: 0 },
    query: '',
    selectedIndex: 0,
    filteredCommands: [],
    range: null,
  });

  /**
   * 计算菜单位置
   */
  const calculateMenuPosition = useCallback((range: { from: number; to: number }) => {
    if (!editor) return { x: 0, y: 0 };

    const { view } = editor;
    const { from } = range;
    
    // 获取光标位置的 DOM 坐标
    const coords = view.coordsAtPos(from);
    const editorRect = view.dom.getBoundingClientRect();
    
    // 计算相对于编辑器的位置
    const x = coords.left - editorRect.left;
    const y = coords.bottom - editorRect.top + 8; // 8px 间距
    
    return { x, y };
  }, [editor]);

  /**
   * 打开命令菜单
   */
  const openMenu = useCallback((query: string, range: { from: number; to: number }) => {
    const filteredCommands = filterSlashCommands(query);
    const position = calculateMenuPosition(range);
    
    setMenuState({
      isOpen: true,
      position,
      query,
      selectedIndex: 0,
      filteredCommands,
      range,
    });
  }, [calculateMenuPosition]);

  /**
   * 关闭命令菜单
   */
  const closeMenu = useCallback(() => {
    setMenuState(prev => ({
      ...prev,
      isOpen: false,
      selectedIndex: 0,
      filteredCommands: [],
      range: null,
    }));
  }, []);

  /**
   * 更新查询
   */
  const updateQuery = useCallback((query: string, range: { from: number; to: number }) => {
    const filteredCommands = filterSlashCommands(query);
    const position = calculateMenuPosition(range);
    
    setMenuState(prev => ({
      ...prev,
      position,
      query,
      selectedIndex: 0,
      filteredCommands,
      range,
    }));
  }, [calculateMenuPosition]);

  /**
   * 导航选择
   */
  const navigate = useCallback((direction: 'up' | 'down') => {
    setMenuState(prev => {
      const { filteredCommands, selectedIndex } = prev;
      let newIndex = selectedIndex;
      
      if (direction === 'up') {
        newIndex = selectedIndex > 0 ? selectedIndex - 1 : filteredCommands.length - 1;
      } else {
        newIndex = selectedIndex < filteredCommands.length - 1 ? selectedIndex + 1 : 0;
      }
      
      return {
        ...prev,
        selectedIndex: newIndex,
      };
    });
  }, []);

  /**
   * 执行选中的命令
   */
  const executeSelectedCommand = useCallback(() => {
    const { filteredCommands, selectedIndex, range } = menuState;
    
    if (!editor || !range || filteredCommands.length === 0) {
      return;
    }
    
    const selectedCommand = filteredCommands[selectedIndex];
    if (selectedCommand) {
      executeCommand(selectedCommand);
    }
  }, [editor, menuState]);

  /**
   * 执行指定命令
   */
  const executeCommand = useCallback(async (command: SlashCommand) => {
    if (!editor || !menuState.range) {
      return;
    }

    try {
      // 关闭菜单
      closeMenu();
      
      // 执行命令
      await command.action(editor, menuState.range);
      
      // 聚焦编辑器
      editor.commands.focus();
    } catch (error) {
      console.error('执行斜杠命令时出错:', error);
    }
  }, [editor, menuState.range, closeMenu]);

  /**
   * 处理编辑器事件
   */
  useEffect(() => {
    if (!editor) return;

    const editorElement = editor.view.dom;

    // 监听斜杠命令事件
    const handleQueryUpdate = (event: CustomEvent) => {
      const { query, range } = event.detail;
      updateQuery(query, range);
    };

    const handleClose = () => {
      closeMenu();
    };

    const handleNavigate = (event: CustomEvent) => {
      const { direction } = event.detail;
      navigate(direction);
    };

    const handleEnter = () => {
      executeSelectedCommand();
    };

    const handleExecute = (event: CustomEvent) => {
      const { commandId } = event.detail;
      const command = findSlashCommand(commandId);
      if (command) {
        executeCommand(command);
      }
    };

    // 添加事件监听器
    editorElement.addEventListener('slash-command-query-update', handleQueryUpdate as EventListener);
    editorElement.addEventListener('slash-command-close', handleClose);
    editorElement.addEventListener('slash-command-navigate', handleNavigate as EventListener);
    editorElement.addEventListener('slash-command-enter', handleEnter);
    editorElement.addEventListener('slash-command-execute', handleExecute as EventListener);

    // 清理事件监听器
    return () => {
      editorElement.removeEventListener('slash-command-query-update', handleQueryUpdate as EventListener);
      editorElement.removeEventListener('slash-command-close', handleClose);
      editorElement.removeEventListener('slash-command-navigate', handleNavigate as EventListener);
      editorElement.removeEventListener('slash-command-enter', handleEnter);
      editorElement.removeEventListener('slash-command-execute', handleExecute as EventListener);
    };
  }, [editor, updateQuery, closeMenu, navigate, executeSelectedCommand, executeCommand]);

  /**
   * 监听斜杠命令扩展的存储变化
   */
  useEffect(() => {
    if (!editor) return;

    const checkSlashCommandState = () => {
      const extension = editor.extensionManager.extensions.find(ext => ext.name === 'slashCommand');
      if (extension?.storage) {
        const { isOpen, range, query } = extension.storage;
        
        if (isOpen && range && !menuState.isOpen) {
          openMenu(query, range);
        } else if (!isOpen && menuState.isOpen) {
          closeMenu();
        }
      }
    };

    // 定期检查状态
    const interval = setInterval(checkSlashCommandState, 100);
    
    return () => clearInterval(interval);
  }, [editor, menuState.isOpen, openMenu, closeMenu]);

  /**
   * 处理点击外部关闭菜单
   */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        closeMenu();
      }
    };

    if (menuState.isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [menuState.isOpen, closeMenu]);

  if (!menuState.isOpen || menuState.filteredCommands.length === 0) {
    return null;
  }

  return (
    <div
      ref={menuRef}
      className={cn(
        'absolute z-50 w-72 sm:w-80 max-h-64 sm:max-h-80 overflow-y-auto',
        'bg-background border border-border rounded-lg shadow-lg',
        'py-2 touch-manipulation',
        className
      )}
      style={{
        left: Math.min(menuState.position.x, window.innerWidth - (window.innerWidth < 640 ? 288 : 320) - 16),
        top: menuState.position.y,
      }}
    >
      {/* 搜索提示 */}
      {menuState.query && (
        <div className="px-3 py-2 text-xs text-muted-foreground border-b border-border">
          搜索: "{menuState.query}"
        </div>
      )}

      {/* 命令列表 */}
      <div className="space-y-1">
        {menuState.filteredCommands.map((command, index) => (
          <button
            key={command.id}
            className={cn(
              'w-full flex items-center gap-3 px-3 py-3 sm:py-2 text-left',
              'hover:bg-accent hover:text-accent-foreground',
              'transition-colors duration-150 touch-manipulation',
              'active:bg-accent/80',
              index === menuState.selectedIndex && 'bg-accent text-accent-foreground'
            )}
            onClick={() => executeCommand(command)}
            onMouseEnter={() => setMenuState(prev => ({ ...prev, selectedIndex: index }))}
          >
            {/* 图标 */}
            <div className="flex-shrink-0 w-8 h-8 sm:w-8 sm:h-8 flex items-center justify-center bg-muted rounded text-sm">
              {command.icon}
            </div>
            
            {/* 内容 */}
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm sm:text-sm">{command.label}</div>
              <div className="text-xs text-muted-foreground truncate hidden sm:block">
                {command.description}
              </div>
            </div>
            
            {/* 快捷键 - 移动端隐藏 */}
            {command.shortcut && (
              <div className="flex-shrink-0 text-xs text-muted-foreground hidden sm:block">
                {command.shortcut}
              </div>
            )}
          </button>
        ))}
      </div>

      {/* 无结果提示 */}
      {menuState.filteredCommands.length === 0 && (
        <div className="px-3 py-4 text-center text-sm text-muted-foreground">
          未找到匹配的命令
        </div>
      )}
    </div>
  );
}

/**
 * 按分类显示的斜杠命令菜单（备用组件）
 */
export function SlashCommandMenuByCategory({ editor, className }: SlashCommandMenuProps) {
  // 类似的实现，但按分类显示命令
  // 这里可以作为未来的增强功能
  return null;
}

export default SlashCommandMenu;