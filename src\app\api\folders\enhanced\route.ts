import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/db/prisma';
import { FolderService } from '@/lib/services/folder-service';
import { 
  withAuth, 
  withValidation, 
  withRateLimit, 
  withLogging,
  compose,
  APIError 
} from '@/lib/api/middleware';

// 文件夹创建的验证模式
const createFolderSchema = z.object({
  name: z.string().min(1, '文件夹名称不能为空').max(255, '文件夹名称长度不能超过255个字符'),
  parentId: z.string().optional(),
});

/**
 * 创建文件夹的处理函数
 */
async function createFolderHandler(
  request: NextRequest,
  session: any,
  validatedData: z.infer<typeof createFolderSchema>
) {
  const { name, parentId } = validatedData;

  // 检查文件夹名称是否重复
  const isDuplicate = await FolderService.isFolderNameDuplicate(
    name,
    session.user.id,
    parentId
  );

  if (isDuplicate) {
    throw new APIError('同一位置已存在同名文件夹', 409, 'DUPLICATE_NAME');
  }

  // 如果指定了父文件夹ID，验证父文件夹是否存在且属于当前用户
  if (parentId) {
    const parentFolder = await FolderService.validateFolderAccess(parentId, session.user.id);
    if (!parentFolder) {
      throw new APIError('父文件夹未找到', 404, 'PARENT_NOT_FOUND');
    }
  }

  const folder = await prisma.folder.create({
    data: {
      name,
      parentId,
      userId: session.user.id,
    },
    include: {
      parent: {
        select: {
          id: true,
          name: true,
        },
      },
      children: true,
      documents: {
        select: {
          id: true,
          title: true,
          createdAt: true,
          updatedAt: true,
          wordCount: true,
          charCount: true,
        },
      },
      _count: {
        select: {
          documents: true,
          children: true,
        },
      },
    },
  });

  return NextResponse.json({ folder }, { status: 201 });
}

/**
 * 获取文件夹列表的处理函数
 */
async function getFoldersHandler(request: NextRequest, session: any) {
  const { searchParams } = new URL(request.url);
  const parentId = searchParams.get('parentId');
  const includeDocuments = searchParams.get('includeDocuments') === 'true';
  const flat = searchParams.get('flat') === 'true';
  const search = searchParams.get('search');

  // 如果有搜索关键词，使用搜索功能
  if (search) {
    const folders = await FolderService.searchFolders(
      session.user.id,
      search,
      { 
        parentId: parentId || undefined, 
        includeDocuments,
        limit: 50,
      }
    );

    // 为每个文件夹添加路径信息
    const foldersWithPath = await Promise.all(
      folders.map(async (folder) => {
        const path = await FolderService.getFolderPath(folder.id);
        return {
          ...folder,
          path,
          pathString: path.join(' / '),
        };
      })
    );

    return NextResponse.json({ 
      folders: foldersWithPath,
      total: folders.length,
      search: true,
    });
  }

  // 如果请求树结构
  if (!flat && !parentId) {
    const tree = await FolderService.buildFolderTree(session.user.id, includeDocuments);
    
    // 获取根目录下的文档（如果需要）
    let rootDocuments = [];
    if (includeDocuments) {
      rootDocuments = await prisma.document.findMany({
        where: {
          userId: session.user.id,
          folderId: null,
        },
        select: {
          id: true,
          title: true,
          createdAt: true,
          updatedAt: true,
          wordCount: true,
          charCount: true,
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });
    }

    return NextResponse.json({
      tree: {
        folders: tree,
        documents: rootDocuments,
      },
      total: tree.length,
    });
  }

  // 普通列表查询
  const whereClause: any = {
    userId: session.user.id,
  };

  if (parentId) {
    whereClause.parentId = parentId;
  } else if (searchParams.has('rootOnly')) {
    whereClause.parentId = null;
  }

  const includeOptions: any = {
    parent: {
      select: {
        id: true,
        name: true,
      },
    },
    _count: {
      select: {
        documents: true,
        children: true,
      },
    },
  };

  if (!flat) {
    includeOptions.children = {
      include: {
        _count: {
          select: {
            documents: true,
            children: true,
          },
        },
      },
    };
  }

  if (includeDocuments) {
    includeOptions.documents = {
      select: {
        id: true,
        title: true,
        createdAt: true,
        updatedAt: true,
        wordCount: true,
        charCount: true,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    };
  }

  const folders = await prisma.folder.findMany({
    where: whereClause,
    include: includeOptions,
    orderBy: [
      { name: 'asc' },
      { createdAt: 'desc' },
    ],
  });

  return NextResponse.json({ 
    folders,
    total: folders.length,
  });
}

// 应用中间件
export const POST = compose(
  withLogging,
  withRateLimit(30, 60 * 1000), // 每分钟最多30次请求
  withAuth,
  withValidation(createFolderSchema)
)(createFolderHandler);

export const GET = compose(
  withLogging,
  withRateLimit(100, 60 * 1000), // 每分钟最多100次请求
  withAuth
)(getFoldersHandler);