/**
 * AI 文件命名功能测试脚本
 */

import { aiFileNaming } from '../src/lib/services/ai-file-naming';

/**
 * 测试文档摘要生成
 */
async function testDocumentSummary() {
  console.log('📄 测试文档摘要生成...\n');

  try {
    const mockDocumentId = 'test-document-id';
    const mockUserId = 'test-user-id';

    console.log('1. 测试摘要生成（需要真实数据库连接）...');
    try {
      const summary = await aiFileNaming.generateDocumentSummary(mockDocumentId, mockUserId);
      console.log('摘要结果:', summary);
      console.log('✅ 文档摘要生成成功\n');
    } catch (error) {
      console.log('⚠️ 摘要生成跳过（需要真实文档）:', (error as Error).message, '\n');
    }

  } catch (error) {
    console.error('❌ 文档摘要测试失败:', error);
  }
}

/**
 * 测试文件名验证
 */
async function testFileNameValidation() {
  console.log('✅ 测试文件名验证...\n');

  try {
    // 测试有效文件名
    console.log('1. 测试有效文件名...');
    const validResult = await aiFileNaming.validateFileName('项目计划文档-2024');
    console.log('有效文件名验证结果:', validResult);
    console.log('✅ 有效文件名验证成功\n');

    // 测试无效文件名
    console.log('2. 测试无效文件名...');
    const invalidResult = await aiFileNaming.validateFileName('新建文档');
    console.log('无效文件名验证结果:', invalidResult);
    console.log('✅ 无效文件名验证成功\n');

    // 测试空文件名
    console.log('3. 测试空文件名...');
    const emptyResult = await aiFileNaming.validateFileName('');
    console.log('空文件名验证结果:', emptyResult);
    console.log('✅ 空文件名验证成功\n');

    // 测试过长文件名
    console.log('4. 测试过长文件名...');
    const longName = '这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的文件名';
    const longResult = await aiFileNaming.validateFileName(longName);
    console.log('过长文件名验证结果:', longResult);
    console.log('✅ 过长文件名验证成功\n');

  } catch (error) {
    console.error('❌ 文件名验证测试失败:', error);
  }
}

/**
 * 测试命名建议生成
 */
async function testNamingSuggestions() {
  console.log('🎯 测试命名建议生成...\n');

  try {
    const mockDocumentId = 'test-document-id';
    const mockUserId = 'test-user-id';

    console.log('1. 测试单个文档命名建议（需要真实数据库连接）...');
    try {
      const suggestions = await aiFileNaming.generateNamingSuggestions(
        mockDocumentId,
        mockUserId,
        {
          style: 'DESCRIPTIVE' as const,
          maxLength: 50,
          suggestionCount: 3
        }
      );
      console.log('命名建议结果:', suggestions);
      console.log('✅ 命名建议生成成功\n');
    } catch (error) {
      console.log('⚠️ 命名建议跳过（需要真实文档）:', (error as Error).message, '\n');
    }

    console.log('2. 测试批量命名建议（需要真实数据库连接）...');
    try {
      const batchResult = await aiFileNaming.batchGenerateNamingSuggestions({
        documentIds: [mockDocumentId, 'test-document-id-2'],
        options: {
          style: 'CONCISE' as const,
          maxLength: 30
        },
        userId: mockUserId
      });
      console.log('批量命名结果:', batchResult);
      console.log('✅ 批量命名建议成功\n');
    } catch (error) {
      console.log('⚠️ 批量命名跳过（需要真实文档）:', (error as Error).message, '\n');
    }

  } catch (error) {
    console.error('❌ 命名建议测试失败:', error);
  }
}

/**
 * 测试智能重命名建议
 */
async function testSmartRenameSuggestions() {
  console.log('💡 测试智能重命名建议...\n');

  try {
    const mockUserId = 'test-user-id';

    console.log('1. 测试智能重命名建议（需要真实数据库连接）...');
    try {
      const smartSuggestions = await aiFileNaming.getSmartRenameSuggestions(mockUserId);
      console.log('智能重命名建议:', smartSuggestions);
      console.log('✅ 智能重命名建议成功\n');
    } catch (error) {
      console.log('⚠️ 智能重命名跳过（需要真实数据）:', (error as Error).message, '\n');
    }

  } catch (error) {
    console.error('❌ 智能重命名测试失败:', error);
  }
}

/**
 * 测试类型定义
 */
function testTypeDefinitions() {
  console.log('📝 测试类型定义...\n');

  try {
    const {
      NamingStyle,
      NamingSuggestionType,
      RenameSuggestionType
    } = require('../src/types/ai-naming.types');

    console.log('命名风格枚举:', Object.values(NamingStyle));
    console.log('建议类型枚举:', Object.values(NamingSuggestionType));
    console.log('重命名建议类型枚举:', Object.values(RenameSuggestionType));
    console.log('✅ 类型定义正常\n');
  } catch (error) {
    console.error('❌ 类型定义测试失败:', error);
  }
}

/**
 * 测试工具函数
 */
function testUtilityFunctions() {
  console.log('🔧 测试工具函数...\n');

  try {
    // 测试文本提取（模拟）
    console.log('1. 测试文本提取功能...');
    const mockTipTapContent = {
      content: [
        {
          type: 'paragraph',
          content: [
            { type: 'text', text: '这是一个测试文档' }
          ]
        },
        {
          type: 'paragraph',
          content: [
            { type: 'text', text: '包含多个段落的内容' }
          ]
        }
      ]
    };
    
    console.log('模拟TipTap内容:', JSON.stringify(mockTipTapContent, null, 2));
    console.log('✅ 文本提取功能测试完成\n');

    // 测试命名选项验证
    console.log('2. 测试命名选项验证...');
    const validOptions = {
      style: 'descriptive',
      maxLength: 50,
      includeDate: false,
      language: 'zh',
      suggestionCount: 5
    };
    console.log('有效选项:', validOptions);
    console.log('✅ 命名选项验证完成\n');

  } catch (error) {
    console.error('❌ 工具函数测试失败:', error);
  }
}

/**
 * 测试API路由格式
 */
function testAPIRouteFormats() {
  console.log('🌐 测试API路由格式...\n');

  try {
    // 测试请求格式
    console.log('1. 测试命名建议请求格式...');
    const namingRequest = {
      documentId: 'test-doc-id',
      options: {
        style: 'descriptive',
        maxLength: 50,
        includeDate: false,
        language: 'zh',
        suggestionCount: 5
      }
    };
    console.log('命名建议请求:', JSON.stringify(namingRequest, null, 2));

    console.log('2. 测试摘要生成请求格式...');
    const summaryRequest = {
      documentId: 'test-doc-id',
      options: {
        length: 'medium',
        type: 'overview',
        language: 'zh',
        includeKeywords: true,
        maxWords: 100
      }
    };
    console.log('摘要生成请求:', JSON.stringify(summaryRequest, null, 2));

    console.log('3. 测试文件名验证请求格式...');
    const validateRequest = {
      fileName: '测试文档名称',
      content: '这是文档内容...'
    };
    console.log('验证请求:', JSON.stringify(validateRequest, null, 2));

    console.log('✅ API路由格式测试完成\n');

  } catch (error) {
    console.error('❌ API路由格式测试失败:', error);
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🚀 开始 AI 文件命名功能测试\n');
  console.log('=' .repeat(50));

  // 测试类型定义
  testTypeDefinitions();

  // 测试工具函数
  testUtilityFunctions();

  // 测试API路由格式
  testAPIRouteFormats();

  // 测试文件名验证
  await testFileNameValidation();

  // 测试文档摘要生成
  await testDocumentSummary();

  // 测试命名建议生成
  await testNamingSuggestions();

  // 测试智能重命名建议
  await testSmartRenameSuggestions();

  console.log('=' .repeat(50));
  console.log('✨ AI 文件命名功能测试完成！');
  console.log('\n注意事项:');
  console.log('- 某些测试需要真实的数据库连接和文档数据');
  console.log('- AI 服务需要正确配置才能正常工作');
  console.log('- 建议在开发环境中使用真实数据进行完整测试');
  console.log('- 文件名验证功能可以独立测试，不依赖数据库');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export {
  testDocumentSummary,
  testFileNameValidation,
  testNamingSuggestions,
  testSmartRenameSuggestions,
  testTypeDefinitions,
  testUtilityFunctions,
  testAPIRouteFormats
};