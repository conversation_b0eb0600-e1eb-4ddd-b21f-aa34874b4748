/**
 * AI 处理状态栏组件
 * 在页面底部显示当前的 AI 处理状态
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  ChevronUp, 
  ChevronDown, 
  X, 
  Loader2, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Zap,
  Clock,
  Activity
} from 'lucide-react';
import { AIProcessingIndicator } from './AIProcessingIndicator';
import { useMultipleAIProcessing } from '@/hooks/use-ai-processing';
import type { AIProcessingProgress } from '@/types/ai-status.types';

/**
 * AI 处理状态栏属性
 */
interface AIProcessingStatusBarProps {
  /** 文档 ID */
  documentId?: string;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * AI 处理状态栏组件
 */
export function AIProcessingStatusBar({
  documentId,
  showDetails = true,
  className = '',
}: AIProcessingStatusBarProps) {
  const {
    allProgress,
    activeCount,
    stats,
    cancelProcessing,
    cancelAllProcessing,
  } = useMultipleAIProcessing(documentId);

  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // 控制状态栏的显示/隐藏
  useEffect(() => {
    const hasActiveProcessing = activeCount > 0;
    const hasRecentCompleted = Array.from(allProgress.values()).some(
      progress => (progress.status === 'completed' || progress.status === 'error') &&
                  Date.now() - progress.startTime.getTime() < 10000 // 10秒内完成的
    );

    setIsVisible(hasActiveProcessing || hasRecentCompleted);

    // 如果有活动处理，自动展开
    if (hasActiveProcessing && !isExpanded) {
      setIsExpanded(true);
    }
  }, [activeCount, allProgress, isExpanded]);

  if (!isVisible) {
    return null;
  }

  /**
   * 获取状态栏摘要信息
   */
  const getSummaryInfo = () => {
    const activeProcesses = Array.from(allProgress.values()).filter(
      progress => progress.status === 'preparing' || 
                 progress.status === 'connecting' || 
                 progress.status === 'processing' || 
                 progress.status === 'generating'
    );

    const completedProcesses = Array.from(allProgress.values()).filter(
      progress => progress.status === 'completed'
    );

    const errorProcesses = Array.from(allProgress.values()).filter(
      progress => progress.status === 'error'
    );

    return {
      active: activeProcesses.length,
      completed: completedProcesses.length,
      errors: errorProcesses.length,
      totalProgress: activeProcesses.length > 0 
        ? Math.round(activeProcesses.reduce((sum, p) => sum + p.overallProgress, 0) / activeProcesses.length)
        : 100,
    };
  };

  const summary = getSummaryInfo();

  /**
   * 获取主要状态图标
   */
  const getMainStatusIcon = () => {
    if (summary.active > 0) {
      return <Loader2 className="w-4 h-4 animate-spin text-blue-600" />;
    } else if (summary.errors > 0) {
      return <XCircle className="w-4 h-4 text-red-600" />;
    } else if (summary.completed > 0) {
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    }
    return <Activity className="w-4 h-4 text-gray-600" />;
  };

  /**
   * 获取状态文本
   */
  const getStatusText = () => {
    if (summary.active > 0) {
      return `正在处理 ${summary.active} 个任务 (${summary.totalProgress}%)`;
    } else if (summary.errors > 0) {
      return `${summary.errors} 个任务失败`;
    } else if (summary.completed > 0) {
      return `${summary.completed} 个任务已完成`;
    }
    return 'AI 处理状态';
  };

  return (
    <div className={`fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg transition-all duration-300 ${className}`}>
      {/* 状态栏头部 */}
      <div className="flex items-center justify-between px-4 py-2 cursor-pointer hover:bg-gray-50"
           onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex items-center space-x-3">
          {getMainStatusIcon()}
          <span className="text-sm font-medium text-gray-900">
            {getStatusText()}
          </span>
          
          {/* 快速统计 */}
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            {stats.totalTokensUsed > 0 && (
              <div className="flex items-center space-x-1">
                <Zap className="w-3 h-3" />
                <span>{stats.totalTokensUsed.toLocaleString()} tokens</span>
              </div>
            )}
            
            {stats.averageProcessingTime > 0 && (
              <div className="flex items-center space-x-1">
                <Clock className="w-3 h-3" />
                <span>平均 {(stats.averageProcessingTime / 1000).toFixed(1)}s</span>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* 取消所有按钮 */}
          {summary.active > 0 && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                cancelAllProcessing();
              }}
              className="px-2 py-1 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
            >
              取消所有
            </button>
          )}

          {/* 展开/收起按钮 */}
          <button className="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-100">
            {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
          </button>

          {/* 关闭按钮 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              setIsVisible(false);
            }}
            className="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-100"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 展开的详细内容 */}
      {isExpanded && showDetails && (
        <div className="border-t border-gray-100 bg-gray-50 max-h-80 overflow-y-auto">
          <div className="p-4">
            {/* 活动处理列表 */}
            {Array.from(allProgress.entries()).map(([id, progress]) => (
              <div key={id} className="mb-4 last:mb-0">
                <ProcessingItem
                  id={id}
                  progress={progress}
                  onCancel={() => cancelProcessing(id)}
                />
              </div>
            ))}

            {/* 空状态 */}
            {allProgress.size === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">暂无 AI 处理任务</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * 处理项组件
 */
interface ProcessingItemProps {
  id: string;
  progress: AIProcessingProgress;
  onCancel: () => void;
}

function ProcessingItem({ id, progress, onCancel }: ProcessingItemProps) {
  const [showDetails, setShowDetails] = useState(false);

  /**
   * 获取状态颜色
   */
  const getStatusColor = () => {
    switch (progress.status) {
      case 'preparing':
      case 'connecting':
      case 'processing':
      case 'generating':
        return 'border-blue-200 bg-blue-50';
      case 'completed':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'cancelled':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  /**
   * 获取状态图标
   */
  const getStatusIcon = () => {
    switch (progress.status) {
      case 'preparing':
      case 'connecting':
      case 'processing':
      case 'generating':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-600" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'cancelled':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  /**
   * 格式化时间
   */
  const formatElapsedTime = () => {
    const elapsed = Date.now() - progress.startTime.getTime();
    if (elapsed < 1000) return '刚刚';
    if (elapsed < 60000) return `${Math.floor(elapsed / 1000)}秒前`;
    return `${Math.floor(elapsed / 60000)}分钟前`;
  };

  return (
    <div className={`border rounded-lg p-3 ${getStatusColor()}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1">
          {getStatusIcon()}
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900 truncate">
                处理 ID: {id.slice(-8)}
              </span>
              <span className="text-xs text-gray-500">
                {formatElapsedTime()}
              </span>
            </div>
            
            {/* 进度条 */}
            <div className="mt-1">
              <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                <span>{progress.currentStage}</span>
                <span>{Math.round(progress.overallProgress)}%</span>
              </div>
              <div className="w-full bg-white rounded-full h-1.5">
                <div
                  className="h-1.5 rounded-full bg-blue-500 transition-all duration-300"
                  style={{ width: `${progress.overallProgress}%` }}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2 ml-3">
          {/* 详情按钮 */}
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="px-2 py-1 text-xs text-gray-600 hover:text-gray-800 hover:bg-white rounded transition-colors"
          >
            {showDetails ? '收起' : '详情'}
          </button>

          {/* 取消按钮 */}
          {progress.cancellable && (
            <button
              onClick={onCancel}
              className="px-2 py-1 text-xs text-red-600 hover:text-red-700 hover:bg-white rounded transition-colors"
            >
              取消
            </button>
          )}
        </div>
      </div>

      {/* 详细信息 */}
      {showDetails && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <AIProcessingIndicator
            progress={progress}
            compact={false}
            options={{
              showDetailedProgress: true,
              showTokenCount: true,
              showTimeEstimate: true,
            }}
            className="bg-white"
          />
        </div>
      )}

      {/* 错误信息 */}
      {progress.error && (
        <div className="mt-2 p-2 bg-white border border-red-200 rounded text-xs text-red-700">
          {progress.error}
        </div>
      )}
    </div>
  );
}