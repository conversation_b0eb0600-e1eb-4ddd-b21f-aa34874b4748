'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { UltimateAIAssistantPanel } from './UltimateAIAssistantPanel';
import {
  SparklesIcon,
  BrainIcon,
  MessageSquareIcon
} from 'lucide-react';

/**
 * 简单的 AI 切换按钮属性
 */
interface SimpleAIToggleProps {
  /** 按钮位置 */
  position?: 'left' | 'right' | 'bottom-right' | 'bottom-left';
  /** 按钮样式 */
  variant?: 'default' | 'floating' | 'minimal';
  /** 面板宽度 */
  panelWidth?: number;
  /** AI 功能回调 */
  onAIAction?: (actionId: string, data?: any) => void;
  /** 当前选中的文本 */
  selectedText?: string;
  /** 是否正在处理 AI 请求 */
  isProcessing?: boolean;
  /** 处理状态信息 */
  processingStatus?: string;
  /** 启用的功能 */
  enabledFeatures?: {
    advancedFeatures?: boolean;
    historyTracking?: boolean;
    configSync?: boolean;
    personalization?: boolean;
    accessibility?: boolean;
  };
  /** 主题 */
  theme?: 'light' | 'dark' | 'auto';
  /** 收藏的功能列表 */
  favoriteFeatures?: string[];
  /** 切换收藏状态 */
  onToggleFavorite?: (featureId: string) => void;
}

/**
 * 简单的 AI 切换按钮组件
 * 提供类似 ai-assistant-integrated 页面的简单按钮效果
 */
export function SimpleAIToggle({
  position = 'bottom-right',
  variant = 'floating',
  panelWidth = 420,
  onAIAction,
  selectedText,
  isProcessing = false,
  processingStatus,
  enabledFeatures = {
    advancedFeatures: true,
    historyTracking: true,
    configSync: true,
    personalization: true,
    accessibility: true
  },
  theme = 'light',
  favoriteFeatures = [],
  onToggleFavorite
}: SimpleAIToggleProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 响应式检测
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  /**
   * 切换面板状态
   */
  const handleToggle = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  /**
   * 获取按钮位置样式
   */
  const getPositionStyle = () => {
    switch (position) {
      case 'left':
        return 'fixed left-4 top-1/2 -translate-y-1/2';
      case 'right':
        return 'fixed right-4 top-1/2 -translate-y-1/2';
      case 'bottom-left':
        return 'fixed bottom-6 left-6';
      case 'bottom-right':
      default:
        return 'fixed bottom-6 right-6';
    }
  };

  /**
   * 获取按钮样式
   */
  const getButtonStyle = () => {
    const baseStyle = 'z-30 transition-all duration-300 ease-in-out';

    switch (variant) {
      case 'floating':
        return `${baseStyle} shadow-lg hover:shadow-xl bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white border-0 rounded-full w-14 h-14 p-0`;
      case 'minimal':
        return `${baseStyle} bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 rounded-lg`;
      case 'default':
      default:
        return `${baseStyle} bg-blue-600 hover:bg-blue-700 text-white border-0 rounded-lg shadow-md hover:shadow-lg`;
    }
  };

  /**
   * 获取面板位置
   */
  const getPanelPosition = (): 'left' | 'right' => {
    return position.includes('left') ? 'left' : 'right';
  };

  return (
    <>
      {/* 切换按钮 */}
      <Button
        onClick={handleToggle}
        className={`${getPositionStyle()} ${getButtonStyle()}`}
        disabled={isProcessing}
      >
        {isProcessing ? (
          <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent" />
        ) : variant === 'floating' ? (
          <BrainIcon className="h-6 w-6" />
        ) : (
          <div className="flex items-center gap-2">
            <SparklesIcon className="h-4 w-4" />
            <span className="text-sm font-medium">AI 助手</span>
          </div>
        )}
      </Button>

      {/* 处理状态指示器 */}
      {isProcessing && processingStatus && (
        <div className={`
          ${getPositionStyle().replace('bottom-6', 'bottom-24').replace('top-1/2', 'top-1/2 translate-y-16')}
          bg-blue-600 text-white px-3 py-2 rounded-lg shadow-lg text-sm max-w-xs z-20
        `}>
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent" />
            <span>{processingStatus}</span>
          </div>
        </div>
      )}

      {/* AI 助手面板 */}
      <UltimateAIAssistantPanel
        isOpen={isOpen}
        onToggle={handleToggle}
        position={getPanelPosition()}
        width={isMobile ? undefined : panelWidth}
        isMobile={isMobile}
        onAIAction={onAIAction}
        selectedText={selectedText}
        isProcessing={isProcessing}
        processingStatus={processingStatus}
        enableAdvancedFeatures={enabledFeatures.advancedFeatures}
        enableHistoryTracking={enabledFeatures.historyTracking}
        enableConfigSync={enabledFeatures.configSync}
        enablePersonalization={enabledFeatures.personalization}
        enableAccessibility={enabledFeatures.accessibility}
        theme={theme}
        favoriteFeatures={favoriteFeatures}
        onToggleFavorite={onToggleFavorite}
      />
    </>
  );
}

/**
 * 预设配置的简单 AI 切换按钮
 */
export function FloatingAIButton(props: Omit<SimpleAIToggleProps, 'variant' | 'position'>) {
  return (
    <SimpleAIToggle
      {...props}
      variant="floating"
      position="bottom-right"
    />
  );
}

/**
 * 最小化的 AI 切换按钮
 */
export function MinimalAIButton(props: Omit<SimpleAIToggleProps, 'variant'>) {
  return (
    <SimpleAIToggle
      {...props}
      variant="minimal"
    />
  );
}

/**
 * 侧边栏 AI 切换按钮
 */
export function SidebarAIButton(props: Omit<SimpleAIToggleProps, 'variant' | 'position'>) {
  return (
    <SimpleAIToggle
      {...props}
      variant="default"
      position="right"
    />
  );
}