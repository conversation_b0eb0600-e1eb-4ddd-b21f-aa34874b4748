'use client';

/**
 * AI 配置表单组件
 * 用于创建和编辑 AI 服务配置
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, Zap, Eye, EyeOff } from 'lucide-react';

import { getRecommendedModels, getDefaultEndpoint, requiresApiKey, requiresEndpoint } from '@/lib/services/ai-helpers';
import type { AIProvider, AIServiceConfig } from '@/types/ai.types';
import type { FullAIConfigData, ConnectionTestResult } from '@/hooks/useAIConfig';

interface AIConfigFormProps {
  /** 初始配置数据（编辑模式） */
  initialData?: FullAIConfigData;
  /** 提交回调 */
  onSubmit: (data: Omit<FullAIConfigData, 'id' | 'createdAt' | 'updatedAt'>) => Promise<boolean>;
  /** 连接测试回调 */
  onTestConnection: (config: AIServiceConfig) => Promise<ConnectionTestResult>;
  /** 取消回调 */
  onCancel?: () => void;
  /** 是否为编辑模式 */
  isEditing?: boolean;
}

export function AIConfigForm({
  initialData,
  onSubmit,
  onTestConnection,
  onCancel,
  isEditing = false
}: AIConfigFormProps) {
  // 表单状态
  const [formData, setFormData] = useState({
    provider: (initialData?.provider as AIProvider) || 'openai',
    apiKey: initialData?.apiKey || '',
    endpoint: initialData?.endpoint || '',
    model: initialData?.model || '',
    maxTokens: initialData?.maxTokens || 2000,
    temperature: initialData?.temperature || 0.7,
    isDefault: initialData?.isDefault || false
  });

  // 当 initialData 变化时更新表单数据（用于编辑模式）
  useEffect(() => {
    if (initialData) {
      setFormData({
        provider: (initialData.provider as AIProvider) || 'openai',
        apiKey: initialData.apiKey || '',
        endpoint: initialData.endpoint || '',
        model: initialData.model || '',
        maxTokens: initialData.maxTokens || 2000,
        temperature: initialData.temperature || 0.7,
        isDefault: initialData.isDefault || false
      });
    }
  }, [initialData]);

  // UI 状态
  const [showApiKey, setShowApiKey] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<ConnectionTestResult | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 获取推荐模型 - 使用 useMemo 避免不必要的重新计算
  const recommendedModels = useMemo(() =>
    getRecommendedModels(formData.provider),
    [formData.provider]
  );

  // 当提供商变化时重置相关字段
  useEffect(() => {
    const defaultEndpoint = getDefaultEndpoint(formData.provider);
    const newModel = recommendedModels[0] || '';

    setFormData(prev => {
      // 只有在值真正改变时才更新
      const needsUpdate =
        prev.endpoint !== (defaultEndpoint || '') ||
        prev.model !== newModel ||
        (requiresApiKey(formData.provider) ? false : prev.apiKey !== '');

      if (!needsUpdate) {
        return prev;
      }

      return {
        ...prev,
        endpoint: defaultEndpoint || '',
        model: newModel,
        apiKey: requiresApiKey(formData.provider) ? prev.apiKey : ''
      };
    });

    setTestResult(null);
    setErrors(prev => ({})); // 重置错误状态
  }, [formData.provider, recommendedModels]); // 现在可以安全地包含 recommendedModels

  /**
   * 处理表单字段变化
   */
  const handleFieldChange = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // 清除相关错误
    setErrors(prev => {
      if (prev[field]) {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      }
      return prev;
    });

    // 清除测试结果
    if (['provider', 'apiKey', 'endpoint', 'model'].includes(field)) {
      setTestResult(null);
    }
  }, []);

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.provider) {
      newErrors.provider = '请选择服务提供商';
    }

    if (!formData.model) {
      newErrors.model = '请选择模型';
    }

    if (requiresApiKey(formData.provider) && !formData.apiKey) {
      newErrors.apiKey = 'API 密钥是必需的';
    }

    if (requiresEndpoint(formData.provider) && !formData.endpoint) {
      // 对于 Ollama，端点是必需的；对于 OpenAI，端点是可选的（代理）
      if (formData.provider === 'ollama') {
        newErrors.endpoint = '服务端点是必需的';
      }
    }

    if (formData.maxTokens < 1 || formData.maxTokens > 100000) {
      newErrors.maxTokens = '最大令牌数必须在 1-100000 之间';
    }

    if (formData.temperature < 0 || formData.temperature > 2) {
      newErrors.temperature = '温度参数必须在 0-2 之间';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * 测试连接
   */
  const handleTestConnection = async () => {
    if (!validateForm()) {
      return;
    }

    setIsTesting(true);
    setTestResult(null);

    try {
      const config: AIServiceConfig = {
        provider: formData.provider,
        apiKey: formData.apiKey || undefined,
        endpoint: formData.endpoint || undefined,
        model: formData.model,
        maxTokens: formData.maxTokens,
        temperature: formData.temperature
      };

      const result = await onTestConnection(config);
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : '连接测试失败',
        provider: formData.provider,
        model: formData.model
      });
    } finally {
      setIsTesting(false);
    }
  };

  /**
   * 提交表单
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const success = await onSubmit({
        provider: formData.provider,
        apiKey: formData.apiKey || undefined,
        endpoint: formData.endpoint || undefined,
        model: formData.model,
        maxTokens: formData.maxTokens,
        temperature: formData.temperature,
        isDefault: formData.isDefault
      });

      if (!success) {
        // 错误已在 hook 中处理
        return;
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>
          {isEditing ? '编辑 AI 配置' : '添加 AI 配置'}
        </CardTitle>
        <CardDescription>
          配置您的 AI 服务提供商和参数
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 服务提供商 */}
          <div className="space-y-2">
            <Label htmlFor="provider">服务提供商 *</Label>
            <Select
              value={formData.provider}
              onValueChange={(value) => handleFieldChange('provider', value as AIProvider)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI</SelectItem>
                <SelectItem value="ollama">Ollama</SelectItem>
                <SelectItem value="gemini">Gemini</SelectItem>
              </SelectContent>
            </Select>
            {errors.provider && (
              <p className="text-sm text-red-600">{errors.provider}</p>
            )}
          </div>

          {/* API 密钥 */}
          {requiresApiKey(formData.provider) && (
            <div className="space-y-2">
              <Label htmlFor="apiKey">API 密钥 *</Label>
              <div className="relative">
                <Input
                  id="apiKey"
                  type={showApiKey ? 'text' : 'password'}
                  value={formData.apiKey}
                  onChange={(e) => handleFieldChange('apiKey', e.target.value)}
                  placeholder="输入 API 密钥"
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowApiKey(!showApiKey)}
                >
                  {showApiKey ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.apiKey && (
                <p className="text-sm text-red-600">{errors.apiKey}</p>
              )}
            </div>
          )}

          {/* 服务端点 */}
          {requiresEndpoint(formData.provider) && (
            <div className="space-y-2">
              <Label htmlFor="endpoint">
                {formData.provider === 'ollama' ? '服务端点' : 'HTTP 代理地址'} {formData.provider === 'ollama' ? '*' : '(可选)'}
                {(formData.provider === 'openai' || formData.provider === 'gemini') && (
                  <span className="text-sm text-muted-foreground ml-2">
                    (HTTP 代理服务器地址)
                  </span>
                )}
              </Label>
              <Input
                id="endpoint"
                value={formData.endpoint}
                onChange={(e) => handleFieldChange('endpoint', e.target.value)}
                placeholder={
                  formData.provider === 'ollama'
                    ? "http://localhost:11454"
                    : "留空使用环境变量配置，或输入自定义代理地址"
                }
              />
              {(formData.provider === 'openai' || formData.provider === 'gemini') && (
                <p className="text-xs text-muted-foreground">
                  可选：输入 HTTP 代理服务器地址。留空则使用环境变量 HTTP_PROXY 配置，如果环境变量也未设置则直连官方 API。
                </p>
              )}
              {errors.endpoint && (
                <p className="text-sm text-red-600">{errors.endpoint}</p>
              )}
            </div>
          )}

          {/* 模型 */}
          <div className="space-y-2">
            <Label htmlFor="model">模型 *</Label>
            <Select
              value={formData.model}
              onValueChange={(value) => handleFieldChange('model', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择模型" />
              </SelectTrigger>
              <SelectContent>
                {recommendedModels.map(model => (
                  <SelectItem key={model} value={model}>
                    {model}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.model && (
              <p className="text-sm text-red-600">{errors.model}</p>
            )}
          </div>

          {/* 高级参数 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="maxTokens">最大令牌数</Label>
              <Input
                id="maxTokens"
                type="number"
                value={formData.maxTokens}
                onChange={(e) => handleFieldChange('maxTokens', parseInt(e.target.value))}
                min="1"
                max="100000"
              />
              {errors.maxTokens && (
                <p className="text-sm text-red-600">{errors.maxTokens}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="temperature">温度参数</Label>
              <Input
                id="temperature"
                type="number"
                step="0.1"
                value={formData.temperature}
                onChange={(e) => handleFieldChange('temperature', parseFloat(e.target.value))}
                min="0"
                max="2"
              />
              {errors.temperature && (
                <p className="text-sm text-red-600">{errors.temperature}</p>
              )}
            </div>
          </div>

          {/* 默认配置 */}
          <div className="flex items-center space-x-2">
            <input
              id="isDefault"
              type="checkbox"
              checked={formData.isDefault}
              onChange={(e) => handleFieldChange('isDefault', e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor="isDefault">设为默认配置</Label>
          </div>

          {/* 连接测试 */}
          <div className="space-y-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleTestConnection}
              disabled={isTesting}
              className="w-full"
            >
              {isTesting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  测试中...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  测试连接
                </>
              )}
            </Button>

            {testResult && (
              <Alert variant={testResult.success ? 'default' : 'destructive'}>
                {testResult.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <XCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  {testResult.success ? (
                    `连接成功！响应时间: ${testResult.responseTime}ms`
                  ) : (
                    `连接失败: ${testResult.error}`
                  )}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                取消
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {isEditing ? '更新中...' : '创建中...'}
                </>
              ) : (
                isEditing ? '更新配置' : '创建配置'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}