import { NextResponse } from 'next/server';
import { testDatabaseConnection, initializeDatabase } from '@/lib/db/test-connection';

export async function GET() {
  try {
    // Test database connection
    const connectionTest = await testDatabaseConnection();
    
    if (!connectionTest.success) {
      // Try to initialize database
      const initTest = await initializeDatabase();
      
      return NextResponse.json({
        connection: connectionTest,
        initialization: initTest,
      }, { status: connectionTest.success ? 200 : 500 });
    }

    return NextResponse.json({
      connection: connectionTest,
      status: 'Database is ready',
    });
  } catch (error) {
    console.error('Database test API error:', error);
    return NextResponse.json(
      {
        error: 'Database test failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
