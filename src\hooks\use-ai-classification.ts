/**
 * AI 分类功能的自定义 Hook
 * 提供文档分类、文件夹建议和相关推荐的状态管理
 */

import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import type {
  DocumentClassification,
  FolderStructureSuggestion,
  RelatedDocumentRecommendation,
  SmartOrganizationSuggestion,
  ClassificationOptions,
  BatchClassificationResult
} from '@/types/ai-classification.types';

interface UseAIClassificationOptions {
  /** 是否自动加载建议 */
  autoLoad?: boolean;
  /** 默认分类选项 */
  defaultOptions?: Partial<ClassificationOptions>;
}

interface UseAIClassificationReturn {
  // 状态
  isLoading: boolean;
  isClassifying: boolean;
  isApplying: boolean;
  
  // 分类相关
  classification: DocumentClassification | null;
  batchResult: BatchClassificationResult | null;
  suggestedCategories: string[];
  classificationOptions: ClassificationOptions;
  
  // 文件夹建议相关
  folderSuggestions: FolderStructureSuggestion[];
  smartSuggestions: SmartOrganizationSuggestion[];
  
  // 推荐相关
  relatedDocuments: RelatedDocumentRecommendation[];
  recentDocuments: RelatedDocumentRecommendation[];
  smartRecommendations: RelatedDocumentRecommendation[];
  
  // 操作方法
  classifyDocument: (documentId: string, options?: Partial<ClassificationOptions>) => Promise<void>;
  batchClassifyDocuments: (documentIds: string[], options?: Partial<ClassificationOptions>) => Promise<void>;
  loadFolderSuggestions: () => Promise<void>;
  loadSmartSuggestions: () => Promise<void>;
  applySuggestion: (suggestionId: string) => Promise<boolean>;
  loadRelatedDocuments: (documentId: string, limit?: number) => Promise<void>;
  loadRecentDocuments: (days?: number, limit?: number) => Promise<void>;
  loadSmartRecommendations: (contextDocumentId?: string, limit?: number) => Promise<void>;
  searchByTopic: (topic: string, excludeDocumentId?: string, limit?: number) => Promise<RelatedDocumentRecommendation[]>;
  updateClassificationOptions: (options: Partial<ClassificationOptions>) => void;
  reset: () => void;
}

/**
 * AI 分类功能 Hook
 */
export function useAIClassification(options: UseAIClassificationOptions = {}): UseAIClassificationReturn {
  const { toast } = useToast();
  const { autoLoad = false, defaultOptions = {} } = options;

  // 状态定义
  const [isLoading, setIsLoading] = useState(false);
  const [isClassifying, setIsClassifying] = useState(false);
  const [isApplying, setIsApplying] = useState(false);

  // 分类相关状态
  const [classification, setClassification] = useState<DocumentClassification | null>(null);
  const [batchResult, setBatchResult] = useState<BatchClassificationResult | null>(null);
  const [suggestedCategories, setSuggestedCategories] = useState<string[]>([]);
  const [classificationOptions, setClassificationOptions] = useState<ClassificationOptions>({
    enableAutoClassification: true,
    enableFolderSuggestions: true,
    enableRelatedDocuments: true,
    minConfidenceThreshold: 0.7,
    maxSuggestions: 10,
    analysisDepth: 'detailed',
    ...defaultOptions
  });

  // 文件夹建议相关状态
  const [folderSuggestions, setFolderSuggestions] = useState<FolderStructureSuggestion[]>([]);
  const [smartSuggestions, setSmartSuggestions] = useState<SmartOrganizationSuggestion[]>([]);

  // 推荐相关状态
  const [relatedDocuments, setRelatedDocuments] = useState<RelatedDocumentRecommendation[]>([]);
  const [recentDocuments, setRecentDocuments] = useState<RelatedDocumentRecommendation[]>([]);
  const [smartRecommendations, setSmartRecommendations] = useState<RelatedDocumentRecommendation[]>([]);

  // 自动加载
  useEffect(() => {
    if (autoLoad) {
      loadSuggestedCategories();
      loadFolderSuggestions();
      loadSmartSuggestions();
      loadRecentDocuments();
    }
  }, [autoLoad]);

  /**
   * 加载建议分类
   */
  const loadSuggestedCategories = useCallback(async () => {
    try {
      const response = await fetch('/api/ai/classify-document/categories');
      if (response.ok) {
        const data = await response.json();
        setSuggestedCategories(data.data || []);
      }
    } catch (error) {
      console.error('加载建议分类失败:', error);
    }
  }, []);

  /**
   * 分类单个文档
   */
  const classifyDocument = useCallback(async (
    documentId: string,
    options?: Partial<ClassificationOptions>
  ) => {
    setIsClassifying(true);
    setClassification(null);

    try {
      const response = await fetch('/api/ai/classify-document', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentId,
          options: { ...classificationOptions, ...options }
        })
      });

      if (!response.ok) {
        throw new Error('文档分类请求失败');
      }

      const data = await response.json();
      setClassification(data.data);

      toast({
        title: '分类完成',
        description: `文档已分类为: ${data.data.primaryCategory}`
      });
    } catch (error) {
      console.error('文档分类失败:', error);
      toast({
        title: '分类失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      });
    } finally {
      setIsClassifying(false);
    }
  }, [classificationOptions, toast]);

  /**
   * 批量分类文档
   */
  const batchClassifyDocuments = useCallback(async (
    documentIds: string[],
    options?: Partial<ClassificationOptions>
  ) => {
    setIsClassifying(true);
    setBatchResult(null);

    try {
      const response = await fetch('/api/ai/classify-document', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentIds,
          options: { ...classificationOptions, ...options }
        })
      });

      if (!response.ok) {
        throw new Error('批量分类请求失败');
      }

      const data = await response.json();
      setBatchResult(data.data);

      toast({
        title: '批量分类完成',
        description: `成功分类 ${data.data.processedDocuments} 个文档`
      });
    } catch (error) {
      console.error('批量分类失败:', error);
      toast({
        title: '批量分类失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      });
    } finally {
      setIsClassifying(false);
    }
  }, [classificationOptions, toast]);

  /**
   * 加载文件夹建议
   */
  const loadFolderSuggestions = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/ai/folder-suggestions');
      if (response.ok) {
        const data = await response.json();
        setFolderSuggestions(data.data || []);
      }
    } catch (error) {
      console.error('加载文件夹建议失败:', error);
      toast({
        title: '加载失败',
        description: '无法加载文件夹建议',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  /**
   * 加载智能建议
   */
  const loadSmartSuggestions = useCallback(async () => {
    try {
      const response = await fetch('/api/ai/folder-suggestions/smart', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });
      
      if (response.ok) {
        const data = await response.json();
        setSmartSuggestions(data.data || []);
      }
    } catch (error) {
      console.error('加载智能建议失败:', error);
    }
  }, []);

  /**
   * 应用建议
   */
  const applySuggestion = useCallback(async (suggestionId: string): Promise<boolean> => {
    setIsApplying(true);
    try {
      const response = await fetch('/api/ai/folder-suggestions/apply', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ suggestionId })
      });

      const data = await response.json();
      
      if (data.success) {
        toast({
          title: '应用成功',
          description: data.message
        });
        
        // 更新建议状态
        setSmartSuggestions(prev => 
          prev.map(suggestion => 
            suggestion.id === suggestionId 
              ? { ...suggestion, applied: true }
              : suggestion
          )
        );
        
        return true;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('应用建议失败:', error);
      toast({
        title: '应用失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      });
      return false;
    } finally {
      setIsApplying(false);
    }
  }, [toast]);

  /**
   * 加载相关文档
   */
  const loadRelatedDocuments = useCallback(async (documentId: string, limit = 10) => {
    try {
      const response = await fetch('/api/ai/document-recommendations/related', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ documentId, limit })
      });

      if (response.ok) {
        const data = await response.json();
        setRelatedDocuments(data.data || []);
      }
    } catch (error) {
      console.error('加载相关文档失败:', error);
      toast({
        title: '加载失败',
        description: '无法加载相关文档推荐',
        variant: 'destructive'
      });
    }
  }, [toast]);

  /**
   * 加载最近文档
   */
  const loadRecentDocuments = useCallback(async (days = 7, limit = 10) => {
    try {
      const response = await fetch('/api/ai/document-recommendations/recent', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ days, limit })
      });

      if (response.ok) {
        const data = await response.json();
        setRecentDocuments(data.data || []);
      }
    } catch (error) {
      console.error('加载最近文档失败:', error);
    }
  }, []);

  /**
   * 加载智能推荐
   */
  const loadSmartRecommendations = useCallback(async (contextDocumentId?: string, limit = 10) => {
    try {
      const response = await fetch('/api/ai/document-recommendations/smart', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ contextDocumentId, limit })
      });

      if (response.ok) {
        const data = await response.json();
        setSmartRecommendations(data.data || []);
      }
    } catch (error) {
      console.error('加载智能推荐失败:', error);
    }
  }, []);

  /**
   * 按主题搜索
   */
  const searchByTopic = useCallback(async (
    topic: string,
    excludeDocumentId?: string,
    limit = 10
  ): Promise<RelatedDocumentRecommendation[]> => {
    try {
      const response = await fetch(
        `/api/ai/document-recommendations/by-topic?topic=${encodeURIComponent(topic)}&excludeDocumentId=${excludeDocumentId || ''}&limit=${limit}`
      );

      if (response.ok) {
        const data = await response.json();
        return data.data || [];
      }
      
      throw new Error('主题搜索失败');
    } catch (error) {
      console.error('主题搜索失败:', error);
      toast({
        title: '搜索失败',
        description: '无法搜索相关主题文档',
        variant: 'destructive'
      });
      return [];
    }
  }, [toast]);

  /**
   * 更新分类选项
   */
  const updateClassificationOptions = useCallback((options: Partial<ClassificationOptions>) => {
    setClassificationOptions(prev => ({ ...prev, ...options }));
  }, []);

  /**
   * 重置所有状态
   */
  const reset = useCallback(() => {
    setClassification(null);
    setBatchResult(null);
    setFolderSuggestions([]);
    setSmartSuggestions([]);
    setRelatedDocuments([]);
    setRecentDocuments([]);
    setSmartRecommendations([]);
  }, []);

  return {
    // 状态
    isLoading,
    isClassifying,
    isApplying,
    
    // 分类相关
    classification,
    batchResult,
    suggestedCategories,
    classificationOptions,
    
    // 文件夹建议相关
    folderSuggestions,
    smartSuggestions,
    
    // 推荐相关
    relatedDocuments,
    recentDocuments,
    smartRecommendations,
    
    // 操作方法
    classifyDocument,
    batchClassifyDocuments,
    loadFolderSuggestions,
    loadSmartSuggestions,
    applySuggestion,
    loadRelatedDocuments,
    loadRecentDocuments,
    loadSmartRecommendations,
    searchByTopic,
    updateClassificationOptions,
    reset
  };
}