import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/db/prisma';
import { DocumentService } from '@/lib/services/document-service';
import { 
  withAuth, 
  withValidation, 
  withRateLimit, 
  withLogging,
  compose,
  APIError 
} from '@/lib/api/middleware';

// 文档创建的验证模式
const createDocumentSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(255, '标题长度不能超过255个字符'),
  content: z.string().optional().default(''),
  folderId: z.string().optional(),
});

/**
 * 创建文档的处理函数
 */
async function createDocumentHandler(
  request: NextRequest,
  session: any,
  validatedData: z.infer<typeof createDocumentSchema>
) {
  const { title, content, folderId } = validatedData;

  // 检查标题是否重复
  const isDuplicate = await DocumentService.isDocumentTitleDuplicate(
    title,
    session.user.id,
    folderId
  );

  if (isDuplicate) {
    throw new APIError('文档标题已存在', 409, 'DUPLICATE_TITLE');
  }

  // 如果指定了文件夹ID，验证文件夹是否存在且属于当前用户
  if (folderId) {
    const folder = await DocumentService.validateFolderAccess(folderId, session.user.id);
    if (!folder) {
      throw new APIError('文件夹未找到', 404, 'FOLDER_NOT_FOUND');
    }
  }

  // 计算字数和字符数
  const { wordCount, charCount } = DocumentService.calculateTextStats(content);

  // 使用事务创建文档和初始历史记录
  const result = await prisma.$transaction(async (tx) => {
    // 创建文档
    const document = await tx.document.create({
      data: {
        title,
        content,
        folderId,
        userId: session.user.id,
        wordCount,
        charCount,
      },
      include: {
        folder: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // 创建初始历史记录
    await DocumentService.createDocumentHistory(document.id, content, 'user');

    return document;
  });

  return NextResponse.json({ document: result }, { status: 201 });
}

/**
 * 获取文档列表的处理函数
 */
async function getDocumentsHandler(request: NextRequest, session: any) {
  const { searchParams } = new URL(request.url);
  const folderId = searchParams.get('folderId');
  const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
  const offset = parseInt(searchParams.get('offset') || '0');
  const sortBy = searchParams.get('sortBy') || 'updatedAt';
  const sortOrder = searchParams.get('sortOrder') || 'desc';
  const search = searchParams.get('search');

  // 如果有搜索关键词，使用搜索功能
  if (search) {
    const documents = await DocumentService.searchDocuments(
      session.user.id,
      search,
      { folderId: folderId || undefined, limit }
    );

    return NextResponse.json({ 
      documents,
      pagination: {
        total: documents.length,
        limit,
        offset: 0,
        hasMore: false,
      },
    });
  }

  // 构建查询条件
  const whereClause: any = {
    userId: session.user.id,
  };

  if (folderId) {
    whereClause.folderId = folderId;
  } else if (searchParams.has('rootOnly')) {
    whereClause.folderId = null;
  }

  // 构建排序条件
  const orderBy: any = {};
  if (['title', 'createdAt', 'updatedAt', 'wordCount'].includes(sortBy)) {
    orderBy[sortBy] = sortOrder === 'asc' ? 'asc' : 'desc';
  } else {
    orderBy.updatedAt = 'desc';
  }

  // 并行执行查询和计数
  const [documents, totalCount] = await Promise.all([
    prisma.document.findMany({
      where: whereClause,
      include: {
        folder: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy,
      take: limit,
      skip: offset,
    }),
    prisma.document.count({
      where: whereClause,
    }),
  ]);

  return NextResponse.json({ 
    documents,
    pagination: {
      total: totalCount,
      limit,
      offset,
      hasMore: offset + documents.length < totalCount,
    },
  });
}

// 应用中间件
export const POST = compose(
  withLogging,
  withRateLimit(20, 60 * 1000), // 每分钟最多20次请求
  withAuth,
  withValidation(createDocumentSchema)
)(createDocumentHandler);

export const GET = compose(
  withLogging,
  withRateLimit(100, 60 * 1000), // 每分钟最多100次请求
  withAuth
)(getDocumentsHandler);
