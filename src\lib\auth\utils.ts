import { getServerSession } from "next-auth/next";
import { authOptions } from "./config";
import { redirect } from "next/navigation";

/**
 * 获取当前登录用户信息
 * 在服务端组件中使用，获取当前会话的用户信息
 * @returns 用户信息或 undefined（未登录）
 */
export async function getCurrentUser() {
  const session = await getServerSession(authOptions);
  return session?.user;
}

/**
 * 要求用户必须登录
 * 如果用户未登录，自动重定向到登录页面
 * 在需要认证的服务端组件中使用
 * @returns 当前登录的用户信息
 */
export async function requireAuth() {
  const user = await getCurrentUser();
  if (!user) {
    redirect("/auth/signin");
  }
  return user;
}

/**
 * 获取用户姓名的首字母缩写
 * @param name 用户姓名或邮箱
 * @returns 首字母缩写（最多2个字符）
 */
export function getInitials(name: string): string {
  if (!name) return "U"; // 默认返回 "U" (User)
  
  // 如果是邮箱，取邮箱用户名部分
  if (name.includes("@")) {
    name = name.split("@")[0];
  }
  
  return name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
}

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否为有效邮箱格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 生成随机字符串（用于令牌等）
 * @param length 字符串长度
 * @returns 随机字符串
 */
export function generateRandomString(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}