import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { VersionHistoryService } from '@/lib/services/version-history';
import { DocumentService } from '@/lib/services/document-service';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * 获取文档版本历史
 * GET /api/documents/[id]/versions
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const documentId = params.id;
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search');

    // 验证文档访问权限
    const document = await DocumentService.validateDocumentAccess(
      documentId,
      session.user.id
    );

    if (!document) {
      return NextResponse.json(
        { error: '文档不存在或无权限访问' },
        { status: 404 }
      );
    }

    let versions;
    if (search) {
      // 搜索版本历史
      versions = await VersionHistoryService.searchVersionHistory(
        documentId,
        search,
        limit
      );
    } else {
      // 获取版本历史
      versions = await VersionHistoryService.getVersionHistory(
        documentId,
        limit,
        offset
      );
    }

    // 获取版本统计信息
    const stats = await VersionHistoryService.getVersionStats(documentId);

    return NextResponse.json({
      versions,
      stats,
      pagination: {
        limit,
        offset,
        total: stats.totalVersions
      }
    });

  } catch (error) {
    console.error('获取版本历史失败:', error);
    return NextResponse.json(
      { error: '获取版本历史失败' },
      { status: 500 }
    );
  }
}

/**
 * 创建新版本
 * POST /api/documents/[id]/versions
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const documentId = params.id;
    const { content, title, changeType, changeDescription } = await request.json();

    // 验证文档访问权限
    const document = await DocumentService.validateDocumentAccess(
      documentId,
      session.user.id
    );

    if (!document) {
      return NextResponse.json(
        { error: '文档不存在或无权限访问' },
        { status: 404 }
      );
    }

    // 创建版本记录
    const version = await VersionHistoryService.createVersion(
      documentId,
      content,
      title,
      changeType || 'user',
      changeDescription
    );

    return NextResponse.json({
      success: true,
      version,
      message: '版本创建成功'
    });

  } catch (error) {
    console.error('创建版本失败:', error);
    return NextResponse.json(
      { error: '创建版本失败' },
      { status: 500 }
    );
  }
}