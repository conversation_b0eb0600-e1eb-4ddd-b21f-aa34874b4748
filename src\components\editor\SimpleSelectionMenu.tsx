'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/Button';
import { 
  MessageCircleIcon, 
  EditIcon, 
  BoldIcon,
  ItalicIcon,
  CopyIcon,
  LinkIcon
} from 'lucide-react';

/**
 * 选择菜单动作接口
 */
export interface SelectionAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  description?: string;
  action: (selectedText: string, editor: Editor) => void;
}

/**
 * 简单选择菜单属性
 */
interface SimpleSelectionMenuProps {
  /** TipTap 编辑器实例 */
  editor: Editor;
  /** 是否启用 */
  enabled?: boolean;
  /** 自定义动作列表 */
  customActions?: SelectionAction[];
  /** 最小选择文本长度 */
  minSelectionLength?: number;
  /** Chat 动作回调 */
  onChatAction?: (type: string, selectedText: string) => void;
  /** Edit 动作回调 */
  onEditAction?: (type: string, selectedText: string) => void;
}

/**
 * 菜单状态
 */
interface MenuState {
  visible: boolean;
  position: { x: number; y: number };
  selectedText: string;
  selectionRange: { from: number; to: number };
}

/**
 * 简单文本选择菜单组件
 */
export function SimpleSelectionMenu({
  editor,
  enabled = true,
  customActions = [],
  minSelectionLength = 2,
  onChatAction,
  onEditAction
}: SimpleSelectionMenuProps) {
  const [menuState, setMenuState] = useState<MenuState>({
    visible: false,
    position: { x: 0, y: 0 },
    selectedText: '',
    selectionRange: { from: 0, to: 0 }
  });

  const menuRef = useRef<HTMLDivElement>(null);
  const showTimeoutRef = useRef<NodeJS.Timeout>();
  const hideTimeoutRef = useRef<NodeJS.Timeout>();

  /**
   * 获取默认动作
   */
  const getDefaultActions = useCallback((): SelectionAction[] => {
    const actions: SelectionAction[] = [];

    // Chat 动作
    if (onChatAction) {
      actions.push(
        {
          id: 'chat-explain',
          label: '解释',
          icon: <MessageCircleIcon className="h-4 w-4" />,
          description: '让 AI 解释选中的内容',
          action: (selectedText) => onChatAction('explain', selectedText)
        },
        {
          id: 'chat-translate',
          label: '翻译',
          icon: <MessageCircleIcon className="h-4 w-4" />,
          description: '翻译选中的文本',
          action: (selectedText) => onChatAction('translate', selectedText)
        }
      );
    }

    // Edit 动作
    if (onEditAction) {
      actions.push(
        {
          id: 'edit-improve',
          label: '改进',
          icon: <EditIcon className="h-4 w-4" />,
          description: '改进选中文本的表达',
          action: (selectedText) => onEditAction('improve', selectedText)
        },
        {
          id: 'edit-rewrite',
          label: '重写',
          icon: <EditIcon className="h-4 w-4" />,
          description: '重新组织选中的文本',
          action: (selectedText) => onEditAction('rewrite', selectedText)
        }
      );
    }

    // 格式化动作
    actions.push(
      {
        id: 'bold',
        label: '加粗',
        icon: <BoldIcon className="h-4 w-4" />,
        action: (_, editor) => {
          if (editor.can().toggleBold()) {
            editor.chain().focus().toggleBold().run();
          }
        }
      },
      {
        id: 'italic',
        label: '斜体',
        icon: <ItalicIcon className="h-4 w-4" />,
        action: (_, editor) => {
          if (editor.can().toggleItalic()) {
            editor.chain().focus().toggleItalic().run();
          }
        }
      }
    );

    // 实用工具动作
    actions.push(
      {
        id: 'copy',
        label: '复制',
        icon: <CopyIcon className="h-4 w-4" />,
        action: (selectedText) => {
          navigator.clipboard.writeText(selectedText);
        }
      },
      {
        id: 'link',
        label: '链接',
        icon: <LinkIcon className="h-4 w-4" />,
        action: (_, editor) => {
          const url = window.prompt('请输入链接地址:');
          if (url && editor.can().setLink({ href: url })) {
            editor.chain().focus().setLink({ href: url }).run();
          }
        }
      }
    );

    return actions;
  }, [onChatAction, onEditAction]);

  /**
   * 计算菜单位置
   */
  const calculateMenuPosition = useCallback((coords: { left: number; top: number; bottom: number }) => {
    const editorElement = editor.view.dom;
    const editorRect = editorElement.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    let x = coords.left - editorRect.left;
    let y = coords.bottom - editorRect.top + 8;
    
    // 估算菜单尺寸
    const menuWidth = 250;
    const menuHeight = 200;
    
    // 防止超出边界
    if (x + menuWidth > viewportWidth - 20) {
      x = Math.max(10, viewportWidth - menuWidth - 20);
    }
    
    if (coords.bottom + menuHeight > viewportHeight - 20) {
      y = coords.top - editorRect.top - menuHeight - 8;
    }
    
    x = Math.max(10, x);
    y = Math.max(10, y);
    
    return { x, y };
  }, [editor]);

  /**
   * 显示菜单
   */
  const showMenu = useCallback((selectedText: string, range: { from: number; to: number }) => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = undefined;
    }

    try {
      const coords = editor.view.coordsAtPos(range.to);
      const position = calculateMenuPosition(coords);

      setMenuState({
        visible: true,
        position,
        selectedText,
        selectionRange: range
      });
    } catch (error) {
      console.warn('无法计算菜单位置:', error);
    }
  }, [editor, calculateMenuPosition]);

  /**
   * 隐藏菜单
   */
  const hideMenu = useCallback(() => {
    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current);
      showTimeoutRef.current = undefined;
    }
    setMenuState(prev => ({ ...prev, visible: false }));
  }, []);

  /**
   * 延迟隐藏菜单
   */
  const delayedHideMenu = useCallback(() => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }
    hideTimeoutRef.current = setTimeout(hideMenu, 300);
  }, [hideMenu]);

  /**
   * 处理选择变化
   */
  const handleSelectionChange = useCallback(() => {
    if (!enabled) return;

    const { from, to } = editor.state.selection;
    const selectedText = editor.state.doc.textBetween(from, to);

    if (!selectedText.trim() || selectedText.length < minSelectionLength) {
      delayedHideMenu();
      return;
    }

    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current);
    }

    showTimeoutRef.current = setTimeout(() => {
      showMenu(selectedText, { from, to });
    }, 150);
  }, [editor, enabled, minSelectionLength, showMenu, delayedHideMenu]);

  /**
   * 执行动作
   */
  const executeAction = useCallback((action: SelectionAction) => {
    const { selectedText, selectionRange } = menuState;
    editor.chain().focus().setTextSelection(selectionRange).run();
    action.action(selectedText, editor);
    hideMenu();
  }, [editor, menuState, hideMenu]);

  // 监听编辑器选择变化
  useEffect(() => {
    if (!editor) return;

    const handleUpdate = () => {
      setTimeout(handleSelectionChange, 10);
    };

    editor.on('selectionUpdate', handleUpdate);
    return () => {
      editor.off('selectionUpdate', handleUpdate);
    };
  }, [editor, handleSelectionChange]);

  // 监听鼠标和键盘事件
  useEffect(() => {
    const handleMouseDown = (event: MouseEvent) => {
      const target = event.target as Element;
      
      if (menuRef.current?.contains(target)) {
        return;
      }
      
      if (editor.view.dom.contains(target)) {
        delayedHideMenu();
        return;
      }
      
      hideMenu();
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && menuState.visible) {
        event.preventDefault();
        hideMenu();
      }
    };

    if (menuState.visible) {
      document.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('keydown', handleKeyDown);
      
      return () => {
        document.removeEventListener('mousedown', handleMouseDown);
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [menuState.visible, editor, delayedHideMenu, hideMenu]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (showTimeoutRef.current) clearTimeout(showTimeoutRef.current);
      if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);
    };
  }, []);

  if (!enabled || !menuState.visible) {
    return null;
  }

  const allActions = [...getDefaultActions(), ...customActions];

  return (
    <div
      ref={menuRef}
      className="
        absolute bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-50
        animate-in slide-in-from-bottom-2 fade-in duration-200
        max-w-xs min-w-[200px]
      "
      style={{
        left: menuState.position.x,
        top: menuState.position.y,
      }}
      onMouseEnter={() => {
        if (hideTimeoutRef.current) {
          clearTimeout(hideTimeoutRef.current);
          hideTimeoutRef.current = undefined;
        }
      }}
      onMouseLeave={delayedHideMenu}
    >
      {/* 头部 */}
      <div className="flex items-center gap-2 px-2 py-1 mb-2 border-b">
        <span className="text-sm font-medium text-gray-700">文本操作</span>
        <span className="text-xs text-gray-500 ml-auto">
          {menuState.selectedText.length} 字符
        </span>
      </div>

      {/* 动作按钮 */}
      <div className="space-y-1">
        {allActions.map((action) => (
          <Button
            key={action.id}
            variant="ghost"
            size="sm"
            onClick={() => executeAction(action)}
            className="
              w-full justify-start h-auto p-2 text-left
              hover:bg-blue-50 hover:text-blue-700
              group
            "
          >
            <div className="flex items-start gap-2 w-full">
              <div className="text-blue-600 group-hover:text-blue-700 mt-0.5">
                {action.icon}
              </div>
              <div className="flex-1 min-w-0">
                <span className="text-sm font-medium">{action.label}</span>
                {action.description && (
                  <p className="text-xs text-gray-500 mt-0.5 leading-tight">
                    {action.description}
                  </p>
                )}
              </div>
            </div>
          </Button>
        ))}
      </div>

      {/* 提示信息 */}
      <div className="mt-2 pt-2 border-t">
        <p className="text-xs text-gray-400 text-center">
          选择操作或按 ESC 取消
        </p>
      </div>
    </div>
  );
}