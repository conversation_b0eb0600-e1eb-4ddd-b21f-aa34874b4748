'use client';

import { useState, useRef, useCallback } from 'react';
import { Editor } from '@tiptap/react';
import { Upload, Image as ImageIcon, X, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/Button';

interface ImageUploadProps {
  editor: Editor | null;
  onClose: () => void;
  className?: string;
}

interface ImageUploadState {
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
  previewUrl: string | null;
}

/**
 * 图片上传组件
 * 支持拖拽上传、文件选择和 URL 输入
 */
export function ImageUpload({ editor, onClose, className }: ImageUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadState, setUploadState] = useState<ImageUploadState>({
    isUploading: false,
    uploadProgress: 0,
    error: null,
    previewUrl: null,
  });
  const [imageUrl, setImageUrl] = useState('');
  const [altText, setAltText] = useState('');
  const [uploadMethod, setUploadMethod] = useState<'file' | 'url'>('file');

  /**
   * 处理文件选择
   */
  const handleFileSelect = useCallback((file: File) => {
    if (!file.type.startsWith('image/')) {
      setUploadState(prev => ({
        ...prev,
        error: '请选择有效的图片文件',
      }));
      return;
    }

    // 检查文件大小 (5MB 限制)
    if (file.size > 5 * 1024 * 1024) {
      setUploadState(prev => ({
        ...prev,
        error: '图片文件大小不能超过 5MB',
      }));
      return;
    }

    setUploadState(prev => ({
      ...prev,
      isUploading: true,
      error: null,
      uploadProgress: 0,
    }));

    // 创建预览
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setUploadState(prev => ({
        ...prev,
        previewUrl: result,
        isUploading: false,
        uploadProgress: 100,
      }));
      setImageUrl(result);
      setAltText(file.name.replace(/\.[^/.]+$/, ''));
    };

    reader.onerror = () => {
      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        error: '读取文件失败',
      }));
    };

    reader.readAsDataURL(file);
  }, []);

  /**
   * 处理文件输入变化
   */
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  /**
   * 处理拖拽上传
   */
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  /**
   * 处理 URL 输入
   */
  const handleUrlSubmit = useCallback(() => {
    if (!imageUrl.trim()) {
      setUploadState(prev => ({
        ...prev,
        error: '请输入图片 URL',
      }));
      return;
    }

    // 简单的 URL 验证
    try {
      new URL(imageUrl);
    } catch {
      setUploadState(prev => ({
        ...prev,
        error: '请输入有效的图片 URL',
      }));
      return;
    }

    setUploadState(prev => ({
      ...prev,
      previewUrl: imageUrl,
      error: null,
    }));
  }, [imageUrl]);

  /**
   * 插入图片到编辑器
   */
  const handleInsertImage = useCallback(() => {
    if (!editor || !imageUrl) return;

    editor
      .chain()
      .focus()
      .setImage({
        src: imageUrl,
        alt: altText || '图片',
        title: altText || '图片',
      })
      .run();

    onClose();
  }, [editor, imageUrl, altText, onClose]);

  /**
   * 重置状态
   */
  const handleReset = useCallback(() => {
    setUploadState({
      isUploading: false,
      uploadProgress: 0,
      error: null,
      previewUrl: null,
    });
    setImageUrl('');
    setAltText('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  return (
    <div className={`bg-background border border-border rounded-lg shadow-lg p-6 w-96 ${className}`}>
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <ImageIcon className="h-5 w-5" />
          插入图片
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* 上传方式选择 */}
      <div className="flex gap-2 mb-4">
        <Button
          variant={uploadMethod === 'file' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setUploadMethod('file')}
        >
          文件上传
        </Button>
        <Button
          variant={uploadMethod === 'url' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setUploadMethod('url')}
        >
          URL 链接
        </Button>
      </div>

      {/* 文件上传区域 */}
      {uploadMethod === 'file' && (
        <div
          className="border-2 border-dashed border-border rounded-lg p-6 text-center mb-4 transition-colors hover:border-primary/50"
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          {uploadState.isUploading ? (
            <div className="space-y-2">
              <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto" />
              <p className="text-sm text-muted-foreground">上传中...</p>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadState.uploadProgress}%` }}
                />
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <Upload className="h-8 w-8 text-muted-foreground mx-auto" />
              <p className="text-sm text-muted-foreground">
                拖拽图片到此处或
                <button
                  className="text-primary hover:underline ml-1"
                  onClick={() => fileInputRef.current?.click()}
                >
                  点击选择文件
                </button>
              </p>
              <p className="text-xs text-muted-foreground">
                支持 JPG、PNG、GIF 格式，最大 5MB
              </p>
            </div>
          )}
        </div>
      )}

      {/* URL 输入区域 */}
      {uploadMethod === 'url' && (
        <div className="space-y-3 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">图片 URL</label>
            <div className="flex gap-2">
              <input
                type="url"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="https://example.com/image.jpg"
                className="flex-1 px-3 py-2 border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
              />
              <Button
                size="sm"
                onClick={handleUrlSubmit}
                disabled={!imageUrl.trim()}
              >
                预览
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {uploadState.error && (
        <div className="flex items-center gap-2 p-3 bg-destructive/10 text-destructive rounded-md mb-4">
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span className="text-sm">{uploadState.error}</span>
        </div>
      )}

      {/* 图片预览 */}
      {uploadState.previewUrl && (
        <div className="space-y-3 mb-4">
          <div className="border border-border rounded-md overflow-hidden">
            <img
              src={uploadState.previewUrl}
              alt="预览"
              className="w-full h-48 object-cover"
            />
          </div>
          
          {/* Alt 文本输入 */}
          <div>
            <label className="block text-sm font-medium mb-1">图片描述 (Alt 文本)</label>
            <input
              type="text"
              value={altText}
              onChange={(e) => setAltText(e.target.value)}
              placeholder="描述图片内容..."
              className="w-full px-3 py-2 border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            />
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex gap-2 justify-end">
        <Button
          variant="outline"
          size="sm"
          onClick={handleReset}
          disabled={uploadState.isUploading}
        >
          重置
        </Button>
        <Button
          size="sm"
          onClick={handleInsertImage}
          disabled={!uploadState.previewUrl || uploadState.isUploading}
          className="flex items-center gap-2"
        >
          <Check className="h-4 w-4" />
          插入图片
        </Button>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  );
}