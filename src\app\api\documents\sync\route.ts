import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';
import { DocumentService } from '@/lib/services/document-service';

/**
 * 获取需要同步的文档列表
 * GET /api/documents/sync
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const lastSyncAt = searchParams.get('lastSyncAt');
    const includeDeleted = searchParams.get('includeDeleted') === 'true';

    // 构建查询条件
    const whereClause: any = {
      userId: session.user.id,
    };

    // 如果提供了上次同步时间，只获取之后更新的文档
    if (lastSyncAt) {
      whereClause.updatedAt = {
        gt: new Date(lastSyncAt)
      };
    }

    // 是否包含已删除的文档
    if (!includeDeleted) {
      whereClause.isDeleted = false;
    }

    const documents = await prisma.document.findMany({
      where: whereClause,
      include: {
        folder: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    return NextResponse.json(documents);

  } catch (error) {
    console.error('获取同步文档失败:', error);
    return NextResponse.json(
      { error: '获取同步文档失败' },
      { status: 500 }
    );
  }
}

/**
 * 上传文档进行同步
 * POST /api/documents/sync
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { document, force = false } = await request.json();

    // console.log('收到同步请求:', {
    //   documentId: document?.id,
    //   userId: document?.userId,
    //   folderId: document?.folderId,
    //   sessionUserId: session.user.id
    // });

    if (!document) {
      return NextResponse.json(
        { error: '文档数据不能为空' },
        { status: 400 }
      );
    }

    // 验证文档所有权
    if (document.userId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限操作此文档' },
        { status: 403 }
      );
    }

    // 检查文档是否存在
    // 确保 ID 是字符串类型
    const documentId = String(document.id);
    const existingDocument = await prisma.document.findUnique({
      where: { id: documentId },
    });

    if (existingDocument) {
      // 检查冲突（除非强制更新）
      if (!force) {
        const localUpdated = new Date(document.updatedAt).getTime();
        const remoteUpdated = existingDocument.updatedAt.getTime();
        const lastSync = document.lastSyncAt ? new Date(document.lastSyncAt).getTime() : 0;

        // 如果远程版本在本地上次同步后更新，且本地也有更新，则存在冲突
        if (remoteUpdated > lastSync && localUpdated > lastSync && localUpdated !== remoteUpdated) {
          return NextResponse.json(
            {
              error: '检测到同步冲突',
              conflict: {
                localVersion: document,
                remoteVersion: existingDocument,
                conflictType: 'content'
              }
            },
            { status: 409 }
          );
        }
      }

      // 验证文件夹是否存在（如果指定了 folderId）
      // 处理空字符串的情况
      let validFolderId = document.folderId && document.folderId.trim() !== '' ? document.folderId : null;

      if (validFolderId) {
        const folderExists = await DocumentService.validateFolderAccess(
          validFolderId,
          session.user.id
        );
        if (!folderExists) {
          console.log(`文件夹 ${validFolderId} 不存在，将文档移到根目录`);
          validFolderId = null; // 将文档移到根目录
        }
      }

      // 计算文本统计
      const stats = DocumentService.calculateTextStats(document.content || '');

      // 确保 content 是字符串格式
      const contentString = typeof document.content === 'string'
        ? document.content
        : JSON.stringify(document.content || '');

      // 更新现有文档
      const updatedDocument = await prisma.document.update({
        where: { id: documentId },
        data: {
          title: document.title,
          content: contentString,
          folderId: validFolderId,
          wordCount: stats.wordCount,
          charCount: stats.charCount,
          isPublic: document.metadata?.isPublic || false,
          shareToken: document.metadata?.shareToken,
          lastSyncAt: new Date(),
        },
        include: {
          folder: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // 创建历史记录
      await DocumentService.createDocumentHistory(
        documentId,
        contentString,
        'user'
      );

      console.log(`用户 ${session.user.id} 更新了文档: ${document.title}`);

      return NextResponse.json({
        success: true,
        document: updatedDocument,
        message: '文档同步成功'
      });

    } else {
      // 验证文件夹权限（如果指定了文件夹）
      if (document.folderId) {
        const folder = await DocumentService.validateFolderAccess(
          document.folderId,
          session.user.id
        );
        if (!folder) {
          return NextResponse.json(
            { error: '指定的文件夹不存在或无权限访问' },
            { status: 400 }
          );
        }
      }

      // 验证文件夹是否存在（如果指定了 folderId）
      // 处理空字符串的情况
      let validFolderId = document.folderId && document.folderId.trim() !== '' ? document.folderId : null;

      if (validFolderId) {
        const folderExists = await DocumentService.validateFolderAccess(
          validFolderId,
          session.user.id
        );
        if (!folderExists) {
          console.log(`文件夹 ${validFolderId} 不存在，将文档移到根目录`);
          validFolderId = null; // 将文档移到根目录
        }
      }

      // 检查标题重复
      const isDuplicate = await DocumentService.isDocumentTitleDuplicate(
        document.title,
        session.user.id,
        validFolderId
      );

      if (isDuplicate) {
        return NextResponse.json(
          { error: '文档标题已存在' },
          { status: 400 }
        );
      }

      // 计算文本统计
      const stats = DocumentService.calculateTextStats(document.content || '');

      // 确保 content 是字符串格式
      const contentString = typeof document.content === 'string'
        ? document.content
        : JSON.stringify(document.content || '');

      // 创建新文档
      const newDocument = await prisma.document.create({
        data: {
          id: documentId, // 使用客户端提供的ID（转换为字符串）
          title: document.title,
          content: contentString,
          folderId: validFolderId,
          userId: session.user.id,
          wordCount: stats.wordCount,
          charCount: stats.charCount,
          isPublic: document.metadata?.isPublic || false,
          shareToken: document.metadata?.shareToken,
          lastSyncAt: new Date(),
          createdAt: document.createdAt ? new Date(document.createdAt) : new Date(),
        },
        include: {
          folder: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // 创建历史记录
      await DocumentService.createDocumentHistory(
        documentId,
        contentString,
        'user'
      );

      console.log(`用户 ${session.user.id} 创建了文档: ${document.title}`);

      return NextResponse.json({
        success: true,
        document: newDocument,
        message: '文档创建并同步成功'
      });
    }

  } catch (error) {
    console.error('文档同步失败:', error);

    // 处理外键约束错误
    if (error instanceof Error && error.message.includes('Foreign key constraint')) {
      return NextResponse.json(
        { error: '数据关联错误：可能是文件夹不存在或用户不匹配' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '文档同步失败' },
      { status: 500 }
    );
  }
}

/**
 * 批量同步文档
 * PUT /api/documents/sync
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { documents, options = {} } = await request.json();

    if (!Array.isArray(documents) || documents.length === 0) {
      return NextResponse.json(
        { error: '文档列表不能为空' },
        { status: 400 }
      );
    }

    const results = [];
    const { force = false, batchSize = 10 } = options;

    // 分批处理文档
    for (let i = 0; i < documents.length; i += batchSize) {
      const batch = documents.slice(i, i + batchSize);

      for (const document of batch) {
        try {
          // 验证文档所有权
          if (document.userId !== session.user.id) {
            results.push({
              documentId: String(document.id),
              success: false,
              error: '无权限操作此文档'
            });
            continue;
          }

          // 模拟单个文档同步逻辑
          const syncRequest = new NextRequest(request.url, {
            method: 'POST',
            body: JSON.stringify({ document, force }),
            headers: request.headers,
          });

          const syncResponse = await POST(syncRequest);
          const syncResult = await syncResponse.json();

          results.push({
            documentId: document.id,
            success: syncResponse.ok,
            ...syncResult
          });

        } catch (error) {
          results.push({
            documentId: document.id,
            success: false,
            error: error instanceof Error ? error.message : '同步失败'
          });
        }
      }
    }

    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;

    return NextResponse.json({
      success: errorCount === 0,
      results,
      summary: {
        total: documents.length,
        success: successCount,
        errors: errorCount
      }
    });

  } catch (error) {
    console.error('批量同步失败:', error);
    return NextResponse.json(
      { error: '批量同步失败' },
      { status: 500 }
    );
  }
}
