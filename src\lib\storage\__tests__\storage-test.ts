// Simple test to verify storage functionality
import { documentService } from '../document-service';
import { db } from '../database';
import { JSONContent } from '@tiptap/react';

export async function testDocumentStorage() {
  console.log('🧪 Testing Document Storage...');
  
  try {
    // Clear any existing test data
    await documentService.clearUserDocuments('test-user');
    console.log('✅ Cleared existing test data');

    // Test 1: Create a document
    const testContent: JSONContent = {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: 'This is a test document with some content to verify word and character counting.'
            }
          ]
        }
      ]
    };

    const newDoc = await documentService.createDocument({
      title: 'Test Document',
      content: testContent,
      userId: 'test-user',
      metadata: {
        tags: ['test'],
        wordCount: 0,
        characterCount: 0,
        isPublic: false
      }
    });

    console.log('✅ Created document:', {
      id: newDoc.id,
      title: newDoc.title,
      wordCount: newDoc.metadata.wordCount,
      characterCount: newDoc.metadata.characterCount
    });

    // Test 2: Get the document
    const retrievedDoc = await documentService.getDocument(newDoc.id);
    console.log('✅ Retrieved document:', retrievedDoc?.title);

    // Test 3: Update the document
    const updatedDoc = await documentService.updateDocument(newDoc.id, {
      title: 'Updated Test Document',
      content: {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'This is updated content with more words to test the counting functionality properly.'
              }
            ]
          }
        ]
      }
    });

    console.log('✅ Updated document:', {
      title: updatedDoc.title,
      wordCount: updatedDoc.metadata.wordCount,
      characterCount: updatedDoc.metadata.characterCount
    });

    // Test 4: Get all documents
    const allDocs = await documentService.getDocuments({ userId: 'test-user' });
    console.log('✅ Retrieved all documents:', allDocs.length);

    // Test 5: Get statistics
    const stats = await documentService.getDocumentStats('test-user');
    console.log('✅ Document statistics:', stats);

    // Test 6: Search documents
    const searchResults = await documentService.searchDocuments('test-user', 'test');
    console.log('✅ Search results:', searchResults.length);

    // Test 7: Delete document
    await documentService.deleteDocument(newDoc.id);
    const deletedDoc = await documentService.getDocument(newDoc.id);
    console.log('✅ Document deleted (should be null):', deletedDoc);

    console.log('🎉 All tests passed!');
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Export for use in browser console or test runner
if (typeof window !== 'undefined') {
  (window as any).testDocumentStorage = testDocumentStorage;
}