'use client';

import React, { useState, useCallback } from 'react';
import {
  SimpleAIToggle,
  FloatingAIButton,
  MinimalAIButton,
  SidebarAIButton,
} from '@/components/ai/SimpleAIToggle';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import {
  SparklesIcon,
  BrainIcon,
  MessageSquareIcon,
  SettingsIcon,
  InfoIcon,
  PlayIcon,
  PauseIcon,
  RotateCcwIcon,
  EyeIcon,
  MousePointerIcon,
  SmartphoneIcon,
  MonitorIcon,
} from 'lucide-react';

/**
 * 简单 AI 助手按钮演示页面
 * 展示类似 ai-assistant-integrated 的简单按钮效果
 */
export default function AIAssistantSimpleDemoPage() {
  const [selectedText, setSelectedText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('');
  const [demoMode, setDemoMode] = useState<
    'floating' | 'minimal' | 'sidebar' | 'custom'
  >('floating');
  const [favoriteFeatures, setFavoriteFeatures] = useState<string[]>([
    'ai-continue',
    'ai-rewrite',
  ]);
  const [interactionCount, setInteractionCount] = useState(0);
  const [aiResults, setAiResults] = useState<
    Array<{
      id: string;
      action: string;
      input: string;
      output: string;
      timestamp: Date;
    }>
  >([]);

  const [content, setContent] = useState(`# AI 助手简单演示

欢迎使用简化版的 AI 助手！这个版本提供了一个简单的浮动按钮，点击后可以打开完整的 AI 助手面板。

## 主要特点

- **简单易用**：只需一个浮动按钮，点击即可访问所有 AI 功能
- **响应式设计**：自动适配移动端和桌面端
- **完整功能**：包含所有 AI 功能，如写作助手、文档分析、语言工具等
- **个性化**：支持功能收藏、历史记录、配置同步等高级功能

## 使用方法

1. 点击右下角的 AI 按钮打开助手面板
2. 选择文本后使用相关 AI 功能
3. 查看处理结果和历史记录
4. 收藏常用功能便于快速访问

## 示例内容

这是一段示例文本，您可以选择它来测试 AI 功能。人工智能技术正在快速发展，为我们的工作和生活带来了巨大的变化。通过智能写作助手，我们可以更高效地创作内容、分析文档、翻译语言等。

请尝试选择上面的文本，然后使用 AI 助手的各种功能来体验完整的 AI 辅助写作流程。`);

  /**
   * 处理 AI 操作
   */
  const handleAIAction = useCallback(
    async (actionId: string, data?: any) => {
      console.log('AI Action:', actionId, data);

      setIsProcessing(true);
      setInteractionCount((prev) => prev + 1);

      // 设置处理状态消息
      const statusMessages: Record<string, string> = {
        'ai-continue': '正在生成续写内容...',
        'ai-rewrite': '正在改写文本...',
        'ai-summarize': '正在生成摘要...',
        'ai-translate': '正在翻译文本...',
        'ai-explain': '正在生成解释...',
        'ai-keywords': '正在提取关键词...',
        'ai-outline': '正在生成大纲...',
        'ai-analysis': '正在分析内容...',
        'ai-creative': '正在创作内容...',
        'ai-custom': '正在执行自定义指令...',
        'ai-chat': '正在准备对话...',
        'ai-grammar': '正在检查语法...',
        'ai-expand': '正在扩展内容...',
        'ai-classify': '正在分类文档...',
        'ai-naming': '正在生成文件名建议...',
        'ai-settings': '正在打开设置...',
      };

      setProcessingStatus(statusMessages[actionId] || '正在处理请求...');

      try {
        // 模拟处理延迟
        await new Promise((resolve) =>
          setTimeout(resolve, 1500 + Math.random() * 2000)
        );

        // 模拟 AI 响应
        const mockResponse = generateMockAIResponse(actionId, data);

        // 记录结果
        const result = {
          id: Math.random().toString(36).substr(2, 9),
          action: actionId,
          input: data?.text || selectedText || '无输入内容',
          output: mockResponse,
          timestamp: new Date(),
        };

        setAiResults((prev) => [result, ...prev.slice(0, 9)]);

        console.log(`AI action ${actionId} completed successfully`);
      } catch (error) {
        console.error('AI action failed:', error);
      } finally {
        setIsProcessing(false);
        setProcessingStatus('');
      }
    },
    [selectedText]
  );

  /**
   * 生成模拟 AI 响应
   */
  const generateMockAIResponse = (actionId: string, data?: any): string => {
    const responses: Record<string, string> = {
      'ai-continue':
        '这是 AI 生成的续写内容。基于前面的上下文，我们可以继续探讨这个话题的更多细节和相关观点。',
      'ai-rewrite':
        '这是经过 AI 优化的文本版本，语言更加流畅，表达更加清晰准确。',
      'ai-summarize':
        '文档摘要：本文档介绍了 AI 助手的主要功能，包括写作辅助、文档分析和语言工具等核心特性。',
      'ai-translate':
        'This is the AI-translated version of the selected text, maintaining the original meaning while adapting to the target language.',
      'ai-explain':
        '这个概念的解释：这是一个复杂的技术概念，涉及多个相关领域的知识，需要从不同角度来理解。',
      'ai-keywords':
        '关键词：AI助手, 智能写作, 文档分析, 自然语言处理, 用户体验',
      'ai-outline':
        '# 文档大纲\n1. 引言\n2. 主要功能\n   - 写作助手\n   - 文档分析\n   - 语言工具\n3. 使用方法\n4. 总结',
      'ai-analysis':
        '内容分析：文档结构清晰，语言专业，适合技术文档的风格。建议增加更多实例说明。',
      'ai-creative':
        '创意内容：在数字化时代的浪潮中，AI 技术如同一位智慧的伙伴，默默地协助我们完成各种创作任务...',
      'ai-custom':
        '根据您的自定义指令，AI 已经处理了相关内容并生成了符合要求的结果。',
      'ai-chat':
        '我是您的 AI 助手，很高兴为您提供帮助。请告诉我您需要什么协助？',
      'ai-grammar':
        '语法检查完成：发现 2 处语法问题，已提供修正建议。整体语言质量良好。',
      'ai-expand':
        '扩展内容：基于原始文本，我添加了更多详细信息、背景知识和相关例子，使内容更加丰富完整。',
      'ai-classify': '文档分类结果：技术文档 - 人工智能类别，置信度 95%',
      'ai-naming': '文件名建议：AI助手功能演示文档.md',
      'ai-settings': '设置页面已打开，您可以配置 AI 服务参数。',
    };

    return responses[actionId] || '这是 AI 生成的响应内容。';
  };

  /**
   * 切换收藏状态
   */
  const handleToggleFavorite = useCallback((featureId: string) => {
    setFavoriteFeatures((prev) => {
      if (prev.includes(featureId)) {
        return prev.filter((id) => id !== featureId);
      } else {
        return [...prev, featureId];
      }
    });
  }, []);

  /**
   * 重置演示
   */
  const resetDemo = useCallback(() => {
    setSelectedText('');
    setInteractionCount(0);
    setAiResults([]);
    setFavoriteFeatures(['ai-continue', 'ai-rewrite']);
  }, []);

  /**
   * 获取操作显示名称
   */
  const getActionDisplayName = (action: string): string => {
    const names: Record<string, string> = {
      'ai-continue': 'AI 续写',
      'ai-rewrite': 'AI 改写',
      'ai-summarize': '文档摘要',
      'ai-translate': 'AI 翻译',
      'ai-explain': 'AI 解释',
      'ai-keywords': '关键词提取',
      'ai-outline': '生成大纲',
      'ai-analysis': '内容分析',
      'ai-creative': '创意写作',
      'ai-custom': '自定义指令',
      'ai-chat': 'AI 对话',
      'ai-grammar': '语法检查',
      'ai-expand': '内容扩展',
      'ai-classify': '文档分类',
      'ai-naming': '文件命名',
      'ai-settings': 'AI 设置',
    };
    return names[action] || action;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl space-y-6 p-4">
        {/* 页面标题 */}
        <div className="space-y-2 text-center">
          <h1 className="flex items-center justify-center gap-2 text-3xl font-bold text-gray-900">
            <BrainIcon className="h-8 w-8 text-blue-600" />
            简单 AI 助手演示
          </h1>
          <p className="text-gray-600">
            类似 ai-assistant-integrated 的简单按钮设计，点击即可访问完整 AI
            功能
          </p>
          <div className="flex items-center justify-center gap-2">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              简单易用
            </Badge>
            <Badge variant="outline">完整功能</Badge>
          </div>
        </div>

        {/* 演示模式选择 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <SettingsIcon className="h-5 w-5 text-blue-600" />
              演示模式
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs
              value={demoMode}
              onValueChange={(value) => setDemoMode(value as any)}
            >
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="floating">浮动按钮</TabsTrigger>
                <TabsTrigger value="minimal">最小化</TabsTrigger>
                <TabsTrigger value="sidebar">侧边栏</TabsTrigger>
                <TabsTrigger value="custom">自定义</TabsTrigger>
              </TabsList>

              <TabsContent value="floating" className="space-y-4">
                <div className="rounded-lg bg-blue-50 p-4">
                  <h4 className="mb-2 font-medium text-blue-900">
                    浮动按钮模式
                  </h4>
                  <p className="text-sm text-blue-800">
                    在右下角显示一个圆形的浮动按钮，具有渐变背景和阴影效果。
                    这是最常用的模式，适合大多数场景。
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="minimal" className="space-y-4">
                <div className="rounded-lg bg-green-50 p-4">
                  <h4 className="mb-2 font-medium text-green-900">
                    最小化模式
                  </h4>
                  <p className="text-sm text-green-800">
                    显示一个简洁的矩形按钮，适合需要低调设计的场景。
                    按钮样式更加内敛，不会过于突出。
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="sidebar" className="space-y-4">
                <div className="rounded-lg bg-purple-50 p-4">
                  <h4 className="mb-2 font-medium text-purple-900">
                    侧边栏模式
                  </h4>
                  <p className="text-sm text-purple-800">
                    在页面右侧中央显示按钮，适合需要固定位置的场景。
                    按钮位置更加稳定，便于用户找到。
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="custom" className="space-y-4">
                <div className="rounded-lg bg-orange-50 p-4">
                  <h4 className="mb-2 font-medium text-orange-900">
                    自定义模式
                  </h4>
                  <p className="text-sm text-orange-800">
                    可以自定义按钮的位置、样式和行为。 适合有特殊需求的场景。
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 gap-6 xl:grid-cols-3">
          {/* 编辑器区域 */}
          <div className="space-y-6 xl:col-span-2">
            {/* 状态面板 */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
              <Card className="border-blue-200 bg-blue-50">
                <CardContent className="p-4 text-center">
                  <SparklesIcon className="mx-auto mb-2 h-8 w-8 text-blue-600" />
                  <div className="font-semibold text-gray-900">交互次数</div>
                  <div className="text-sm text-gray-600">
                    {interactionCount}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-green-200 bg-green-50">
                <CardContent className="p-4 text-center">
                  <MessageSquareIcon className="mx-auto mb-2 h-8 w-8 text-green-600" />
                  <div className="font-semibold text-gray-900">处理状态</div>
                  <div className="text-sm text-gray-600">
                    {isProcessing ? '处理中' : '空闲'}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-purple-200 bg-purple-50">
                <CardContent className="p-4 text-center">
                  <EyeIcon className="mx-auto mb-2 h-8 w-8 text-purple-600" />
                  <div className="font-semibold text-gray-900">选中文本</div>
                  <div className="text-sm text-gray-600">
                    {selectedText ? `${selectedText.length} 字符` : '未选择'}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-orange-200 bg-orange-50">
                <CardContent className="p-4 text-center">
                  <BrainIcon className="mx-auto mb-2 h-8 w-8 text-orange-600" />
                  <div className="font-semibold text-gray-900">收藏功能</div>
                  <div className="text-sm text-gray-600">
                    {favoriteFeatures.length}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 控制面板 */}
            <Card>
              <CardHeader>
                <CardTitle>演示控制</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap items-center gap-3">
                  <Button
                    onClick={resetDemo}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <RotateCcwIcon className="h-4 w-4" />
                    重置演示
                  </Button>

                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <MousePointerIcon className="h-4 w-4" />
                    <span>点击右下角 AI 按钮开始体验</span>
                  </div>

                  {isProcessing && (
                    <div className="flex items-center gap-2 text-sm text-blue-600">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
                      <span>AI 处理中...</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 编辑器 */}
            <Card>
              <CardHeader>
                <CardTitle>文档编辑器</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  onSelect={(e) => {
                    const target = e.target as HTMLTextAreaElement;
                    const selected = target.value.substring(
                      target.selectionStart,
                      target.selectionEnd
                    );
                    setSelectedText(selected);
                  }}
                  className="min-h-[400px] text-base leading-relaxed"
                  placeholder="开始输入您的文档内容..."
                />

                <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center gap-4">
                    <span>
                      字数:{' '}
                      {
                        content.split(/\s+/).filter((word) => word.length > 0)
                          .length
                      }
                    </span>
                    <span>字符: {content.length}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>AI 已启用</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 信息面板 */}
          <div className="space-y-6">
            {/* 使用说明 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <InfoIcon className="h-5 w-5 text-blue-600" />
                  使用说明
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="rounded-lg bg-blue-50 p-3">
                  <h4 className="mb-1 font-medium text-blue-900">
                    1. 打开 AI 助手
                  </h4>
                  <p className="text-blue-800">
                    点击右下角的 AI 按钮打开助手面板
                  </p>
                </div>

                <div className="rounded-lg bg-green-50 p-3">
                  <h4 className="mb-1 font-medium text-green-900">
                    2. 选择文本
                  </h4>
                  <p className="text-green-800">
                    在编辑器中选择文本以启用相关功能
                  </p>
                </div>

                <div className="rounded-lg bg-purple-50 p-3">
                  <h4 className="mb-1 font-medium text-purple-900">
                    3. 使用功能
                  </h4>
                  <p className="text-purple-800">
                    选择需要的 AI 功能并查看结果
                  </p>
                </div>

                <div className="rounded-lg bg-orange-50 p-3">
                  <h4 className="mb-1 font-medium text-orange-900">
                    4. 管理收藏
                  </h4>
                  <p className="text-orange-800">收藏常用功能便于快速访问</p>
                </div>
              </CardContent>
            </Card>

            {/* AI 处理结果 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SparklesIcon className="h-5 w-5 text-purple-600" />
                  AI 处理结果
                </CardTitle>
              </CardHeader>
              <CardContent>
                {aiResults.length === 0 ? (
                  <div className="py-8 text-center">
                    <BrainIcon className="mx-auto mb-3 h-12 w-12 text-gray-300" />
                    <p className="text-sm text-gray-500">
                      使用 AI 功能后，结果将显示在这里
                    </p>
                  </div>
                ) : (
                  <div className="max-h-64 space-y-3 overflow-y-auto">
                    {aiResults.map((result) => (
                      <div
                        key={result.id}
                        className="space-y-2 rounded-lg border bg-gray-50 p-3"
                      >
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs">
                            {getActionDisplayName(result.action)}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {result.timestamp.toLocaleTimeString()}
                          </span>
                        </div>

                        <div className="text-sm text-gray-800">
                          <p className="font-medium">结果:</p>
                          <p className="mt-1 leading-relaxed text-gray-700">
                            {result.output}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 设备适配说明 */}
            <Card>
              <CardHeader>
                <CardTitle>设备适配</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3 rounded bg-blue-50 p-2">
                  <MonitorIcon className="h-5 w-5 text-blue-600" />
                  <div>
                    <div className="font-medium text-blue-900">桌面端</div>
                    <div className="text-xs text-blue-700">侧边面板模式</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 rounded bg-green-50 p-2">
                  <SmartphoneIcon className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium text-green-900">移动端</div>
                    <div className="text-xs text-green-700">全屏覆盖模式</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 根据选择的模式显示不同的 AI 按钮 */}
      {demoMode === 'floating' && (
        <FloatingAIButton
          onAIAction={handleAIAction}
          selectedText={selectedText}
          isProcessing={isProcessing}
          processingStatus={processingStatus}
          favoriteFeatures={favoriteFeatures}
          onToggleFavorite={handleToggleFavorite}
        />
      )}

      {demoMode === 'minimal' && (
        <MinimalAIButton
          position="bottom-right"
          onAIAction={handleAIAction}
          selectedText={selectedText}
          isProcessing={isProcessing}
          processingStatus={processingStatus}
          favoriteFeatures={favoriteFeatures}
          onToggleFavorite={handleToggleFavorite}
        />
      )}

      {demoMode === 'sidebar' && (
        <SidebarAIButton
          onAIAction={handleAIAction}
          selectedText={selectedText}
          isProcessing={isProcessing}
          processingStatus={processingStatus}
          favoriteFeatures={favoriteFeatures}
          onToggleFavorite={handleToggleFavorite}
        />
      )}

      {demoMode === 'custom' && (
        <SimpleAIToggle
          position="bottom-left"
          variant="default"
          panelWidth={500}
          onAIAction={handleAIAction}
          selectedText={selectedText}
          isProcessing={isProcessing}
          processingStatus={processingStatus}
          favoriteFeatures={favoriteFeatures}
          onToggleFavorite={handleToggleFavorite}
        />
      )}
    </div>
  );
}
