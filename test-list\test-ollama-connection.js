/**
 * 测试 Ollama 连接
 */

require('dotenv').config();

async function testOllamaConnection() {
  console.log('🧪 测试 Ollama 连接...');

  try {
    const { OllamaService } = require('../src/lib/services/ai/ollama-service.ts');

    const config = {
      provider: 'ollama',
      endpoint: 'http://localhost:11454', // 你提供的地址
      model: 'gemma3:27b', // 使用推荐的模型
      // model: 'mistral:latest', // 使用推荐的模型
      maxTokens: 50,
      temperature: 0.7,
      timeout: 60000 // 60秒超时
    };

    console.log('配置:', config);

    const service = new OllamaService(config);
    console.log('Ollama 服务创建成功');

    // 1. 测试连接
    console.log('1️⃣ 测试基础连接...');
    const connected = await service.testConnection();

    if (connected) {
      console.log('✅ Ollama 基础连接成功!');

      // 2. 获取可用模型
      console.log('2️⃣ 获取可用模型列表...');
      try {
        const models = await service.getAvailableModels();
        console.log('📋 可用模型:', models);

        if (models.length > 0) {
          // 使用第一个可用模型进行测试
          const testModel = models[0];
          console.log('🎯 使用模型进行测试:', testModel);

          // 更新配置使用可用的模型
          // const testConfig = { ...config, model: testModel };
          const testConfig = { ...config };
          const testService = new OllamaService(testConfig);

          // 3. 生成文本测试
          console.log('3️⃣ 生成文本测试...');
          try {
            const response = await testService._generateText({
              prompt: '你好，请简单回复',
              maxTokens: 20
            });
            console.log('📝 生成文本成功:', response.content);
            console.log('🔢 使用令牌数:', response.tokensUsed);
            console.log('⏱️ 响应时间:', response.responseTime + 'ms');
            console.log('🤖 使用模型:', response.model);
          } catch (error) {
            console.log('📝 生成文本失败:', error.message);
          }
        } else {
          console.log('⚠️ 没有找到可用的模型');
        }
      } catch (error) {
        console.log('📋 获取模型列表失败:', error.message);
      }
    } else {
      console.log('❌ Ollama 基础连接失败');
      console.log('💡 请检查：');
      console.log('   - Ollama 服务是否在 http://localhost:11454 运行');
      console.log('   - 端口是否正确');
      console.log('   - 防火墙是否阻止连接');
    }
  } catch (error) {
    console.log('❌ 测试异常:', error.message);
    console.log('错误详情:', error);
  }
}

console.log('📋 Ollama 连接测试说明:');
console.log('1. 测试 Ollama 本地服务连接');
console.log('2. 服务地址: http://localhost:11454');
console.log('3. 获取可用模型列表');
console.log('4. 测试文本生成功能');
console.log('');

testOllamaConnection();