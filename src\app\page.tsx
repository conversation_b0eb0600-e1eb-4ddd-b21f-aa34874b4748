import Link from "next/link";
import { getCurrentUser } from "@/lib/auth/utils";

export default async function HomePage() {
  // 检查用户是否已登录
  const user = await getCurrentUser();

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-4 sm:p-8 md:p-16 lg:p-24 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-4xl w-full text-center">
        {/* 主标题 */}
        <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6">
          AI 文档编辑器
        </h1>
        
        <p className="text-base sm:text-lg md:text-xl text-gray-600 mb-8 sm:mb-12 max-w-2xl mx-auto px-4">
          基于人工智能的智能文档编辑器，让您的写作更加高效和专业。
          支持多种 AI 服务，提供智能写作辅助功能。
        </p>

        {/* 功能特色 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6 md:gap-8 mb-8 sm:mb-12 px-4">
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md">
            <div className="text-2xl sm:text-3xl mb-3 sm:mb-4">✨</div>
            <h3 className="text-base sm:text-lg font-semibold mb-2">AI 智能写作</h3>
            <p className="text-sm sm:text-base text-gray-600">
              集成 OpenAI、Ollama、Gemini 等多种 AI 服务，提供续写、改写、总结等功能
            </p>
          </div>
          
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md">
            <div className="text-2xl sm:text-3xl mb-3 sm:mb-4">📝</div>
            <h3 className="text-base sm:text-lg font-semibold mb-2">现代编辑器</h3>
            <p className="text-sm sm:text-base text-gray-600">
              基于 TipTap 的现代富文本编辑器，支持斜杠命令和实时协作
            </p>
          </div>
          
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md">
            <div className="text-2xl sm:text-3xl mb-3 sm:mb-4">☁️</div>
            <h3 className="text-base sm:text-lg font-semibold mb-2">云端同步</h3>
            <p className="text-sm sm:text-base text-gray-600">
              文档自动同步到云端，支持多设备访问，永不丢失您的创作
            </p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4">
          {user ? (
            // 已登录用户显示仪表板链接
            <>
              <Link
                href="/dashboard"
                className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 sm:px-8 rounded-lg transition-colors text-base sm:text-lg text-center touch-manipulation"
              >
                进入仪表板
              </Link>
              <p className="text-sm sm:text-base text-gray-600 text-center">
                欢迎回来，{user.name || user.email}！
              </p>
            </>
          ) : (
            // 未登录用户显示登录/注册链接
            <>
              <Link
                href="/auth/signin"
                className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 sm:px-8 rounded-lg transition-colors text-base sm:text-lg text-center touch-manipulation"
              >
                立即登录
              </Link>
              <Link
                href="/auth/signup"
                className="w-full sm:w-auto bg-white hover:bg-gray-50 text-blue-600 font-medium py-3 px-6 sm:px-8 rounded-lg border-2 border-blue-600 transition-colors text-base sm:text-lg text-center touch-manipulation"
              >
                免费注册
              </Link>
            </>
          )}
        </div>

        {/* 开发测试链接 */}
        <div className="mt-12 sm:mt-16 pt-6 sm:pt-8 border-t border-gray-200 px-4">
          <p className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4">开发测试</p>
          <div className="flex justify-center gap-2 sm:gap-4 flex-wrap">
            <Link
              href="/editor"
              className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm underline font-medium touch-manipulation"
            >
              编辑器测试
            </Link>
            <Link
              href="/mobile-editor-test"
              className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm underline font-medium touch-manipulation"
            >
              移动端编辑器
            </Link>
            <Link
              href="/db-test"
              className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm underline touch-manipulation"
            >
              数据库测试
            </Link>
            <Link
              href="/auth-demo"
              className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm underline font-medium touch-manipulation"
            >
              认证演示
            </Link>
            <Link
              href="/auth-test"
              className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm underline touch-manipulation"
            >
              认证系统测试
            </Link>
            <Link
              href="/api/auth/test"
              className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm underline touch-manipulation"
            >
              认证 API 测试
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}