import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';
import { z } from 'zod';

// 文档创建的验证模式
const createDocumentSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(255, '标题长度不能超过255个字符'),
  content: z.string().optional().default(''),
  folderId: z.string().optional(),
});

/**
 * GET /api/documents - 获取当前用户的所有文档
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const folderId = searchParams.get('folderId');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const sortBy = searchParams.get('sortBy') || 'updatedAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // 构建查询条件
    const whereClause: any = {
      userId: session.user.id,
      isDeleted: false, // 默认不包含已删除的文档
    };

    // 按文件夹筛选
    if (folderId) {
      whereClause.folderId = folderId;
    } else if (searchParams.has('rootOnly')) {
      // 获取根目录下的文档（无文件夹）
      whereClause.folderId = null;
    }

    // 构建排序条件
    const orderBy: any = {};
    if (sortBy === 'title' || sortBy === 'createdAt' || sortBy === 'updatedAt' || sortBy === 'wordCount') {
      orderBy[sortBy] = sortOrder === 'asc' ? 'asc' : 'desc';
    } else {
      orderBy.updatedAt = 'desc';
    }

    // 并行执行查询和计数
    const [documents, totalCount] = await Promise.all([
      prisma.document.findMany({
        where: whereClause,
        include: {
          folder: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy,
        take: Math.min(limit, 100), // 限制最多返回100条记录
        skip: offset,
      }),
      prisma.document.count({
        where: whereClause,
      }),
    ]);

    return NextResponse.json({
      documents,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + documents.length < totalCount,
      },
    });
  } catch (error) {
    console.error('获取文档列表失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/documents - 创建新文档
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createDocumentSchema.parse(body);

    // 如果指定了文件夹ID，验证文件夹是否存在且属于当前用户
    if (validatedData.folderId) {
      const folder = await prisma.folder.findFirst({
        where: {
          id: validatedData.folderId,
          userId: session.user.id,
        },
      });

      if (!folder) {
        return NextResponse.json(
          { error: '文件夹未找到' },
          { status: 404 }
        );
      }
    }

    // 检查同一位置是否已存在同标题的活跃文档
    const existingDocument = await prisma.document.findFirst({
      where: {
        title: validatedData.title,
        folderId: validatedData.folderId || null,
        userId: session.user.id,
        isDeleted: false, // 只检查活跃的文档
      },
    });

    if (existingDocument) {
      return NextResponse.json(
        { error: '同一位置已存在同标题文档' },
        { status: 409 }
      );
    }

    // 计算字数和字符数
    const plainText = validatedData.content.replace(/<[^>]*>/g, '').trim();
    const wordCount = plainText ? plainText.split(/\s+/).length : 0;
    const charCount = plainText.length;

    // 使用事务创建文档和初始历史记录
    const result = await prisma.$transaction(async (tx) => {
      // 创建文档
      const document = await tx.document.create({
        data: {
          title: validatedData.title,
          content: validatedData.content,
          folderId: validatedData.folderId,
          userId: session.user.id,
          wordCount,
          charCount,
        },
        include: {
          folder: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // 创建初始历史记录
      await tx.documentHistory.create({
        data: {
          documentId: document.id,
          version: 1,
          content: validatedData.content,
          changeType: 'user',
        },
      });

      return document;
    });

    console.log(`用户 ${session.user.id} 创建了新文档: ${result.title}`);

    return NextResponse.json({ document: result }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '输入参数无效', details: error.issues },
        { status: 400 }
      );
    }

    console.error('创建文档失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
