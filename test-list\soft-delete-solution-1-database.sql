-- 方案1：数据库层面的部分唯一索引解决方案
-- 
-- 文件作用：
-- 1. 为软删除功能提供数据库层面的唯一性约束
-- 2. 确保同一用户下只能有一个活跃的同名文件夹
-- 3. 允许多个已删除的同名文件夹存在
--
-- 使用场景：
-- - 用户删除文件夹后想要创建同名文件夹
-- - 防止用户创建重复的活跃文件夹
-- - 支持文件夹的恢复功能
--
-- 注意事项：
-- - 这个索引只在 isDeleted = false 的记录上生效
-- - SQLite 3.8+ 支持部分索引
-- - 其他数据库（PostgreSQL, MySQL 8.0+）也支持类似功能

-- 创建部分唯一索引：只对未删除的文件夹强制唯一性
CREATE UNIQUE INDEX IF NOT EXISTS unique_active_folders 
ON folders(userId, name) 
WHERE isDeleted = false;

-- 为文档表也创建类似的索引
CREATE UNIQUE INDEX IF NOT EXISTS unique_active_documents 
ON documents(userId, title, folderId) 
WHERE isDeleted = false;

-- 查看索引信息
-- .indices folders

-- 测试索引效果的 SQL 语句：

-- 1. 插入第一个文件夹（应该成功）
-- INSERT INTO folders (id, name, userId, isDeleted) 
-- VALUES ('test1', 'MyFolder', 'user1', false);

-- 2. 尝试插入同名活跃文件夹（应该失败）
-- INSERT INTO folders (id, name, userId, isDeleted) 
-- VALUES ('test2', 'MyFolder', 'user1', false);
-- 预期错误: UNIQUE constraint failed

-- 3. 软删除第一个文件夹
-- UPDATE folders SET isDeleted = true WHERE id = 'test1';

-- 4. 再次插入同名文件夹（应该成功）
-- INSERT INTO folders (id, name, userId, isDeleted) 
-- VALUES ('test3', 'MyFolder', 'user1', false);

-- 5. 插入已删除的同名文件夹（应该成功）
-- INSERT INTO folders (id, name, userId, isDeleted) 
-- VALUES ('test4', 'MyFolder', 'user1', true);

-- 优点：
-- ✅ 性能最优（数据库层面约束）
-- ✅ 并发安全（数据库自动处理）
-- ✅ 数据一致性强
-- ✅ 维护成本低

-- 缺点：
-- ❌ 需要数据库迁移
-- ❌ 依赖数据库特性（部分索引）
