import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';

/**
 * GET /api/documents/stats - 获取用户文档统计信息
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const folderId = searchParams.get('folderId');

    // 构建查询条件
    const whereClause: any = {
      userId: session.user.id,
    };

    if (folderId) {
      whereClause.folderId = folderId;
    }

    // 并行执行多个统计查询
    const [
      totalDocuments,
      totalWords,
      totalCharacters,
      recentDocuments,
      documentsByFolder,
      documentsCreatedThisWeek,
      documentsCreatedThisMonth,
    ] = await Promise.all([
      // 文档总数
      prisma.document.count({
        where: whereClause,
      }),

      // 总字数
      prisma.document.aggregate({
        where: whereClause,
        _sum: {
          wordCount: true,
        },
      }),

      // 总字符数
      prisma.document.aggregate({
        where: whereClause,
        _sum: {
          charCount: true,
        },
      }),

      // 最近更新的文档
      prisma.document.findMany({
        where: whereClause,
        orderBy: {
          updatedAt: 'desc',
        },
        take: 5,
        select: {
          id: true,
          title: true,
          updatedAt: true,
          wordCount: true,
        },
      }),

      // 按文件夹分组的文档数量
      prisma.document.groupBy({
        by: ['folderId'],
        where: {
          userId: session.user.id,
        },
        _count: {
          id: true,
        },
      }),

      // 本周创建的文档数量
      prisma.document.count({
        where: {
          ...whereClause,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
        },
      }),

      // 本月创建的文档数量
      prisma.document.count({
        where: {
          ...whereClause,
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          },
        },
      }),
    ]);

    // 获取文件夹信息用于统计
    const folderStats = await Promise.all(
      documentsByFolder.map(async (stat) => {
        if (!stat.folderId) {
          return {
            folderId: null,
            folderName: '根目录',
            documentCount: stat._count.id,
          };
        }

        const folder = await prisma.folder.findUnique({
          where: { id: stat.folderId },
          select: { name: true },
        });

        return {
          folderId: stat.folderId,
          folderName: folder?.name || '未知文件夹',
          documentCount: stat._count.id,
        };
      })
    );

    const stats = {
      overview: {
        totalDocuments,
        totalWords: totalWords._sum.wordCount || 0,
        totalCharacters: totalCharacters._sum.charCount || 0,
        documentsCreatedThisWeek,
        documentsCreatedThisMonth,
      },
      recentDocuments,
      folderStats,
      averageWordsPerDocument: totalDocuments > 0 
        ? Math.round((totalWords._sum.wordCount || 0) / totalDocuments)
        : 0,
    };

    return NextResponse.json({ stats });
  } catch (error) {
    console.error('获取文档统计信息失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
