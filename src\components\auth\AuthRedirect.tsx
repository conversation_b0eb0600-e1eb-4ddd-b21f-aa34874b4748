"use client";

import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface AuthRedirectProps {
  children: React.ReactNode;
  redirectTo?: string;
  redirectWhen?: "authenticated" | "unauthenticated";
}

/**
 * 认证重定向组件
 * 用于在特定认证状态下重定向用户
 */
export function AuthRedirect({ 
  children, 
  redirectTo = "/dashboard",
  redirectWhen = "authenticated"
}: AuthRedirectProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (redirectWhen === "authenticated" && isAuthenticated) {
        router.push(redirectTo);
      } else if (redirectWhen === "unauthenticated" && !isAuthenticated) {
        router.push(redirectTo);
      }
    }
  }, [isAuthenticated, isLoading, redirectTo, redirectWhen, router]);

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <p className="text-gray-600">正在验证身份...</p>
        </div>
      </div>
    );
  }

  // 如果需要重定向，显示重定向信息
  if (
    (redirectWhen === "authenticated" && isAuthenticated) ||
    (redirectWhen === "unauthenticated" && !isAuthenticated)
  ) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">正在跳转...</p>
        </div>
      </div>
    );
  }

  // 显示内容
  return <>{children}</>;
}