/**
 * AI 配置同步服务
 * 处理 AI 配置在不同设备间的同步
 */

import { prisma } from '@/lib/db/prisma';
import { encrypt, decrypt, hash, generateToken } from '@/lib/utils/encryption';
import type { AIServiceConfig } from '@/types/ai.types';

/**
 * 同步状态枚举
 */
export enum SyncStatus {
  PENDING = 'pending',
  SYNCING = 'syncing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

/**
 * 设备信息接口
 */
export interface DeviceInfo {
  id: string;
  name: string;
  type: 'desktop' | 'mobile' | 'tablet' | 'web';
  platform: string;
  lastSyncAt: Date;
  isActive: boolean;
}

/**
 * 同步记录接口
 */
export interface SyncRecord {
  id: string;
  userId: string;
  deviceId: string;
  configId: string;
  action: 'create' | 'update' | 'delete';
  status: SyncStatus;
  data?: any;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
}

/**
 * 配置同步数据接口
 */
export interface ConfigSyncData {
  id: string;
  provider: string;
  model: string;
  endpoint?: string;
  maxTokens: number;
  temperature: number;
  isDefault: boolean;
  encryptedApiKey?: string;
  checksum: string;
  version: number;
  lastModified: Date;
}

/**
 * AI 配置同步服务类
 */
export class AIConfigSyncService {
  /**
   * 获取用户的设备列表
   */
  async getUserDevices(userId: string): Promise<DeviceInfo[]> {
    // 这里应该从设备注册表中获取，暂时返回模拟数据
    return [
      {
        id: 'device-1',
        name: 'Desktop PC',
        type: 'desktop',
        platform: 'Windows',
        lastSyncAt: new Date(),
        isActive: true
      },
      {
        id: 'device-2',
        name: 'MacBook Pro',
        type: 'desktop',
        platform: 'macOS',
        lastSyncAt: new Date(Date.now() - 3600000), // 1小时前
        isActive: true
      }
    ];
  }

  /**
   * 注册设备
   */
  async registerDevice(userId: string, deviceInfo: Omit<DeviceInfo, 'id' | 'lastSyncAt' | 'isActive'>): Promise<string> {
    const deviceId = generateToken(16);
    
    // 这里应该将设备信息存储到数据库
    // 暂时返回生成的设备ID
    console.log(`注册设备: ${deviceInfo.name} (${deviceId})`);
    
    return deviceId;
  }

  /**
   * 准备配置同步数据
   */
  async prepareConfigForSync(configId: string, userId: string): Promise<ConfigSyncData | null> {
    const config = await prisma.aIConfiguration.findFirst({
      where: {
        id: configId,
        userId: userId
      }
    });

    if (!config) {
      return null;
    }

    // 加密敏感数据
    const encryptedApiKey = config.apiKey ? encrypt(config.apiKey) : undefined;
    
    // 生成校验和
    const dataForChecksum = JSON.stringify({
      provider: config.provider,
      model: config.model,
      endpoint: config.endpoint,
      maxTokens: config.maxTokens,
      temperature: config.temperature,
      isDefault: config.isDefault,
      apiKey: config.apiKey // 使用原始 API 密钥计算校验和
    });
    const checksum = hash(dataForChecksum);

    return {
      id: config.id,
      provider: config.provider,
      model: config.model,
      endpoint: config.endpoint || undefined,
      maxTokens: config.maxTokens,
      temperature: config.temperature,
      isDefault: config.isDefault,
      encryptedApiKey,
      checksum,
      version: 1, // 这里应该从版本控制字段获取
      lastModified: config.updatedAt
    };
  }

  /**
   * 应用同步的配置数据
   */
  async applySyncedConfig(syncData: ConfigSyncData, userId: string, deviceId: string): Promise<boolean> {
    try {
      // 解密敏感数据
      const apiKey = syncData.encryptedApiKey ? decrypt(syncData.encryptedApiKey) : undefined;

      // 验证校验和
      const dataForChecksum = JSON.stringify({
        provider: syncData.provider,
        model: syncData.model,
        endpoint: syncData.endpoint,
        maxTokens: syncData.maxTokens,
        temperature: syncData.temperature,
        isDefault: syncData.isDefault,
        apiKey: apiKey
      });
      const calculatedChecksum = hash(dataForChecksum);

      if (calculatedChecksum !== syncData.checksum) {
        throw new Error('配置数据校验失败');
      }

      // 检查配置是否已存在
      const existingConfig = await prisma.aIConfiguration.findFirst({
        where: {
          id: syncData.id,
          userId: userId
        }
      });

      if (existingConfig) {
        // 更新现有配置
        await prisma.aIConfiguration.update({
          where: { id: syncData.id },
          data: {
            provider: syncData.provider,
            model: syncData.model,
            endpoint: syncData.endpoint,
            apiKey: apiKey,
            maxTokens: syncData.maxTokens,
            temperature: syncData.temperature,
            isDefault: syncData.isDefault
          }
        });
      } else {
        // 创建新配置
        await prisma.aIConfiguration.create({
          data: {
            id: syncData.id,
            userId: userId,
            provider: syncData.provider,
            model: syncData.model,
            endpoint: syncData.endpoint,
            apiKey: apiKey,
            maxTokens: syncData.maxTokens,
            temperature: syncData.temperature,
            isDefault: syncData.isDefault
          }
        });
      }

      return true;
    } catch (error) {
      console.error('应用同步配置失败:', error);
      return false;
    }
  }

  /**
   * 获取配置的同步状态
   */
  async getConfigSyncStatus(configId: string, userId: string): Promise<{
    lastSyncAt?: Date;
    syncedDevices: string[];
    pendingDevices: string[];
    conflicts: boolean;
  }> {
    // 这里应该从同步记录表中获取实际状态
    // 暂时返回模拟数据
    return {
      lastSyncAt: new Date(),
      syncedDevices: ['device-1', 'device-2'],
      pendingDevices: [],
      conflicts: false
    };
  }

  /**
   * 检测配置冲突
   */
  async detectConfigConflicts(userId: string): Promise<{
    configId: string;
    conflicts: Array<{
      deviceId: string;
      field: string;
      localValue: any;
      remoteValue: any;
    }>;
  }[]> {
    // 这里应该实现实际的冲突检测逻辑
    // 暂时返回空数组
    return [];
  }

  /**
   * 解决配置冲突
   */
  async resolveConfigConflict(
    configId: string,
    userId: string,
    resolution: 'local' | 'remote' | 'merge',
    mergeData?: Partial<ConfigSyncData>
  ): Promise<boolean> {
    try {
      switch (resolution) {
        case 'local':
          // 使用本地版本，标记为已解决
          console.log(`使用本地版本解决冲突: ${configId}`);
          break;
        case 'remote':
          // 使用远程版本，更新本地配置
          console.log(`使用远程版本解决冲突: ${configId}`);
          break;
        case 'merge':
          // 合并版本
          if (mergeData && mergeData.id) {
            await this.applySyncedConfig(mergeData as ConfigSyncData, userId, 'merge-resolution');
          }
          console.log(`合并版本解决冲突: ${configId}`);
          break;
      }
      return true;
    } catch (error) {
      console.error('解决配置冲突失败:', error);
      return false;
    }
  }

  /**
   * 同步用户的所有配置
   */
  async syncAllConfigs(userId: string, deviceId: string): Promise<{
    success: boolean;
    syncedCount: number;
    failedCount: number;
    conflicts: number;
  }> {
    let configs: any[] = [];
    try {
      configs = await prisma.aIConfiguration.findMany({
        where: { userId }
      });

      let syncedCount = 0;
      let failedCount = 0;
      const conflicts = 0; // 暂时设为0

      for (const config of configs) {
        try {
          const syncData = await this.prepareConfigForSync(config.id, userId);
          if (syncData) {
            // 这里应该将同步数据发送到其他设备
            // 暂时只是模拟同步成功
            syncedCount++;
            console.log(`同步配置成功: ${config.provider}/${config.model}`);
          }
        } catch (error) {
          failedCount++;
          console.error(`同步配置失败: ${config.id}`, error);
        }
      }

      return {
        success: failedCount === 0,
        syncedCount,
        failedCount,
        conflicts
      };
    } catch (error) {
      console.error('同步所有配置失败:', error);
      return {
        success: false,
        syncedCount: 0,
        failedCount: configs?.length || 0,
        conflicts: 0
      };
    }
  }

  /**
   * 获取同步历史记录
   */
  async getSyncHistory(userId: string, limit: number = 50): Promise<SyncRecord[]> {
    // 这里应该从同步记录表中获取实际历史
    // 暂时返回模拟数据
    return [
      {
        id: 'sync-1',
        userId,
        deviceId: 'device-1',
        configId: 'config-1',
        action: 'update',
        status: SyncStatus.COMPLETED,
        createdAt: new Date(),
        completedAt: new Date()
      },
      {
        id: 'sync-2',
        userId,
        deviceId: 'device-2',
        configId: 'config-2',
        action: 'create',
        status: SyncStatus.COMPLETED,
        createdAt: new Date(Date.now() - 3600000),
        completedAt: new Date(Date.now() - 3500000)
      }
    ];
  }

  /**
   * 清理过期的同步记录
   */
  async cleanupSyncHistory(userId: string, olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);
    
    // 这里应该删除过期的同步记录
    // 暂时返回模拟的清理数量
    console.log(`清理 ${olderThanDays} 天前的同步记录`);
    return 5; // 模拟清理了5条记录
  }
}

// 导出单例实例
export const aiConfigSyncService = new AIConfigSyncService();