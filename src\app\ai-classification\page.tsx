/**
 * AI 文档分类和建议管理页面
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/Button';
import { 
  Brain, 
  FolderOpen, 
  FileText, 
  Settings,
  BarChart3,
  Sparkles
} from 'lucide-react';
import { AIDocumentClassifier } from '@/components/ai/AIDocumentClassifier';
import { AIFolderSuggestions } from '@/components/ai/AIFolderSuggestions';
import { AIDocumentRecommendations } from '@/components/ai/AIDocumentRecommendations';
import { useToast } from '@/hooks/use-toast';

/**
 * AI 分类和建议管理页面
 */
export default function AIClassificationPage() {
  const { toast } = useToast();
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>('');
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);

  /**
   * 处理分类完成
   */
  const handleClassificationComplete = (result: any) => {
    console.log('分类完成:', result);
    toast({
      title: '分类完成',
      description: '文档分类已完成，您可以查看结果和建议'
    });
  };

  /**
   * 处理建议应用
   */
  const handleSuggestionApplied = (suggestionId: string) => {
    console.log('建议已应用:', suggestionId);
    toast({
      title: '建议已应用',
      description: '文件夹整理建议已成功应用'
    });
  };

  /**
   * 处理推荐点击
   */
  const handleRecommendationClick = (documentId: string) => {
    console.log('推荐文档点击:', documentId);
    setSelectedDocumentId(documentId);
    // 这里可以导航到文档详情页面
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      {/* 页面头部 */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Brain className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">AI 智能分类与建议</h1>
            <p className="text-gray-600">使用人工智能技术自动分类文档、组织文件夹结构并推荐相关内容</p>
          </div>
        </div>

        {/* 快速操作 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Brain className="h-8 w-8 text-blue-500" />
                <div>
                  <h3 className="font-medium">智能分类</h3>
                  <p className="text-sm text-gray-600">自动分析文档内容并分类</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <FolderOpen className="h-8 w-8 text-green-500" />
                <div>
                  <h3 className="font-medium">文件夹建议</h3>
                  <p className="text-sm text-gray-600">智能文件夹结构优化建议</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Sparkles className="h-8 w-8 text-purple-500" />
                <div>
                  <h3 className="font-medium">内容推荐</h3>
                  <p className="text-sm text-gray-600">基于内容的相关文档推荐</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 主要功能区域 */}
      <Tabs defaultValue="classifier" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="classifier" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            文档分类
          </TabsTrigger>
          <TabsTrigger value="folders" className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            文件夹建议
          </TabsTrigger>
          <TabsTrigger value="recommendations" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            文档推荐
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            分析统计
          </TabsTrigger>
        </TabsList>

        {/* 文档分类 */}
        <TabsContent value="classifier" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>文档选择</CardTitle>
              <CardDescription>选择要分类的文档</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">单个文档分类</label>
                  <input
                    type="text"
                    placeholder="输入文档ID"
                    value={selectedDocumentId}
                    onChange={(e) => setSelectedDocumentId(e.target.value)}
                    className="w-full p-2 border rounded-md"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">批量文档分类</label>
                  <textarea
                    placeholder="输入文档ID列表，每行一个"
                    value={selectedDocumentIds.join('\n')}
                    onChange={(e) => setSelectedDocumentIds(e.target.value.split('\n').filter(id => id.trim()))}
                    className="w-full p-2 border rounded-md h-24"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <AIDocumentClassifier
            documentId={selectedDocumentId || undefined}
            documentIds={selectedDocumentIds.length > 0 ? selectedDocumentIds : undefined}
            onClassificationComplete={handleClassificationComplete}
            showAdvancedOptions={true}
          />
        </TabsContent>

        {/* 文件夹建议 */}
        <TabsContent value="folders" className="space-y-6">
          <AIFolderSuggestions
            onSuggestionApplied={handleSuggestionApplied}
            autoLoad={true}
          />
        </TabsContent>

        {/* 文档推荐 */}
        <TabsContent value="recommendations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>上下文文档</CardTitle>
              <CardDescription>选择一个文档作为推荐的上下文</CardDescription>
            </CardHeader>
            <CardContent>
              <input
                type="text"
                placeholder="输入文档ID（可选）"
                value={selectedDocumentId}
                onChange={(e) => setSelectedDocumentId(e.target.value)}
                className="w-full p-2 border rounded-md"
              />
            </CardContent>
          </Card>

          <AIDocumentRecommendations
            currentDocumentId={selectedDocumentId || undefined}
            onRecommendationClick={handleRecommendationClick}
            maxRecommendations={15}
          />
        </TabsContent>

        {/* 分析统计 */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">分类统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">已分类文档</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">分类准确率</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">平均置信度</span>
                    <span className="font-medium">--</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">文件夹优化</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">建议数量</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">已应用建议</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">优化效果</span>
                    <span className="font-medium">--</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">推荐效果</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">推荐准确率</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">点击率</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">用户满意度</span>
                    <span className="font-medium">--</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>使用提示</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm text-gray-600">
                <p>• <strong>文档分类:</strong> AI会分析文档内容，自动识别主题和类型，提供分类建议</p>
                <p>• <strong>文件夹建议:</strong> 基于文档分类结果，AI会建议最优的文件夹结构</p>
                <p>• <strong>内容推荐:</strong> 根据文档内容相似性，推荐相关的文档供您参考</p>
                <p>• <strong>持续优化:</strong> AI会根据您的使用习惯不断优化推荐效果</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}