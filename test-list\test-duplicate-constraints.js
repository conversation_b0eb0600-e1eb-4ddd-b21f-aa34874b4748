/**
 * 重复约束测试用例
 * 
 * 文件作用：
 * 1. 测试文档和文件夹的重复名称约束行为
 * 2. 验证应用层和数据库层的约束一致性
 * 3. 分析当前约束设计的问题和改进建议
 * 
 * 测试场景：
 * - 同一文件夹下创建重复名称的文档
 * - 同一文件夹下创建重复名称的子文件夹
 * - 验证错误处理和用户提示
 * 
 * 发现的问题：
 * - 文档有应用层重复检查，但错误提示可能不够友好
 * - 文件夹没有应用层重复检查，数据库层也没有约束
 * - 两者行为不一致
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * 测试文档重复约束
 */
async function testDocumentDuplicateConstraint(userId, folderId = null) {
  console.log('\n📄 测试文档重复约束');
  const testTitle = 'TestDocument';
  const createdDocs = [];
  
  try {
    // 第一次创建文档
    console.log(`   创建第一个文档: "${testTitle}"`);
    const doc1 = await prisma.document.create({
      data: {
        title: testTitle,
        content: '第一个文档的内容',
        userId: userId,
        folderId: folderId,
        isDeleted: false
      }
    });
    createdDocs.push(doc1);
    console.log(`   ✅ 第一个文档创建成功: ${doc1.id.slice(-8)}...`);
    
    // 第二次创建同名文档
    console.log(`   尝试创建第二个同名文档: "${testTitle}"`);
    try {
      const doc2 = await prisma.document.create({
        data: {
          title: testTitle,
          content: '第二个文档的内容',
          userId: userId,
          folderId: folderId,
          isDeleted: false
        }
      });
      createdDocs.push(doc2);
      console.log(`   ⚠️  第二个同名文档也创建成功: ${doc2.id.slice(-8)}...`);
      console.log(`   📝 数据库层面没有阻止重复文档标题`);
    } catch (error) {
      console.log(`   ❌ 第二个同名文档创建失败: ${error.message}`);
      console.log(`   📝 数据库层面阻止了重复文档标题`);
    }
    
    return { success: true, createdDocs };
    
  } catch (error) {
    console.log(`   ❌ 文档测试失败: ${error.message}`);
    return { success: false, error: error.message, createdDocs };
  }
}

/**
 * 测试文件夹重复约束
 */
async function testFolderDuplicateConstraint(userId, parentId = null) {
  console.log('\n📁 测试文件夹重复约束');
  const testName = 'TestFolder';
  const createdFolders = [];
  
  try {
    // 第一次创建文件夹
    console.log(`   创建第一个文件夹: "${testName}"`);
    const folder1 = await prisma.folder.create({
      data: {
        name: testName,
        userId: userId,
        parentId: parentId,
        isDeleted: false
      }
    });
    createdFolders.push(folder1);
    console.log(`   ✅ 第一个文件夹创建成功: ${folder1.id.slice(-8)}...`);
    
    // 第二次创建同名文件夹
    console.log(`   尝试创建第二个同名文件夹: "${testName}"`);
    try {
      const folder2 = await prisma.folder.create({
        data: {
          name: testName,
          userId: userId,
          parentId: parentId,
          isDeleted: false
        }
      });
      createdFolders.push(folder2);
      console.log(`   ⚠️  第二个同名文件夹也创建成功: ${folder2.id.slice(-8)}...`);
      console.log(`   📝 数据库层面没有阻止重复文件夹名称`);
    } catch (error) {
      console.log(`   ❌ 第二个同名文件夹创建失败: ${error.message}`);
      console.log(`   📝 数据库层面阻止了重复文件夹名称`);
    }
    
    return { success: true, createdFolders };
    
  } catch (error) {
    console.log(`   ❌ 文件夹测试失败: ${error.message}`);
    return { success: false, error: error.message, createdFolders };
  }
}

/**
 * 测试应用层重复检查逻辑
 */
async function testApplicationLayerChecks(userId) {
  console.log('\n🔍 测试应用层重复检查逻辑');
  
  // 导入服务（模拟）
  const DocumentService = {
    async isDocumentTitleDuplicate(title, userId, folderId) {
      const existing = await prisma.document.findFirst({
        where: {
          title,
          userId,
          folderId: folderId || null,
          isDeleted: false
        }
      });
      return !!existing;
    }
  };
  
  const FolderService = {
    async isFolderNameDuplicate(name, userId, parentId) {
      const existing = await prisma.folder.findFirst({
        where: {
          name,
          userId,
          parentId: parentId || null,
          isDeleted: false
        }
      });
      return !!existing;
    }
  };
  
  // 测试文档重复检查
  console.log('   测试文档重复检查逻辑:');
  const docExists = await DocumentService.isDocumentTitleDuplicate('TestDocument', userId, null);
  console.log(`   - 文档 "TestDocument" 是否存在: ${docExists ? '是' : '否'}`);
  
  // 测试文件夹重复检查
  console.log('   测试文件夹重复检查逻辑:');
  const folderExists = await FolderService.isFolderNameDuplicate('TestFolder', userId, null);
  console.log(`   - 文件夹 "TestFolder" 是否存在: ${folderExists ? '是' : '否'}`);
  
  return { docExists, folderExists };
}

/**
 * 分析当前约束设计
 */
function analyzeConstraintDesign(docResult, folderResult, appLayerResult) {
  console.log('\n📊 约束设计分析:');
  
  console.log('\n   📄 文档约束:');
  if (docResult.createdDocs.length > 1) {
    console.log('   ❌ 数据库层面: 无约束，允许重复标题');
  } else {
    console.log('   ✅ 数据库层面: 有约束，阻止重复标题');
  }
  console.log('   ✅ 应用层面: 有检查逻辑 (DocumentService.isDocumentTitleDuplicate)');
  
  console.log('\n   📁 文件夹约束:');
  if (folderResult.createdFolders.length > 1) {
    console.log('   ❌ 数据库层面: 无约束，允许重复名称');
  } else {
    console.log('   ✅ 数据库层面: 有约束，阻止重复名称');
  }
  console.log('   ✅ 应用层面: 有检查逻辑 (FolderService.isFolderNameDuplicate)');
  
  console.log('\n   🔍 问题分析:');
  if (docResult.createdDocs.length > 1 && folderResult.createdFolders.length > 1) {
    console.log('   ⚠️  数据库层面都没有约束，完全依赖应用层检查');
    console.log('   💡 建议: 添加数据库约束作为最后防线');
  }
  
  console.log('\n   💡 改进建议:');
  console.log('   1. 添加数据库层面的唯一约束:');
  console.log('      - 文档: UNIQUE(userId, title, folderId) WHERE isDeleted = false');
  console.log('      - 文件夹: UNIQUE(userId, name, parentId) WHERE isDeleted = false');
  console.log('   2. 改善前端错误提示的用户体验');
  console.log('   3. 确保所有 API 端点都使用应用层检查');
}

/**
 * 主测试函数
 */
async function testDuplicateConstraints() {
  console.log('🚀 开始重复约束测试');
  console.log('=' .repeat(60));
  
  let allCreatedItems = [];
  
  try {
    // 获取测试用户
    const user = await prisma.user.findFirst();
    if (!user) {
      console.log('❌ 没有测试用户');
      return;
    }
    console.log(`👤 使用测试用户: ${user.name || user.email}`);
    
    // 测试文档重复约束
    const docResult = await testDocumentDuplicateConstraint(user.id);
    allCreatedItems.push(...docResult.createdDocs);
    
    // 测试文件夹重复约束
    const folderResult = await testFolderDuplicateConstraint(user.id);
    allCreatedItems.push(...folderResult.createdFolders);
    
    // 测试应用层检查
    const appLayerResult = await testApplicationLayerChecks(user.id);
    
    // 分析约束设计
    analyzeConstraintDesign(docResult, folderResult, appLayerResult);
    
    console.log('\n🎯 测试总结:');
    console.log(`   📄 创建了 ${docResult.createdDocs.length} 个测试文档`);
    console.log(`   📁 创建了 ${folderResult.createdFolders.length} 个测试文件夹`);
    
    if (docResult.createdDocs.length > 1 || folderResult.createdFolders.length > 1) {
      console.log('   ⚠️  发现数据库层面缺少唯一约束');
      console.log('   📝 当前完全依赖应用层检查，存在数据一致性风险');
    } else {
      console.log('   ✅ 数据库层面有适当的约束保护');
    }
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
  } finally {
    // 清理测试数据
    console.log('\n🧹 清理测试数据...');
    
    // 清理文档
    const docIds = allCreatedItems.filter(item => item.title).map(item => item.id);
    if (docIds.length > 0) {
      await prisma.document.deleteMany({
        where: { id: { in: docIds } }
      });
      console.log(`   ✅ 清理了 ${docIds.length} 个测试文档`);
    }
    
    // 清理文件夹
    const folderIds = allCreatedItems.filter(item => item.name).map(item => item.id);
    if (folderIds.length > 0) {
      await prisma.folder.deleteMany({
        where: { id: { in: folderIds } }
      });
      console.log(`   ✅ 清理了 ${folderIds.length} 个测试文件夹`);
    }
    
    await prisma.$disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

// 执行测试
console.log('🚀 开始执行重复约束测试...');
testDuplicateConstraints();
