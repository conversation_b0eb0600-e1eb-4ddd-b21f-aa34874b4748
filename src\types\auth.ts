import { User } from "@prisma/client";

/**
 * 认证用户信息接口
 * 定义在认证系统中使用的用户基本信息
 */
export interface AuthUser {
  id: string;              // 用户唯一标识符
  email: string;           // 用户邮箱地址
  name?: string | null;    // 用户姓名（可选）
  image?: string | null;   // 用户头像URL（可选）
}

/**
 * 登录凭据接口
 * 定义用户登录时需要提供的信息
 */
export interface LoginCredentials {
  email: string;    // 邮箱地址
  password: string; // 密码
}

/**
 * 注册数据接口
 * 定义用户注册时需要提供的信息
 */
export interface RegisterData {
  name: string;     // 用户姓名
  email: string;    // 邮箱地址
  password: string; // 密码
}

/**
 * 认证会话接口
 * 定义用户会话的结构
 */
export interface AuthSession {
  user: AuthUser;   // 用户信息
  expires: string;  // 会话过期时间
}

/**
 * 认证错误接口
 * 定义认证过程中可能出现的错误
 */
export interface AuthError {
  message: string;  // 错误消息
  code?: string;    // 错误代码（可选）
}

/**
 * 扩展 NextAuth.js 的默认类型定义
 * 为了更好的 TypeScript 支持
 */

// 扩展 NextAuth 会话类型
declare module "next-auth" {
  interface Session {
    user: AuthUser;
  }

  interface User extends AuthUser {}
}

// 扩展 NextAuth JWT 类型
declare module "next-auth/jwt" {
  interface JWT {
    id: string; // 在 JWT 中包含用户ID
  }
}