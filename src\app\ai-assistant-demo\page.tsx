'use client';

import React, { useState, useCallback } from 'react';
import { AIAssistantContainer } from '@/components/ai/AIAssistantPanel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { 
  SparklesIcon,
  FileTextIcon,
  SettingsIcon,
  InfoIcon
} from 'lucide-react';

/**
 * AI 助手面板演示页面
 */
export default function AIAssistantDemoPage() {
  const [selectedText, setSelectedText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('');
  const [actionLog, setActionLog] = useState<Array<{ id: string; action: string; data?: any; timestamp: Date }>>([]);

  /**
   * 模拟文本选择
   */
  const handleTextSelection = useCallback((text: string) => {
    setSelectedText(text);
  }, []);

  /**
   * 处理 AI 功能调用
   */
  const handleAIAction = useCallback(async (actionId: string, data?: any) => {
    // 记录操作日志
    const logEntry = {
      id: Math.random().toString(36).substr(2, 9),
      action: actionId,
      data,
      timestamp: new Date()
    };
    setActionLog(prev => [logEntry, ...prev.slice(0, 9)]);

    // 模拟处理过程
    setIsProcessing(true);
    setProcessingStatus(getProcessingMessage(actionId));

    // 模拟异步处理
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

    setIsProcessing(false);
    setProcessingStatus('');
  }, []);

  /**
   * 获取处理状态消息
   */
  const getProcessingMessage = (actionId: string): string => {
    const messages: Record<string, string> = {
      'ai-continue': '正在生成续写内容...',
      'ai-rewrite': '正在改写文本...',
      'ai-summarize': '正在生成摘要...',
      'ai-translate': '正在翻译文本...',
      'ai-explain': '正在生成解释...',
      'ai-keywords': '正在提取关键词...',
      'ai-outline': '正在生成大纲...',
      'ai-analysis': '正在分析内容...',
      'ai-creative': '正在创作内容...',
      'ai-custom': '正在执行自定义指令...',
      'ai-chat': '正在准备对话...',
      'ai-grammar': '正在检查语法...',
      'ai-expand': '正在扩展内容...',
      'ai-settings': '正在打开设置...'
    };
    return messages[actionId] || '正在处理请求...';
  };

  /**
   * 获取操作显示名称
   */
  const getActionDisplayName = (actionId: string): string => {
    const names: Record<string, string> = {
      'ai-continue': 'AI 续写',
      'ai-rewrite': 'AI 改写',
      'ai-summarize': '文档摘要',
      'ai-translate': 'AI 翻译',
      'ai-explain': 'AI 解释',
      'ai-keywords': '关键词提取',
      'ai-outline': '生成大纲',
      'ai-analysis': '内容分析',
      'ai-creative': '创意写作',
      'ai-custom': '自定义指令',
      'ai-chat': 'AI 对话',
      'ai-grammar': '语法检查',
      'ai-expand': '内容扩展',
      'ai-settings': 'AI 设置'
    };
    return names[actionId] || actionId;
  };

  // 示例文本内容
  const sampleTexts = [
    '人工智能技术正在快速发展，深度学习和机器学习算法在各个领域都有广泛应用。',
    'The rapid advancement of artificial intelligence is transforming industries worldwide.',
    '在这个数字化时代，我们需要思考如何平衡技术进步与人文关怀。',
    'Machine learning algorithms can process vast amounts of data to identify patterns and make predictions.',
    '可持续发展是当今世界面临的重要挑战，需要全球合作来应对气候变化。'
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-2">
            <SparklesIcon className="h-8 w-8 text-blue-600" />
            AI 助手面板演示
          </h1>
          <p className="text-gray-600">
            体验可折叠的 AI 助手面板，包含各种 AI 功能的分类和导航界面
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 主要内容区域 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 功能说明 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <InfoIcon className="h-5 w-5 text-blue-600" />
                  功能特性
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">📱 响应式设计</h4>
                    <p className="text-sm text-gray-600">
                      自动适配桌面和移动设备，提供最佳用户体验
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">🎯 功能分类</h4>
                    <p className="text-sm text-gray-600">
                      AI 功能按类别组织，便于快速查找和使用
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">⚡ 快速操作</h4>
                    <p className="text-sm text-gray-600">
                      常用功能快捷访问，支持键盘快捷键
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">📝 上下文感知</h4>
                    <p className="text-sm text-gray-600">
                      根据选中文本智能启用相关功能
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 文本选择测试 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileTextIcon className="h-5 w-5 text-green-600" />
                  文本选择测试
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600">
                  点击下面的文本片段来模拟文本选择，然后使用 AI 助手面板中的相关功能：
                </p>
                <div className="space-y-3">
                  {sampleTexts.map((text, index) => (
                    <div
                      key={index}
                      className={`
                        p-3 rounded-lg border cursor-pointer transition-colors
                        ${selectedText === text 
                          ? 'bg-blue-50 border-blue-300 text-blue-900' 
                          : 'bg-white border-gray-200 hover:bg-gray-50'
                        }
                      `}
                      onClick={() => handleTextSelection(text)}
                    >
                      <p className="text-sm leading-relaxed">{text}</p>
                      {selectedText === text && (
                        <Badge variant="secondary" className="mt-2 text-xs">
                          已选择 ({text.length} 字符)
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
                
                {selectedText && (
                  <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <p className="text-sm text-blue-800">
                      <strong>当前选择:</strong> {selectedText}
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      现在可以使用 AI 助手面板中的文本相关功能了！
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 使用说明 */}
            <Card>
              <CardHeader>
                <CardTitle>使用说明</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">1. 打开面板</h4>
                  <p className="text-sm text-gray-600">
                    点击右侧的 "AI" 按钮打开助手面板
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">2. 选择功能</h4>
                  <p className="text-sm text-gray-600">
                    在面板中选择所需的 AI 功能，功能按类别组织
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">3. 查看结果</h4>
                  <p className="text-sm text-gray-600">
                    操作日志会显示在右侧，实际应用中会显示 AI 处理结果
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 操作日志 */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SettingsIcon className="h-5 w-5 text-purple-600" />
                  操作日志
                </CardTitle>
              </CardHeader>
              <CardContent>
                {actionLog.length === 0 ? (
                  <p className="text-sm text-gray-500 text-center py-4">
                    暂无操作记录
                  </p>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {actionLog.map((log) => (
                      <div
                        key={log.id}
                        className="p-3 bg-gray-50 rounded-lg border"
                      >
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-sm text-gray-900">
                            {getActionDisplayName(log.action)}
                          </span>
                          <span className="text-xs text-gray-500">
                            {log.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        {log.data && (
                          <div className="text-xs text-gray-600">
                            {log.data.text && (
                              <p>文本: {log.data.text.substring(0, 50)}...</p>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 状态信息 */}
            <Card>
              <CardHeader>
                <CardTitle>状态信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">选中文本:</span>
                  <Badge variant={selectedText ? 'default' : 'secondary'}>
                    {selectedText ? `${selectedText.length} 字符` : '无'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">处理状态:</span>
                  <Badge variant={isProcessing ? 'destructive' : 'secondary'}>
                    {isProcessing ? '处理中' : '空闲'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">操作次数:</span>
                  <Badge variant="outline">
                    {actionLog.length}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* AI 助手面板 */}
      <AIAssistantContainer
        position="right"
        width={320}
        onAIAction={handleAIAction}
        selectedText={selectedText}
        isProcessing={isProcessing}
        processingStatus={processingStatus}
      />
    </div>
  );
}