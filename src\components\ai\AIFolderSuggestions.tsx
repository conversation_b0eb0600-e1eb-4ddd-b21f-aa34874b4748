/**
 * AI 文件夹建议组件
 * 提供智能文件夹结构建议和整理功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FolderPlus, 
  FolderOpen, 
  Move, 
  Merge, 
  Sparkles, 
  CheckCircle, 
  AlertTriangle,
  Loader2,
  RefreshCw,
  ArrowRight,
  FileText
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import type { 
  FolderStructureSuggestion, 
  SmartOrganizationSuggestion 
} from '@/types/ai-classification.types';

interface AIFolderSuggestionsProps {
  /** 建议应用完成回调 */
  onSuggestionApplied?: (suggestionId: string) => void;
  /** 是否自动加载建议 */
  autoLoad?: boolean;
}

/**
 * AI 文件夹建议组件
 */
export function AIFolderSuggestions({
  onSuggestionApplied,
  autoLoad = true
}: AIFolderSuggestionsProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isApplying, setIsApplying] = useState<string | null>(null);
  const [folderSuggestions, setFolderSuggestions] = useState<FolderStructureSuggestion[]>([]);
  const [smartSuggestions, setSmartSuggestions] = useState<SmartOrganizationSuggestion[]>([]);

  // 自动加载建议
  useEffect(() => {
    if (autoLoad) {
      loadFolderSuggestions();
      loadSmartSuggestions();
    }
  }, [autoLoad]);

  /**
   * 加载文件夹结构建议
   */
  const loadFolderSuggestions = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/ai/folder-suggestions');
      if (response.ok) {
        const data = await response.json();
        setFolderSuggestions(data.data || []);
      } else {
        throw new Error('加载文件夹建议失败');
      }
    } catch (error) {
      console.error('加载文件夹建议失败:', error);
      toast({
        title: '加载失败',
        description: '无法加载文件夹建议',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 加载智能整理建议
   */
  const loadSmartSuggestions = async () => {
    try {
      const response = await fetch('/api/ai/folder-suggestions/smart', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });
      
      if (response.ok) {
        const data = await response.json();
        setSmartSuggestions(data.data || []);
      }
    } catch (error) {
      console.error('加载智能建议失败:', error);
    }
  };

  /**
   * 应用建议
   */
  const applySuggestion = async (suggestionId: string) => {
    setIsApplying(suggestionId);
    try {
      const response = await fetch('/api/ai/folder-suggestions/apply', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ suggestionId })
      });

      const data = await response.json();
      
      if (data.success) {
        toast({
          title: '应用成功',
          description: data.message
        });
        
        // 更新建议状态
        setSmartSuggestions(prev => 
          prev.map(suggestion => 
            suggestion.id === suggestionId 
              ? { ...suggestion, applied: true }
              : suggestion
          )
        );
        
        onSuggestionApplied?.(suggestionId);
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('应用建议失败:', error);
      toast({
        title: '应用失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      });
    } finally {
      setIsApplying(null);
    }
  };

  /**
   * 获取建议类型图标
   */
  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'create':
        return <FolderPlus className="h-4 w-4" />;
      case 'move':
        return <Move className="h-4 w-4" />;
      case 'merge':
        return <Merge className="h-4 w-4" />;
      case 'folder_creation':
        return <FolderPlus className="h-4 w-4" />;
      case 'document_move':
        return <Move className="h-4 w-4" />;
      case 'duplicate_merge':
        return <Merge className="h-4 w-4" />;
      case 'tag_addition':
        return <FileText className="h-4 w-4" />;
      default:
        return <Sparkles className="h-4 w-4" />;
    }
  };

  /**
   * 获取优先级颜色
   */
  const getPriorityColor = (priority: number) => {
    if (priority >= 4) return 'bg-red-100 text-red-800';
    if (priority >= 3) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  /**
   * 获取优先级文本
   */
  const getPriorityText = (priority: number) => {
    if (priority >= 4) return '高';
    if (priority >= 3) return '中';
    return '低';
  };

  /**
   * 渲染文件夹结构建议
   */
  const renderFolderSuggestions = () => {
    if (folderSuggestions.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <FolderOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>暂无文件夹结构建议</p>
          <p className="text-sm">当您有更多文档时，AI会提供智能的文件夹组织建议</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {folderSuggestions.map((suggestion, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {getSuggestionIcon(suggestion.type)}
                    <h3 className="font-medium">{suggestion.name}</h3>
                    <Badge className={getPriorityColor(suggestion.priority)}>
                      {getPriorityText(suggestion.priority)}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{suggestion.reason}</p>
                  
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span>路径: {suggestion.path}</span>
                    <span>影响文档: {suggestion.documentCount}</span>
                  </div>
                </div>
                
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => applySuggestion(`folder_${index}`)}
                  disabled={isApplying === `folder_${index}`}
                  className="ml-4"
                >
                  {isApplying === `folder_${index}` ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    '应用'
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  /**
   * 渲染智能整理建议
   */
  const renderSmartSuggestions = () => {
    if (smartSuggestions.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <Sparkles className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>暂无智能整理建议</p>
          <p className="text-sm">AI正在分析您的文档结构，稍后会提供整理建议</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {smartSuggestions.map((suggestion) => (
          <Card 
            key={suggestion.id} 
            className={`hover:shadow-md transition-shadow ${
              suggestion.applied ? 'bg-green-50 border-green-200' : ''
            }`}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {getSuggestionIcon(suggestion.type)}
                    <h3 className="font-medium">{suggestion.title}</h3>
                    <Badge className={getPriorityColor(suggestion.priority)}>
                      {getPriorityText(suggestion.priority)}
                    </Badge>
                    {suggestion.applied && (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        已应用
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{suggestion.description}</p>
                  
                  <div className="text-xs text-gray-500 mb-2">
                    <span className="font-medium">预期收益:</span> {suggestion.expectedBenefit}
                  </div>
                  
                  {suggestion.affectedDocuments.length > 0 && (
                    <div className="text-xs text-gray-500">
                      影响文档: {suggestion.affectedDocuments.length} 个
                    </div>
                  )}
                  
                  {/* 操作详情 */}
                  {suggestion.actions.length > 0 && (
                    <div className="mt-3 p-2 bg-gray-50 rounded text-xs">
                      <div className="font-medium mb-1">操作详情:</div>
                      {suggestion.actions.map((action, actionIndex) => (
                        <div key={actionIndex} className="flex items-center gap-2 text-gray-600">
                          <ArrowRight className="h-3 w-3" />
                          <span>{action.description}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                
                <Button
                  size="sm"
                  variant={suggestion.applied ? "secondary" : "outline"}
                  onClick={() => applySuggestion(suggestion.id)}
                  disabled={suggestion.applied || isApplying === suggestion.id}
                  className="ml-4"
                >
                  {isApplying === suggestion.id ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : suggestion.applied ? (
                    '已应用'
                  ) : (
                    '应用'
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* 头部操作 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            AI 文件夹建议
          </CardTitle>
          <CardDescription>
            基于文档内容分析的智能文件夹结构和整理建议
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button
              onClick={() => {
                loadFolderSuggestions();
                loadSmartSuggestions();
              }}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              刷新建议
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 建议内容 */}
      <Tabs defaultValue="smart" className="w-full">
        <TabsList>
          <TabsTrigger value="smart">智能整理</TabsTrigger>
          <TabsTrigger value="folder">文件夹结构</TabsTrigger>
        </TabsList>

        <TabsContent value="smart" className="space-y-4">
          {renderSmartSuggestions()}
        </TabsContent>

        <TabsContent value="folder" className="space-y-4">
          {renderFolderSuggestions()}
        </TabsContent>
      </Tabs>
    </div>
  );
}