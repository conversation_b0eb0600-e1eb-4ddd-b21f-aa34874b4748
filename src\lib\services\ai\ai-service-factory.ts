/**
 * AI 服务工厂
 * 负责创建和管理不同的 AI 服务实例
 */

import { IAIService } from './base-ai-service';
import { OpenAIService } from './openai-service';
import { OllamaService } from './ollama-service';
import { GeminiService } from './gemini-service';
import {
  AIServiceConfig,
  AIProvider,
  AIServiceError,
  AIErrorType
} from '@/types/ai.types';

/**
 * AI 服务工厂类
 */
export class AIServiceFactory {
  private static instances = new Map<string, IAIService>();

  /**
   * 创建 AI 服务实例
   * @param config AI 服务配置
   * @returns AI 服务实例
   */
  static createService(config: AIServiceConfig): IAIService {
    // 生成缓存键
    const cacheKey = this.generateCacheKey(config);

    // 检查缓存
    if (this.instances.has(cacheKey)) {
      const instance = this.instances.get(cacheKey)!;
      // 更新配置（如果有变化）
      if (this.configChanged(instance.config, config)) {
        this.instances.delete(cacheKey);
      } else {
        return instance;
      }
    }

    // 创建新实例
    let service: IAIService;

    switch (config.provider) {
      case 'openai':
        service = new OpenAIService(config);
        break;
      case 'ollama':
        service = new OllamaService(config);
        break;
      case 'gemini':
        service = new GeminiService(config);
        break;
      default:
        throw new AIServiceError(
          AIErrorType.INVALID_REQUEST,
          `不支持的 AI 服务提供商: ${config.provider}`,
          config.provider as AIProvider
        );
    }

    // 缓存实例
    this.instances.set(cacheKey, service);

    return service;
  }

  /**
   * 获取所有支持的提供商
   */
  static getSupportedProviders(): AIProvider[] {
    return ['openai', 'ollama', 'gemini'];
  }

  /**
   * 验证配置是否有效
   * @param config AI 服务配置
   * @returns 验证结果
   */
  static validateConfig(config: AIServiceConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查必需字段
    if (!config.provider) {
      errors.push('服务提供商不能为空');
    }

    if (!config.model) {
      errors.push('模型名称不能为空');
    }

    // 检查提供商特定的配置
    switch (config.provider) {
      case 'openai':
        if (!config.apiKey) {
          errors.push('OpenAI API 密钥是必需的');
        }
        break;
      case 'gemini':
        if (!config.apiKey) {
          errors.push('Gemini API 密钥是必需的');
        }
        break;
      case 'ollama':
        if (!config.endpoint) {
          errors.push('Ollama 服务端点是必需的');
        }
        break;
    }

    // 检查数值范围
    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 100000)) {
      errors.push('最大令牌数必须在 1-100000 之间');
    }

    if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
      errors.push('温度参数必须在 0-2 之间');
    }

    if (config.timeout && config.timeout < 1000) {
      errors.push('超时时间不能少于 1000 毫秒');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取提供商的默认配置
   * @param provider AI 服务提供商
   * @returns 默认配置
   */
  static getDefaultConfig(provider: AIProvider): Partial<AIServiceConfig> {
    const baseConfig = {
      maxTokens: 2000,
      temperature: 0.7,
      timeout: 30000
    };

    switch (provider) {
      case 'openai':
        return {
          ...baseConfig,
          provider,
          model: 'gpt-3.5-turbo',
          endpoint: 'http://127.0.0.1:57800'
        };
      case 'ollama':
        return {
          ...baseConfig,
          provider,
          model: 'llama2',
          endpoint: 'http://localhost:11434',
          timeout: 60000 // Ollama 需要更长时间
        };
      case 'gemini':
        return {
          ...baseConfig,
          provider,
          model: 'gemini-pro',
          endpoint: 'https://generativelanguage.googleapis.com/v1beta'
        };
      default:
        return baseConfig;
    }
  }

  /**
   * 清除缓存的服务实例
   * @param provider 可选的提供商过滤
   */
  static clearCache(provider?: AIProvider): void {
    if (provider) {
      // 清除特定提供商的缓存
      for (const [key, service] of Array.from(this.instances.entries())) {
        if (service.provider === provider) {
          this.instances.delete(key);
        }
      }
    } else {
      // 清除所有缓存
      this.instances.clear();
    }
  }

  /**
   * 生成缓存键
   */
  private static generateCacheKey(config: AIServiceConfig): string {
    const keyParts = [
      config.provider,
      config.model,
      config.endpoint || '',
      config.apiKey ? 'with-key' : 'no-key'
    ];
    return keyParts.join('|');
  }

  /**
   * 检查配置是否发生变化
   */
  private static configChanged(oldConfig: AIServiceConfig, newConfig: AIServiceConfig): boolean {
    const keys: (keyof AIServiceConfig)[] = [
      'provider', 'model', 'apiKey', 'endpoint', 'maxTokens', 'temperature', 'timeout'
    ];

    return keys.some(key => oldConfig[key] !== newConfig[key]);
  }
}

/**
 * AI 服务管理器
 * 提供更高级的服务管理功能
 */
export class AIServiceManager {
  private defaultService?: IAIService;
  private services = new Map<string, IAIService>();

  /**
   * 设置默认服务
   * @param config AI 服务配置
   */
  setDefaultService(config: AIServiceConfig): void {
    this.defaultService = AIServiceFactory.createService(config);
  }

  /**
   * 获取默认服务
   */
  getDefaultService(): IAIService {
    if (!this.defaultService) {
      throw new AIServiceError(
        AIErrorType.INVALID_REQUEST,
        '未设置默认 AI 服务',
        'openai'
      );
    }
    return this.defaultService;
  }

  /**
   * 添加命名服务
   * @param name 服务名称
   * @param config AI 服务配置
   */
  addService(name: string, config: AIServiceConfig): void {
    const service = AIServiceFactory.createService(config);
    this.services.set(name, service);
  }

  /**
   * 获取命名服务
   * @param name 服务名称
   */
  getService(name: string): IAIService {
    const service = this.services.get(name);
    if (!service) {
      throw new AIServiceError(
        AIErrorType.INVALID_REQUEST,
        `未找到名为 "${name}" 的 AI 服务`,
        'openai'
      );
    }
    return service;
  }

  /**
   * 移除命名服务
   * @param name 服务名称
   */
  removeService(name: string): void {
    this.services.delete(name);
  }

  /**
   * 获取所有服务名称
   */
  getServiceNames(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * 测试所有服务连接
   */
  async testAllConnections(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    // 测试默认服务
    if (this.defaultService) {
      results['default'] = await this.defaultService.testConnection();
    }

    // 测试命名服务
    for (const [name, service] of Array.from(this.services.entries())) {
      results[name] = await service.testConnection();
    }

    return results;
  }

  /**
   * 清除所有服务
   */
  clear(): void {
    this.defaultService = undefined;
    this.services.clear();
    AIServiceFactory.clearCache();
  }
}

// 导出单例实例
export const aiServiceManager = new AIServiceManager();