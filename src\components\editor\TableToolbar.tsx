'use client';

import { Editor } from '@tiptap/react';
import { 
  Plus, 
  Minus, 
  Trash2, 
  Table as TableIcon,
  MoreHorizontal,
  MoreVertical
} from 'lucide-react';
import { Button } from '@/components/ui/Button';

interface TableToolbarProps {
  editor: Editor | null;
  className?: string;
}

/**
 * 表格操作工具栏组件
 * 提供表格的增删改查功能
 */
export function TableToolbar({ editor, className }: TableToolbarProps) {
  if (!editor || !editor.isActive('table')) {
    return null;
  }

  const ToolbarButton = ({ 
    onClick, 
    disabled = false, 
    children, 
    title 
  }: {
    onClick: () => void;
    disabled?: boolean;
    children: React.ReactNode;
    title: string;
  }) => (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      disabled={disabled}
      title={title}
      className="h-8 w-8 p-0"
    >
      {children}
    </Button>
  );

  return (
    <div className={`flex items-center gap-1 p-2 bg-background border border-border rounded-lg shadow-sm ${className}`}>
      {/* 表格信息 */}
      <div className="flex items-center gap-2 px-2 text-sm text-muted-foreground border-r border-border pr-3 mr-3">
        <TableIcon className="h-4 w-4" />
        <span>表格</span>
      </div>

      {/* 行操作 */}
      <div className="flex items-center gap-1 border-r border-border pr-3 mr-3">
        <ToolbarButton
          onClick={() => editor.chain().focus().addRowBefore().run()}
          title="在上方插入行"
        >
          <div className="flex flex-col items-center">
            <Plus className="h-3 w-3" />
            <MoreHorizontal className="h-2 w-2" />
          </div>
        </ToolbarButton>
        
        <ToolbarButton
          onClick={() => editor.chain().focus().addRowAfter().run()}
          title="在下方插入行"
        >
          <div className="flex flex-col items-center">
            <MoreHorizontal className="h-2 w-2" />
            <Plus className="h-3 w-3" />
          </div>
        </ToolbarButton>
        
        <ToolbarButton
          onClick={() => editor.chain().focus().deleteRow().run()}
          title="删除当前行"
        >
          <div className="flex flex-col items-center">
            <Trash2 className="h-3 w-3" />
            <MoreHorizontal className="h-2 w-2" />
          </div>
        </ToolbarButton>
      </div>

      {/* 列操作 */}
      <div className="flex items-center gap-1 border-r border-border pr-3 mr-3">
        <ToolbarButton
          onClick={() => editor.chain().focus().addColumnBefore().run()}
          title="在左侧插入列"
        >
          <div className="flex items-center">
            <Plus className="h-3 w-3" />
            <MoreVertical className="h-2 w-2" />
          </div>
        </ToolbarButton>
        
        <ToolbarButton
          onClick={() => editor.chain().focus().addColumnAfter().run()}
          title="在右侧插入列"
        >
          <div className="flex items-center">
            <MoreVertical className="h-2 w-2" />
            <Plus className="h-3 w-3" />
          </div>
        </ToolbarButton>
        
        <ToolbarButton
          onClick={() => editor.chain().focus().deleteColumn().run()}
          title="删除当前列"
        >
          <div className="flex items-center">
            <Trash2 className="h-3 w-3" />
            <MoreVertical className="h-2 w-2" />
          </div>
        </ToolbarButton>
      </div>

      {/* 表格操作 */}
      <div className="flex items-center gap-1">
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleHeaderRow().run()}
          title="切换标题行"
        >
          <div className="text-xs font-bold">H</div>
        </ToolbarButton>
        
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleHeaderColumn().run()}
          title="切换标题列"
        >
          <div className="text-xs font-bold">C</div>
        </ToolbarButton>
        
        <ToolbarButton
          onClick={() => editor.chain().focus().deleteTable().run()}
          title="删除整个表格"
        >
          <Trash2 className="h-4 w-4 text-destructive" />
        </ToolbarButton>
      </div>
    </div>
  );
}

/**
 * 表格浮动工具栏组件
 * 当选中表格时显示的浮动工具栏
 */
export function FloatingTableToolbar({ editor }: { editor: Editor | null }) {
  if (!editor || !editor.isActive('table')) {
    return null;
  }

  return (
    <div className="absolute z-10 -top-12 left-0">
      <TableToolbar editor={editor} />
    </div>
  );
}

export default TableToolbar;