/**
 * 同步相关的类型定义
 */

export interface SyncState {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncAt?: Date;
  pendingChanges: PendingChange[];
  conflicts: SyncConflict[];
  syncProgress?: SyncProgress;
}

export interface PendingChange {
  id: string;
  type: 'create' | 'update' | 'delete';
  entityType: 'document' | 'folder';
  entityId: string;
  data: any;
  timestamp: Date;
  retryCount: number;
}

export interface SyncConflict {
  id: string;
  documentId: string;
  localVersion: any;
  remoteVersion: any;
  conflictType: 'content' | 'metadata' | 'title';
  timestamp: Date;
  resolved: boolean;
}

export interface SyncResult {
  success: boolean;
  documentId?: string;
  folderId?: string;
  status: 'success' | 'conflict' | 'error' | 'skipped';
  message?: string;
  conflict?: SyncConflict;
  lastSyncAt?: Date;
}

export interface SyncProgress {
  total: number;
  completed: number;
  current?: string;
  phase: 'preparing' | 'uploading' | 'downloading' | 'resolving' | 'completed';
}

export interface SyncOptions {
  force?: boolean;
  includeDeleted?: boolean;
  conflictResolution?: 'local' | 'remote' | 'manual';
  batchSize?: number;
}

export interface SyncStats {
  totalSynced: number;
  totalConflicts: number;
  totalErrors: number;
  lastSyncDuration?: number;
  syncFrequency: number;
}

export type SyncEventType = 
  | 'sync_started'
  | 'sync_progress'
  | 'sync_completed'
  | 'sync_error'
  | 'conflict_detected'
  | 'connection_changed';

export interface SyncEvent {
  type: SyncEventType;
  data?: any;
  timestamp: Date;
}

export type SyncEventListener = (event: SyncEvent) => void;