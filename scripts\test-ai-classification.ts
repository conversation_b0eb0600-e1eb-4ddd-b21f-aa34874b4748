/**
 * AI 文档分类功能测试脚本
 */

import { aiDocumentClassifier } from '../src/lib/services/ai-document-classifier';
import { aiFolderOrganizer } from '../src/lib/services/ai-folder-organizer';
import { aiDocumentRecommender } from '../src/lib/services/ai-document-recommender';

/**
 * 测试文档分类功能
 */
async function testDocumentClassification() {
  console.log('🧠 测试 AI 文档分类功能...\n');

  try {
    // 测试获取建议分类
    console.log('1. 测试获取建议分类...');
    const categories = await aiDocumentClassifier.getSuggestedCategories('test-user-id');
    console.log('建议分类:', categories);
    console.log('✅ 获取建议分类成功\n');

    // 注意：以下测试需要真实的文档ID，这里仅作为示例
    const mockDocumentId = 'test-document-id';
    const mockUserId = 'test-user-id';

    // 测试文档分析（需要真实数据库连接）
    console.log('2. 测试文档内容分析...');
    try {
      const analysis = await aiDocumentClassifier.analyzeDocument(mockDocumentId, mockUserId);
      console.log('文档分析结果:', analysis);
      console.log('✅ 文档分析成功\n');
    } catch (error) {
      console.log('⚠️ 文档分析跳过（需要真实文档）:', (error as Error).message, '\n');
    }

    // 测试文档分类（需要真实数据库连接）
    console.log('3. 测试文档分类...');
    try {
      const classification = await aiDocumentClassifier.classifyDocument(mockDocumentId, mockUserId);
      console.log('分类结果:', classification);
      console.log('✅ 文档分类成功\n');
    } catch (error) {
      console.log('⚠️ 文档分类跳过（需要真实文档）:', (error as Error).message, '\n');
    }

  } catch (error) {
    console.error('❌ 文档分类测试失败:', error);
  }
}

/**
 * 测试文件夹组织功能
 */
async function testFolderOrganization() {
  console.log('📁 测试 AI 文件夹组织功能...\n');

  try {
    const mockUserId = 'test-user-id';

    // 测试文件夹结构分析（需要真实数据库连接）
    console.log('1. 测试文件夹结构分析...');
    try {
      const suggestions = await aiFolderOrganizer.analyzeFolderStructure(mockUserId);
      console.log('文件夹建议:', suggestions);
      console.log('✅ 文件夹结构分析成功\n');
    } catch (error) {
      console.log('⚠️ 文件夹分析跳过（需要真实数据）:', (error as Error).message, '\n');
    }

    // 测试智能整理建议（需要真实数据库连接）
    console.log('2. 测试智能整理建议...');
    try {
      const smartSuggestions = await aiFolderOrganizer.generateSmartOrganizationSuggestions(mockUserId);
      console.log('智能建议:', smartSuggestions);
      console.log('✅ 智能整理建议成功\n');
    } catch (error) {
      console.log('⚠️ 智能建议跳过（需要真实数据）:', (error as Error).message, '\n');
    }

  } catch (error) {
    console.error('❌ 文件夹组织测试失败:', error);
  }
}

/**
 * 测试文档推荐功能
 */
async function testDocumentRecommendation() {
  console.log('🔍 测试 AI 文档推荐功能...\n');

  try {
    const mockDocumentId = 'test-document-id';
    const mockUserId = 'test-user-id';

    // 测试相关文档推荐（需要真实数据库连接）
    console.log('1. 测试相关文档推荐...');
    try {
      const related = await aiDocumentRecommender.getRelatedDocuments(mockDocumentId, mockUserId);
      console.log('相关文档:', related);
      console.log('✅ 相关文档推荐成功\n');
    } catch (error) {
      console.log('⚠️ 相关文档推荐跳过（需要真实数据）:', (error as Error).message, '\n');
    }

    // 测试主题搜索（需要真实数据库连接）
    console.log('2. 测试主题搜索...');
    try {
      const topicResults = await aiDocumentRecommender.getDocumentsByTopic('技术', mockUserId);
      console.log('主题搜索结果:', topicResults);
      console.log('✅ 主题搜索成功\n');
    } catch (error) {
      console.log('⚠️ 主题搜索跳过（需要真实数据）:', (error as Error).message, '\n');
    }

    // 测试最近相关文档（需要真实数据库连接）
    console.log('3. 测试最近相关文档...');
    try {
      const recentRelated = await aiDocumentRecommender.getRecentlyRelatedDocuments(mockUserId);
      console.log('最近相关文档:', recentRelated);
      console.log('✅ 最近相关文档成功\n');
    } catch (error) {
      console.log('⚠️ 最近相关文档跳过（需要真实数据）:', (error as Error).message, '\n');
    }

    // 测试智能推荐（需要真实数据库连接）
    console.log('4. 测试智能推荐...');
    try {
      const smartRecs = await aiDocumentRecommender.getSmartRecommendations(mockUserId);
      console.log('智能推荐:', smartRecs);
      console.log('✅ 智能推荐成功\n');
    } catch (error) {
      console.log('⚠️ 智能推荐跳过（需要真实数据）:', (error as Error).message, '\n');
    }

  } catch (error) {
    console.error('❌ 文档推荐测试失败:', error);
  }
}

/**
 * 测试类型定义
 */
function testTypeDefinitions() {
  console.log('📝 测试类型定义...\n');

  // 测试导入类型定义
  try {
    const {
      DocumentType,
    } = require('../src/types/ai-classification.types');

    console.log('文档类型枚举:', Object.values(DocumentType));
    console.log('✅ 类型定义正常\n');
  } catch (error) {
    console.error('❌ 类型定义测试失败:', error);
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🚀 开始 AI 文档分类和建议功能测试\n');
  console.log('=' .repeat(50));

  // 测试类型定义
  testTypeDefinitions();

  // 测试文档分类功能
  await testDocumentClassification();

  // 测试文件夹组织功能
  await testFolderOrganization();

  // 测试文档推荐功能
  await testDocumentRecommendation();

  console.log('=' .repeat(50));
  console.log('✨ AI 分类和建议功能测试完成！');
  console.log('\n注意事项:');
  console.log('- 某些测试需要真实的数据库连接和文档数据');
  console.log('- AI 服务需要正确配置才能正常工作');
  console.log('- 建议在开发环境中使用真实数据进行完整测试');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export {
  testDocumentClassification,
  testFolderOrganization,
  testDocumentRecommendation,
  testTypeDefinitions
};