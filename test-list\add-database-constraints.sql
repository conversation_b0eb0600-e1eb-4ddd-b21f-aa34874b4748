-- 添加数据库约束解决方案
-- 
-- 文件作用：
-- 1. 为文档和文件夹添加数据库层面的唯一约束
-- 2. 确保数据一致性，防止重复数据
-- 3. 支持软删除场景下的约束
--
-- 约束设计原则：
-- - 同一用户在同一位置不能有重复名称的项目
-- - 支持软删除（只对未删除的项目生效）
-- - 允许跨用户、跨位置的同名项目

-- ============================================
-- 1. 文档唯一约束
-- ============================================

-- 为文档添加唯一约束：同一用户在同一文件夹下不能有重复标题的文档
-- 使用部分索引，只对未删除的文档生效
CREATE UNIQUE INDEX IF NOT EXISTS unique_active_document_title 
ON documents(userId, title, COALESCE(folderId, '')) 
WHERE isDeleted = false;

-- 说明：
-- - userId: 确保用户隔离
-- - title: 文档标题
-- - COALESCE(folderId, ''): 处理 NULL 值，根目录用空字符串表示
-- - WHERE isDeleted = false: 只对活跃文档生效

-- ============================================
-- 2. 文件夹唯一约束  
-- ============================================

-- 为文件夹添加唯一约束：同一用户在同一父文件夹下不能有重复名称的文件夹
CREATE UNIQUE INDEX IF NOT EXISTS unique_active_folder_name 
ON folders(userId, name, COALESCE(parentId, '')) 
WHERE isDeleted = false;

-- 说明：
-- - userId: 确保用户隔离
-- - name: 文件夹名称
-- - COALESCE(parentId, ''): 处理 NULL 值，根目录用空字符串表示
-- - WHERE isDeleted = false: 只对活跃文件夹生效

-- ============================================
-- 3. 验证约束效果
-- ============================================

-- 测试文档约束：
-- 以下操作应该成功：
-- INSERT INTO documents (id, title, userId, folderId, content, isDeleted) 
-- VALUES ('test1', 'MyDoc', 'user1', 'folder1', 'content', false);

-- 以下操作应该失败（重复）：
-- INSERT INTO documents (id, title, userId, folderId, content, isDeleted) 
-- VALUES ('test2', 'MyDoc', 'user1', 'folder1', 'content', false);

-- 以下操作应该成功（不同文件夹）：
-- INSERT INTO documents (id, title, userId, folderId, content, isDeleted) 
-- VALUES ('test3', 'MyDoc', 'user1', 'folder2', 'content', false);

-- 以下操作应该成功（已删除）：
-- INSERT INTO documents (id, title, userId, folderId, content, isDeleted) 
-- VALUES ('test4', 'MyDoc', 'user1', 'folder1', 'content', true);

-- 测试文件夹约束：
-- 以下操作应该成功：
-- INSERT INTO folders (id, name, userId, parentId, isDeleted) 
-- VALUES ('test1', 'MyFolder', 'user1', 'parent1', false);

-- 以下操作应该失败（重复）：
-- INSERT INTO folders (id, name, userId, parentId, isDeleted) 
-- VALUES ('test2', 'MyFolder', 'user1', 'parent1', false);

-- ============================================
-- 4. 约束的优点
-- ============================================

-- ✅ 数据一致性保证：数据库层面强制约束
-- ✅ 性能优化：索引提高查询速度
-- ✅ 并发安全：数据库自动处理并发冲突
-- ✅ 软删除支持：只对活跃项目生效
-- ✅ 维护简单：一次设置，永久生效

-- ============================================
-- 5. 注意事项
-- ============================================

-- 1. 现有重复数据处理：
--    在添加约束前，需要清理现有的重复数据

-- 2. 应用层错误处理：
--    需要捕获 UNIQUE constraint failed 错误并提供友好提示

-- 3. 迁移策略：
--    建议在维护窗口期间执行，避免影响正在运行的应用

-- ============================================
-- 6. 清理现有重复数据的脚本
-- ============================================

-- 查找重复文档：
-- SELECT userId, title, folderId, COUNT(*) as count
-- FROM documents 
-- WHERE isDeleted = false
-- GROUP BY userId, title, COALESCE(folderId, '')
-- HAVING COUNT(*) > 1;

-- 查找重复文件夹：
-- SELECT userId, name, parentId, COUNT(*) as count
-- FROM folders 
-- WHERE isDeleted = false
-- GROUP BY userId, name, COALESCE(parentId, '')
-- HAVING COUNT(*) > 1;

-- 清理重复数据（保留最新的）：
-- 注意：这些操作会删除数据，请在执行前备份！

-- 清理重复文档（保留最新创建的）：
-- DELETE FROM documents 
-- WHERE id NOT IN (
--   SELECT MAX(id) 
--   FROM documents 
--   WHERE isDeleted = false
--   GROUP BY userId, title, COALESCE(folderId, '')
-- ) AND isDeleted = false;

-- 清理重复文件夹（保留最新创建的）：
-- DELETE FROM folders 
-- WHERE id NOT IN (
--   SELECT MAX(id) 
--   FROM folders 
--   WHERE isDeleted = false
--   GROUP BY userId, name, COALESCE(parentId, '')
-- ) AND isDeleted = false;
