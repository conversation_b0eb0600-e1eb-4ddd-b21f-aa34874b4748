'use client';

import React, { useState } from 'react';
import { useSync } from '@/hooks/useSync';
import { SyncOptions } from '@/types/sync';
import { 
  RefreshCw, 
  Settings, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff,
  Download,
  Upload
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { SyncStatusIndicator } from './SyncStatusIndicator';
import { SyncProgress } from './SyncProgress';
import { ConflictList } from './ConflictResolver';

interface SyncControlPanelProps {
  className?: string;
}

/**
 * 同步控制面板组件
 * 提供完整的同步管理界面
 */
export function SyncControlPanel({ className }: SyncControlPanelProps) {
  const {
    syncState,
    isOnline,
    isSyncing,
    lastSyncAt,
    syncProgress,
    conflicts,
    hasConflicts,
    manualSync,
    resolveConflict,
    syncStats
  } = useSync({
    onSyncComplete: (results) => {
      console.log('同步完成:', results);
    },
    onSyncError: (error) => {
      console.error('同步错误:', error);
    },
    onConflictDetected: (conflict) => {
      console.log('检测到冲突:', conflict);
    }
  });

  const [showSettings, setShowSettings] = useState(false);
  const [syncOptions, setSyncOptions] = useState<SyncOptions>({
    force: false,
    includeDeleted: false,
    conflictResolution: 'manual',
    batchSize: 10
  });

  const handleManualSync = async () => {
    try {
      await manualSync(syncOptions);
    } catch (error) {
      console.error('手动同步失败:', error);
    }
  };

  const formatLastSync = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    return date.toLocaleDateString();
  };

  return (
    <div className={cn('bg-white border rounded-lg shadow-sm', className)}>
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <h3 className="font-medium text-gray-900">文档同步</h3>
          <SyncStatusIndicator size="sm" />
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-1 text-gray-400 hover:text-gray-600 rounded"
            title="同步设置"
          >
            <Settings className="h-4 w-4" />
          </button>
          <button
            onClick={handleManualSync}
            disabled={isSyncing || !isOnline}
            className={cn(
              'flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md',
              isSyncing || !isOnline
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            )}
          >
            <RefreshCw className={cn('h-4 w-4', isSyncing && 'animate-spin')} />
            {isSyncing ? '同步中...' : '立即同步'}
          </button>
        </div>
      </div>

      {/* 同步设置 */}
      {showSettings && (
        <div className="p-4 border-b bg-gray-50">
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">同步设置</h4>
            
            <div className="space-y-2">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={syncOptions.force}
                  onChange={(e) => setSyncOptions(prev => ({ ...prev, force: e.target.checked }))}
                  className="rounded border-gray-300"
                />
                <span className="text-sm text-gray-700">强制同步（覆盖冲突）</span>
              </label>
              
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={syncOptions.includeDeleted}
                  onChange={(e) => setSyncOptions(prev => ({ ...prev, includeDeleted: e.target.checked }))}
                  className="rounded border-gray-300"
                />
                <span className="text-sm text-gray-700">包含已删除的文档</span>
              </label>
            </div>

            <div className="space-y-2">
              <label className="text-sm text-gray-700">冲突解决策略:</label>
              <select
                value={syncOptions.conflictResolution}
                onChange={(e) => setSyncOptions(prev => ({ 
                  ...prev, 
                  conflictResolution: e.target.value as 'local' | 'remote' | 'manual' 
                }))}
                className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md"
              >
                <option value="manual">手动解决</option>
                <option value="local">优先本地版本</option>
                <option value="remote">优先远程版本</option>
              </select>
            </div>

            <div className="space-y-2">
              <label className="text-sm text-gray-700">批处理大小:</label>
              <input
                type="number"
                min="1"
                max="50"
                value={syncOptions.batchSize}
                onChange={(e) => setSyncOptions(prev => ({ 
                  ...prev, 
                  batchSize: parseInt(e.target.value) || 10 
                }))}
                className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>
          </div>
        </div>
      )}

      {/* 同步进度 */}
      {syncProgress && (
        <div className="p-4 border-b">
          <SyncProgress progress={syncProgress as any} />
        </div>
      )}

      {/* 冲突列表 */}
      {hasConflicts && (
        <div className="p-4 border-b">
          <ConflictList
            conflicts={conflicts}
            onResolveConflict={resolveConflict}
          />
        </div>
      )}

      {/* 状态信息 */}
      <div className="p-4 space-y-3">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">网络状态</span>
              <div className="flex items-center gap-1">
                {isOnline ? (
                  <>
                    <Wifi className="h-4 w-4 text-green-500" />
                    <span className="text-green-600">在线</span>
                  </>
                ) : (
                  <>
                    <WifiOff className="h-4 w-4 text-red-500" />
                    <span className="text-red-600">离线</span>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">同步状态</span>
              <div className="flex items-center gap-1">
                {isSyncing ? (
                  <>
                    <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
                    <span className="text-blue-600">同步中</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-green-600">已同步</span>
                  </>
                )}
              </div>
            </div>

            {lastSyncAt && (
              <div className="flex items-center justify-between">
                <span className="text-gray-600">上次同步</span>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-500">
                    {formatLastSync(lastSyncAt)}
                  </span>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">已同步</span>
              <span className="text-green-600">{syncStats.totalSynced}</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">冲突</span>
              <span className="text-yellow-600">{syncStats.totalConflicts}</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">错误</span>
              <span className="text-red-600">{syncStats.totalErrors}</span>
            </div>

            {syncStats.lastSyncDuration && (
              <div className="flex items-center justify-between">
                <span className="text-gray-600">耗时</span>
                <span className="text-gray-500">
                  {(syncStats.lastSyncDuration / 1000).toFixed(1)}s
                </span>
              </div>
            )}
          </div>
        </div>

        {/* 待同步项目 */}
        {syncState.pendingChanges.length > 0 && (
          <div className="pt-3 border-t">
            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
              <Upload className="h-4 w-4" />
              <span>待同步: {syncState.pendingChanges.length} 项</span>
            </div>
            <div className="space-y-1">
              {syncState.pendingChanges.slice(0, 3).map((change) => (
                <div key={change.id} className="text-xs text-gray-500 flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  <span>{change.type} - {change.entityType}</span>
                </div>
              ))}
              {syncState.pendingChanges.length > 3 && (
                <div className="text-xs text-gray-400">
                  还有 {syncState.pendingChanges.length - 3} 项...
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}