import { useState, useCallback, useMemo, useEffect } from 'react';

interface ContentChunk {
  id: string;
  content: string;
  startIndex: number;
  endIndex: number;
  wordCount: number;
  characterCount: number;
}

interface VirtualizedContentOptions {
  /** 每个块的大小（字符数） */
  chunkSize?: number;
  /** 预加载的块数量 */
  preloadChunks?: number;
  /** 是否启用虚拟化 */
  enabled?: boolean;
  /** 内容变化时的回调 */
  onContentChange?: (content: string) => void;
}

interface VirtualizedContentState {
  chunks: ContentChunk[];
  visibleChunks: ContentChunk[];
  currentChunkIndex: number;
  totalChunks: number;
  isLoading: boolean;
  error: string | null;
}

/**
 * 虚拟化内容管理 Hook
 * 用于处理大文档的分块加载和管理
 */
export function useVirtualizedContent(
  content: string,
  options: VirtualizedContentOptions = {}
) {
  const {
    chunkSize = 10000, // 10KB per chunk
    preloadChunks = 3,
    enabled = false,
    onContentChange,
  } = options;

  const [state, setState] = useState<VirtualizedContentState>({
    chunks: [],
    visibleChunks: [],
    currentChunkIndex: 0,
    totalChunks: 0,
    isLoading: false,
    error: null,
  });

  // 将内容分割成块
  const createChunks = useCallback((content: string): ContentChunk[] => {
    if (!enabled || content.length < chunkSize) {
      return [{
        id: 'single-chunk',
        content,
        startIndex: 0,
        endIndex: content.length - 1,
        wordCount: content.split(/\s+/).filter(word => word.length > 0).length,
        characterCount: content.length,
      }];
    }

    const chunks: ContentChunk[] = [];
    let startIndex = 0;

    while (startIndex < content.length) {
      const endIndex = Math.min(startIndex + chunkSize, content.length);
      let chunkEnd = endIndex;

      // 尝试在单词边界处分割
      if (endIndex < content.length) {
        const nextSpaceIndex = content.indexOf(' ', endIndex);
        const prevSpaceIndex = content.lastIndexOf(' ', endIndex);
        
        if (nextSpaceIndex !== -1 && nextSpaceIndex - endIndex < 100) {
          chunkEnd = nextSpaceIndex;
        } else if (prevSpaceIndex !== -1 && endIndex - prevSpaceIndex < 100) {
          chunkEnd = prevSpaceIndex;
        }
      }

      const chunkContent = content.slice(startIndex, chunkEnd);
      
      chunks.push({
        id: `chunk-${chunks.length}`,
        content: chunkContent,
        startIndex,
        endIndex: chunkEnd - 1,
        wordCount: chunkContent.split(/\s+/).filter(word => word.length > 0).length,
        characterCount: chunkContent.length,
      });

      startIndex = chunkEnd;
    }

    return chunks;
  }, [chunkSize, enabled]);

  // 获取可见的块
  const getVisibleChunks = useCallback((
    chunks: ContentChunk[],
    currentIndex: number,
    preloadCount: number
  ): ContentChunk[] => {
    const startIndex = Math.max(0, currentIndex - preloadCount);
    const endIndex = Math.min(chunks.length, currentIndex + preloadCount + 1);
    
    return chunks.slice(startIndex, endIndex);
  }, []);

  // 更新内容块
  useEffect(() => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const chunks = createChunks(content);
      const visibleChunks = getVisibleChunks(chunks, 0, preloadChunks);

      setState(prev => ({
        ...prev,
        chunks,
        visibleChunks,
        totalChunks: chunks.length,
        isLoading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '处理内容时发生错误',
        isLoading: false,
      }));
    }
  }, [content, createChunks, getVisibleChunks, preloadChunks]);

  // 导航到指定块
  const navigateToChunk = useCallback((chunkIndex: number) => {
    if (chunkIndex < 0 || chunkIndex >= state.totalChunks) {
      return;
    }

    const visibleChunks = getVisibleChunks(state.chunks, chunkIndex, preloadChunks);
    
    setState(prev => ({
      ...prev,
      currentChunkIndex: chunkIndex,
      visibleChunks,
    }));
  }, [state.chunks, state.totalChunks, getVisibleChunks, preloadChunks]);

  // 搜索内容
  const searchInChunks = useCallback((query: string): Array<{
    chunkIndex: number;
    matches: Array<{ start: number; end: number; context: string }>;
  }> => {
    if (!query.trim()) return [];

    const results: Array<{
      chunkIndex: number;
      matches: Array<{ start: number; end: number; context: string }>;
    }> = [];

    state.chunks.forEach((chunk, chunkIndex) => {
      const matches: Array<{ start: number; end: number; context: string }> = [];
      const regex = new RegExp(query, 'gi');
      let match;

      while ((match = regex.exec(chunk.content)) !== null) {
        const start = Math.max(0, match.index - 50);
        const end = Math.min(chunk.content.length, match.index + match[0].length + 50);
        const context = chunk.content.slice(start, end);

        matches.push({
          start: match.index,
          end: match.index + match[0].length,
          context,
        });
      }

      if (matches.length > 0) {
        results.push({ chunkIndex, matches });
      }
    });

    return results;
  }, [state.chunks]);

  // 获取统计信息
  const getStats = useMemo(() => ({
    totalWords: state.chunks.reduce((sum, chunk) => sum + chunk.wordCount, 0),
    totalCharacters: state.chunks.reduce((sum, chunk) => sum + chunk.characterCount, 0),
    totalChunks: state.totalChunks,
    currentChunk: state.currentChunkIndex + 1,
    isVirtualized: enabled && state.totalChunks > 1,
  }), [state.chunks, state.totalChunks, state.currentChunkIndex, enabled]);

  // 更新块内容
  const updateChunkContent = useCallback((chunkIndex: number, newContent: string) => {
    if (chunkIndex < 0 || chunkIndex >= state.chunks.length) {
      return;
    }

    const updatedChunks = [...state.chunks];
    const chunk = updatedChunks[chunkIndex];
    
    updatedChunks[chunkIndex] = {
      ...chunk,
      content: newContent,
      wordCount: newContent.split(/\s+/).filter(word => word.length > 0).length,
      characterCount: newContent.length,
    };

    setState(prev => ({
      ...prev,
      chunks: updatedChunks,
    }));

    // 通知内容变化
    const fullContent = updatedChunks.map(c => c.content).join('');
    onContentChange?.(fullContent);
  }, [state.chunks, onContentChange]);

  // 合并所有块的内容
  const getFullContent = useCallback(() => {
    return state.chunks.map(chunk => chunk.content).join('');
  }, [state.chunks]);

  return {
    // 状态
    chunks: state.chunks,
    visibleChunks: state.visibleChunks,
    currentChunkIndex: state.currentChunkIndex,
    totalChunks: state.totalChunks,
    isLoading: state.isLoading,
    error: state.error,
    
    // 统计信息
    stats: getStats,
    
    // 操作方法
    navigateToChunk,
    searchInChunks,
    updateChunkContent,
    getFullContent,
    
    // 工具方法
    isVirtualized: enabled && state.totalChunks > 1,
    canNavigatePrev: state.currentChunkIndex > 0,
    canNavigateNext: state.currentChunkIndex < state.totalChunks - 1,
  };
}