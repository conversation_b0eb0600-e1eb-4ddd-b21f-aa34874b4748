'use client';

import React, { useState, useEffect } from 'react';
import { Check, X, RefreshCw, Loader2, Sparkles, ChevronDown, ChevronUp } from 'lucide-react';
import { TextGenerationResult } from '@/lib/services/ai/text-generation-service';

/**
 * AI 生成内容预览组件的属性
 */
interface AIGeneratedContentProps {
  /** 生成的内容结果 */
  result: TextGenerationResult;
  /** 是否显示组件 */
  visible: boolean;
  /** 接受内容的回调 */
  onAccept: (content: string) => void;
  /** 拒绝内容的回调 */
  onReject: () => void;
  /** 重新生成的回调 */
  onRegenerate: () => void;
  /** 是否正在重新生成 */
  isRegenerating?: boolean;
  /** 组件位置 */
  position?: {
    top: number;
    left: number;
  };
  /** 自定义样式类名 */
  className?: string;
}

/**
 * AI 生成内容预览组件
 * 显示 AI 生成的文本内容，提供接受、拒绝、重新生成等操作
 */
export function AIGeneratedContent({
  result,
  visible,
  onAccept,
  onReject,
  onRegenerate,
  isRegenerating = false,
  position,
  className = ''
}: AIGeneratedContentProps) {
  const [selectedOption, setSelectedOption] = useState(0);
  const [isExpanded, setIsExpanded] = useState(true);
  const [animationClass, setAnimationClass] = useState('');

  // 获取所有可选内容（主内容 + 备选方案）
  const allOptions = [result.content, ...(result.alternatives || [])];

  // 组件显示/隐藏动画
  useEffect(() => {
    if (visible) {
      setAnimationClass('animate-in slide-in-from-bottom-2 fade-in duration-200');
    } else {
      setAnimationClass('animate-out slide-out-to-bottom-2 fade-out duration-150');
    }
  }, [visible]);

  // 重置选中选项
  useEffect(() => {
    setSelectedOption(0);
  }, [result.id]);

  if (!visible) {
    return null;
  }

  const handleAccept = () => {
    onAccept(allOptions[selectedOption]);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'Enter':
        if (event.metaKey || event.ctrlKey) {
          handleAccept();
        }
        break;
      case 'Escape':
        onReject();
        break;
      case 'Tab':
        event.preventDefault();
        onRegenerate();
        break;
    }
  };

  const positionStyle = position ? {
    position: 'absolute' as const,
    top: position.top,
    left: position.left,
    zIndex: 50
  } : {};

  return (
    <div
      className={`
        ai-generated-content
        bg-gradient-to-r from-blue-50 to-purple-50 
        border-2 border-blue-200 
        rounded-lg shadow-lg 
        p-4 max-w-2xl min-w-96
        ${animationClass}
        ${className}
      `}
      style={positionStyle}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      {/* 头部 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Sparkles className="w-4 h-4 text-blue-500" />
          <span className="text-sm font-medium text-blue-700">
            AI 生成内容
          </span>
          <span className="text-xs text-gray-500">
            ({result.tokensUsed} tokens, {result.responseTime}ms)
          </span>
        </div>
        
        <div className="flex items-center gap-1">
          {/* 展开/折叠按钮 */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 hover:bg-blue-100 rounded transition-colors"
            title={isExpanded ? '折叠' : '展开'}
          >
            {isExpanded ? (
              <ChevronUp className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-gray-500" />
            )}
          </button>
          
          {/* 关闭按钮 */}
          <button
            onClick={onReject}
            className="p-1 hover:bg-red-100 rounded transition-colors"
            title="关闭 (Esc)"
          >
            <X className="w-4 h-4 text-gray-500 hover:text-red-500" />
          </button>
        </div>
      </div>

      {/* 内容区域 */}
      {isExpanded && (
        <>
          {/* 选项切换 */}
          {allOptions.length > 1 && (
            <div className="flex gap-1 mb-3">
              {allOptions.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedOption(index)}
                  className={`
                    px-3 py-1 text-xs rounded-full transition-colors
                    ${selectedOption === index
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                    }
                  `}
                >
                  选项 {index + 1}
                </button>
              ))}
            </div>
          )}

          {/* 生成的内容 */}
          <div className="mb-4">
            <div className="
              bg-white 
              border border-gray-200 
              rounded-md p-3 
              text-sm leading-relaxed
              max-h-60 overflow-y-auto
              ai-generated-text
            ">
              {allOptions[selectedOption]}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              {/* 接受按钮 */}
              <button
                onClick={handleAccept}
                className="
                  flex items-center gap-2 
                  px-4 py-2 
                  bg-green-500 hover:bg-green-600 
                  text-white text-sm font-medium 
                  rounded-md transition-colors
                  focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
                "
                title="接受内容 (Ctrl+Enter)"
              >
                <Check className="w-4 h-4" />
                接受
              </button>

              {/* 拒绝按钮 */}
              <button
                onClick={onReject}
                className="
                  flex items-center gap-2 
                  px-4 py-2 
                  bg-gray-500 hover:bg-gray-600 
                  text-white text-sm font-medium 
                  rounded-md transition-colors
                  focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2
                "
                title="拒绝内容 (Esc)"
              >
                <X className="w-4 h-4" />
                拒绝
              </button>
            </div>

            {/* 重新生成按钮 */}
            <button
              onClick={onRegenerate}
              disabled={isRegenerating}
              className="
                flex items-center gap-2 
                px-3 py-2 
                bg-blue-500 hover:bg-blue-600 
                disabled:bg-blue-300 disabled:cursor-not-allowed
                text-white text-sm font-medium 
                rounded-md transition-colors
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
              "
              title="重新生成 (Tab)"
            >
              {isRegenerating ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
              重新生成
            </button>
          </div>

          {/* 快捷键提示 */}
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="text-xs text-gray-500 space-y-1">
              <div>快捷键：Ctrl+Enter 接受 | Esc 拒绝 | Tab 重新生成</div>
              {allOptions.length > 1 && (
                <div>点击上方选项按钮切换不同的生成结果</div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}

/**
 * AI 生成内容的加载状态组件
 */
interface AIGenerationLoadingProps {
  visible: boolean;
  onCancel: () => void;
  message?: string;
  position?: {
    top: number;
    left: number;
  };
}

export function AIGenerationLoading({
  visible,
  onCancel,
  message = '正在生成内容...',
  position
}: AIGenerationLoadingProps) {
  if (!visible) {
    return null;
  }

  const positionStyle = position ? {
    position: 'absolute' as const,
    top: position.top,
    left: position.left,
    zIndex: 50
  } : {};

  return (
    <div
      className="
        bg-white border border-gray-200 rounded-lg shadow-lg p-4
        animate-in slide-in-from-bottom-2 fade-in duration-200
      "
      style={positionStyle}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
          <span className="text-sm text-gray-700">{message}</span>
        </div>
        
        <button
          onClick={onCancel}
          className="p-1 hover:bg-gray-100 rounded transition-colors"
          title="取消"
        >
          <X className="w-4 h-4 text-gray-500" />
        </button>
      </div>
    </div>
  );
}