'use client';

/**
 * AI 配置管理页面
 * 提供 AI 配置的完整管理界面
 */

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AIConfigList } from '@/components/ai/AIConfigList';
import { AIConfigForm } from '@/components/ai/AIConfigForm';
import { EnvConfigStatus } from '@/components/ai/EnvConfigStatus';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, AlertCircle, Settings, Shield, Info } from 'lucide-react';

import { useAIConfig } from '@/hooks/useAIConfig';
import type { AIConfigData, FullAIConfigData } from '@/hooks/useAIConfig';

type ViewMode = 'list' | 'add' | 'edit';

export default function AIConfigPage() {
  const router = useRouter();
  const {
    configs,
    loading,
    error,
    createConfig,
    updateConfig,
    deleteConfig,
    setDefaultConfig,
    testConnection,
    getConfig,
    clearError
  } = useAIConfig();

  const [activeTab, setActiveTab] = useState('configs');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [editingConfig, setEditingConfig] = useState<FullAIConfigData | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  /**
   * 处理添加配置
   */
  const handleAdd = () => {
    setViewMode('add');
    setEditingConfig(null);
    setFormError(null);
    clearError();
  };

  /**
   * 处理编辑配置
   */
  const handleEdit = async (config: AIConfigData) => {
    try {
      const fullConfig = await getConfig(config.id);
      if (fullConfig) {
        setEditingConfig(fullConfig);
        setViewMode('edit');
        setFormError(null);
        clearError();
      } else {
        setFormError('获取配置详情失败');
      }
    } catch (err) {
      setFormError('获取配置详情失败');
    }
  };

  /**
   * 处理表单提交
   */
  const handleFormSubmit = async (data: Omit<FullAIConfigData, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> => {
    try {
      let success = false;

      if (viewMode === 'add') {
        success = await createConfig(data);
      } else if (viewMode === 'edit' && editingConfig) {
        success = await updateConfig(editingConfig.id, data);
      }

      if (success) {
        setViewMode('list');
        setEditingConfig(null);
        setFormError(null);
        return true;
      }

      return false;
    } catch (err) {
      setFormError(err instanceof Error ? err.message : '操作失败');
      return false;
    }
  };

  /**
   * 处理取消操作
   */
  const handleCancel = () => {
    setViewMode('list');
    setEditingConfig(null);
    setFormError(null);
    clearError();
  };

  /**
   * 处理连接测试（从列表页面）
   */
  const handleTestFromList = async (config: AIConfigData) => {
    try {
      const fullConfig = await getConfig(config.id);
      if (fullConfig) {
        const result = await testConnection({
          provider: fullConfig.provider as any,
          apiKey: fullConfig.apiKey,
          endpoint: fullConfig.endpoint,
          model: fullConfig.model,
          maxTokens: fullConfig.maxTokens,
          temperature: fullConfig.temperature
        });

        // 显示测试结果
        if (result.success) {
          alert(`连接测试成功！\n响应时间: ${result.responseTime}ms`);
        } else {
          alert(`连接测试失败：\n${result.error}`);
        }
      }
    } catch (err) {
      alert('连接测试失败：获取配置详情失败');
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* 页面头部 */}
      <div className="mb-6">
        {viewMode !== 'list' ? (
          <Button
            variant="ghost"
            onClick={handleCancel}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回列表
          </Button>
        ) : (
          <div className="mb-4">
            <h1 className="text-3xl font-bold text-gray-900">AI 配置管理</h1>
            <p className="text-gray-600 mt-1">
              管理您的 AI 服务配置、代理设置和连接测试
            </p>
          </div>
        )}
      </div>

      {/* 错误提示 */}
      {(error || formError) && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || formError}
          </AlertDescription>
        </Alert>
      )}

      {/* 主要内容 */}
      {viewMode === 'list' ? (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="configs" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              AI 配置
            </TabsTrigger>
            <TabsTrigger value="proxy" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              代理设置
            </TabsTrigger>
            <TabsTrigger value="help" className="flex items-center gap-2">
              <Info className="h-4 w-4" />
              使用说明
            </TabsTrigger>
          </TabsList>

          <TabsContent value="configs">
            <div className="space-y-6">
              {/* 环境变量状态 */}
              <EnvConfigStatus />

              {/* AI 配置列表 */}
              <AIConfigList
                configs={configs}
                loading={loading}
                error={error}
                onAdd={handleAdd}
                onEdit={handleEdit}
                onDelete={deleteConfig}
                onSetDefault={setDefaultConfig}
                onTestConnection={handleTestFromList}
              />
            </div>
          </TabsContent>

          <TabsContent value="proxy">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  代理配置说明
                </CardTitle>
                <CardDescription>
                  了解如何配置 HTTP 代理以访问 OpenAI API
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">环境变量配置（推荐）</h4>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600 mb-2">在项目根目录的 <code className="bg-gray-200 px-1 rounded">.env</code> 文件中配置：</p>
                      <pre className="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">
{`# HTTP 代理配置
HTTP_PROXY="http://127.0.0.1:57800"
HTTPS_PROXY="http://127.0.0.1:57800"

# API 密钥（可选）
OPENAI_API_KEY="your-api-key"
GEMINI_API_KEY="your-api-key"`}
                      </pre>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">页面配置</h4>
                    <p className="text-sm text-gray-600 mb-2">
                      在添加 OpenAI 配置时，可以在"HTTP 代理地址"字段中输入代理地址，这会覆盖环境变量配置。
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">常用代理软件配置</h4>
                    <div className="space-y-2 text-sm">
                      <div className="p-3 bg-blue-50 rounded">
                        <strong>Clash:</strong> HTTP: 127.0.0.1:7890, SOCKS5: 127.0.0.1:7891
                      </div>
                      <div className="p-3 bg-green-50 rounded">
                        <strong>V2Ray:</strong> HTTP: 127.0.0.1:10809, SOCKS5: 127.0.0.1:10808
                      </div>
                      <div className="p-3 bg-purple-50 rounded">
                        <strong>Shadowsocks:</strong> SOCKS5: 127.0.0.1:1080
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">配置优先级</h4>
                    <div className="p-4 bg-yellow-50 rounded-lg">
                      <p className="text-sm text-yellow-800">
                        <strong>页面配置</strong> &gt; <strong>环境变量</strong> &gt; <strong>默认值</strong>
                      </p>
                      <p className="text-xs text-yellow-700 mt-1">
                        在 AI 配置页面中设置的代理地址会覆盖环境变量中的配置
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="help">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  使用说明
                </CardTitle>
                <CardDescription>
                  AI 配置管理的详细使用指南
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">快速开始</h4>
                    <ol className="text-sm text-gray-600 space-y-2 list-decimal list-inside">
                      <li>点击"添加配置"按钮</li>
                      <li>选择服务提供商（OpenAI、Ollama、Gemini）</li>
                      <li>输入 API 密钥（如果需要）</li>
                      <li>配置代理地址（如果需要）</li>
                      <li>选择模型并调整参数</li>
                      <li>点击"测试连接"验证配置</li>
                      <li>保存配置</li>
                    </ol>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">支持的服务</h4>
                    <div className="space-y-2 text-sm">
                      <div className="p-3 bg-green-50 rounded">
                        <strong>OpenAI:</strong> GPT-3.5, GPT-4 等模型
                        <br />
                        <span className="text-gray-600">需要 API 密钥，支持代理</span>
                      </div>
                      <div className="p-3 bg-blue-50 rounded">
                        <strong>Ollama:</strong> 本地部署的开源模型
                        <br />
                        <span className="text-gray-600">需要配置服务端点</span>
                      </div>
                      <div className="p-3 bg-purple-50 rounded">
                        <strong>Gemini:</strong> Google 的 AI 模型
                        <br />
                        <span className="text-gray-600">需要 API 密钥</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">常见问题</h4>
                  <div className="space-y-3">
                    <details className="p-3 bg-gray-50 rounded">
                      <summary className="font-medium cursor-pointer">连接测试失败怎么办？</summary>
                      <div className="mt-2 text-sm text-gray-600">
                        <ul className="list-disc list-inside space-y-1">
                          <li>检查 API 密钥是否正确</li>
                          <li>确认代理服务器是否正常运行</li>
                          <li>验证网络连接是否正常</li>
                          <li>查看浏览器控制台的错误信息</li>
                        </ul>
                      </div>
                    </details>

                    <details className="p-3 bg-gray-50 rounded">
                      <summary className="font-medium cursor-pointer">如何设置默认配置？</summary>
                      <div className="mt-2 text-sm text-gray-600">
                        在配置列表中点击星形图标，或在编辑配置时勾选"设为默认配置"选项。
                      </div>
                    </details>

                    <details className="p-3 bg-gray-50 rounded">
                      <summary className="font-medium cursor-pointer">代理配置不生效？</summary>
                      <div className="mt-2 text-sm text-gray-600">
                        <ul className="list-disc list-inside space-y-1">
                          <li>确认 .env 文件在项目根目录</li>
                          <li>重启开发服务器</li>
                          <li>检查代理地址格式是否正确</li>
                          <li>确认代理服务器支持 HTTPS 请求</li>
                        </ul>
                      </div>
                    </details>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      ) : (
        <div className="flex justify-center">
          <AIConfigForm
            initialData={editingConfig || undefined}
            onSubmit={handleFormSubmit}
            onTestConnection={testConnection}
            onCancel={handleCancel}
            isEditing={viewMode === 'edit'}
          />
        </div>
      )}
    </div>
  );
}