"use client";

import { useAuth } from "@/hooks/useAuth";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import Link from "next/link";

export default function AuthTestPage() {
  const { user, isAuthenticated, isLoading, logout } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>正在验证身份...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold mb-6">认证系统测试</h1>
          
          <div className="space-y-4">
            <div>
              <strong>认证状态:</strong> {isAuthenticated ? "已登录" : "未登录"}
            </div>
            
            {isAuthenticated && user && (
              <div className="space-y-2">
                <div><strong>用户ID:</strong> {user.id}</div>
                <div><strong>姓名:</strong> {user.name || "未设置"}</div>
                <div><strong>邮箱:</strong> {user.email}</div>
              </div>
            )}
            
            <div className="flex space-x-4 pt-4">
              {!isAuthenticated ? (
                <>
                  <Link
                    href="/auth/signin"
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                  >
                    登录
                  </Link>
                  <Link
                    href="/auth/signup"
                    className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                  >
                    注册
                  </Link>
                </>
              ) : (
                <>
                  <Link
                    href="/dashboard"
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                  >
                    仪表板
                  </Link>
                  <button
                    onClick={logout}
                    className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                  >
                    退出登录
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
        
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">受保护的内容测试</h2>
          <ProtectedRoute>
            <div className="bg-green-50 border border-green-200 p-4 rounded">
              <p className="text-green-800">
                🎉 恭喜！您可以看到这个受保护的内容，说明认证系统工作正常。
              </p>
            </div>
          </ProtectedRoute>
        </div>
      </div>
    </div>
  );
}