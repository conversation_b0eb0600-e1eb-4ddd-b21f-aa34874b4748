'use client';

import { 
  FileText, 
  Save, 
  FolderOpen, 
  Download,
  Settings,
  User
} from 'lucide-react';
import { Button } from '@/components/ui/Button';

interface TopMenuBarProps {
  onNew?: () => void;
  onSave?: () => void;
  onOpen?: () => void;
  onExport?: () => void;
  onSettings?: () => void;
  onProfile?: () => void;
  documentTitle?: string;
  isSaving?: boolean;
}

export function TopMenuBar({
  onNew,
  onSave,
  onOpen,
  onExport,
  onSettings,
  onProfile,
  documentTitle = '未命名文档',
  isSaving = false,
}: TopMenuBarProps) {
  return (
    <div className="fixed top-0 left-0 right-0 z-50 h-12 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
      <div className="flex items-center justify-between h-full px-2 sm:px-4">
        {/* Left section - File operations */}
        <div className="flex items-center gap-1 sm:gap-2">
          {/* Desktop buttons */}
          <div className="hidden sm:flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onNew}
              title="新建文档 (Ctrl+N)"
              className="h-8 px-3"
            >
              <FileText className="h-4 w-4 mr-1" />
              新建
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onSave}
              disabled={isSaving}
              title="保存文档 (Ctrl+S)"
              className="h-8 px-3"
            >
              <Save className="h-4 w-4 mr-1" />
              {isSaving ? '保存中...' : '保存'}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onOpen}
              title="打开文档 (Ctrl+O)"
              className="h-8 px-3"
            >
              <FolderOpen className="h-4 w-4 mr-1" />
              打开
            </Button>
            
            <div className="h-6 w-px bg-border mx-2" />
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onExport}
              title="导出文档"
              className="h-8 px-3"
            >
              <Download className="h-4 w-4 mr-1" />
              导出
            </Button>
          </div>

          {/* Mobile buttons - icon only */}
          <div className="flex sm:hidden items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={onNew}
              title="新建文档"
              className="h-8 w-8 p-0"
            >
              <FileText className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onSave}
              disabled={isSaving}
              title="保存文档"
              className="h-8 w-8 p-0"
            >
              <Save className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onOpen}
              title="打开文档"
              className="h-8 w-8 p-0"
            >
              <FolderOpen className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Center section - Document title */}
        <div className="flex-1 flex justify-center px-2">
          <div className="max-w-[200px] sm:max-w-md truncate text-sm font-medium text-foreground">
            {documentTitle}
          </div>
        </div>

        {/* Right section - User actions */}
        <div className="flex items-center gap-1 sm:gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onSettings}
            title="设置"
            className="h-8 w-8 p-0"
          >
            <Settings className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onProfile}
            title="用户资料"
            className="h-8 w-8 p-0"
          >
            <User className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}