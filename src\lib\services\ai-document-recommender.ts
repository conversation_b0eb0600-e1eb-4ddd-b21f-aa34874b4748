/**
 * AI 文档推荐服务
 * 提供基于内容相似性的相关文档推荐功能
 */

import { prisma } from '@/lib/db/prisma';
import { aiServiceManager } from './ai/ai-service-factory';
import { aiDocumentClassifier } from './ai-document-classifier';
import {
  RelatedDocumentRecommendation,
  DocumentContentAnalysis
} from '@/types/ai-classification.types';

/**
 * AI 文档推荐服务类
 */
export class AIDocumentRecommenderService {
  
  /**
   * 获取相关文档推荐
   */
  async getRelatedDocuments(
    documentId: string,
    userId: string,
    limit: number = 10
  ): Promise<RelatedDocumentRecommendation[]> {
    // 获取目标文档
    const targetDocument = await prisma.document.findFirst({
      where: { id: documentId, userId },
      select: { id: true, title: true, content: true }
    });

    if (!targetDocument) {
      throw new Error('文档不存在或无权限访问');
    }

    // 获取用户的其他文档
    const otherDocuments = await prisma.document.findMany({
      where: { 
        userId,
        id: { not: documentId }
      },
      select: { id: true, title: true, content: true, createdAt: true },
      orderBy: { updatedAt: 'desc' },
      take: 50 // 限制候选文档数量以提高性能
    });

    if (otherDocuments.length === 0) {
      return [];
    }

    // 分析目标文档
    const targetAnalysis = await aiDocumentClassifier.analyzeDocument(documentId, userId);
    
    // 计算相关性
    const recommendations = await this.calculateDocumentRelevance(
      targetDocument,
      targetAnalysis,
      otherDocuments,
      userId
    );

    // 排序并限制结果数量
    return recommendations
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, limit);
  }

  /**
   * 基于主题获取相关文档
   */
  async getDocumentsByTopic(
    topic: string,
    userId: string,
    excludeDocumentId?: string,
    limit: number = 10
  ): Promise<RelatedDocumentRecommendation[]> {
    const documents = await prisma.document.findMany({
      where: { 
        userId,
        id: excludeDocumentId ? { not: excludeDocumentId } : undefined
      },
      select: { id: true, title: true, content: true }
    });

    const recommendations: RelatedDocumentRecommendation[] = [];

    for (const doc of documents) {
      const relevance = await this.calculateTopicRelevance(doc, topic);
      
      if (relevance > 0.3) { // 相关性阈值
        recommendations.push({
          targetDocumentId: excludeDocumentId || '',
          relatedDocumentId: doc.id,
          relatedDocumentTitle: doc.title,
          relevanceScore: relevance,
          relationType: 'same_topic',
          description: `与主题"${topic}"相关`,
          reason: `文档内容与主题"${topic}"的相关性为 ${Math.round(relevance * 100)}%`
        });
      }
    }

    return recommendations
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, limit);
  }

  /**
   * 获取最近相关的文档
   */
  async getRecentlyRelatedDocuments(
    userId: string,
    days: number = 7,
    limit: number = 10
  ): Promise<RelatedDocumentRecommendation[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const recentDocuments = await prisma.document.findMany({
      where: {
        userId,
        updatedAt: { gte: cutoffDate }
      },
      select: { id: true, title: true, content: true, updatedAt: true },
      orderBy: { updatedAt: 'desc' },
      take: 20
    });

    if (recentDocuments.length < 2) {
      return [];
    }

    const recommendations: RelatedDocumentRecommendation[] = [];

    // 分析最近文档之间的关联性
    for (let i = 0; i < recentDocuments.length - 1; i++) {
      for (let j = i + 1; j < recentDocuments.length; j++) {
        const doc1 = recentDocuments[i];
        const doc2 = recentDocuments[j];
        
        const relevance = await this.calculateContentSimilarity(doc1, doc2);
        
        if (relevance > 0.4) {
          recommendations.push({
            targetDocumentId: doc1.id,
            relatedDocumentId: doc2.id,
            relatedDocumentTitle: doc2.title,
            relevanceScore: relevance,
            relationType: 'similar_content',
            description: '最近编辑的相关文档',
            reason: `两个文档在最近${days}天内都有更新，且内容相关性较高`
          });
        }
      }
    }

    return recommendations
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, limit);
  }

  /**
   * 智能推荐：基于用户行为和内容分析
   */
  async getSmartRecommendations(
    userId: string,
    contextDocumentId?: string,
    limit: number = 10
  ): Promise<RelatedDocumentRecommendation[]> {
    const allRecommendations: RelatedDocumentRecommendation[] = [];

    // 如果有上下文文档，获取相关推荐
    if (contextDocumentId) {
      const contextRelated = await this.getRelatedDocuments(contextDocumentId, userId, 5);
      allRecommendations.push(...contextRelated);
    }

    // 获取最近相关文档
    const recentRelated = await this.getRecentlyRelatedDocuments(userId, 7, 5);
    allRecommendations.push(...recentRelated);

    // 基于AI交互历史推荐
    const aiBasedRecommendations = await this.getAIBasedRecommendations(userId, 5);
    allRecommendations.push(...aiBasedRecommendations);

    // 去重并排序
    const uniqueRecommendations = this.deduplicateRecommendations(allRecommendations);
    
    return uniqueRecommendations
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, limit);
  }

  /**
   * 计算文档相关性
   */
  private async calculateDocumentRelevance(
    targetDocument: any,
    targetAnalysis: DocumentContentAnalysis,
    candidateDocuments: any[],
    userId: string
  ): Promise<RelatedDocumentRecommendation[]> {
    const recommendations: RelatedDocumentRecommendation[] = [];

    for (const candidate of candidateDocuments) {
      try {
        // 分析候选文档
        const candidateAnalysis = await aiDocumentClassifier.analyzeDocument(candidate.id, userId);
        
        // 计算多维度相关性
        const topicSimilarity = this.calculateTopicSimilarity(
          targetAnalysis.mainTopics,
          candidateAnalysis.mainTopics
        );
        
        const conceptSimilarity = this.calculateConceptSimilarity(
          targetAnalysis.keyConcepts,
          candidateAnalysis.keyConcepts
        );
        
        const contentSimilarity = await this.calculateSemanticSimilarity(
          targetDocument.content,
          candidate.content
        );

        // 综合相关性评分
        const relevanceScore = (topicSimilarity * 0.4 + conceptSimilarity * 0.3 + contentSimilarity * 0.3);
        
        if (relevanceScore > 0.3) { // 相关性阈值
          recommendations.push({
            targetDocumentId: targetDocument.id,
            relatedDocumentId: candidate.id,
            relatedDocumentTitle: candidate.title,
            relevanceScore,
            relationType: this.determineRelationType(topicSimilarity, conceptSimilarity, contentSimilarity),
            description: this.generateRelevanceDescription(targetAnalysis, candidateAnalysis),
            reason: `基于内容分析的相关性评分: ${Math.round(relevanceScore * 100)}%`
          });
        }
      } catch (error) {
        console.error(`分析文档 ${candidate.id} 失败:`, error);
      }
    }

    return recommendations;
  }

  /**
   * 计算主题相似性
   */
  private calculateTopicSimilarity(topics1: string[], topics2: string[]): number {
    if (topics1.length === 0 || topics2.length === 0) return 0;
    
    const intersection = topics1.filter(topic => topics2.includes(topic));
    const union = [...new Set([...topics1, ...topics2])];
    
    return intersection.length / union.length;
  }

  /**
   * 计算概念相似性
   */
  private calculateConceptSimilarity(concepts1: string[], concepts2: string[]): number {
    if (concepts1.length === 0 || concepts2.length === 0) return 0;
    
    const intersection = concepts1.filter(concept => concepts2.includes(concept));
    const union = [...new Set([...concepts1, ...concepts2])];
    
    return intersection.length / union.length;
  }

  /**
   * 计算语义相似性（使用AI）
   */
  private async calculateSemanticSimilarity(content1: string, content2: string): Promise<number> {
    try {
      const text1 = this.extractTextFromContent(content1).substring(0, 500);
      const text2 = this.extractTextFromContent(content2).substring(0, 500);
      
      if (text1.length < 10 || text2.length < 10) return 0;

      const prompt = `
请分析以下两段文本的语义相似性，返回0-1之间的相似度分数：

文本1: ${text1}

文本2: ${text2}

请只返回数字分数，例如：0.75
`;

      const aiService = aiServiceManager.getDefaultService();
      const response = await aiService.generateText({
        prompt,
        maxTokens: 10
      });

      const score = parseFloat(response.content.trim());
      return isNaN(score) ? 0 : Math.max(0, Math.min(1, score));
    } catch (error) {
      console.error('计算语义相似性失败:', error);
      return 0;
    }
  }

  /**
   * 计算主题相关性
   */
  private async calculateTopicRelevance(document: any, topic: string): Promise<number> {
    try {
      const text = this.extractTextFromContent(document.content).substring(0, 1000);
      
      const prompt = `
请分析以下文档内容与主题"${topic}"的相关性，返回0-1之间的相关性分数：

文档标题: ${document.title}
文档内容: ${text}

请只返回数字分数，例如：0.85
`;

      const aiService = aiServiceManager.getDefaultService();
      const response = await aiService.generateText({
        prompt,
        maxTokens: 10
      });

      const score = parseFloat(response.content.trim());
      return isNaN(score) ? 0 : Math.max(0, Math.min(1, score));
    } catch (error) {
      console.error('计算主题相关性失败:', error);
      return 0;
    }
  }

  /**
   * 计算内容相似性
   */
  private async calculateContentSimilarity(doc1: any, doc2: any): Promise<number> {
    const text1 = this.extractTextFromContent(doc1.content);
    const text2 = this.extractTextFromContent(doc2.content);
    
    // 简单的词汇重叠计算
    const words1 = text1.toLowerCase().split(/\s+/).filter(w => w.length > 2);
    const words2 = text2.toLowerCase().split(/\s+/).filter(w => w.length > 2);
    
    if (words1.length === 0 || words2.length === 0) return 0;
    
    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];
    
    return intersection.length / union.length;
  }

  /**
   * 基于AI交互历史的推荐
   */
  private async getAIBasedRecommendations(
    userId: string,
    limit: number
  ): Promise<RelatedDocumentRecommendation[]> {
    // 获取用户的AI交互历史
    const interactions = await prisma.aIInteraction.findMany({
      where: { userId },
      select: { documentId: true, type: true, input: true, output: true },
      orderBy: { createdAt: 'desc' },
      take: 20
    });

    const recommendations: RelatedDocumentRecommendation[] = [];
    
    // 基于AI交互类型和内容推荐相关文档
    // 这里可以实现更复杂的推荐逻辑
    
    return recommendations.slice(0, limit);
  }

  /**
   * 确定关联类型
   */
  private determineRelationType(
    topicSimilarity: number,
    conceptSimilarity: number,
    contentSimilarity: number
  ): 'similar_content' | 'same_topic' | 'complementary' | 'reference' {
    if (contentSimilarity > 0.7) return 'similar_content';
    if (topicSimilarity > 0.6) return 'same_topic';
    if (conceptSimilarity > 0.5) return 'complementary';
    return 'reference';
  }

  /**
   * 生成相关性描述
   */
  private generateRelevanceDescription(
    analysis1: DocumentContentAnalysis,
    analysis2: DocumentContentAnalysis
  ): string {
    const commonTopics = analysis1.mainTopics.filter(topic => 
      analysis2.mainTopics.includes(topic)
    );
    
    const commonConcepts = analysis1.keyConcepts.filter(concept => 
      analysis2.keyConcepts.includes(concept)
    );

    if (commonTopics.length > 0) {
      return `共同主题: ${commonTopics.join(', ')}`;
    }
    
    if (commonConcepts.length > 0) {
      return `相关概念: ${commonConcepts.join(', ')}`;
    }
    
    return '内容相关性较高';
  }

  /**
   * 去重推荐结果
   */
  private deduplicateRecommendations(
    recommendations: RelatedDocumentRecommendation[]
  ): RelatedDocumentRecommendation[] {
    const seen = new Set<string>();
    return recommendations.filter(rec => {
      const key = `${rec.targetDocumentId}-${rec.relatedDocumentId}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  /**
   * 从文档内容中提取纯文本
   */
  private extractTextFromContent(content: string): string {
    try {
      const parsed = JSON.parse(content);
      return this.extractTextFromTipTapContent(parsed);
    } catch {
      return content;
    }
  }

  /**
   * 从TipTap内容中提取文本
   */
  private extractTextFromTipTapContent(content: any): string {
    if (!content || !content.content) return '';
    
    let text = '';
    
    const extractFromNode = (node: any): void => {
      if (node.text) {
        text += node.text + ' ';
      }
      
      if (node.content && Array.isArray(node.content)) {
        node.content.forEach(extractFromNode);
      }
    };
    
    content.content.forEach(extractFromNode);
    return text.trim();
  }
}

// 导出单例实例
export const aiDocumentRecommender = new AIDocumentRecommenderService();