import NextAuth from "next-auth";
import { authOptions } from "@/lib/auth/config";

/**
 * NextAuth.js API 路由处理器
 * 处理所有认证相关的 API 请求，包括：
 * - /api/auth/signin - 登录
 * - /api/auth/signout - 退出
 * - /api/auth/session - 获取会话
 * - /api/auth/providers - 获取认证提供商
 * - /api/auth/callback/* - OAuth 回调
 * - /api/auth/csrf - CSRF 令牌
 */
const handler = NextAuth(authOptions);

// 导出 GET 和 POST 方法处理器
// Next.js 13+ App Router 要求显式导出 HTTP 方法
export { handler as GET, handler as POST };