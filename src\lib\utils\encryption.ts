/**
 * 加密工具
 * 用于安全处理敏感配置信息
 */

import * as crypto from 'crypto';

// 加密算法
const ALGORITHM = 'aes-256-cbc';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits

/**
 * 生成加密密钥
 */
function getEncryptionKey(): Buffer {
  const secret = process.env.ENCRYPTION_SECRET || 'default-secret-key-change-in-production';
  return crypto.scryptSync(secret, 'salt', KEY_LENGTH);
}

/**
 * 加密文本
 * @param text 要加密的文本
 * @returns 加密后的数据（包含 IV）
 */
export function encrypt(text: string): string {
  if (!text) return '';
  
  try {
    const key = getEncryptionKey();
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // 组合 IV + 加密数据
    const result = iv.toString('hex') + encrypted;
    return result;
  } catch (error) {
    console.error('加密失败:', error);
    throw new Error('数据加密失败');
  }
}

/**
 * 解密文本
 * @param encryptedData 加密的数据
 * @returns 解密后的文本
 */
export function decrypt(encryptedData: string): string {
  if (!encryptedData) return '';
  
  try {
    const key = getEncryptionKey();
    
    // 提取 IV 和加密数据
    const iv = Buffer.from(encryptedData.slice(0, IV_LENGTH * 2), 'hex');
    const encrypted = encryptedData.slice(IV_LENGTH * 2);
    
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    console.error('解密失败:', error);
    throw new Error('数据解密失败');
  }
}

/**
 * 生成哈希值
 * @param data 要哈希的数据
 * @returns SHA-256 哈希值
 */
export function hash(data: string): string {
  return crypto.createHash('sha256').update(data).digest('hex');
}

/**
 * 验证哈希值
 * @param data 原始数据
 * @param hashValue 哈希值
 * @returns 是否匹配
 */
export function verifyHash(data: string, hashValue: string): boolean {
  return hash(data) === hashValue;
}

/**
 * 生成随机令牌
 * @param length 令牌长度（字节）
 * @returns 随机令牌
 */
export function generateToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * 安全比较两个字符串
 * @param a 字符串 A
 * @param b 字符串 B
 * @returns 是否相等
 */
export function safeCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }
  
  return crypto.timingSafeEqual(Buffer.from(a), Buffer.from(b));
}