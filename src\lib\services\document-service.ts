import { prisma } from '@/lib/db/prisma';
import { Document, DocumentHistory, Folder } from '@prisma/client';

/**
 * 文档及其关联数据的类型
 */
export type DocumentWithRelations = Document & {
  folder?: {
    id: string;
    name: string;
  } | null;
  history?: DocumentHistory[];
};

/**
 * 文档服务类
 * 提供文档相关的业务逻辑操作
 */
export class DocumentService {
  /**
   * 计算文本的字数和字符数
   * @param content 文档内容（可能包含HTML标签，或者是对象）
   * @returns 字数和字符数统计
   */
  static calculateTextStats(content: any): { wordCount: number; charCount: number } {
    // 确保 content 是字符串类型
    let textContent = '';
    if (typeof content === 'string') {
      textContent = content;
    } else if (content && typeof content === 'object') {
      // 如果是对象，尝试序列化为字符串
      textContent = JSON.stringify(content);
    } else {
      textContent = String(content || '');
    }

    const plainText = textContent.replace(/<[^>]*>/g, '').trim();
    const wordCount = plainText ? plainText.split(/\s+/).length : 0;
    const charCount = plainText.length;

    return { wordCount, charCount };
  }

  /**
   * 验证用户是否有权限访问文档
   * @param documentId 文档ID
   * @param userId 用户ID
   * @returns 文档信息（如果有权限）
   */
  static async validateDocumentAccess(
    documentId: string,
    userId: string
  ): Promise<Document | null> {
    return await prisma.document.findFirst({
      where: {
        id: documentId,
        userId: userId,
        isDeleted: false, // 只返回未删除的文档
      },
    });
  }

  /**
   * 验证用户是否有权限访问文件夹
   * @param folderId 文件夹ID
   * @param userId 用户ID
   * @returns 文件夹信息（如果有权限）
   */
  static async validateFolderAccess(
    folderId: string,
    userId: string
  ): Promise<Folder | null> {
    return await prisma.folder.findFirst({
      where: {
        id: folderId,
        userId: userId,
      },
    });
  }

  /**
   * 创建文档历史记录
   * @param documentId 文档ID
   * @param content 文档内容
   * @param changeType 变更类型
   * @returns 创建的历史记录
   */
  static async createDocumentHistory(
    documentId: string,
    content: string,
    changeType: 'user' | 'ai' = 'user'
  ): Promise<DocumentHistory> {
    // 获取当前最大版本号
    const lastHistory = await prisma.documentHistory.findFirst({
      where: { documentId },
      orderBy: { version: 'desc' },
    });

    const nextVersion = (lastHistory?.version || 0) + 1;

    return await prisma.documentHistory.create({
      data: {
        documentId,
        version: nextVersion,
        content,
        changeType,
      },
    });
  }

  /**
   * 获取文档的完整路径
   * @param documentId 文档ID
   * @returns 文档路径数组
   */
  static async getDocumentPath(documentId: string): Promise<string[]> {
    const document = await prisma.document.findUnique({
      where: { id: documentId },
      include: {
        folder: true,
      },
    });

    if (!document) {
      return [];
    }

    const path = [document.title];

    if (document.folder) {
      const folderPath = await this.getFolderPath(document.folder.id);
      path.unshift(...folderPath);
    }

    return path;
  }

  /**
   * 获取文件夹的完整路径
   * @param folderId 文件夹ID
   * @returns 文件夹路径数组
   */
  static async getFolderPath(folderId: string): Promise<string[]> {
    const folder = await prisma.folder.findUnique({
      where: { id: folderId },
      include: {
        parent: true,
      },
    });

    if (!folder) {
      return [];
    }

    const path = [folder.name];

    if (folder.parent) {
      const parentPath = await this.getFolderPath(folder.parent.id);
      path.unshift(...parentPath);
    }

    return path;
  }

  /**
   * 检查文档标题是否在指定文件夹中重复
   * @param title 文档标题
   * @param folderId 文件夹ID（可选）
   * @param userId 用户ID
   * @param excludeDocumentId 排除的文档ID（用于更新时检查）
   * @returns 是否重复
   */
  static async isDocumentTitleDuplicate(
    title: string,
    userId: string,
    folderId?: string | null,
    excludeDocumentId?: string
  ): Promise<boolean> {
    const whereClause: any = {
      title,
      userId,
      folderId: folderId || null,
      isDeleted: false, // 只检查未删除的文档
    };

    if (excludeDocumentId) {
      whereClause.id = { not: excludeDocumentId };
    }

    const existingDocument = await prisma.document.findFirst({
      where: whereClause,
    });

    return !!existingDocument;
  }

  /**
   * 获取用户的文档统计信息
   * @param userId 用户ID
   * @param folderId 文件夹ID（可选，用于获取特定文件夹的统计）
   * @returns 统计信息
   */
  static async getUserDocumentStats(userId: string, folderId?: string) {
    const whereClause: any = { userId };
    if (folderId) {
      whereClause.folderId = folderId;
    }

    const [
      totalDocuments,
      totalWords,
      totalCharacters,
      documentsThisWeek,
      documentsThisMonth,
    ] = await Promise.all([
      prisma.document.count({ where: whereClause }),
      prisma.document.aggregate({
        where: whereClause,
        _sum: { wordCount: true },
      }),
      prisma.document.aggregate({
        where: whereClause,
        _sum: { charCount: true },
      }),
      prisma.document.count({
        where: {
          ...whereClause,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
        },
      }),
      prisma.document.count({
        where: {
          ...whereClause,
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          },
        },
      }),
    ]);

    return {
      totalDocuments,
      totalWords: totalWords._sum.wordCount || 0,
      totalCharacters: totalCharacters._sum.charCount || 0,
      documentsThisWeek,
      documentsThisMonth,
      averageWordsPerDocument: totalDocuments > 0
        ? Math.round((totalWords._sum.wordCount || 0) / totalDocuments)
        : 0,
    };
  }

  /**
   * 搜索用户的文档
   * @param userId 用户ID
   * @param query 搜索关键词
   * @param options 搜索选项
   * @returns 搜索结果
   */
  static async searchDocuments(
    userId: string,
    query: string,
    options: {
      folderId?: string;
      limit?: number;
      includeContent?: boolean;
    } = {}
  ) {
    const { folderId, limit = 50, includeContent = false } = options;

    const whereClause: any = {
      userId,
      isDeleted: false, // 只搜索未删除的文档
      OR: [
        {
          title: {
            contains: query,
            mode: 'insensitive',
          },
        },
        ...(includeContent ? [{
          content: {
            contains: query,
            mode: 'insensitive',
          },
        }] : []),
      ],
    };

    if (folderId) {
      whereClause.folderId = folderId;
    }

    return await prisma.document.findMany({
      where: whereClause,
      include: {
        folder: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
      take: limit,
    });
  }

  /**
   * 清理旧的文档历史记录
   * @param documentId 文档ID
   * @param keepCount 保留的历史记录数量
   */
  static async cleanupDocumentHistory(documentId: string, keepCount: number = 50) {
    // 获取需要删除的历史记录
    const historyToDelete = await prisma.documentHistory.findMany({
      where: { documentId },
      orderBy: { createdAt: 'desc' },
      skip: keepCount,
      select: { id: true },
    });

    if (historyToDelete.length > 0) {
      await prisma.documentHistory.deleteMany({
        where: {
          id: {
            in: historyToDelete.map(h => h.id),
          },
        },
      });
    }
  }
}