import { documentService } from '@/lib/storage';
import { LocalDocument } from '@/lib/storage/database';
import { 
  SyncState, 
  SyncResult, 
  SyncConflict, 
  SyncOptions, 
  SyncProgress,
  SyncEvent,
  SyncEventListener,
  PendingChange
} from '@/types/sync';
import { ConflictDetectionService, DetailedConflict } from './conflict-detection';
import { VersionHistoryService } from './version-history';

/**
 * 文档同步服务
 * 负责客户端和服务器之间的数据同步
 */
export class SyncService {
  private syncState: SyncState = {
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    isSyncing: false,
    pendingChanges: [],
    conflicts: []
  };

  private eventListeners: Map<string, SyncEventListener[]> = new Map();
  private syncTimer?: NodeJS.Timeout;
  private readonly AUTO_SYNC_INTERVAL = 30000; // 30秒自动同步
  private readonly MAX_RETRY_COUNT = 3;
  private readonly BATCH_SIZE = 10;

  constructor() {
    this.initializeNetworkListeners();
    this.startAutoSync();
  }

  /**
   * 初始化网络状态监听
   */
  private initializeNetworkListeners(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        this.syncState.isOnline = true;
        this.emitEvent('connection_changed', { isOnline: true });
        this.triggerSync();
      });

      window.addEventListener('offline', () => {
        this.syncState.isOnline = false;
        this.emitEvent('connection_changed', { isOnline: false });
      });
    }
  }

  /**
   * 启动自动同步
   */
  private startAutoSync(): void {
    this.syncTimer = setInterval(() => {
      if (this.syncState.isOnline && !this.syncState.isSyncing) {
        this.triggerSync();
      }
    }, this.AUTO_SYNC_INTERVAL);
  }

  /**
   * 停止自动同步
   */
  public stopAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = undefined;
    }
  }

  /**
   * 获取当前同步状态
   */
  public getSyncState(): SyncState {
    return { ...this.syncState };
  }

  /**
   * 手动触发同步
   */
  public async manualSync(options: SyncOptions = {}): Promise<SyncResult[]> {
    return this.performSync(options, true);
  }

  /**
   * 自动同步触发
   */
  private async triggerSync(): Promise<void> {
    if (this.syncState.isSyncing || !this.syncState.isOnline) {
      return;
    }

    try {
      await this.performSync();
    } catch (error) {
      console.error('Auto sync failed:', error);
    }
  }

  /**
   * 执行同步操作
   */
  private async performSync(options: SyncOptions = {}, isManual = false): Promise<SyncResult[]> {
    if (this.syncState.isSyncing) {
      throw new Error('同步正在进行中');
    }

    this.syncState.isSyncing = true;
    const startTime = Date.now();
    const results: SyncResult[] = [];

    try {
      this.emitEvent('sync_started', { isManual, options });

      // 获取需要同步的文档
      const userId = await this.getCurrentUserId();
      if (!userId) {
        throw new Error('用户未登录');
      }

      const dirtyDocuments = await documentService.getDirtyDocuments(userId);
      
      if (dirtyDocuments.length === 0 && !isManual) {
        return results;
      }

      // 设置同步进度
      this.syncState.syncProgress = {
        total: dirtyDocuments.length,
        completed: 0,
        phase: 'preparing'
      };

      // 批量同步文档
      const batches = this.createBatches(dirtyDocuments, options.batchSize || this.BATCH_SIZE);
      
      for (const batch of batches) {
        this.syncState.syncProgress!.phase = 'uploading';
        const batchResults = await this.syncDocumentBatch(batch, options);
        results.push(...batchResults);
        
        this.syncState.syncProgress!.completed += batch.length;
        this.emitEvent('sync_progress', { 
          progress: this.syncState.syncProgress,
          results: batchResults 
        });
      }

      // 下载远程更新
      this.syncState.syncProgress!.phase = 'downloading';
      const downloadResults = await this.downloadRemoteUpdates(userId, options);
      results.push(...downloadResults);

      // 处理冲突
      if (this.syncState.conflicts.length > 0) {
        this.syncState.syncProgress!.phase = 'resolving';
        await this.handleConflicts(options);
      }

      this.syncState.syncProgress!.phase = 'completed';
      this.syncState.lastSyncAt = new Date();

      this.emitEvent('sync_completed', { 
        results, 
        duration: Date.now() - startTime,
        conflicts: this.syncState.conflicts.length 
      });

      return results;

    } catch (error) {
      this.emitEvent('sync_error', { error, results });
      throw error;
    } finally {
      this.syncState.isSyncing = false;
      this.syncState.syncProgress = undefined;
    }
  }

  /**
   * 同步文档批次
   */
  private async syncDocumentBatch(documents: LocalDocument[], options: SyncOptions): Promise<SyncResult[]> {
    const results: SyncResult[] = [];

    for (const document of documents) {
      try {
        const result = await this.syncSingleDocument(document, options);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          documentId: document.id,
          status: 'error',
          message: error instanceof Error ? error.message : '同步失败'
        });
      }
    }

    return results;
  }

  /**
   * 同步单个文档
   */
  private async syncSingleDocument(document: LocalDocument, options: SyncOptions): Promise<SyncResult> {
    try {
      // 检查远程版本
      const remoteDocument = await this.fetchRemoteDocument(document.id);
      
      if (remoteDocument) {
        // 检查冲突
        const conflict = this.detectConflict(document, remoteDocument);
        if (conflict) {
          this.syncState.conflicts.push(conflict);
          this.emitEvent('conflict_detected', { conflict });
          
          return {
            success: false,
            documentId: document.id,
            status: 'conflict',
            conflict
          };
        }
      }

      // 上传文档到服务器
      const uploadResult = await this.uploadDocument(document);
      
      if (uploadResult.success) {
        // 创建版本历史记录
        await VersionHistoryService.createVersion(
          document.id,
          document.content,
          document.title,
          'sync',
          '同步到服务器'
        );

        // 标记为已同步
        await documentService.markDocumentSynced(document.id);
        
        return {
          success: true,
          documentId: document.id,
          status: 'success',
          lastSyncAt: new Date()
        };
      } else {
        throw new Error(uploadResult.message || '上传失败');
      }

    } catch (error) {
      return {
        success: false,
        documentId: document.id,
        status: 'error',
        message: error instanceof Error ? error.message : '同步失败'
      };
    }
  }

  /**
   * 检测同步冲突（使用高级冲突检测）
   */
  private detectConflict(localDoc: LocalDocument, remoteDoc: any): DetailedConflict | null {
    return ConflictDetectionService.detectConflict(localDoc, remoteDoc);
  }

  /**
   * 下载远程更新
   */
  private async downloadRemoteUpdates(userId: string, options: SyncOptions): Promise<SyncResult[]> {
    try {
      const response = await fetch('/api/documents/sync', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取远程更新失败');
      }

      const remoteDocuments = await response.json();
      const results: SyncResult[] = [];

      for (const remoteDoc of remoteDocuments) {
        try {
          const localDoc = await documentService.getDocument(remoteDoc.id);
          
          if (!localDoc) {
            // 新文档，直接下载
            await this.downloadDocument(remoteDoc);
            results.push({
              success: true,
              documentId: remoteDoc.id,
              status: 'success'
            });
          } else if (new Date(remoteDoc.updatedAt) > localDoc.updatedAt) {
            // 远程版本更新，检查冲突
            const conflict = this.detectConflict(localDoc, remoteDoc);
            if (conflict) {
              this.syncState.conflicts.push(conflict);
              results.push({
                success: false,
                documentId: remoteDoc.id,
                status: 'conflict',
                conflict
              });
            } else {
              // 无冲突，更新本地版本
              await this.updateLocalDocument(remoteDoc);
              results.push({
                success: true,
                documentId: remoteDoc.id,
                status: 'success'
              });
            }
          }
        } catch (error) {
          results.push({
            success: false,
            documentId: remoteDoc.id,
            status: 'error',
            message: error instanceof Error ? error.message : '下载失败'
          });
        }
      }

      return results;
    } catch (error) {
      throw new Error(`下载远程更新失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 处理同步冲突
   */
  private async handleConflicts(options: SyncOptions): Promise<void> {
    const { conflictResolution = 'manual' } = options;

    for (const conflict of this.syncState.conflicts) {
      if (conflict.resolved) continue;

      try {
        switch (conflictResolution) {
          case 'local':
            await this.resolveConflictWithLocal(conflict);
            break;
          case 'remote':
            await this.resolveConflictWithRemote(conflict);
            break;
          case 'manual':
            // 手动解决冲突，等待用户操作
            break;
        }
      } catch (error) {
        console.error('解决冲突失败:', error);
      }
    }
  }

  /**
   * 使用本地版本解决冲突
   */
  private async resolveConflictWithLocal(conflict: DetailedConflict): Promise<void> {
    const localDoc = conflict.localVersion as LocalDocument;
    
    // 创建冲突解决的版本记录
    await VersionHistoryService.createVersion(
      conflict.documentId,
      localDoc.content,
      localDoc.title,
      'merge',
      '冲突解决：使用本地版本',
      'local'
    );

    await this.uploadDocument(localDoc, true); // 强制上传
    conflict.resolved = true;
  }

  /**
   * 使用远程版本解决冲突
   */
  private async resolveConflictWithRemote(conflict: DetailedConflict): Promise<void> {
    const remoteDoc = conflict.remoteVersion;
    
    // 创建冲突解决的版本记录
    await VersionHistoryService.createVersion(
      conflict.documentId,
      remoteDoc.content,
      remoteDoc.title,
      'merge',
      '冲突解决：使用远程版本',
      'remote'
    );

    await this.updateLocalDocument(remoteDoc);
    conflict.resolved = true;
  }

  /**
   * 手动解决冲突
   */
  public async resolveConflictManually(
    conflictId: string, 
    resolution: 'local' | 'remote' | 'merge',
    mergedData?: any
  ): Promise<void> {
    const conflict = this.syncState.conflicts.find(c => c.id === conflictId) as DetailedConflict;
    if (!conflict) {
      throw new Error('冲突不存在');
    }

    try {
      switch (resolution) {
        case 'local':
          await this.resolveConflictWithLocal(conflict);
          break;
        case 'remote':
          await this.resolveConflictWithRemote(conflict);
          break;
        case 'merge':
          if (mergedData) {
            // 创建合并版本的历史记录
            await VersionHistoryService.createVersion(
              conflict.documentId,
              mergedData.content,
              mergedData.title,
              'merge',
              '冲突解决：手动合并',
              'merge'
            );

            await this.updateLocalDocument(mergedData);
            await this.uploadDocument(mergedData, true);
          } else if (conflict.autoMergeable) {
            // 自动合并
            const autoMerged = ConflictDetectionService.autoMergeConflicts(
              conflict.localVersion as LocalDocument,
              conflict.remoteVersion,
              conflict
            );
            
            if (autoMerged) {
              await VersionHistoryService.createVersion(
                conflict.documentId,
                autoMerged.content,
                autoMerged.title,
                'merge',
                '冲突解决：自动合并',
                'merge'
              );

              await this.updateLocalDocument(autoMerged);
              await this.uploadDocument(autoMerged, true);
            }
          }
          break;
      }

      conflict.resolved = true;
      this.syncState.conflicts = this.syncState.conflicts.filter(c => c.id !== conflictId);
    } catch (error) {
      throw new Error(`解决冲突失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 上传文档到服务器
   */
  private async uploadDocument(document: LocalDocument, force = false): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await fetch('/api/documents/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          document,
          force
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        return { success: false, message: error.message || '上传失败' };
      }

      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        message: error instanceof Error ? error.message : '网络错误' 
      };
    }
  }

  /**
   * 获取远程文档
   */
  private async fetchRemoteDocument(documentId: string): Promise<any | null> {
    try {
      const response = await fetch(`/api/documents/${documentId}/sync`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 404) {
        return null;
      }

      if (!response.ok) {
        throw new Error('获取远程文档失败');
      }

      return await response.json();
    } catch (error) {
      console.error('获取远程文档失败:', error);
      return null;
    }
  }

  /**
   * 下载文档到本地
   */
  private async downloadDocument(remoteDoc: any): Promise<void> {
    await documentService.createDocument({
      title: remoteDoc.title,
      content: remoteDoc.content,
      folderId: remoteDoc.folderId,
      userId: remoteDoc.userId,
      metadata: remoteDoc.metadata
    });
  }

  /**
   * 更新本地文档
   */
  private async updateLocalDocument(remoteDoc: any): Promise<void> {
    await documentService.updateDocument(remoteDoc.id, {
      title: remoteDoc.title,
      content: remoteDoc.content,
      folderId: remoteDoc.folderId,
      metadata: remoteDoc.metadata
    });
  }

  /**
   * 创建批次
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 获取当前用户ID
   */
  private async getCurrentUserId(): Promise<string | null> {
    try {
      const response = await fetch('/api/auth/session');
      if (response.ok) {
        const session = await response.json();
        return session?.user?.id || null;
      }
      return null;
    } catch (error) {
      console.error('获取用户ID失败:', error);
      return null;
    }
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(type: string, listener: SyncEventListener): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, []);
    }
    this.eventListeners.get(type)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(type: string, listener: SyncEventListener): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  private emitEvent(type: string, data?: any): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const event: SyncEvent = {
        type: type as any,
        data,
        timestamp: new Date()
      };
      
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('事件监听器执行失败:', error);
        }
      });
    }
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.stopAutoSync();
    this.eventListeners.clear();
  }
}

// 导出单例实例
export const syncService = new SyncService();