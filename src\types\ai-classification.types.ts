/**
 * AI 文件分类和建议相关的类型定义
 */

/**
 * 文档分类结果
 */
export interface DocumentClassification {
  /** 文档ID */
  documentId: string;
  /** 主要分类 */
  primaryCategory: string;
  /** 次要分类 */
  secondaryCategories: string[];
  /** 分类置信度 (0-1) */
  confidence: number;
  /** 建议的标签 */
  suggestedTags: string[];
  /** 分类原因 */
  reasoning: string;
  /** 分析时间 */
  analyzedAt: Date;
}

/**
 * 文件夹结构建议
 */
export interface FolderStructureSuggestion {
  /** 建议的文件夹名称 */
  name: string;
  /** 建议的父文件夹ID */
  parentId?: string;
  /** 建议的文件夹路径 */
  path: string;
  /** 建议原因 */
  reason: string;
  /** 相关文档数量 */
  documentCount: number;
  /** 建议优先级 (1-5) */
  priority: number;
  /** 建议类型 */
  type: 'create' | 'move' | 'merge' | 'rename';
}

/**
 * 相关文档推荐
 */
export interface RelatedDocumentRecommendation {
  /** 目标文档ID */
  targetDocumentId: string;
  /** 相关文档ID */
  relatedDocumentId: string;
  /** 相关文档标题 */
  relatedDocumentTitle: string;
  /** 相关性评分 (0-1) */
  relevanceScore: number;
  /** 相关性类型 */
  relationType: 'similar_content' | 'same_topic' | 'complementary' | 'reference';
  /** 相关性描述 */
  description: string;
  /** 推荐原因 */
  reason: string;
}

/**
 * 文档内容分析结果
 */
export interface DocumentContentAnalysis {
  /** 文档ID */
  documentId: string;
  /** 主要主题 */
  mainTopics: string[];
  /** 关键概念 */
  keyConcepts: string[];
  /** 文档类型 */
  documentType: DocumentType;
  /** 内容复杂度 (1-5) */
  complexity: number;
  /** 专业领域 */
  domain?: string;
  /** 语言 */
  language: string;
  /** 内容摘要 */
  summary: string;
  /** 分析时间 */
  analyzedAt: Date;
}

/**
 * 文档类型枚举
 */
export enum DocumentType {
  /** 笔记 */
  NOTE = 'note',
  /** 文章 */
  ARTICLE = 'article',
  /** 报告 */
  REPORT = 'report',
  /** 会议记录 */
  MEETING_MINUTES = 'meeting_minutes',
  /** 待办事项 */
  TODO_LIST = 'todo_list',
  /** 项目计划 */
  PROJECT_PLAN = 'project_plan',
  /** 研究资料 */
  RESEARCH = 'research',
  /** 教程 */
  TUTORIAL = 'tutorial',
  /** 参考资料 */
  REFERENCE = 'reference',
  /** 其他 */
  OTHER = 'other'
}

/**
 * 分类配置选项
 */
export interface ClassificationOptions {
  /** 是否启用自动分类 */
  enableAutoClassification: boolean;
  /** 是否启用文件夹建议 */
  enableFolderSuggestions: boolean;
  /** 是否启用相关文档推荐 */
  enableRelatedDocuments: boolean;
  /** 最小置信度阈值 */
  minConfidenceThreshold: number;
  /** 最大建议数量 */
  maxSuggestions: number;
  /** 分析深度 */
  analysisDepth: 'basic' | 'detailed' | 'comprehensive';
  /** 自定义分类规则 */
  customRules?: ClassificationRule[];
}

/**
 * 自定义分类规则
 */
export interface ClassificationRule {
  /** 规则ID */
  id: string;
  /** 规则名称 */
  name: string;
  /** 匹配条件 */
  conditions: ClassificationCondition[];
  /** 分类结果 */
  category: string;
  /** 规则优先级 */
  priority: number;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 分类条件
 */
export interface ClassificationCondition {
  /** 条件类型 */
  type: 'title_contains' | 'content_contains' | 'word_count' | 'has_tags' | 'folder_path';
  /** 条件值 */
  value: string | number | string[];
  /** 操作符 */
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than';
}

/**
 * 批量分类请求
 */
export interface BatchClassificationRequest {
  /** 文档ID列表 */
  documentIds: string[];
  /** 分类选项 */
  options: ClassificationOptions;
  /** 用户ID */
  userId: string;
}

/**
 * 批量分类结果
 */
export interface BatchClassificationResult {
  /** 请求ID */
  requestId: string;
  /** 处理状态 */
  status: 'pending' | 'processing' | 'completed' | 'failed';
  /** 总文档数 */
  totalDocuments: number;
  /** 已处理文档数 */
  processedDocuments: number;
  /** 分类结果 */
  classifications: DocumentClassification[];
  /** 文件夹建议 */
  folderSuggestions: FolderStructureSuggestion[];
  /** 相关文档推荐 */
  relatedDocuments: RelatedDocumentRecommendation[];
  /** 错误信息 */
  errors: string[];
  /** 开始时间 */
  startedAt: Date;
  /** 完成时间 */
  completedAt?: Date;
}

/**
 * 智能整理建议
 */
export interface SmartOrganizationSuggestion {
  /** 建议ID */
  id: string;
  /** 建议类型 */
  type: 'folder_creation' | 'document_move' | 'tag_addition' | 'duplicate_merge';
  /** 建议标题 */
  title: string;
  /** 建议描述 */
  description: string;
  /** 影响的文档 */
  affectedDocuments: string[];
  /** 建议的操作 */
  actions: OrganizationAction[];
  /** 预期收益 */
  expectedBenefit: string;
  /** 建议优先级 */
  priority: number;
  /** 是否已应用 */
  applied: boolean;
  /** 创建时间 */
  createdAt: Date;
}

/**
 * 整理操作
 */
export interface OrganizationAction {
  /** 操作类型 */
  type: 'create_folder' | 'move_document' | 'add_tag' | 'merge_documents' | 'rename';
  /** 目标资源 */
  target: string;
  /** 操作参数 */
  params: Record<string, any>;
  /** 操作描述 */
  description: string;
}