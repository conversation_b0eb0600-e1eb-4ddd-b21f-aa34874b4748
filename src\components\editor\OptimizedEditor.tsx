'use client';

import { useState, useEffect, useMemo, useCallback, Suspense } from 'react';
import { useEditor, EditorContent, Editor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import { VirtualizedEditor } from './VirtualizedEditor';
import { useVirtualizedContent } from '@/hooks/useVirtualizedContent';
import { useCodeSplitting } from '@/hooks/useCodeSplitting';
import { LazyLoader } from '@/components/common/LazyLoader';
import { PerformanceMonitor } from '@/components/common/PerformanceMonitor';

// 懒加载的编辑器扩展
const LazySlashCommandMenu = LazyLoader({
  componentName: '斜杠命令菜单',
  delay: 50,
})(({ editor }: { editor: Editor | null }) => {
  const { Component } = useCodeSplitting(
    () => import('./SlashCommandMenu'),
    { preloadOnHover: true }
  );
  return Component ? <Component editor={editor} /> : null;
});

const LazyAIComponents = LazyLoader({
  componentName: 'AI功能组件',
  delay: 100,
})(({ editor, enabled }: { editor: Editor | null; enabled: boolean }) => {
  const { Component: AITextGenerationManager } = useCodeSplitting(
    () => import('./AITextGenerationManager'),
    { preloadOnVisible: true }
  );
  
  const { Component: AITextRewriteManager } = useCodeSplitting(
    () => import('./AITextRewriteManager'),
    { preloadOnVisible: true }
  );
  
  const { Component: EnhancedSelectionMenu } = useCodeSplitting(
    () => import('./EnhancedSelectionMenu'),
    { preloadOnHover: true }
  );

  if (!enabled) return null;

  return (
    <>
      {AITextGenerationManager && (
        <AITextGenerationManager editor={editor} enabled={enabled} />
      )}
      {AITextRewriteManager && (
        <AITextRewriteManager editor={editor} enabled={enabled} />
      )}
      {EnhancedSelectionMenu && (
        <EnhancedSelectionMenu 
          editor={editor} 
          enabled={true}
          enableAI={enabled}
          minSelectionLength={2}
        />
      )}
    </>
  );
});

interface OptimizedEditorProps {
  content?: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  editable?: boolean;
  className?: string;
  /** 是否启用 AI 功能 */
  enableAI?: boolean;
  /** 是否启用虚拟化（大文档优化） */
  enableVirtualization?: boolean;
  /** 虚拟化阈值（字符数） */
  virtualizationThreshold?: number;
  /** 是否启用性能监控 */
  enablePerformanceMonitor?: boolean;
  /** 是否启用懒加载 */
  enableLazyLoading?: boolean;
}

/**
 * 优化版编辑器组件
 * 集成虚拟化、懒加载、缓存等性能优化功能
 */
export function OptimizedEditor({
  content = '',
  placeholder = '开始写作...',
  onChange,
  editable = true,
  className = '',
  enableAI = true,
  enableVirtualization = true,
  virtualizationThreshold = 50000, // 50KB
  enablePerformanceMonitor = process.env.NODE_ENV === 'development',
  enableLazyLoading = true,
}: OptimizedEditorProps) {
  const [isLargeDocument, setIsLargeDocument] = useState(false);
  const [editorMode, setEditorMode] = useState<'standard' | 'virtualized'>('standard');

  // 检查文档大小
  useEffect(() => {
    const isLarge = content.length > virtualizationThreshold;
    setIsLargeDocument(isLarge);
    
    if (enableVirtualization && isLarge) {
      setEditorMode('virtualized');
    } else {
      setEditorMode('standard');
    }
  }, [content.length, virtualizationThreshold, enableVirtualization]);

  // 虚拟化内容管理
  const virtualizedContent = useVirtualizedContent(content, {
    enabled: enableVirtualization && isLargeDocument,
    chunkSize: 10000,
    preloadChunks: 2,
    onContentChange: onChange,
  });

  // 编辑器配置（使用 useMemo 优化）
  const editorConfig = useMemo(() => ({
    extensions: [
      StarterKit.configure({
        heading: { levels: [1, 2, 3] },
        bulletList: { keepMarks: true, keepAttributes: false },
        orderedList: { keepMarks: true, keepAttributes: false },
        codeBlock: { HTMLAttributes: { class: 'code-block' } },
        horizontalRule: false,
      }),
      Placeholder.configure({
        placeholder: `${placeholder} (输入 "/" 查看命令)`,
        emptyEditorClass: 'is-editor-empty',
      }),
      CharacterCount.configure({ limit: null }),
    ],
    content: editorMode === 'virtualized' ? '' : content,
    editable,
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none touch-manipulation',
      },
    },
  }), [content, editable, placeholder, editorMode]);

  // 标准编辑器
  const editor = useEditor({
    ...editorConfig,
    onUpdate: ({ editor }) => {
      if (editorMode === 'standard') {
        const html = editor.getHTML();
        onChange?.(html);
      }
    },
  });

  // 性能优化的回调函数
  const handleContentChange = useCallback((newContent: string) => {
    onChange?.(newContent);
  }, [onChange]);

  // 获取统计信息
  const getStats = useCallback(() => {
    if (editorMode === 'virtualized') {
      return virtualizedContent.stats;
    }
    
    if (editor) {
      return {
        totalWords: editor.storage.characterCount.words(),
        totalCharacters: editor.storage.characterCount.characters(),
        totalChunks: 1,
        currentChunk: 1,
        isVirtualized: false,
      };
    }
    
    return {
      totalWords: 0,
      totalCharacters: 0,
      totalChunks: 0,
      currentChunk: 0,
      isVirtualized: false,
    };
  }, [editor, editorMode, virtualizedContent.stats]);

  // 渲染虚拟化编辑器
  if (editorMode === 'virtualized') {
    return (
      <div className={`w-full ${className}`}>
        <VirtualizedEditor
          content={content}
          placeholder={placeholder}
          onChange={handleContentChange}
          editable={editable}
          enableVirtualization={true}
          itemsPerPage={50}
          itemHeight={24}
        />
        
        {/* 性能监控 */}
        {enablePerformanceMonitor && <PerformanceMonitor />}
      </div>
    );
  }

  // 渲染标准编辑器
  if (!editor) {
    return (
      <div className={`min-h-[400px] w-full ${className}`}>
        <div className="flex items-center justify-center h-full">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-sm text-gray-600">加载编辑器...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      {/* 文档大小提示 */}
      {isLargeDocument && !enableVirtualization && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
          <div className="flex items-center space-x-2">
            <div className="text-yellow-600">⚠️</div>
            <div className="text-sm text-yellow-800">
              检测到大文档 ({(content.length / 1024).toFixed(1)}KB)，
              建议启用虚拟化模式以获得更好的性能。
            </div>
            <button
              onClick={() => setEditorMode('virtualized')}
              className="ml-auto px-3 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700"
            >
              启用虚拟化
            </button>
          </div>
        </div>
      )}

      <div className="relative">
        <EditorContent 
          editor={editor} 
          className="min-h-[400px] w-full px-3 sm:px-4 py-4 sm:py-6 focus-within:outline-none touch-manipulation"
        />
        
        {/* 懒加载的组件 */}
        {enableLazyLoading ? (
          <Suspense fallback={<div className="text-xs text-gray-500">加载功能组件...</div>}>
            <LazySlashCommandMenu editor={editor} />
            <LazyAIComponents editor={editor} enabled={enableAI} />
          </Suspense>
        ) : (
          <>
            {/* 这里可以放置非懒加载的组件 */}
          </>
        )}
        
        {/* 统计信息 */}
        <div className="absolute bottom-2 right-3 sm:right-4 text-xs text-muted-foreground">
          <div className="flex items-center space-x-2">
            <span className="hidden sm:inline">
              {getStats().totalWords} 词 · {getStats().totalCharacters.toLocaleString()} 字符
            </span>
            <span className="sm:hidden">
              {getStats().totalWords}词
            </span>
            
            {/* 性能指示器 */}
            {isLargeDocument && (
              <span className="text-blue-600" title="大文档模式">
                ⚡
              </span>
            )}
            
            {enableAI && (
              <span className="text-purple-600" title="AI功能已启用">
                🤖
              </span>
            )}
          </div>
        </div>
      </div>

      {/* 性能监控 */}
      {enablePerformanceMonitor && <PerformanceMonitor showDetails={true} />}
    </div>
  );
}