import { prisma } from './index';

export async function testDatabaseConnection(): Promise<{
  success: boolean;
  message: string;
  error?: string;
}> {
  try {
    // Test the connection by running a simple query
    await prisma.$queryRaw`SELECT 1`;
    
    // Test if we can read from the User table
    const userCount = await prisma.user.count();
    
    return {
      success: true,
      message: `Database connection successful. Found ${userCount} users.`,
    };
  } catch (error) {
    console.error('Database connection test failed:', error);
    return {
      success: false,
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function initializeDatabase(): Promise<{
  success: boolean;
  message: string;
  error?: string;
}> {
  try {
    // Check if tables exist by trying to count users
    await prisma.user.count();
    
    return {
      success: true,
      message: 'Database is already initialized',
    };
  } catch (error) {
    return {
      success: false,
      message: 'Database needs to be initialized. Please run: npm run db:push',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
