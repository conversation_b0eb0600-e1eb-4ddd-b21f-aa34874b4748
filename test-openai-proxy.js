/**
 * 测试通过代理连接 OpenAI API
 */

async function testOpenAIProxy() {
  const apiKey = '***************************************************';
  const proxyUrl = 'http://127.0.0.1:57800';

  console.log('🚀 开始测试 OpenAI 代理连接...');
  console.log('代理地址:', proxyUrl);
  console.log('API 密钥:', apiKey.substring(0, 20) + '...');

  try {
    const url = `${proxyUrl}/v1/chat/completions`;
    console.log('请求 URL:', url);

    const requestBody = {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: '你好，这是一个连接测试' }],
      max_tokens: 10,
    };

    console.log('请求体:', JSON.stringify(requestBody, null, 2));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(15000),
    });

    console.log('响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 代理连接成功!');
      console.log('响应数据:', JSON.stringify(data, null, 2));

      if (data.choices && data.choices[0] && data.choices[0].message) {
        console.log('🎉 AI 回复:', data.choices[0].message.content);
      }
    } else {
      const errorText = await response.text();
      console.log('❌ 代理连接失败');
      console.log('错误状态:', response.status);
      console.log('错误内容:', errorText);
    }
  } catch (error) {
    console.log('❌ 连接异常:', error.message);

    if (error.name === 'AbortError') {
      console.log('请求超时，可能是代理服务器没有响应');
    } else if (error.code === 'ECONNREFUSED') {
      console.log(
        '连接被拒绝，请检查代理服务器是否运行在 http://127.0.0.1:57800'
      );
    }
  }
}

// 同时测试 Gemini
async function testGeminiProxy() {
  const apiKey = 'AIzaSyBABZ7zimsYl4zdKxcKXnGu-h8ebR9GKeU';

  console.log('\n🚀 开始测试 Gemini 连接...');
  console.log('API 密钥:', apiKey.substring(0, 20) + '...');

  try {
    const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`;
    console.log('请求 URL:', url);

    const requestBody = {
      contents: [
        {
          parts: [{ text: '你好，这是一个连接测试' }],
        },
      ],
    };

    console.log('请求体:', JSON.stringify(requestBody, null, 2));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(15000),
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Gemini 连接成功!');
      console.log('响应数据:', JSON.stringify(data, null, 2));

      if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        console.log('🎉 AI 回复:', data.candidates[0].content.parts[0].text);
      }
    } else {
      const errorText = await response.text();
      console.log('❌ Gemini 连接失败');
      console.log('错误状态:', response.status);
      console.log('错误内容:', errorText);
    }
  } catch (error) {
    console.log('❌ Gemini 连接异常:', error.message);
  }
}

// 运行测试
async function runTests() {
  await testOpenAIProxy();
  await testGeminiProxy();

  console.log('\n📋 测试完成！');
  console.log('如果 OpenAI 代理连接成功，你可以在 AI 配置页面中使用以下设置：');
  console.log('- 服务提供商: OpenAI');
  console.log(
    '- API 密钥: ***************************************************'
  );
  console.log('- 服务端点: http://127.0.0.1:57800/v1');
  console.log('- 模型: gpt-3.5-turbo');

  console.log('\n如果 Gemini 连接成功，你可以使用以下设置：');
  console.log('- 服务提供商: Gemini');
  console.log('- API 密钥: AIzaSyBABZ7zimsYl4zdKxcKXnGu-h8ebR9GKeU');
  console.log('- 模型: gemini-pro');
}

runTests();
