/**
 * 测试 Ollama 连接 - 简化版
 */

require('dotenv').config();

async function testOllamaSimple() {
  console.log('🧪 测试 Ollama 连接（简化版）...');

  try {
    const { OllamaService } = require('./src/lib/services/ai/ollama-service.ts');

    // 使用较小的模型进行测试
    const config = {
      provider: 'ollama',
      endpoint: 'http://localhost:11454',
      model: 'mistral:latest', // 使用相对较小的模型
      maxTokens: 20,
      temperature: 0.7,
      timeout: 120000 // 增加到 120 秒超时
    };

    console.log('配置:', config);

    const service = new OllamaService(config);
    console.log('Ollama 服务创建成功');

    // 测试连接
    console.log('测试连接...');
    const connected = await service.testConnection();

    if (connected) {
      console.log('✅ Ollama 连接成功!');

      // 获取模型列表
      try {
        const models = await service.getAvailableModels();
        console.log('📋 找到', models.length, '个可用模型');
        console.log('前5个模型:', models.slice(0, 5));

        // 简单的文本生成测试
        console.log('开始文本生成测试（可能需要较长时间）...');

        const startTime = Date.now();
        const response = await service._generateText({
          prompt: 'Hello',
          maxTokens: 10
        });

        console.log('📝 生成文本成功!');
        console.log('内容:', response.content.substring(0, 100) + (response.content.length > 100 ? '...' : ''));
        console.log('令牌数:', response.tokensUsed);
        console.log('响应时间:', response.responseTime + 'ms');
        console.log('模型:', response.model);

      } catch (error) {
        console.log('📝 生成文本失败:', error.message);
        if (error.message.includes('timeout')) {
          console.log('💡 提示：模型可能比较大，需要更长的加载时间');
        }
      }
    } else {
      console.log('❌ Ollama 连接失败');
    }
  } catch (error) {
    console.log('❌ 测试异常:', error.message);
  }
}

console.log('📋 Ollama 简化测试:');
console.log('1. 连接到 http://localhost:11454');
console.log('2. 使用 mistral:latest 模型');
console.log('3. 简单的文本生成测试');
console.log('');

testOllamaSimple();