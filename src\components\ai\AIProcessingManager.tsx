/**
 * AI 处理管理器组件
 * 提供完整的 AI 处理状态管理和显示功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  Activity, 
  BarChart3, 
  Settings, 
  RefreshCw,
  Trash2,
  Download,
  Filter,
  Search,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { AIProcessingIndicator } from './AIProcessingIndicator';
import { useMultipleAIProcessing } from '@/hooks/use-ai-processing';
import type { AIProcessingProgress, AIProcessingStats } from '@/types/ai-status.types';

/**
 * AI 处理管理器属性
 */
interface AIProcessingManagerProps {
  /** 文档 ID */
  documentId?: string;
  /** 是否显示统计信息 */
  showStats?: boolean;
  /** 是否显示历史记录 */
  showHistory?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 过滤选项
 */
interface FilterOptions {
  status: string;
  type: string;
  timeRange: string;
}

/**
 * AI 处理管理器组件
 */
export function AIProcessingManager({
  documentId,
  showStats = true,
  showHistory = true,
  className = '',
}: AIProcessingManagerProps) {
  const {
    allProgress,
    activeCount,
    stats,
    startProcessing,
    cancelProcessing,
    cancelAllProcessing,
    clearCompleted,
  } = useMultipleAIProcessing(documentId);

  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<FilterOptions>({
    status: 'all',
    type: 'all',
    timeRange: 'all',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

  /**
   * 过滤处理记录
   */
  const filteredProgress = Array.from(allProgress.entries()).filter(([id, progress]) => {
    // 搜索过滤
    if (searchTerm && !id.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    // 状态过滤
    if (filters.status !== 'all' && progress.status !== filters.status) {
      return false;
    }

    // 时间范围过滤
    if (filters.timeRange !== 'all') {
      const now = Date.now();
      const elapsed = now - progress.startTime.getTime();
      
      switch (filters.timeRange) {
        case 'recent':
          if (elapsed > 5 * 60 * 1000) return false; // 5分钟内
          break;
        case 'today':
          if (elapsed > 24 * 60 * 60 * 1000) return false; // 24小时内
          break;
        case 'week':
          if (elapsed > 7 * 24 * 60 * 60 * 1000) return false; // 7天内
          break;
      }
    }

    return true;
  });

  /**
   * 切换选择项
   */
  const toggleSelection = (id: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedItems(newSelected);
  };

  /**
   * 全选/取消全选
   */
  const toggleSelectAll = () => {
    if (selectedItems.size === filteredProgress.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(filteredProgress.map(([id]) => id)));
    }
  };

  /**
   * 批量取消选中的处理
   */
  const cancelSelected = () => {
    selectedItems.forEach(id => {
      const progress = allProgress.get(id);
      if (progress?.cancellable) {
        cancelProcessing(id);
      }
    });
    setSelectedItems(new Set());
  };

  /**
   * 导出处理记录
   */
  const exportRecords = () => {
    const records = filteredProgress.map(([id, progress]) => ({
      id,
      status: progress.status,
      progress: progress.overallProgress,
      startTime: progress.startTime.toISOString(),
      currentStage: progress.currentStage,
      error: progress.error,
    }));

    const blob = new Blob([JSON.stringify(records, null, 2)], {
      type: 'application/json',
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-processing-records-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <Activity className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-900">AI 处理管理器</h2>
          
          {activeCount > 0 && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {activeCount} 个活动任务
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* 过滤器按钮 */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-lg transition-colors ${
              showFilters ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
            title="过滤器"
          >
            <Filter className="w-4 h-4" />
          </button>

          {/* 导出按钮 */}
          <button
            onClick={exportRecords}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            title="导出记录"
          >
            <Download className="w-4 h-4" />
          </button>

          {/* 清除完成按钮 */}
          <button
            onClick={clearCompleted}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            title="清除已完成"
          >
            <Trash2 className="w-4 h-4" />
          </button>

          {/* 取消所有按钮 */}
          {activeCount > 0 && (
            <button
              onClick={cancelAllProcessing}
              className="px-3 py-1.5 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
            >
              取消所有
            </button>
          )}
        </div>
      </div>

      {/* 统计信息 */}
      {showStats && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <StatsPanel stats={stats} />
        </div>
      )}

      {/* 过滤器面板 */}
      {showFilters && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <FilterPanel
            filters={filters}
            onFiltersChange={setFilters}
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
          />
        </div>
      )}

      {/* 批量操作栏 */}
      {selectedItems.size > 0 && (
        <div className="flex items-center justify-between p-3 bg-blue-50 border-b border-blue-200">
          <span className="text-sm text-blue-700">
            已选择 {selectedItems.size} 个项目
          </span>
          <div className="flex items-center space-x-2">
            <button
              onClick={cancelSelected}
              className="px-3 py-1 text-sm text-red-600 hover:text-red-700 hover:bg-white rounded transition-colors"
            >
              取消选中
            </button>
            <button
              onClick={() => setSelectedItems(new Set())}
              className="px-3 py-1 text-sm text-gray-600 hover:text-gray-700 hover:bg-white rounded transition-colors"
            >
              取消选择
            </button>
          </div>
        </div>
      )}

      {/* 处理记录列表 */}
      <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
        {/* 全选控制 */}
        {filteredProgress.length > 0 && (
          <div className="flex items-center px-4 py-2 bg-gray-50">
            <input
              type="checkbox"
              checked={selectedItems.size === filteredProgress.length && filteredProgress.length > 0}
              onChange={toggleSelectAll}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label className="ml-2 text-sm text-gray-600">
              {selectedItems.size > 0 
                ? `已选择 ${selectedItems.size} 项`
                : '全选'
              }
            </label>
          </div>
        )}

        {/* 处理记录项 */}
        {filteredProgress.map(([id, progress]) => (
          <ProcessingRecord
            key={id}
            id={id}
            progress={progress}
            selected={selectedItems.has(id)}
            onToggleSelect={() => toggleSelection(id)}
            onCancel={() => cancelProcessing(id)}
          />
        ))}

        {/* 空状态 */}
        {filteredProgress.length === 0 && (
          <div className="flex flex-col items-center justify-center py-12 text-gray-500">
            <Activity className="w-12 h-12 mb-3 opacity-50" />
            <p className="text-lg font-medium mb-1">暂无处理记录</p>
            <p className="text-sm">
              {searchTerm || filters.status !== 'all' || filters.timeRange !== 'all'
                ? '没有符合条件的记录'
                : '开始使用 AI 功能后，处理记录将显示在这里'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * 统计面板组件
 */
interface StatsPanelProps {
  stats: AIProcessingStats;
}

function StatsPanel({ stats }: StatsPanelProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div className="text-center">
        <div className="text-2xl font-bold text-blue-600">
          {stats.totalProcessed}
        </div>
        <div className="text-sm text-gray-600">总处理数</div>
      </div>
      
      <div className="text-center">
        <div className="text-2xl font-bold text-green-600">
          {stats.successCount}
        </div>
        <div className="text-sm text-gray-600">成功数</div>
      </div>
      
      <div className="text-center">
        <div className="text-2xl font-bold text-orange-600">
          {stats.totalTokensUsed.toLocaleString()}
        </div>
        <div className="text-sm text-gray-600">总令牌数</div>
      </div>
      
      <div className="text-center">
        <div className="text-2xl font-bold text-purple-600">
          {stats.averageProcessingTime > 0 ? `${(stats.averageProcessingTime / 1000).toFixed(1)}s` : '-'}
        </div>
        <div className="text-sm text-gray-600">平均时间</div>
      </div>
    </div>
  );
}

/**
 * 过滤器面板组件
 */
interface FilterPanelProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
}

function FilterPanel({ filters, onFiltersChange, searchTerm, onSearchChange }: FilterPanelProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {/* 搜索 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          搜索
        </label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder="搜索处理 ID..."
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* 状态过滤 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          状态
        </label>
        <select
          value={filters.status}
          onChange={(e) => onFiltersChange({ ...filters, status: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">全部状态</option>
          <option value="preparing">准备中</option>
          <option value="connecting">连接中</option>
          <option value="processing">处理中</option>
          <option value="generating">生成中</option>
          <option value="completed">已完成</option>
          <option value="error">错误</option>
          <option value="cancelled">已取消</option>
        </select>
      </div>

      {/* 类型过滤 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          类型
        </label>
        <select
          value={filters.type}
          onChange={(e) => onFiltersChange({ ...filters, type: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">全部类型</option>
          <option value="generate">生成</option>
          <option value="rewrite">改写</option>
          <option value="summarize">总结</option>
          <option value="translate">翻译</option>
          <option value="analyze">分析</option>
          <option value="explain">解释</option>
        </select>
      </div>

      {/* 时间范围过滤 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          时间范围
        </label>
        <select
          value={filters.timeRange}
          onChange={(e) => onFiltersChange({ ...filters, timeRange: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">全部时间</option>
          <option value="recent">最近5分钟</option>
          <option value="today">今天</option>
          <option value="week">本周</option>
        </select>
      </div>
    </div>
  );
}

/**
 * 处理记录组件
 */
interface ProcessingRecordProps {
  id: string;
  progress: AIProcessingProgress;
  selected: boolean;
  onToggleSelect: () => void;
  onCancel: () => void;
}

function ProcessingRecord({ id, progress, selected, onToggleSelect, onCancel }: ProcessingRecordProps) {
  const [showDetails, setShowDetails] = useState(false);

  /**
   * 格式化时间
   */
  const formatElapsedTime = () => {
    const elapsed = Date.now() - progress.startTime.getTime();
    if (elapsed < 1000) return '刚刚';
    if (elapsed < 60000) return `${Math.floor(elapsed / 1000)}秒前`;
    if (elapsed < 3600000) return `${Math.floor(elapsed / 60000)}分钟前`;
    return `${Math.floor(elapsed / 3600000)}小时前`;
  };

  return (
    <div className={`p-4 hover:bg-gray-50 transition-colors ${selected ? 'bg-blue-50' : ''}`}>
      <div className="flex items-start space-x-3">
        {/* 选择框 */}
        <input
          type="checkbox"
          checked={selected}
          onChange={onToggleSelect}
          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1"
        />

        {/* 处理信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-900">
                {id.slice(-12)}
              </span>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                progress.status === 'completed' ? 'bg-green-100 text-green-800' :
                progress.status === 'error' ? 'bg-red-100 text-red-800' :
                progress.status === 'cancelled' ? 'bg-yellow-100 text-yellow-800' :
                'bg-blue-100 text-blue-800'
              }`}>
                {progress.status}
              </span>
            </div>
            
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <span>{formatElapsedTime()}</span>
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-blue-600 hover:text-blue-700"
              >
                {showDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
              </button>
            </div>
          </div>

          {/* 简化的进度显示 */}
          <AIProcessingIndicator
            progress={progress}
            compact={!showDetails}
            onCancel={progress.cancellable ? onCancel : undefined}
            options={{
              showDetailedProgress: showDetails,
              showTokenCount: showDetails,
              showTimeEstimate: showDetails,
            }}
          />
        </div>
      </div>
    </div>
  );
}