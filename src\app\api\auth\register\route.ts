import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import { prisma } from "@/lib/db/prisma";
import { z } from "zod";

/**
 * 用户注册数据验证模式
 */
const registerSchema = z.object({
  name: z.string().min(2, "姓名至少需要2个字符"),
  email: z.string().email("请输入有效的邮箱地址"),
  password: z.string().min(6, "密码至少需要6个字符"),
});

/**
 * 用户注册 API 路由
 * POST /api/auth/register
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json();
    
    // 验证输入数据
    const validatedData = registerSchema.parse(body);
    const { name, email, password } = validatedData;

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "该邮箱已被注册" },
        { status: 400 }
      );
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12);

    // 创建新用户
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
      },
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true,
      }
    });

    // 返回成功响应
    return NextResponse.json(
      { 
        message: "用户注册成功",
        user 
      },
      { status: 201 }
    );

  } catch (error) {
    console.error("用户注册错误:", error);
    
    // 处理验证错误
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "输入数据无效", details: error.issues },
        { status: 400 }
      );
    }

    // 返回服务器错误
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
}
