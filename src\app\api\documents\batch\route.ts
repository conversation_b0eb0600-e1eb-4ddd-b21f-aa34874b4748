import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';
import { z } from 'zod';

// 批量操作的验证模式
const batchOperationSchema = z.object({
  operation: z.enum(['delete', 'move', 'duplicate']),
  documentIds: z.array(z.string()).min(1).max(100), // 限制最多100个文档
  targetFolderId: z.string().optional(), // 移动操作时的目标文件夹
});

/**
 * POST /api/documents/batch - 批量操作文档
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = batchOperationSchema.parse(body);
    const { operation, documentIds, targetFolderId } = validatedData;

    // 验证所有文档都属于当前用户
    const documents = await prisma.document.findMany({
      where: {
        id: { in: documentIds },
        userId: session.user.id,
      },
    });

    if (documents.length !== documentIds.length) {
      return NextResponse.json(
        { error: '部分文档未找到或无权限访问' },
        { status: 404 }
      );
    }

    let result;

    switch (operation) {
      case 'delete':
        // 批量删除文档
        result = await prisma.document.deleteMany({
          where: {
            id: { in: documentIds },
            userId: session.user.id,
          },
        });
        break;

      case 'move':
        // 验证目标文件夹（如果指定）
        if (targetFolderId) {
          const targetFolder = await prisma.folder.findFirst({
            where: {
              id: targetFolderId,
              userId: session.user.id,
            },
          });

          if (!targetFolder) {
            return NextResponse.json(
              { error: '目标文件夹未找到' },
              { status: 404 }
            );
          }
        }

        // 批量移动文档
        result = await prisma.document.updateMany({
          where: {
            id: { in: documentIds },
            userId: session.user.id,
          },
          data: {
            folderId: targetFolderId || null,
          },
        });
        break;

      case 'duplicate':
        // 批量复制文档
        const duplicatedDocuments = [];
        
        for (const document of documents) {
          const duplicated = await prisma.document.create({
            data: {
              title: `${document.title} (副本)`,
              content: document.content,
              folderId: document.folderId,
              userId: session.user.id,
              wordCount: document.wordCount,
              charCount: document.charCount,
            },
          });
          duplicatedDocuments.push(duplicated);
        }

        result = { count: duplicatedDocuments.length, documents: duplicatedDocuments };
        break;

      default:
        return NextResponse.json(
          { error: '不支持的操作类型' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      message: `批量${operation === 'delete' ? '删除' : operation === 'move' ? '移动' : '复制'}操作完成`,
      result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '输入参数无效', details: error.issues },
        { status: 400 }
      );
    }

    console.error('批量操作文档失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
