'use client';

import { useState, useEffect } from 'react';
import { EditorLayout } from './EditorLayout';
import { MobileEditor } from './MobileEditor';

interface ResponsiveEditorProps {
  initialContent?: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  onSave?: (content: string) => void;
}

/**
 * 响应式编辑器组件
 * 根据设备类型自动选择最适合的编辑器布局
 */
export function ResponsiveEditor({
  initialContent = '',
  placeholder = '开始写作...',
  onChange,
  onSave,
}: ResponsiveEditorProps) {
  const [isMobile, setIsMobile] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth;
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      
      // 综合考虑屏幕宽度和触摸能力
      setIsMobile(width < 768 && isTouchDevice);
      setIsLoading(false);
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  const handleContentChange = (content: string) => {
    onChange?.(content);
  };

  const handleSave = (content: string) => {
    onSave?.(content);
  };

  // 加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-muted-foreground">加载编辑器...</div>
      </div>
    );
  }

  // 移动端使用专门优化的编辑器
  if (isMobile) {
    return (
      <div className="h-screen bg-background">
        <MobileEditor
          content={initialContent}
          placeholder={placeholder}
          onChange={handleContentChange}
          className="h-full"
        />
      </div>
    );
  }

  // 桌面端使用完整的编辑器布局
  return (
    <EditorLayout
      initialContent={initialContent}
      placeholder={placeholder}
      onChange={handleContentChange}
      onSave={handleSave}
    />
  );
}