#!/usr/bin/env tsx

/**
 * AI 文本生成功能测试脚本
 * 测试 AI 文本生成服务的各项功能
 */

import { 
  TextGenerationService, 
  TextGenerationRequest 
} from '../src/lib/services/ai/text-generation-service';
import { AIServiceFactory } from '../src/lib/services/ai/ai-service-factory';
import { AIServiceConfig } from '../src/types/ai.types';

// 测试配置
const TEST_CONFIG: AIServiceConfig = {
  provider: 'openai',
  apiKey: process.env.OPENAI_API_KEY || 'test-key',
  model: 'gpt-3.5-turbo',
  maxTokens: 500,
  temperature: 0.7,
  timeout: 30000
};

// 测试用例
const TEST_CASES = {
  continue: {
    context: '人工智能技术正在快速发展，它正在改变我们的生活方式。从智能手机到自动驾驶汽车，AI 的应用无处不在。',
    beforeCursor: '人工智能技术正在快速发展，它正在改变我们的生活方式。从智能手机到自动驾驶汽车，AI 的应用无处不在。',
    type: 'continue' as const,
    length: 'medium' as const,
    style: 'formal' as const
  },
  expand: {
    context: '机器学习是人工智能的一个重要分支。它使计算机能够从数据中学习，而无需明确编程。',
    selectedText: '机器学习',
    type: 'expand' as const,
    length: 'long' as const,
    style: 'technical' as const
  },
  complete: {
    context: '深度学习网络由多个层组成，每一层都会对输入数据进行处理和转换。',
    selectedText: '深度学习网络由多个层组成',
    beforeCursor: '深度学习是机器学习的一个子集。',
    afterCursor: '这种架构使得网络能够学习复杂的模式。',
    type: 'complete' as const,
    style: 'formal' as const
  }
};

/**
 * 创建模拟的 AI 服务（用于测试）
 */
class MockAIService {
  provider = 'openai' as const;
  config = TEST_CONFIG;

  async generateText(request: any) {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const responses = {
      continue: '在未来几年里，我们可以期待看到更多创新的 AI 应用。这些技术将进一步提高工作效率，改善生活质量，并为各行各业带来新的机遇。然而，我们也需要关注 AI 发展带来的挑战，如就业影响、隐私保护和伦理问题。',
      expand: '机器学习是人工智能领域的核心技术之一，它通过算法和统计模型使计算机系统能够从经验中学习和改进。与传统编程不同，机器学习不需要程序员为每种情况编写具体的指令，而是让系统通过分析大量数据来识别模式、做出预测或决策。主要的机器学习方法包括监督学习、无监督学习和强化学习，每种方法都适用于不同类型的问题和数据集。',
      complete: '深度学习网络由多个层组成，包括输入层、隐藏层和输出层，每一层都包含多个神经元节点，这些节点通过权重连接相互影响，对输入数据进行非线性变换和特征提取'
    };
    
    const type = request.prompt.includes('继续') ? 'continue' : 
                 request.prompt.includes('扩展') ? 'expand' : 'complete';
    
    return {
      content: responses[type],
      tokensUsed: Math.floor(Math.random() * 200) + 50,
      responseTime: 1000,
      model: 'gpt-3.5-turbo',
      provider: 'openai' as const
    };
  }

  async testConnection() {
    return true;
  }

  async getAvailableModels() {
    return ['gpt-3.5-turbo', 'gpt-4'];
  }
}

/**
 * 测试文本生成功能
 */
async function testTextGeneration() {
  console.log('🚀 开始测试 AI 文本生成功能...\n');

  try {
    // 创建模拟服务
    const mockService = new MockAIService();
    const textGenService = new TextGenerationService(mockService as any);

    // 测试续写功能
    console.log('📝 测试续写功能...');
    const continueRequest: TextGenerationRequest = TEST_CASES.continue;
    const continueResult = await textGenService.generateText(continueRequest);
    
    console.log('✅ 续写测试结果:');
    console.log(`   内容: ${continueResult.content.substring(0, 100)}...`);
    console.log(`   令牌数: ${continueResult.tokensUsed}`);
    console.log(`   响应时间: ${continueResult.responseTime}ms`);
    console.log(`   ID: ${continueResult.id}\n`);

    // 测试扩展功能
    console.log('📝 测试扩展功能...');
    const expandRequest: TextGenerationRequest = TEST_CASES.expand;
    const expandResult = await textGenService.generateText(expandRequest);
    
    console.log('✅ 扩展测试结果:');
    console.log(`   内容: ${expandResult.content.substring(0, 100)}...`);
    console.log(`   令牌数: ${expandResult.tokensUsed}`);
    console.log(`   响应时间: ${expandResult.responseTime}ms`);
    console.log(`   ID: ${expandResult.id}\n`);

    // 测试补全功能
    console.log('📝 测试补全功能...');
    const completeRequest: TextGenerationRequest = TEST_CASES.complete;
    const completeResult = await textGenService.generateText(completeRequest);
    
    console.log('✅ 补全测试结果:');
    console.log(`   内容: ${completeResult.content.substring(0, 100)}...`);
    console.log(`   令牌数: ${completeResult.tokensUsed}`);
    console.log(`   响应时间: ${completeResult.responseTime}ms`);
    console.log(`   ID: ${completeResult.id}\n`);

    // 测试多选项生成
    console.log('📝 测试多选项生成...');
    const multipleResults = await textGenService.generateMultipleOptions(continueRequest, 2);
    
    console.log('✅ 多选项生成测试结果:');
    console.log(`   生成数量: ${multipleResults.length}`);
    multipleResults.forEach((result, index) => {
      console.log(`   选项 ${index + 1}: ${result.content.substring(0, 50)}...`);
    });
    console.log();

    // 测试错误处理
    console.log('📝 测试错误处理...');
    try {
      const invalidRequest: TextGenerationRequest = {
        context: '',
        type: 'continue'
      };
      await textGenService.generateText(invalidRequest);
    } catch (error) {
      console.log('✅ 错误处理测试通过:', error instanceof Error ? error.message : '未知错误');
    }

    console.log('\n🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

/**
 * 测试提示构建功能
 */
function testPromptBuilding() {
  console.log('\n🔧 测试提示构建功能...');

  const mockService = new MockAIService();
  const service = new TextGenerationService(mockService as any);
  
  // 测试不同类型的提示构建
  const testCases = [
    {
      name: '续写提示',
      request: {
        context: '测试上下文',
        beforeCursor: '这是一个测试段落',
        type: 'continue' as const,
        style: 'formal' as const,
        length: 'medium' as const
      }
    },
    {
      name: '扩展提示',
      request: {
        context: '测试上下文',
        selectedText: '人工智能',
        type: 'expand' as const,
        style: 'technical' as const,
        length: 'long' as const
      }
    },
    {
      name: '补全提示',
      request: {
        context: '测试上下文',
        selectedText: '机器学习是',
        beforeCursor: '前文内容',
        afterCursor: '后文内容',
        type: 'complete' as const,
        style: 'academic' as const
      }
    }
  ];

  testCases.forEach(testCase => {
    console.log(`\n📋 ${testCase.name}:`);
    try {
      // 这里我们需要访问私有方法，在实际测试中可能需要调整
      const prompt = (service as any).buildPrompt(testCase.request);
      console.log(`   提示长度: ${prompt.length} 字符`);
      console.log(`   包含关键词: ${prompt.includes('要求') ? '✅' : '❌'}`);
      console.log(`   包含风格说明: ${prompt.includes('风格') ? '✅' : '❌'}`);
    } catch (error) {
      console.log(`   ❌ 构建失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  });
}

/**
 * 性能测试
 */
async function performanceTest() {
  console.log('\n⚡ 性能测试...');

  const mockService = new MockAIService();
  const textGenService = new TextGenerationService(mockService as any);

  const startTime = Date.now();
  const promises = [];

  // 并发生成测试
  for (let i = 0; i < 5; i++) {
    promises.push(textGenService.generateText(TEST_CASES.continue));
  }

  const results = await Promise.allSettled(promises);
  const endTime = Date.now();

  const successful = results.filter(r => r.status === 'fulfilled').length;
  const failed = results.filter(r => r.status === 'rejected').length;

  console.log(`✅ 并发测试结果:`);
  console.log(`   总耗时: ${endTime - startTime}ms`);
  console.log(`   成功: ${successful}/${results.length}`);
  console.log(`   失败: ${failed}/${results.length}`);
  console.log(`   平均响应时间: ${(endTime - startTime) / results.length}ms`);
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🧪 AI 文本生成功能测试套件\n');
  console.log('=' .repeat(50));

  try {
    await testTextGeneration();
    testPromptBuilding();
    await performanceTest();
    
    console.log('\n' + '='.repeat(50));
    console.log('🎊 所有测试完成！功能正常工作。');
    
  } catch (error) {
    console.error('\n❌ 测试套件执行失败:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testTextGeneration, testPromptBuilding, performanceTest };