'use client';

import React, { useState, useEffect } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/Button';
import { 
  EditIcon, 
  XIcon,
  CheckIcon,
  RefreshCwIcon,
  SparklesIcon,
  BookOpenIcon,
  MessageCircleIcon,
  ZapIcon,
  GraduationCapIcon,
  PaletteIcon,
  ArrowRightIcon
} from 'lucide-react';

/**
 * Edit 动作类型
 */
export interface EditAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  description: string;
  prompt: string;
}

/**
 * Edit 对话框属性
 */
interface EditDialogProps {
  /** 是否显示对话框 */
  visible: boolean;
  /** 关闭对话框回调 */
  onClose: () => void;
  /** 选中的文本 */
  selectedText: string;
  /** 编辑器实例 */
  editor: Editor;
  /** 选择范围 */
  selectionRange: { from: number; to: number };
  /** 初始动作类型 */
  initialAction?: string;
  /** 应用编辑回调 */
  onApplyEdit?: (originalText: string, editedText: string) => void;
}

/**
 * 预设的 Edit 动作
 */
const EDIT_ACTIONS: EditAction[] = [
  {
    id: 'improve',
    label: '改进优化',
    icon: <SparklesIcon className="h-4 w-4" />,
    description: '提升文本质量和表达效果',
    prompt: '请改进以下文本，使其更加清晰、准确和有说服力：'
  },
  {
    id: 'rewrite',
    label: '重新组织',
    icon: <EditIcon className="h-4 w-4" />,
    description: '重新组织文本结构和表达方式',
    prompt: '请重新组织以下文本，改善其结构和表达方式：'
  },
  {
    id: 'formal',
    label: '正式化',
    icon: <BookOpenIcon className="h-4 w-4" />,
    description: '调整为正式、严谨的表达风格',
    prompt: '请将以下文本调整为正式、专业的表达风格：'
  },
  {
    id: 'casual',
    label: '口语化',
    icon: <MessageCircleIcon className="h-4 w-4" />,
    description: '调整为轻松、随意的表达风格',
    prompt: '请将以下文本调整为轻松、口语化的表达风格：'
  },
  {
    id: 'simplify',
    label: '简化',
    icon: <ZapIcon className="h-4 w-4" />,
    description: '使文本更简洁明了',
    prompt: '请简化以下文本，使其更加简洁明了：'
  },
  {
    id: 'academic',
    label: '学术化',
    icon: <GraduationCapIcon className="h-4 w-4" />,
    description: '调整为学术、专业的表达风格',
    prompt: '请将以下文本调整为学术、专业的表达风格：'
  },
  {
    id: 'creative',
    label: '创意化',
    icon: <PaletteIcon className="h-4 w-4" />,
    description: '增加创意和生动性',
    prompt: '请为以下文本增加创意和生动性：'
  }
];

/**
 * Edit 对话框组件
 * 提供文本编辑和改写功能，支持直接替换选中文本
 */
export function EditDialog({
  visible,
  onClose,
  selectedText,
  editor,
  selectionRange,
  initialAction = 'improve',
  onApplyEdit
}: EditDialogProps) {
  const [currentAction, setCurrentAction] = useState(initialAction);
  const [editedText, setEditedText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [hasResult, setHasResult] = useState(false);

  /**
   * 获取当前动作
   */
  const getCurrentAction = () => {
    return EDIT_ACTIONS.find(action => action.id === currentAction) || EDIT_ACTIONS[0];
  };

  /**
   * 初始化编辑
   */
  useEffect(() => {
    if (visible && selectedText && !hasResult) {
      handleEdit(currentAction);
    }
  }, [visible, selectedText, currentAction]);

  /**
   * 重置状态
   */
  useEffect(() => {
    if (!visible) {
      setEditedText('');
      setHasResult(false);
      setIsLoading(false);
    }
  }, [visible]);

  /**
   * 处理编辑请求
   */
  const handleEdit = async (actionId: string) => {
    const action = EDIT_ACTIONS.find(a => a.id === actionId);
    if (!action || !selectedText) return;

    setIsLoading(true);
    setHasResult(false);

    try {
      // 模拟 AI 编辑（实际项目中应该调用真实的 AI 服务）
      const result = await simulateAIEdit(selectedText, action);
      setEditedText(result);
      setHasResult(true);
    } catch (error) {
      console.error('编辑失败:', error);
      setEditedText('抱歉，编辑服务暂时不可用。请稍后再试。');
      setHasResult(true);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 模拟 AI 编辑
   */
  const simulateAIEdit = async (text: string, action: EditAction): Promise<string> => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2000));
    
    // 根据不同的动作类型生成不同的编辑结果
    switch (action.id) {
      case 'improve':
        return `${text}（已优化：这是改进后的版本，表达更加清晰准确）`;
      
      case 'rewrite':
        return `重新组织后的内容：${text.split('').reverse().join('')}（这是重新组织的示例）`;
      
      case 'formal':
        return `正式表达：${text}。此内容已调整为正式、专业的表达风格。`;
      
      case 'casual':
        return `嘿，${text}，这样说是不是更轻松一些？`;
      
      case 'simplify':
        return text.length > 20 ? `${text.substring(0, 20)}...（已简化）` : `${text}（已简化）`;
      
      case 'academic':
        return `根据相关研究表明，${text}。这一观点在学术界得到了广泛认可。`;
      
      case 'creative':
        return `✨ ${text} ✨ —— 这就像是文字的魔法，让思想在纸上翩翩起舞。`;
      
      default:
        return `${text}（已编辑）`;
    }
  };

  /**
   * 应用编辑结果
   */
  const applyEdit = () => {
    if (!editedText || !hasResult) return;

    // 替换编辑器中的选中文本
    editor.chain()
      .focus()
      .setTextSelection(selectionRange)
      .insertContent(editedText)
      .run();

    onApplyEdit?.(selectedText, editedText);
    onClose();
  };

  /**
   * 重新生成
   */
  const regenerate = () => {
    handleEdit(currentAction);
  };

  /**
   * 切换动作
   */
  const switchAction = (actionId: string) => {
    setCurrentAction(actionId);
    handleEdit(actionId);
  };

  if (!visible) return null;

  const action = getCurrentAction();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <EditIcon className="h-5 w-5 text-green-600" />
            <h3 className="text-lg font-semibold">AI 编辑</h3>
            <span className="text-sm text-gray-500">
              ({selectedText.length} 字符)
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </div>

        {/* 动作选择 */}
        <div className="p-4 border-b bg-gray-50">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
            {EDIT_ACTIONS.map((editAction) => (
              <Button
                key={editAction.id}
                variant={currentAction === editAction.id ? "default" : "outline"}
                size="sm"
                onClick={() => switchAction(editAction.id)}
                className="flex flex-col items-center gap-1 h-auto py-2"
                disabled={isLoading}
              >
                {editAction.icon}
                <span className="text-xs">{editAction.label}</span>
              </Button>
            ))}
          </div>
          <p className="text-sm text-gray-600 mt-2">
            {action.description}
          </p>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-2 h-full">
            {/* 原文 */}
            <div className="p-4 border-r">
              <h4 className="text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                <span>原文</span>
              </h4>
              <div className="bg-gray-50 rounded-lg p-3 h-[300px] overflow-y-auto">
                <div className="text-sm whitespace-pre-wrap">
                  {selectedText}
                </div>
              </div>
            </div>

            {/* 编辑结果 */}
            <div className="p-4">
              <h4 className="text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                <span>编辑结果</span>
                {hasResult && (
                  <ArrowRightIcon className="h-4 w-4 text-green-600" />
                )}
              </h4>
              <div className="bg-green-50 rounded-lg p-3 h-[300px] overflow-y-auto">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                      AI 正在编辑...
                    </div>
                  </div>
                ) : hasResult ? (
                  <div className="text-sm whitespace-pre-wrap">
                    {editedText}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500 text-sm">
                    选择编辑类型开始处理
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 底部操作 */}
        <div className="flex items-center justify-between p-4 border-t bg-gray-50">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={regenerate}
              disabled={isLoading || !selectedText}
              className="flex items-center gap-1"
            >
              <RefreshCwIcon className="h-4 w-4" />
              重新生成
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={onClose}
            >
              取消
            </Button>
            <Button
              onClick={applyEdit}
              disabled={!hasResult || isLoading}
              className="flex items-center gap-1"
            >
              <CheckIcon className="h-4 w-4" />
              应用编辑
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}