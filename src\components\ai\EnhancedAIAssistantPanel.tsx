'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  PanelRightIcon,
  PanelLeftIcon,
  SparklesIcon,
  EditIcon,
  FileTextIcon,
  LanguagesIcon,
  MessageSquareIcon,
  SettingsIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  HelpCircleIcon,
  PenToolIcon,
  BarChartIcon,
  TagIcon,
  ListIcon,
  TerminalIcon,
  BookOpenIcon,
  LightbulbIcon,
  RefreshCwIcon,
  XIcon,
  MenuIcon,
  HistoryIcon,
  TrendingUpIcon,
  ZapIcon,
  StarIcon,
  ClockIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  MaximizeIcon,
  MinimizeIcon
} from 'lucide-react';

/**
 * AI 功能类别定义
 */
interface AIFeatureCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  color: string;
  features: AIFeature[];
}

/**
 * AI 功能定义
 */
interface AIFeature {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  shortcut?: string;
  action: () => void;
  disabled?: boolean;
  premium?: boolean;
  usage?: number;
}

/**
 * 增强版 AI 助手面板属性
 */
interface EnhancedAIAssistantPanelProps {
  /** 是否显示面板 */
  isOpen: boolean;
  /** 切换面板显示状态 */
  onToggle: () => void;
  /** 面板位置 */
  position?: 'left' | 'right';
  /** 面板宽度 */
  width?: number;
  /** 是否为移动端 */
  isMobile?: boolean;
  /** 自定义类名 */
  className?: string;
  /** AI 功能回调 */
  onAIAction?: (actionId: string, data?: any) => void;
  /** 当前选中的文本 */
  selectedText?: string;
  /** 是否正在处理 AI 请求 */
  isProcessing?: boolean;
  /** 处理状态信息 */
  processingStatus?: string;
  /** 是否启用高级功能 */
  enableAdvancedFeatures?: boolean;
  /** 主题模式 */
  theme?: 'light' | 'dark';
}

/**
 * 增强版 AI 助手面板组件
 * 提供完整的 AI 功能导航、状态管理和响应式设计
 */
export function EnhancedAIAssistantPanel({
  isOpen,
  onToggle,
  position = 'right',
  width = 380,
  isMobile = false,
  className = '',
  onAIAction,
  selectedText,
  isProcessing = false,
  processingStatus,
  enableAdvancedFeatures = true,
  theme = 'light'
}: EnhancedAIAssistantPanelProps) {
  const [activeTab, setActiveTab] = useState('features');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['writing']));
  const [recentActions, setRecentActions] = useState<string[]>([]);
  const [actionHistory, setActionHistory] = useState<Array<{
    id: string;
    action: string;
    timestamp: Date;
    success: boolean;
    duration?: number;
  }>>([]);
  const [isMaximized, setIsMaximized] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const panelRef = useRef<HTMLDivElement>(null);

  /**
   * 切换类别展开状态
   */
  const toggleCategory = useCallback((categoryId: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  }, []);

  /**
   * 执行 AI 功能
   */
  const handleAIAction = useCallback(async (actionId: string, data?: any) => {
    if (onAIAction) {
      const startTime = Date.now();
      
      try {
        await onAIAction(actionId, data);
        
        // 记录成功操作
        const duration = Date.now() - startTime;
        const historyItem = {
          id: Math.random().toString(36).substr(2, 9),
          action: actionId,
          timestamp: new Date(),
          success: true,
          duration
        };
        
        setActionHistory(prev => [historyItem, ...prev.slice(0, 19)]);
        setRecentActions(prev => {
          const newActions = [actionId, ...prev.filter(id => id !== actionId)].slice(0, 8);
          return newActions;
        });
      } catch (error) {
        // 记录失败操作
        const historyItem = {
          id: Math.random().toString(36).substr(2, 9),
          action: actionId,
          timestamp: new Date(),
          success: false
        };
        
        setActionHistory(prev => [historyItem, ...prev.slice(0, 19)]);
      }
    }
  }, [onAIAction]);

  /**
   * AI 功能分类配置
   */
  const aiCategories: AIFeatureCategory[] = [
    {
      id: 'writing',
      name: '写作助手',
      icon: PenToolIcon,
      description: '文本生成、续写和创作',
      color: 'blue',
      features: [
        {
          id: 'ai-continue',
          name: 'AI 续写',
          description: '基于上下文继续写作',
          icon: SparklesIcon,
          shortcut: 'Ctrl+Shift+C',
          action: () => handleAIAction('ai-continue'),
          usage: recentActions.filter(a => a === 'ai-continue').length
        },
        {
          id: 'ai-rewrite',
          name: 'AI 改写',
          description: '改写和优化选中文本',
          icon: EditIcon,
          shortcut: 'Ctrl+Shift+R',
          action: () => handleAIAction('ai-rewrite', { text: selectedText }),
          disabled: !selectedText,
          usage: recentActions.filter(a => a === 'ai-rewrite').length
        },
        {
          id: 'ai-creative',
          name: '创意写作',
          description: '创作故事、诗歌等内容',
          icon: LightbulbIcon,
          action: () => handleAIAction('ai-creative'),
          premium: true,
          usage: recentActions.filter(a => a === 'ai-creative').length
        },
        {
          id: 'ai-expand',
          name: '内容扩展',
          description: '扩展和详细化内容',
          icon: TrendingUpIcon,
          action: () => handleAIAction('ai-expand', { text: selectedText }),
          disabled: !selectedText,
          usage: recentActions.filter(a => a === 'ai-expand').length
        }
      ]
    },
    {
      id: 'analysis',
      name: '文档分析',
      icon: BarChartIcon,
      description: '分析、总结和提取信息',
      color: 'green',
      features: [
        {
          id: 'ai-summarize',
          name: '文档摘要',
          description: '生成文档摘要',
          icon: FileTextIcon,
          shortcut: 'Ctrl+Shift+S',
          action: () => handleAIAction('ai-summarize'),
          usage: recentActions.filter(a => a === 'ai-summarize').length
        },
        {
          id: 'ai-keywords',
          name: '关键词提取',
          description: '提取关键词和主题',
          icon: TagIcon,
          action: () => handleAIAction('ai-keywords'),
          usage: recentActions.filter(a => a === 'ai-keywords').length
        },
        {
          id: 'ai-outline',
          name: '生成大纲',
          description: '自动生成文档大纲',
          icon: ListIcon,
          action: () => handleAIAction('ai-outline'),
          usage: recentActions.filter(a => a === 'ai-outline').length
        },
        {
          id: 'ai-analysis',
          name: '内容分析',
          description: '分析语调、结构和质量',
          icon: BarChartIcon,
          action: () => handleAIAction('ai-analysis'),
          premium: true,
          usage: recentActions.filter(a => a === 'ai-analysis').length
        }
      ]
    },
    {
      id: 'language',
      name: '语言工具',
      icon: LanguagesIcon,
      description: '翻译、解释和语言处理',
      color: 'purple',
      features: [
        {
          id: 'ai-translate',
          name: 'AI 翻译',
          description: '翻译选中文本',
          icon: LanguagesIcon,
          shortcut: 'Ctrl+Shift+T',
          action: () => handleAIAction('ai-translate', { text: selectedText }),
          disabled: !selectedText,
          usage: recentActions.filter(a => a === 'ai-translate').length
        },
        {
          id: 'ai-explain',
          name: 'AI 解释',
          description: '解释复杂概念',
          icon: HelpCircleIcon,
          action: () => handleAIAction('ai-explain', { text: selectedText }),
          disabled: !selectedText,
          usage: recentActions.filter(a => a === 'ai-explain').length
        },
        {
          id: 'ai-grammar',
          name: '语法检查',
          description: '检查和修正语法',
          icon: BookOpenIcon,
          action: () => handleAIAction('ai-grammar', { text: selectedText }),
          disabled: !selectedText,
          usage: recentActions.filter(a => a === 'ai-grammar').length
        }
      ]
    },
    {
      id: 'custom',
      name: '自定义指令',
      icon: TerminalIcon,
      description: '执行自定义 AI 指令',
      color: 'orange',
      features: [
        {
          id: 'ai-custom',
          name: '自定义指令',
          description: '执行自定义 AI 指令',
          icon: TerminalIcon,
          action: () => handleAIAction('ai-custom'),
          usage: recentActions.filter(a => a === 'ai-custom').length
        },
        {
          id: 'ai-chat',
          name: 'AI 对话',
          description: '与 AI 进行对话',
          icon: MessageSquareIcon,
          action: () => handleAIAction('ai-chat'),
          usage: recentActions.filter(a => a === 'ai-chat').length
        }
      ]
    }
  ];

  /**
   * 获取颜色类名
   */
  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'text-blue-600 bg-blue-50 border-blue-200',
      green: 'text-green-600 bg-green-50 border-green-200',
      purple: 'text-purple-600 bg-purple-50 border-purple-200',
      orange: 'text-orange-600 bg-orange-50 border-orange-200'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  /**
   * 渲染功能按钮
   */
  const renderFeatureButton = useCallback((feature: AIFeature) => {
    const IconComponent = feature.icon;
    const isRecent = recentActions.includes(feature.id);
    const hasUsage = (feature.usage || 0) > 0;
    
    return (
      <Button
        key={feature.id}
        variant="ghost"
        size="sm"
        onClick={feature.action}
        disabled={feature.disabled || isProcessing}
        className={`
          w-full justify-start h-auto p-3 text-left relative
          ${feature.disabled ? 'opacity-50' : 'hover:bg-gray-50'}
          ${isRecent ? 'bg-blue-50 border-l-2 border-l-blue-500' : ''}
          ${theme === 'dark' ? 'hover:bg-gray-800 text-gray-100' : ''}
        `}
      >
        <div className="flex items-start gap-3 w-full">
          <IconComponent className="h-4 w-4 mt-0.5 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium text-sm">{feature.name}</span>
              {feature.premium && (
                <Badge variant="secondary" className="text-xs px-1 py-0 bg-yellow-100 text-yellow-800">
                  Pro
                </Badge>
              )}
              {isRecent && (
                <Badge variant="secondary" className="text-xs px-1 py-0 bg-green-100 text-green-800">
                  最近
                </Badge>
              )}
              {feature.shortcut && (
                <Badge variant="outline" className="text-xs px-1 py-0 ml-auto">
                  {feature.shortcut}
                </Badge>
              )}
            </div>
            <p className="text-xs text-gray-600 mb-1">{feature.description}</p>
            {hasUsage && (
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <ZapIcon className="h-3 w-3" />
                <span>使用 {feature.usage} 次</span>
              </div>
            )}
          </div>
        </div>
      </Button>
    );
  }, [recentActions, isProcessing, theme]);

  /**
   * 渲染功能分类
   */
  const renderCategory = useCallback((category: AIFeatureCategory) => {
    const IconComponent = category.icon;
    const isExpanded = expandedCategories.has(category.id);
    const colorClasses = getColorClasses(category.color);
    
    return (
      <div key={category.id} className="border-b border-gray-100 last:border-b-0">
        <Button
          variant="ghost"
          onClick={() => toggleCategory(category.id)}
          className="w-full justify-between p-4 h-auto hover:bg-gray-50"
        >
          <div className="flex items-center gap-3">
            <div className={`p-1.5 rounded-lg ${colorClasses}`}>
              <IconComponent className="h-4 w-4" />
            </div>
            <div className="text-left">
              <div className="font-medium">{category.name}</div>
              <div className="text-xs text-gray-600">{category.description}</div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {category.features.length}
            </Badge>
            {isExpanded ? (
              <ChevronDownIcon className="h-4 w-4" />
            ) : (
              <ChevronRightIcon className="h-4 w-4" />
            )}
          </div>
        </Button>
        
        {isExpanded && (
          <div className="px-2 pb-2 space-y-1">
            {category.features.map(renderFeatureButton)}
          </div>
        )}
      </div>
    );
  }, [expandedCategories, toggleCategory, renderFeatureButton]);

  /**
   * 渲染处理状态
   */
  const renderProcessingStatus = useCallback(() => {
    if (!isProcessing) return null;
    
    return (
      <div className="p-4 bg-blue-50 border-b border-blue-100">
        <div className="flex items-center gap-3">
          <RefreshCwIcon className="h-4 w-4 animate-spin text-blue-600" />
          <div className="flex-1">
            <div className="font-medium text-blue-900">AI 处理中...</div>
            {processingStatus && (
              <div className="text-xs text-blue-700">{processingStatus}</div>
            )}
          </div>
        </div>
      </div>
    );
  }, [isProcessing, processingStatus]);

  /**
   * 渲染快速操作
   */
  const renderQuickActions = useCallback(() => {
    const quickActions = [
      { id: 'ai-continue', name: '续写', icon: SparklesIcon },
      { id: 'ai-rewrite', name: '改写', icon: EditIcon, disabled: !selectedText },
      { id: 'ai-summarize', name: '摘要', icon: FileTextIcon },
      { id: 'ai-translate', name: '翻译', icon: LanguagesIcon, disabled: !selectedText }
    ];
    
    return (
      <div className="p-4 space-y-4">
        <h3 className="font-medium text-sm text-gray-900 flex items-center gap-2">
          <ZapIcon className="h-4 w-4" />
          快速操作
        </h3>
        <div className="grid grid-cols-2 gap-2">
          {quickActions.map(action => {
            const IconComponent = action.icon;
            return (
              <Button
                key={action.id}
                variant="outline"
                size="sm"
                onClick={() => handleAIAction(action.id, { text: selectedText })}
                disabled={action.disabled || isProcessing}
                className="h-auto p-3 flex flex-col items-center gap-2"
              >
                <IconComponent className="h-5 w-5" />
                <span className="text-xs">{action.name}</span>
              </Button>
            );
          })}
        </div>
      </div>
    );
  }, [selectedText, isProcessing, handleAIAction]);

  /**
   * 渲染操作历史
   */
  const renderActionHistory = useCallback(() => {
    return (
      <div className="p-4 space-y-4">
        <h3 className="font-medium text-sm text-gray-900 flex items-center gap-2">
          <HistoryIcon className="h-4 w-4" />
          操作历史
        </h3>
        {actionHistory.length > 0 ? (
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {actionHistory.map(item => (
              <div
                key={item.id}
                className={`
                  p-2 rounded-lg border text-xs flex items-center justify-between
                  ${item.success 
                    ? 'bg-green-50 border-green-200 text-green-800' 
                    : 'bg-red-50 border-red-200 text-red-800'
                  }
                `}
              >
                <div className="flex items-center gap-2">
                  {item.success ? (
                    <CheckCircleIcon className="h-3 w-3" />
                  ) : (
                    <AlertCircleIcon className="h-3 w-3" />
                  )}
                  <span className="font-medium">{item.action}</span>
                </div>
                <div className="flex items-center gap-2 text-xs opacity-75">
                  {item.duration && (
                    <span>{item.duration}ms</span>
                  )}
                  <ClockIcon className="h-3 w-3" />
                  <span>{item.timestamp.toLocaleTimeString()}</span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6 text-gray-500">
            <HistoryIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">暂无操作历史</p>
          </div>
        )}
      </div>
    );
  }, [actionHistory]);

  /**
   * 渲染统计信息
   */
  const renderStats = useCallback(() => {
    const totalActions = actionHistory.length;
    const successfulActions = actionHistory.filter(item => item.success).length;
    const successRate = totalActions > 0 ? (successfulActions / totalActions * 100).toFixed(1) : '0';
    const avgDuration = actionHistory
      .filter(item => item.duration)
      .reduce((sum, item) => sum + (item.duration || 0), 0) / 
      actionHistory.filter(item => item.duration).length || 0;

    return (
      <div className="p-4 space-y-4">
        <h3 className="font-medium text-sm text-gray-900 flex items-center gap-2">
          <BarChartIcon className="h-4 w-4" />
          使用统计
        </h3>
        <div className="grid grid-cols-2 gap-3">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-lg font-bold text-blue-600">{totalActions}</div>
            <div className="text-xs text-blue-700">总操作数</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-lg font-bold text-green-600">{successRate}%</div>
            <div className="text-xs text-green-700">成功率</div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-lg font-bold text-purple-600">{recentActions.length}</div>
            <div className="text-xs text-purple-700">常用功能</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="text-lg font-bold text-orange-600">
              {avgDuration > 0 ? Math.round(avgDuration) : 0}ms
            </div>
            <div className="text-xs text-orange-700">平均响应</div>
          </div>
        </div>
      </div>
    );
  }, [actionHistory, recentActions]);

  // 移动端全屏覆盖样式
  const mobileOverlayStyle = isMobile && isOpen ? 'fixed inset-0 z-50 bg-white' : '';
  
  // 桌面端侧边面板样式
  const desktopPanelStyle = !isMobile ? `
    fixed top-0 ${position === 'right' ? 'right-0' : 'left-0'} h-full z-40
    transform transition-transform duration-300 ease-in-out
    ${isOpen ? 'translate-x-0' : position === 'right' ? 'translate-x-full' : '-translate-x-full'}
    shadow-xl border-l border-gray-200
  ` : '';

  // 最大化样式
  const maximizedStyle = isMaximized && !isMobile ? 'fixed inset-4 z-50 rounded-lg shadow-2xl' : '';

  return (
    <>
      {/* 移动端背景遮罩 */}
      {isMobile && isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40"
          onClick={onToggle}
        />
      )}
      
      {/* 面板主体 */}
      <div
        ref={panelRef}
        className={`
          bg-white flex flex-col
          ${isMobile ? mobileOverlayStyle : maximizedStyle || desktopPanelStyle}
          ${theme === 'dark' ? 'bg-gray-900 text-gray-100' : ''}
          ${className}
        `}
        style={{ 
          width: isMobile ? '100%' : isMaximized ? '100%' : width,
          height: isMaximized ? '100%' : undefined
        }}
      >
        {/* 面板头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center gap-2">
            <SparklesIcon className="h-5 w-5 text-blue-600" />
            <h2 className="font-semibold text-gray-900">AI 助手</h2>
            {selectedText && (
              <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                {selectedText.length} 字符
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-1">
            {!isMobile && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMaximized(!isMaximized)}
                className="h-8 w-8 p-0"
                title={isMaximized ? "还原" : "最大化"}
              >
                {isMaximized ? (
                  <MinimizeIcon className="h-4 w-4" />
                ) : (
                  <MaximizeIcon className="h-4 w-4" />
                )}
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleAIAction('ai-settings')}
              className="h-8 w-8 p-0"
              title="设置"
            >
              <SettingsIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggle}
              className="h-8 w-8 p-0"
              title="关闭"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 处理状态 */}
        {renderProcessingStatus()}

        {/* 面板内容 */}
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="w-full grid grid-cols-4 m-4 mb-0">
              <TabsTrigger value="features" className="text-xs">功能</TabsTrigger>
              <TabsTrigger value="quick" className="text-xs">快捷</TabsTrigger>
              <TabsTrigger value="history" className="text-xs">历史</TabsTrigger>
              <TabsTrigger value="stats" className="text-xs">统计</TabsTrigger>
            </TabsList>
            
            <div className="flex-1 overflow-y-auto">
              <TabsContent value="features" className="mt-0 h-full">
                <div className="space-y-0">
                  {aiCategories.map(renderCategory)}
                </div>
              </TabsContent>
              
              <TabsContent value="quick" className="mt-0">
                {renderQuickActions()}
                
                {/* 最近使用 */}
                {recentActions.length > 0 && (
                  <div className="p-4 border-t border-gray-100">
                    <h3 className="font-medium text-sm text-gray-900 mb-3 flex items-center gap-2">
                      <StarIcon className="h-4 w-4" />
                      最近使用
                    </h3>
                    <div className="space-y-1">
                      {recentActions.slice(0, 4).map(actionId => {
                        const feature = aiCategories
                          .flatMap(cat => cat.features)
                          .find(f => f.id === actionId);
                        
                        if (!feature) return null;
                        
                        return renderFeatureButton(feature);
                      })}
                    </div>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="history" className="mt-0">
                {renderActionHistory()}
              </TabsContent>
              
              <TabsContent value="stats" className="mt-0">
                {renderStats()}
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* 面板底部 */}
        <div className="p-3 border-t border-gray-200 bg-gray-50">
          <div className="text-xs text-gray-600 text-center">
            {selectedText ? (
              <div className="flex items-center justify-center gap-2">
                <CheckCircleIcon className="h-3 w-3 text-green-600" />
                <span>已选择 {selectedText.length} 个字符</span>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2">
                <HelpCircleIcon className="h-3 w-3 text-gray-400" />
                <span>选择文本以启用更多功能</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default EnhancedAIAssistantPanel;