import { prisma } from './prisma'
import type { User } from '@prisma/client'

export async function createUser(data: {
  email: string
  name: string
  image?: string
  subscription?: string
}): Promise<User> {
  return prisma.user.create({
    data,
  })
}

export async function getUserById(id: string): Promise<User | null> {
  return prisma.user.findUnique({
    where: { id },
    include: {
      folders: true,
      documents: true,
      aiConfigs: true,
    },
  })
}

export async function getUserByEmail(email: string): Promise<User | null> {
  return prisma.user.findUnique({
    where: { email },
  })
}

export async function updateUser(
  id: string,
  data: Partial<Pick<User, 'name' | 'image' | 'subscription'>>
): Promise<User> {
  return prisma.user.update({
    where: { id },
    data,
  })
}

export async function deleteUser(id: string): Promise<User> {
  return prisma.user.delete({
    where: { id },
  })
}
