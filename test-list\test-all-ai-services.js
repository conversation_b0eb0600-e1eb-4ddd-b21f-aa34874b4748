/**
 * 测试所有 AI 服务的连接状态
 */

require('dotenv').config();

async function testAllAIServices() {
  console.log('🧪 测试所有 AI 服务连接状态...');
  console.log('');

  const results = {
    openai: { success: false, error: null },
    gemini: { success: false, error: null },
    ollama: { success: false, error: null }
  };

  // 1. 测试 OpenAI
  console.log('1️⃣ 测试 OpenAI 连接...');
  try {
    const { OpenAIService } = require('../src/lib/services/ai/openai-service.ts');
    const openaiConfig = {
      provider: 'openai',
      apiKey: '***************************************************',
      endpoint: 'http://127.0.0.1:57800',
      model: 'gpt-3.5-turbo',
      timeout: 30000
    };

    const openaiService = new OpenAIService(openaiConfig);
    const openaiConnected = await openaiService.testConnection();

    if (openaiConnected) {
      console.log('✅ OpenAI 连接成功');
      results.openai.success = true;
    } else {
      console.log('❌ OpenAI 连接失败');
      results.openai.error = '连接测试失败';
    }
  } catch (error) {
    console.log('❌ OpenAI 连接异常:', error.message);
    results.openai.error = error.message;
  }

  console.log('');

  // 2. 测试 Gemini
  console.log('2️⃣ 测试 Gemini 连接...');
  try {
    const { GeminiService } = require('../src/lib/services/ai/gemini-service.ts');
    const geminiConfig = {
      provider: 'gemini',
      apiKey: 'AIzaSyBABZ7zimsYl4zdKxcKXnGu-h8ebR9GKeU',
      endpoint: 'http://127.0.0.1:57800',
      model: 'gemini-2.5-flash-lite',
      timeout: 30000
    };

    const geminiService = new GeminiService(geminiConfig);
    const geminiConnected = await geminiService.testConnection();

    if (geminiConnected) {
      console.log('✅ Gemini 连接成功');
      results.gemini.success = true;
    } else {
      console.log('❌ Gemini 连接失败');
      results.gemini.error = '连接测试失败';
    }
  } catch (error) {
    console.log('❌ Gemini 连接异常:', error.message);
    results.gemini.error = error.message;
  }

  console.log('');

  // 3. 测试 Ollama
  console.log('3️⃣ 测试 Ollama 连接...');
  try {
    const { OllamaService } = require('../src/lib/services/ai/ollama-service.ts');
    const ollamaConfig = {
      provider: 'ollama',
      endpoint: 'http://localhost:11454',
      model: 'mistral:latest',
      timeout: 30000
    };

    const ollamaService = new OllamaService(ollamaConfig);
    const ollamaConnected = await ollamaService.testConnection();

    if (ollamaConnected) {
      console.log('✅ Ollama 连接成功');
      results.ollama.success = true;

      // 获取模型数量
      try {
        const models = await ollamaService.getAvailableModels();
        console.log(`📋 发现 ${models.length} 个可用模型`);
      } catch (error) {
        console.log('⚠️ 获取模型列表失败');
      }
    } else {
      console.log('❌ Ollama 连接失败');
      results.ollama.error = '连接测试失败';
    }
  } catch (error) {
    console.log('❌ Ollama 连接异常:', error.message);
    results.ollama.error = error.message;
  }

  console.log('');
  console.log('📊 测试结果总结:');
  console.log('==================');

  const successCount = Object.values(results).filter(r => r.success).length;
  const totalCount = Object.keys(results).length;

  console.log(`✅ 成功: ${successCount}/${totalCount} 个服务`);
  console.log('');

  Object.entries(results).forEach(([service, result]) => {
    const status = result.success ? '✅ 正常' : '❌ 失败';
    const error = result.error ? ` (${result.error})` : '';
    console.log(`${service.toUpperCase()}: ${status}${error}`);
  });

  console.log('');
  console.log('🎯 使用建议:');
  if (results.openai.success) {
    console.log('- OpenAI: 适合高质量文本生成，需要 API 密钥和代理');
  }
  if (results.gemini.success) {
    console.log('- Gemini: Google 的 AI 模型，支持多模态，需要 API 密钥和代理');
  }
  if (results.ollama.success) {
    console.log('- Ollama: 本地运行，无需 API 密钥，响应较慢但私密性好');
  }

  console.log('');
  console.log('🚀 现在可以在 http://localhost:4501/ai-config 中配置和使用这些服务！');
  // 结束服务
  process.exit();
}

testAllAIServices();