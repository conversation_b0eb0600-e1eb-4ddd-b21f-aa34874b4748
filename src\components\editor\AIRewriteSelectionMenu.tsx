'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/Button';
import { 
  EditIcon, 
  CheckIcon, 
  SparklesIcon,
  BookOpenIcon,
  MessageCircleIcon,
  ZapIcon,
  GraduationCapIcon,
  PaletteIcon
} from 'lucide-react';

/**
 * 选择文本改写菜单的属性
 */
interface AIRewriteSelectionMenuProps {
  /** TipTap 编辑器实例 */
  editor: Editor;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 改写选项
 */
interface RewriteOption {
  id: string;
  label: string;
  icon: React.ReactNode;
  description: string;
  action: () => void;
  shortcut?: string;
}

/**
 * 菜单状态
 */
interface MenuState {
  visible: boolean;
  position: { x: number; y: number };
  selectedText: string;
  selectionRange: { from: number; to: number };
}

/**
 * AI 改写选择菜单组件
 * 当用户选择文本时显示改写选项
 */
export function AIRewriteSelectionMenu({ 
  editor, 
  enabled = true 
}: AIRewriteSelectionMenuProps) {
  const [menuState, setMenuState] = useState<MenuState>({
    visible: false,
    position: { x: 0, y: 0 },
    selectedText: '',
    selectionRange: { from: 0, to: 0 }
  });

  /**
   * 获取改写选项
   */
  const getRewriteOptions = useCallback((): RewriteOption[] => {
    const aiRewrite = (editor as any).aiTextRewrite;
    if (!aiRewrite) return [];

    return [
      {
        id: 'improve',
        label: '改进优化',
        icon: <SparklesIcon className="h-4 w-4" />,
        description: '提升文本质量和表达效果',
        action: aiRewrite.improve,
        shortcut: '⌘⇧R'
      },
      {
        id: 'grammar',
        label: '语法检查',
        icon: <CheckIcon className="h-4 w-4" />,
        description: '修正语法错误和用词问题',
        action: aiRewrite.checkGrammar,
        shortcut: '⌘⇧G'
      },
      {
        id: 'formal',
        label: '正式化',
        icon: <BookOpenIcon className="h-4 w-4" />,
        description: '调整为正式、严谨的风格',
        action: aiRewrite.makeFormal,
        shortcut: '⌘⇧F'
      },
      {
        id: 'casual',
        label: '口语化',
        icon: <MessageCircleIcon className="h-4 w-4" />,
        description: '调整为轻松、随意的风格',
        action: aiRewrite.makeCasual
      },
      {
        id: 'simplify',
        label: '简化',
        icon: <ZapIcon className="h-4 w-4" />,
        description: '使文本更简洁明了',
        action: aiRewrite.simplify
      },
      {
        id: 'academic',
        label: '学术化',
        icon: <GraduationCapIcon className="h-4 w-4" />,
        description: '调整为学术、专业的风格',
        action: aiRewrite.makeAcademic
      },
      {
        id: 'creative',
        label: '创意化',
        icon: <PaletteIcon className="h-4 w-4" />,
        description: '增加创意和生动性',
        action: aiRewrite.makeCreative
      }
    ];
  }, [editor]);

  /**
   * 处理文本选择
   */
  const handleTextSelection = useCallback(() => {
    if (!enabled) return;

    const { from, to } = editor.state.selection;
    const selectedText = editor.state.doc.textBetween(from, to);

    // 如果没有选中文本或文本太短，隐藏菜单
    if (!selectedText.trim() || selectedText.length < 3) {
      setMenuState(prev => ({ ...prev, visible: false }));
      return;
    }

    // 计算菜单位置
    try {
      const coords = editor.view.coordsAtPos(to);
      const editorElement = editor.view.dom;
      const editorRect = editorElement.getBoundingClientRect();
      
      const position = {
        x: coords.left - editorRect.left,
        y: coords.bottom - editorRect.top + 10
      };

      setMenuState({
        visible: true,
        position,
        selectedText,
        selectionRange: { from, to }
      });
    } catch (error) {
      console.warn('无法计算菜单位置:', error);
    }
  }, [editor, enabled]);

  /**
   * 隐藏菜单
   */
  const hideMenu = useCallback(() => {
    setMenuState(prev => ({ ...prev, visible: false }));
  }, []);

  /**
   * 执行改写操作
   */
  const executeRewrite = useCallback((option: RewriteOption) => {
    // 确保选择范围正确
    const { from, to } = menuState.selectionRange;
    editor.chain().focus().setTextSelection({ from, to }).run();
    
    // 执行改写
    option.action();
    
    // 隐藏菜单
    hideMenu();
  }, [editor, menuState.selectionRange, hideMenu]);

  // 监听编辑器选择变化
  useEffect(() => {
    if (!editor) return;

    const handleSelectionUpdate = () => {
      // 延迟处理，确保选择状态已更新
      setTimeout(handleTextSelection, 10);
    };

    editor.on('selectionUpdate', handleSelectionUpdate);
    
    return () => {
      editor.off('selectionUpdate', handleSelectionUpdate);
    };
  }, [editor, handleTextSelection]);

  // 监听点击事件，点击外部时隐藏菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.ai-rewrite-menu') && !target.closest('.ProseMirror')) {
        hideMenu();
      }
    };

    if (menuState.visible) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [menuState.visible, hideMenu]);

  // 监听键盘事件
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // ESC 键隐藏菜单
      if (event.key === 'Escape' && menuState.visible) {
        hideMenu();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [menuState.visible, hideMenu]);

  if (!enabled || !menuState.visible) {
    return null;
  }

  const rewriteOptions = getRewriteOptions();
  
  if (rewriteOptions.length === 0) {
    return null;
  }

  return (
    <div
      className="
        ai-rewrite-menu
        absolute bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-50
        animate-in slide-in-from-bottom-2 fade-in duration-200
        max-w-xs
      "
      style={{
        left: menuState.position.x,
        top: menuState.position.y,
      }}
    >
      {/* 头部 */}
      <div className="flex items-center gap-2 px-2 py-1 mb-2 border-b">
        <EditIcon className="h-4 w-4 text-blue-600" />
        <span className="text-sm font-medium text-gray-700">AI 改写</span>
        <span className="text-xs text-gray-500 ml-auto">
          {menuState.selectedText.length} 字符
        </span>
      </div>

      {/* 改写选项 */}
      <div className="space-y-1">
        {rewriteOptions.map((option) => (
          <Button
            key={option.id}
            variant="ghost"
            size="sm"
            onClick={() => executeRewrite(option)}
            className="
              w-full justify-start h-auto p-2 text-left
              hover:bg-blue-50 hover:text-blue-700
              group
            "
          >
            <div className="flex items-start gap-2 w-full">
              <div className="text-blue-600 group-hover:text-blue-700 mt-0.5">
                {option.icon}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{option.label}</span>
                  {option.shortcut && (
                    <span className="text-xs text-gray-400 ml-2">
                      {option.shortcut}
                    </span>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-0.5 leading-tight">
                  {option.description}
                </p>
              </div>
            </div>
          </Button>
        ))}
      </div>

      {/* 提示信息 */}
      <div className="mt-2 pt-2 border-t">
        <p className="text-xs text-gray-400 text-center">
          选择选项开始改写，或按 ESC 取消
        </p>
      </div>
    </div>
  );
}

/**
 * 计算菜单的最佳显示位置
 * 确保菜单不会超出视窗边界
 */
function calculateOptimalPosition(
  basePosition: { x: number; y: number },
  menuSize: { width: number; height: number },
  viewportSize: { width: number; height: number }
): { x: number; y: number } {
  let { x, y } = basePosition;
  
  // 防止菜单超出右边界
  if (x + menuSize.width > viewportSize.width) {
    x = viewportSize.width - menuSize.width - 10;
  }
  
  // 防止菜单超出下边界
  if (y + menuSize.height > viewportSize.height) {
    y = basePosition.y - menuSize.height - 10; // 显示在选择文本上方
  }
  
  // 确保菜单不会超出左边界和上边界
  x = Math.max(10, x);
  y = Math.max(10, y);
  
  return { x, y };
}