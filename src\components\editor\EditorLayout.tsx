'use client';

import { useState, useCallback, useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import { MenuBar } from './MenuBar';
import { TopMenuBar } from './TopMenuBar';

interface EditorLayoutProps {
  initialContent?: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  onSave?: (content: string) => void;
  className?: string;
}

export function EditorLayout({
  initialContent = '',
  placeholder = '开始写作...',
  onChange,
  onSave,
  className = '',
}: EditorLayoutProps) {
  const [content, setContent] = useState(initialContent);
  const [documentTitle, setDocumentTitle] = useState('未命名文档');
  const [isSaving, setIsSaving] = useState(false);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
        codeBlock: {
          HTMLAttributes: {
            class: 'code-block',
          },
        },
      }),
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
      CharacterCount.configure({
        limit: null,
      }),
    ],
    content: initialContent,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      setContent(html);
      onChange?.(html);
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose-base lg:prose-lg xl:prose-xl mx-auto focus:outline-none max-w-none',
      },
    },
  });

  const handleSave = useCallback(async () => {
    if (onSave && content) {
      setIsSaving(true);
      try {
        await onSave(content);
      } finally {
        setIsSaving(false);
      }
    }
  }, [content, onSave]);

  const handleNew = useCallback(() => {
    if (editor) {
      editor.commands.setContent('');
      setDocumentTitle('未命名文档');
    }
  }, [editor]);

  const handleOpen = useCallback(() => {
    // TODO: Implement file opening functionality
    console.log('Opening file...');
  }, []);

  const handleExport = useCallback(() => {
    // TODO: Implement export functionality
    console.log('Exporting document...');
  }, []);

  const handleSettings = useCallback(() => {
    // TODO: Implement settings functionality
    console.log('Opening settings...');
  }, []);

  const handleProfile = useCallback(() => {
    // TODO: Implement profile functionality
    console.log('Opening profile...');
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault();
            handleSave();
            break;
          case 'n':
            event.preventDefault();
            handleNew();
            break;
          case 'o':
            event.preventDefault();
            handleOpen();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleSave, handleNew, handleOpen]);

  const getStats = useCallback(() => {
    if (!editor) return { words: 0, characters: 0 };
    return {
      words: editor.storage.characterCount.words(),
      characters: editor.storage.characterCount.characters(),
    };
  }, [editor]);

  if (!editor) {
    return (
      <div className={`flex flex-col h-full ${className}`}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-muted-foreground">加载编辑器...</div>
        </div>
      </div>
    );
  }

  const stats = getStats();

  return (
    <div className={`flex flex-col h-screen bg-background ${className}`}>
      {/* Fixed Top Menu Bar */}
      <TopMenuBar
        onNew={handleNew}
        onSave={handleSave}
        onOpen={handleOpen}
        onExport={handleExport}
        onSettings={handleSettings}
        onProfile={handleProfile}
        documentTitle={documentTitle}
        isSaving={isSaving}
      />

      {/* Content area with top padding to account for fixed menu */}
      <div className="flex flex-col flex-1 pt-12">
        {/* Formatting Toolbar */}
        <div className="sticky top-12 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <MenuBar editor={editor} />
        </div>

        {/* Editor Content Area */}
        <div className="flex-1 overflow-auto">
          <div className="max-w-4xl mx-auto">
            <EditorContent 
              editor={editor} 
              className="min-h-[calc(100vh-180px)] px-3 sm:px-6 py-4 sm:py-8 focus-within:outline-none touch-manipulation"
            />
          </div>
        </div>

        {/* Status Bar - 移动端简化显示 */}
        <div className="border-t border-border bg-muted/50 px-3 sm:px-6 py-2">
          <div className="max-w-4xl mx-auto flex justify-between items-center text-xs text-muted-foreground">
            <div className="flex items-center gap-2 sm:gap-4">
              <span className="hidden sm:inline">{stats.words} 词</span>
              <span className="hidden sm:inline">{stats.characters} 字符</span>
              {/* 移动端只显示字数 */}
              <span className="sm:hidden">{stats.words}词</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="hidden sm:inline">自动保存</span>
              <div className="w-2 h-2 bg-green-500 rounded-full" title="自动保存已启用"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}