'use client';

import React, { useState, useCallback } from 'react';
import { 
  DocumentAnalysisResult, 
  DocumentSummary, 
  KeywordExtraction, 
  DocumentOutline, 
  ContentAnalysis,
  SummaryLength,
  DocumentAnalysisType
} from '@/lib/services/ai/document-analysis-service';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  FileTextIcon, 
  TagIcon, 
  ListIcon, 
  BarChartIcon,
  CopyIcon,
  RefreshCwIcon,
  XIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from 'lucide-react';

/**
 * 文档分析面板的属性
 */
interface AIDocumentAnalysisPanelProps {
  /** 分析结果 */
  result: DocumentAnalysisResult;
  /** 是否显示 */
  visible: boolean;
  /** 关闭面板的回调 */
  onClose: () => void;
  /** 重新分析的回调 */
  onReanalyze: (type: DocumentAnalysisType) => void;
  /** 是否正在分析 */
  isAnalyzing?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 分析类型的显示配置
 */
const ANALYSIS_TYPE_CONFIG = {
  summary: { name: '文档摘要', icon: FileTextIcon, color: 'bg-blue-100 text-blue-800' },
  keywords: { name: '关键词提取', icon: TagIcon, color: 'bg-green-100 text-green-800' },
  outline: { name: '文档大纲', icon: ListIcon, color: 'bg-purple-100 text-purple-800' },
  analysis: { name: '内容分析', icon: BarChartIcon, color: 'bg-orange-100 text-orange-800' },
  topics: { name: '主题分析', icon: TagIcon, color: 'bg-pink-100 text-pink-800' }
};

/**
 * AI 文档分析面板组件
 * 显示文档分析的各种结果
 */
export function AIDocumentAnalysisPanel({
  result,
  visible,
  onClose,
  onReanalyze,
  isAnalyzing = false,
  className = ''
}: AIDocumentAnalysisPanelProps) {
  const [copiedText, setCopiedText] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['main']));

  /**
   * 复制文本到剪贴板
   */
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(text);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  }, []);

  /**
   * 切换章节展开状态
   */
  const toggleSection = useCallback((sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  }, []);

  /**
   * 渲染摘要结果
   */
  const renderSummary = useCallback((summary: DocumentSummary) => {
    return (
      <div className="space-y-4">
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-900 mb-2">文档摘要</h4>
          <p className="text-blue-800 leading-relaxed">{summary.summary}</p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{summary.originalWordCount}</div>
            <div className="text-sm text-gray-600">原文字数</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{summary.summaryWordCount}</div>
            <div className="text-sm text-gray-600">摘要字数</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{summary.compressionRatio}%</div>
            <div className="text-sm text-gray-600">压缩比例</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <Badge variant="outline" className="text-xs">
              {summary.length === 'short' ? '简短' : summary.length === 'medium' ? '中等' : '详细'}
            </Badge>
            <div className="text-sm text-gray-600 mt-1">摘要长度</div>
          </div>
        </div>
        
        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={() => copyToClipboard(summary.summary)}
            className="flex items-center gap-1"
          >
            <CopyIcon className="h-4 w-4" />
            {copiedText === summary.summary ? '已复制' : '复制摘要'}
          </Button>
        </div>
      </div>
    );
  }, [copyToClipboard, copiedText]);

  /**
   * 渲染关键词结果
   */
  const renderKeywords = useCallback((keywords: KeywordExtraction) => {
    return (
      <div className="space-y-4">
        {keywords.keywords.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">关键词</h4>
            <div className="flex flex-wrap gap-2">
              {keywords.keywords.map((keyword, index) => (
                <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-800">
                  {keyword}
                </Badge>
              ))}
            </div>
          </div>
        )}
        
        {keywords.topics.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">主题标签</h4>
            <div className="flex flex-wrap gap-2">
              {keywords.topics.map((topic, index) => (
                <Badge key={index} variant="secondary" className="bg-green-100 text-green-800">
                  {topic}
                </Badge>
              ))}
            </div>
          </div>
        )}
        
        {keywords.concepts.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">重要概念</h4>
            <div className="flex flex-wrap gap-2">
              {keywords.concepts.map((concept, index) => (
                <Badge key={index} variant="secondary" className="bg-purple-100 text-purple-800">
                  {concept}
                </Badge>
              ))}
            </div>
          </div>
        )}
        
        {keywords.terms.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">专业术语</h4>
            <div className="flex flex-wrap gap-2">
              {keywords.terms.map((term, index) => (
                <Badge key={index} variant="secondary" className="bg-orange-100 text-orange-800">
                  {term}
                </Badge>
              ))}
            </div>
          </div>
        )}
        
        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={() => copyToClipboard(
              `关键词：${keywords.keywords.join(', ')}\n主题：${keywords.topics.join(', ')}`
            )}
            className="flex items-center gap-1"
          >
            <CopyIcon className="h-4 w-4" />
            复制关键词
          </Button>
        </div>
      </div>
    );
  }, [copyToClipboard]);

  /**
   * 渲染大纲结果
   */
  const renderOutline = useCallback((outline: DocumentOutline) => {
    const renderOutlineItem = (item: any, index: number) => {
      const hasChildren = item.children && item.children.length > 0;
      const itemId = `outline-${index}`;
      const isExpanded = expandedSections.has(itemId);
      
      return (
        <div key={index} className="border-l-2 border-gray-200 pl-4 ml-2">
          <div className="flex items-start gap-2 py-1">
            {hasChildren && (
              <button
                onClick={() => toggleSection(itemId)}
                className="mt-1 text-gray-400 hover:text-gray-600"
              >
                {isExpanded ? (
                  <ChevronDownIcon className="h-4 w-4" />
                ) : (
                  <ChevronRightIcon className="h-4 w-4" />
                )}
              </button>
            )}
            <div className="flex-1">
              <div className="font-medium text-gray-900">{item.title}</div>
              {item.summary && (
                <div className="text-sm text-gray-600 mt-1">{item.summary}</div>
              )}
            </div>
            <Badge variant="outline" className="text-xs">
              H{item.level}
            </Badge>
          </div>
          {hasChildren && isExpanded && (
            <div className="mt-2 space-y-2">
              {item.children.map((child: any, childIndex: number) => 
                renderOutlineItem(child, childIndex)
              )}
            </div>
          )}
        </div>
      );
    };
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-gray-900">文档大纲</h4>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span>深度: {outline.depth}级</span>
            <span>章节: {outline.totalSections}个</span>
          </div>
        </div>
        
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {outline.items.map((item, index) => renderOutlineItem(item, index))}
        </div>
        
        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const outlineText = outline.items.map(item => 
                `${item.level}. ${item.title}${item.summary ? ` - ${item.summary}` : ''}`
              ).join('\n');
              copyToClipboard(outlineText);
            }}
            className="flex items-center gap-1"
          >
            <CopyIcon className="h-4 w-4" />
            复制大纲
          </Button>
        </div>
      </div>
    );
  }, [expandedSections, toggleSection, copyToClipboard]);

  /**
   * 渲染内容分析结果
   */
  const renderAnalysis = useCallback((analysis: ContentAnalysis) => {
    return (
      <div className="space-y-6">
        {/* 主要主题 */}
        <div>
          <h4 className="font-medium text-gray-900 mb-2">主要主题</h4>
          <div className="flex flex-wrap gap-2">
            {analysis.mainTopics.map((topic, index) => (
              <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-800">
                {topic}
              </Badge>
            ))}
          </div>
        </div>
        
        {/* 语调分析 */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">语调分析</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">整体语调</div>
              <div className="font-medium text-gray-900 capitalize">{analysis.tone.overall}</div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">情感倾向</div>
              <div className="font-medium text-gray-900 capitalize">{analysis.tone.sentiment}</div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">专业程度</div>
              <div className="font-medium text-gray-900 capitalize">{analysis.tone.professionalism}</div>
            </div>
          </div>
        </div>
        
        {/* 结构分析 */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">结构分析</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">文档类型</div>
              <div className="font-medium text-gray-900 capitalize">{analysis.structure.type}</div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">组织方式</div>
              <div className="font-medium text-gray-900 capitalize">{analysis.structure.organization}</div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">段落数量</div>
              <div className="font-medium text-gray-900">{analysis.structure.paragraphCount}</div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">平均段落长度</div>
              <div className="font-medium text-gray-900">{analysis.structure.avgParagraphLength}字</div>
            </div>
          </div>
        </div>
        
        {/* 可读性分析 */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">可读性分析</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">可读性评分</span>
                <span className="font-bold text-2xl text-gray-900">{analysis.readability.score}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${analysis.readability.score}%` }}
                ></div>
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">难度等级</div>
              <div className="font-medium text-gray-900 capitalize">{analysis.readability.level}</div>
            </div>
          </div>
          {analysis.readability.suggestions.length > 0 && (
            <div className="mt-3">
              <div className="text-sm text-gray-600 mb-2">改进建议：</div>
              <ul className="text-sm text-gray-700 space-y-1">
                {analysis.readability.suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-blue-600 mt-1">•</span>
                    <span>{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
        
        {/* 内容质量 */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">内容质量评估</h4>
          <div className="p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm text-gray-600">整体评分</span>
              <span className="font-bold text-3xl text-gray-900">{analysis.quality.score}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
              <div 
                className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full" 
                style={{ width: `${analysis.quality.score}%` }}
              ></div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {analysis.quality.strengths.length > 0 && (
                <div>
                  <div className="text-sm font-medium text-green-800 mb-2">优点</div>
                  <ul className="text-sm text-green-700 space-y-1">
                    {analysis.quality.strengths.map((strength, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-green-600 mt-1">✓</span>
                        <span>{strength}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              {analysis.quality.improvements.length > 0 && (
                <div>
                  <div className="text-sm font-medium text-orange-800 mb-2">改进建议</div>
                  <ul className="text-sm text-orange-700 space-y-1">
                    {analysis.quality.improvements.map((improvement, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-orange-600 mt-1">→</span>
                        <span>{improvement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }, []);

  if (!visible) return null;

  const typeConfig = ANALYSIS_TYPE_CONFIG[result.type];
  const IconComponent = typeConfig.icon;

  return (
    <div
      className={`
        fixed inset-0 bg-black/50 flex items-center justify-center z-50
        animate-in fade-in duration-200
        ${className}
      `}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden mx-4">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${typeConfig.color}`}>
              <IconComponent className="h-5 w-5" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{typeConfig.name}</h2>
              <p className="text-sm text-gray-600">
                分析时间: {result.timestamp.toLocaleString()} · 
                用时: {result.responseTime}ms · 
                令牌: {result.tokensUsed}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onReanalyze(result.type)}
              disabled={isAnalyzing}
              className="flex items-center gap-1"
            >
              <RefreshCwIcon className={`h-4 w-4 ${isAnalyzing ? 'animate-spin' : ''}`} />
              {isAnalyzing ? '分析中...' : '重新分析'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* 内容 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {result.summary && renderSummary(result.summary)}
          {result.keywords && renderKeywords(result.keywords)}
          {result.outline && renderOutline(result.outline)}
          {result.analysis && renderAnalysis(result.analysis)}
        </div>
      </div>
    </div>
  );
}

/**
 * 分析加载状态组件
 */
interface AIDocumentAnalysisLoadingProps {
  visible: boolean;
  onCancel: () => void;
  message?: string;
  type?: DocumentAnalysisType;
}

export function AIDocumentAnalysisLoading({
  visible,
  onCancel,
  message = 'AI 正在分析文档...',
  type
}: AIDocumentAnalysisLoadingProps) {
  if (!visible) return null;

  const typeConfig = type ? ANALYSIS_TYPE_CONFIG[type] : null;
  const IconComponent = typeConfig?.icon || BarChartIcon;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
        <div className="flex items-center gap-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600">
            <IconComponent className="h-4 w-4 text-blue-600" />
          </div>
          <div className="flex-1">
            <div className="font-medium text-gray-900">
              {typeConfig ? `正在${typeConfig.name}...` : '正在分析...'}
            </div>
            <div className="text-sm text-gray-600">{message}</div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="h-8 w-8 p-0"
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}