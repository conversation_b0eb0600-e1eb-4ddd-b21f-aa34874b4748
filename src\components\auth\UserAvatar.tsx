"use client";

import { useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { getInitials } from "@/lib/auth/utils";

/**
 * 用户头像组件属性接口
 */
interface UserAvatarProps {
  size?: "sm" | "md" | "lg"; // 头像尺寸：小、中、大
  showDropdown?: boolean;    // 是否显示下拉菜单
}

/**
 * 用户头像组件
 * 显示用户头像，支持下拉菜单功能
 * 如果用户没有上传头像，则显示姓名首字母
 * @param size 头像尺寸，默认为 "md"
 * @param showDropdown 是否显示下拉菜单，默认为 true
 */
export function UserAvatar({ size = "md", showDropdown = true }: UserAvatarProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { user, logout, isAuthenticated } = useAuth();

  if (!isAuthenticated || !user) {
    return null;
  }

  // 根据尺寸设置样式
  const sizeClasses = {
    sm: "w-8 h-8 text-sm",
    md: "w-10 h-10 text-base",
    lg: "w-12 h-12 text-lg",
  };

  const avatarClass = `${sizeClasses[size]} rounded-full bg-blue-500 text-white flex items-center justify-center font-medium cursor-pointer hover:bg-blue-600 transition-colors`;

  const handleLogout = async () => {
    try {
      await logout();
      setIsDropdownOpen(false);
    } catch (error) {
      console.error("退出登录失败:", error);
    }
  };

  return (
    <div className="relative">
      {/* 用户头像 */}
      <div
        className={avatarClass}
        onClick={() => showDropdown && setIsDropdownOpen(!isDropdownOpen)}
        title={user.name || user.email}
      >
        {user.image ? (
          <img
            src={user.image}
            alt={user.name || "用户头像"}
            className="w-full h-full rounded-full object-cover"
          />
        ) : (
          <span>{getInitials(user.name || user.email)}</span>
        )}
      </div>

      {/* 下拉菜单 */}
      {showDropdown && isDropdownOpen && (
        <>
          {/* 遮罩层 */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsDropdownOpen(false)}
          />
          
          {/* 菜单内容 */}
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-20 border border-gray-200">
            <div className="px-4 py-2 border-b border-gray-100">
              <p className="text-sm font-medium text-gray-900">
                {user.name || "未设置姓名"}
              </p>
              <p className="text-sm text-gray-500">{user.email}</p>
            </div>
            
            <button
              onClick={() => {
                setIsDropdownOpen(false);
                // TODO: 跳转到个人设置页面
              }}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              个人设置
            </button>
            
            <button
              onClick={handleLogout}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              退出登录
            </button>
          </div>
        </>
      )}
    </div>
  );
}