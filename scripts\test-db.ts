import { prisma } from '../src/lib/db/prisma'
import { createUser, getUserById } from '../src/lib/db/users'
import { createDocument, getDocumentById } from '../src/lib/db/documents'
import { createFolder, getFoldersByUserId } from '../src/lib/db/folders'

async function testDatabase() {
  console.log('🧪 Testing database connection and utilities...')

  try {
    // Test database connection
    await prisma.$connect()
    console.log('✅ Database connection successful')

    // Test user operations
    console.log('\n👤 Testing user operations...')
    const testUser = await createUser({
      email: '<EMAIL>',
      name: 'Test User',
      subscription: 'free'
    })
    console.log('✅ User created:', testUser.email)

    const retrievedUser = await getUserById(testUser.id)
    console.log('✅ User retrieved:', retrievedUser?.name)

    // Test folder operations
    console.log('\n📁 Testing folder operations...')
    const testFolder = await createFolder({
      name: 'Test Folder',
      userId: testUser.id
    })
    console.log('✅ Folder created:', testFolder.name)

    const userFolders = await getFoldersByUserId(testUser.id)
    console.log('✅ User folders retrieved:', userFolders.length)

    // Test document operations
    console.log('\n📄 Testing document operations...')
    const testDocument = await createDocument({
      title: 'Test Document',
      content: {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              { type: 'text', text: 'This is a test document.' }
            ]
          }
        ]
      },
      userId: testUser.id,
      folderId: testFolder.id
    })
    console.log('✅ Document created:', testDocument.title)

    const retrievedDocument = await getDocumentById(testDocument.id, testUser.id)
    console.log('✅ Document retrieved:', retrievedDocument?.title)
    console.log('   Word count:', retrievedDocument?.metadata.wordCount)
    console.log('   Character count:', retrievedDocument?.metadata.characterCount)

    // Test database queries
    console.log('\n📊 Testing database queries...')
    const userCount = await prisma.user.count()
    const documentCount = await prisma.document.count()
    const folderCount = await prisma.folder.count()
    
    console.log('✅ Database statistics:')
    console.log('   Users:', userCount)
    console.log('   Documents:', documentCount)
    console.log('   Folders:', folderCount)

    console.log('\n🎉 All database tests passed!')

  } catch (error) {
    console.error('❌ Database test failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

testDatabase()