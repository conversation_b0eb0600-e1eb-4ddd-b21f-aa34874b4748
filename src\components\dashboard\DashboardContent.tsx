'use client';

import { useState, useEffect } from 'react';
import { User } from 'next-auth';
import { DashboardHeader } from './DashboardHeader';
import { DashboardStats } from './DashboardStats';
import { RecentDocuments } from './RecentDocuments';
import { QuickActions } from './QuickActions';
import { UserProfile } from './UserProfile';
import { useDocuments } from '@/hooks/useDocuments';

interface DashboardContentProps {
  user: User;
}

export function DashboardContent({ user }: DashboardContentProps) {
  const [showProfile, setShowProfile] = useState(false);
  const userId = user.id || user.email || '';
  
  const {
    documents,
    loading,
    error,
    stats,
    createDocument,
    getRecentDocuments,
    refreshDocuments
  } = useDocuments({ 
    userId, 
    autoLoad: true 
  });

  const [recentDocuments, setRecentDocuments] = useState<any[]>([]);

  useEffect(() => {
    const loadRecentDocuments = async () => {
      if (userId) {
        const recent = await getRecentDocuments(6);
        setRecentDocuments(recent);
      }
    };
    loadRecentDocuments();
  }, [userId, getRecentDocuments]);

  const handleCreateDocument = async () => {
    const newDoc = await createDocument({
      title: `新文档 ${new Date().toLocaleString()}`,
      content: { type: 'doc', content: [] },
      userId,
      metadata: {
        wordCount: 0,
        characterCount: 0,
        tags: [],
        isPublic: false
      }
    });

    if (newDoc) {
      // 跳转到编辑器
      window.location.href = `/editor?id=${newDoc.id}`;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader 
        user={user} 
        onShowProfile={() => setShowProfile(true)}
      />

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 欢迎信息 */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900">
              欢迎回来，{user.name || user.email?.split('@')[0]}！
            </h1>
            <p className="text-gray-600 mt-1">
              继续您的写作之旅，或者创建新的文档开始工作。
            </p>
          </div>

          {/* 统计卡片 */}
          <DashboardStats 
            stats={stats}
            loading={loading}
          />

          {/* 快速操作 */}
          <QuickActions 
            onCreateDocument={handleCreateDocument}
            onRefresh={refreshDocuments}
          />

          {/* 最近文档 */}
          <RecentDocuments 
            documents={recentDocuments}
            loading={loading}
            error={error}
            onRefresh={refreshDocuments}
          />
        </div>
      </main>

      {/* 用户资料侧边栏 */}
      {showProfile && (
        <UserProfile 
          user={user}
          onClose={() => setShowProfile(false)}
        />
      )}
    </div>
  );
}