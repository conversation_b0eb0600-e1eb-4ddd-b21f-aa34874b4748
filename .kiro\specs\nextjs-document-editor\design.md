# 设计文档

## 概述

本文档描述了基于 Next.js 的智能文档编辑器的技术设计。该应用程序将提供现代化的文档编辑体验，集成多种 AI 服务以提供智能写作辅助功能。设计重点关注可扩展性、性能和用户体验。

## 架构

### 整体架构

```mermaid
graph TB
    subgraph "客户端"
        A[用户界面层] --> B[业务逻辑层]
        B --> C[客户端服务层]
        C --> D[本地数据层]
    end
    
    subgraph "服务端"
        E[API 层] --> F[业务服务层]
        F --> G[数据访问层]
        G --> H[数据库层]
    end
    
    C --> E
    
    A --> A1[React 组件]
    A --> A2[编辑器组件]
    A --> A3[AI 交互组件]
    
    B --> B1[状态管理]
    B --> B2[文档管理]
    B --> B3[同步管理]
    
    C --> C1[AI 服务]
    C --> C2[同步服务]
    C --> C3[本地存储]
    
    E --> E1[认证 API]
    E --> E2[文档 API]
    E --> E3[AI 配置 API]
    
    F --> F1[用户服务]
    F --> F2[文档服务]
    F --> F3[同步服务]
    
    H --> H1[SQLite/PostgreSQL]
    H --> H2[文件存储]
```

### 技术栈

**前端**：
- **框架**：Next.js 14 (App Router)
- **UI 库**：React 18
- **文本编辑器**：TipTap (基于 ProseMirror)
- **状态管理**：Zustand
- **样式**：Tailwind CSS + CSS Modules
- **类型检查**：TypeScript
- **本地存储**：IndexedDB (Dexie.js) + localStorage

**后端**：
- **框架**：Next.js API Routes / Express.js
- **数据库**：SQLite (开发) / PostgreSQL (生产)
- **ORM**：Prisma
- **认证**：NextAuth.js
- **文件存储**：本地文件系统 / AWS S3
- **API 设计**：RESTful API + tRPC (可选)

## 组件和接口

### 核心组件结构

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # 认证相关页面
│   │   ├── login/         # 登录页面
│   │   └── register/      # 注册页面
│   ├── api/               # API 路由
│   │   ├── auth/          # 认证 API
│   │   ├── documents/     # 文档 API
│   │   ├── folders/       # 文件夹 API
│   │   └── ai-config/     # AI 配置 API
│   ├── dashboard/         # 仪表板页面
│   ├── editor/[id]/       # 编辑器页面
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 主页面
│   └── globals.css        # 全局样式
├── components/            # React 组件
│   ├── auth/              # 认证组件
│   │   ├── LoginForm.tsx
│   │   └── RegisterForm.tsx
│   ├── editor/            # 编辑器相关组件
│   │   ├── Editor.tsx     # 主编辑器组件
│   │   ├── MenuBar.tsx    # 菜单栏
│   │   ├── Toolbar.tsx    # 工具栏
│   │   ├── SlashCommand.tsx # 斜杠命令
│   │   └── SelectionMenu.tsx # 选择文本菜单
│   ├── ai/                # AI 功能组件
│   │   ├── AIPanel.tsx    # AI 助手面板
│   │   ├── AISettings.tsx # AI 配置
│   │   ├── AIResults.tsx  # AI 结果显示
│   │   └── ChatEditMenu.tsx # Chat/Edit 菜单
│   ├── file-manager/      # 文件管理组件
│   │   ├── FileTree.tsx   # 文件树
│   │   ├── FolderNode.tsx # 文件夹节点
│   │   ├── FileNode.tsx   # 文件节点
│   │   └── ContextMenu.tsx # 右键菜单
│   └── ui/                # 通用 UI 组件
├── lib/                   # 核心库和工具
│   ├── ai/               # AI 服务
│   ├── auth/             # 认证服务
│   ├── storage/          # 存储服务
│   ├── sync/             # 同步服务
│   ├── db/               # 数据库操作
│   └── utils/            # 工具函数
├── stores/               # 状态管理
├── types/                # TypeScript 类型定义
├── hooks/                # 自定义 React Hooks
└── prisma/               # 数据库 Schema
    ├── schema.prisma
    └── migrations/
```

### 主要接口定义

#### 用户认证接口
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  subscription: 'free' | 'pro' | 'enterprise';
}

interface AuthSession {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}
```

#### 文件夹和文档接口
```typescript
interface Folder {
  id: string;
  name: string;
  parentId?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  children: (Folder | Document)[];
}

interface Document {
  id: string;
  title: string;
  content: JSONContent; // TipTap JSON 格式
  folderId?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  lastSyncAt?: Date;
  metadata: DocumentMetadata;
}

interface DocumentMetadata {
  wordCount: number;
  characterCount: number;
  lastAIInteraction?: Date;
  tags: string[];
  isPublic: boolean;
  shareToken?: string;
}
```

#### 斜杠命令接口
```typescript
interface SlashCommand {
  id: string;
  label: string;
  description: string;
  icon: string;
  category: 'basic' | 'ai' | 'media' | 'advanced';
  shortcut?: string;
  action: (editor: Editor, range: Range) => void;
}

interface SlashCommandCategory {
  name: string;
  commands: SlashCommand[];
}

// 预定义的斜杠命令
const SLASH_COMMANDS: SlashCommandCategory[] = [
  {
    name: '基础',
    commands: [
      { id: 'heading1', label: '标题 1', description: '大标题', icon: 'H1', category: 'basic' },
      { id: 'heading2', label: '标题 2', description: '中标题', icon: 'H2', category: 'basic' },
      { id: 'bullet-list', label: '无序列表', description: '创建无序列表', icon: '•', category: 'basic' },
      { id: 'numbered-list', label: '有序列表', description: '创建有序列表', icon: '1.', category: 'basic' },
      { id: 'quote', label: '引用', description: '创建引用块', icon: '"', category: 'basic' },
      { id: 'code-block', label: '代码块', description: '插入代码块', icon: '</>', category: 'basic' },
    ]
  },
  {
    name: 'AI 功能',
    commands: [
      { id: 'ai-continue', label: 'AI 续写', description: '让 AI 继续写作', icon: '✨', category: 'ai' },
      { id: 'ai-rewrite', label: 'AI 改写', description: '改写选中文本', icon: '🔄', category: 'ai' },
      { id: 'ai-summarize', label: 'AI 总结', description: '总结文档内容', icon: '📝', category: 'ai' },
      { id: 'ai-translate', label: 'AI 翻译', description: '翻译选中文本', icon: '🌐', category: 'ai' },
      { id: 'ai-explain', label: 'AI 解释', description: '解释复杂概念', icon: '💡', category: 'ai' },
    ]
  },
  {
    name: '媒体',
    commands: [
      { id: 'image', label: '图片', description: '插入图片', icon: '🖼️', category: 'media' },
      { id: 'link', label: '链接', description: '插入链接', icon: '🔗', category: 'media' },
      { id: 'pdf', label: 'PDF', description: '插入 PDF 文件', icon: '📄', category: 'media' },
      { id: 'video', label: '视频', description: '插入视频', icon: '🎥', category: 'media' },
    ]
  },
  {
    name: '高级',
    commands: [
      { id: 'table', label: '表格', description: '插入表格', icon: '📊', category: 'advanced' },
      { id: 'divider', label: '分割线', description: '插入分割线', icon: '—', category: 'advanced' },
      { id: 'callout', label: '提示框', description: '插入提示框', icon: '💬', category: 'advanced' },
      { id: 'toggle', label: '折叠块', description: '创建可折叠内容', icon: '▶️', category: 'advanced' },
    ]
  }
];
```

#### AI 服务接口
```typescript
interface AIService {
  name: string;
  type: 'openai' | 'ollama' | 'gemini';
  generateText(prompt: string, context?: string): Promise<string>;
  rewriteText(text: string, style?: string): Promise<string[]>;
  summarizeText(text: string): Promise<string>;
  analyzeText(text: string): Promise<TextAnalysis>;
  translateText(text: string, targetLanguage: string): Promise<string>;
  explainText(text: string): Promise<string>;
}

interface AIConfiguration {
  id: string;
  userId: string;
  provider: string;
  apiKey?: string;
  endpoint?: string;
  model: string;
  maxTokens: number;
  temperature: number;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface TextAnalysis {
  sentiment: 'positive' | 'negative' | 'neutral';
  keyWords: string[];
  topics: string[];
  readabilityScore: number;
  suggestions: string[];
}
```

#### 同步服务接口
```typescript
interface SyncService {
  syncDocument(documentId: string): Promise<SyncResult>;
  syncAllDocuments(): Promise<SyncResult[]>;
  syncAIConfiguration(): Promise<void>;
  resolveConflict(conflict: SyncConflict): Promise<void>;
}

interface SyncResult {
  documentId: string;
  status: 'success' | 'conflict' | 'error';
  lastSyncAt: Date;
  conflict?: SyncConflict;
}

interface SyncConflict {
  documentId: string;
  localVersion: JSONContent;
  remoteVersion: JSONContent;
  conflictType: 'content' | 'metadata';
  timestamp: Date;
}
```

## 数据模型

### 数据库设计 (Prisma Schema)

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id           String   @id @default(cuid())
  email        String   @unique
  name         String
  avatar       String?
  subscription String   @default("free") // free, pro, enterprise
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  folders       Folder[]
  documents     Document[]
  aiConfigs     AIConfiguration[]
  aiInteractions AIInteraction[]

  @@map("users")
}

model Folder {
  id        String   @id @default(cuid())
  name      String
  parentId  String?
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent    Folder?    @relation("FolderHierarchy", fields: [parentId], references: [id])
  children  Folder[]   @relation("FolderHierarchy")
  documents Document[]

  @@map("folders")
}

model Document {
  id          String    @id @default(cuid())
  title       String
  content     String    // JSON string of TipTap content
  folderId    String?
  userId      String
  wordCount   Int       @default(0)
  charCount   Int       @default(0)
  isPublic    Boolean   @default(false)
  shareToken  String?   @unique
  lastSyncAt  DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  user         User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  folder       Folder?           @relation(fields: [folderId], references: [id])
  history      DocumentHistory[]
  aiInteractions AIInteraction[]

  @@map("documents")
}

model DocumentHistory {
  id         String   @id @default(cuid())
  documentId String
  version    Int
  content    String   // JSON string
  changeType String   // user, ai
  createdAt  DateTime @default(now())

  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@map("document_history")
}

model AIConfiguration {
  id          String   @id @default(cuid())
  userId      String
  provider    String   // openai, ollama, gemini
  apiKey      String?
  endpoint    String?
  model       String
  maxTokens   Int      @default(2000)
  temperature Float    @default(0.7)
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ai_configurations")
}

model AIInteraction {
  id         String   @id @default(cuid())
  documentId String
  userId     String
  type       String   // generate, rewrite, summarize, analyze, translate, explain
  input      String
  output     String
  provider   String
  model      String
  tokens     Int      @default(0)
  createdAt  DateTime @default(now())

  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ai_interactions")
}
```

### 文档数据模型

```typescript
// 文档实体
class DocumentEntity {
  id: string;
  title: string;
  content: JSONContent; // TipTap 的 JSON 格式
  folderId?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  lastSyncAt?: Date;
  metadata: DocumentMetadata;
  
  // 方法
  updateContent(content: JSONContent): void;
  getWordCount(): number;
  getPlainText(): string;
  sync(): Promise<SyncResult>;
}

// 文件夹实体
class FolderEntity {
  id: string;
  name: string;
  parentId?: string;
  userId: string;
  children: (FolderEntity | DocumentEntity)[];
  
  // 方法
  addChild(item: FolderEntity | DocumentEntity): void;
  removeChild(id: string): void;
  rename(newName: string): void;
  getPath(): string[];
}
```

### 同步机制设计

```typescript
// 同步状态管理
interface SyncState {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncAt?: Date;
  pendingChanges: PendingChange[];
  conflicts: SyncConflict[];
}

interface PendingChange {
  id: string;
  type: 'create' | 'update' | 'delete';
  entityType: 'document' | 'folder' | 'ai-config';
  entityId: string;
  data: any;
  timestamp: Date;
}

// 同步策略
class SyncManager {
  // 自动同步策略
  async autoSync(): Promise<void> {
    // 每 30 秒检查一次待同步项目
    // 网络恢复时立即同步
    // 用户操作后延迟 5 秒同步
  }
  
  // 手动同步
  async manualSync(): Promise<SyncResult[]> {
    // 用户点击同步按钮时执行
    // 显示同步进度
    // 处理冲突
  }
  
  // 冲突解决
  async resolveConflict(conflict: SyncConflict, resolution: 'local' | 'remote' | 'merge'): Promise<void> {
    // 根据用户选择解决冲突
  }
}
```

### 选择文本菜单设计

```typescript
// 选择文本时显示的菜单
interface SelectionMenuAction {
  id: string;
  label: string;
  icon: string;
  type: 'chat' | 'edit' | 'format';
  action: (selectedText: string, editor: Editor) => void;
}

const SELECTION_MENU_ACTIONS: SelectionMenuAction[] = [
  // Chat 功能
  {
    id: 'chat-explain',
    label: '解释',
    icon: '💡',
    type: 'chat',
    action: (text, editor) => openAIChat('explain', text)
  },
  {
    id: 'chat-translate',
    label: '翻译',
    icon: '🌐',
    type: 'chat',
    action: (text, editor) => openAIChat('translate', text)
  },
  {
    id: 'chat-summarize',
    label: '总结',
    icon: '📝',
    type: 'chat',
    action: (text, editor) => openAIChat('summarize', text)
  },
  
  // Edit 功能
  {
    id: 'edit-rewrite',
    label: '改写',
    icon: '🔄',
    type: 'edit',
    action: (text, editor) => aiRewrite(text, editor)
  },
  {
    id: 'edit-improve',
    label: '优化',
    icon: '✨',
    type: 'edit',
    action: (text, editor) => aiImprove(text, editor)
  },
  {
    id: 'edit-simplify',
    label: '简化',
    icon: '📋',
    type: 'edit',
    action: (text, editor) => aiSimplify(text, editor)
  },
  
  // 格式化功能
  {
    id: 'format-bold',
    label: '加粗',
    icon: 'B',
    type: 'format',
    action: (text, editor) => editor.chain().focus().toggleBold().run()
  },
  {
    id: 'format-italic',
    label: '斜体',
    icon: 'I',
    type: 'format',
    action: (text, editor) => editor.chain().focus().toggleItalic().run()
  }
];

// 选择菜单组件逻辑
class SelectionMenu {
  show(position: { x: number, y: number }, selectedText: string): void;
  hide(): void;
  executeAction(actionId: string, selectedText: string): void;
}
```

### AI 文件目录功能

```typescript
// AI 增强的文件管理功能
interface AIFileFeatures {
  // 智能文件夹建议
  suggestFolderStructure(documents: Document[]): FolderSuggestion[];
  
  // 自动分类文档
  categorizeDocument(document: Document): string[];
  
  // 智能重命名
  suggestFileName(content: string): string[];
  
  // 相关文档推荐
  findRelatedDocuments(documentId: string): Document[];
  
  // 文档摘要生成
  generateDocumentSummary(documentId: string): Promise<string>;
}

interface FolderSuggestion {
  name: string;
  description: string;
  documentIds: string[];
  confidence: number;
}

// AI 文件管理助手
class AIFileManager implements AIFileFeatures {
  async suggestFolderStructure(documents: Document[]): Promise<FolderSuggestion[]> {
    // 分析文档内容，建议文件夹结构
    // 基于主题、类型、日期等维度
  }
  
  async categorizeDocument(document: Document): Promise<string[]> {
    // 自动为文档添加标签和分类
  }
  
  async suggestFileName(content: string): Promise<string[]> {
    // 基于内容生成合适的文件名建议
  }
}
```

## 错误处理

### 错误类型定义

```typescript
enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AI_SERVICE_ERROR = 'AI_SERVICE_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR'
}

interface AppError {
  type: ErrorType;
  message: string;
  details?: any;
  timestamp: Date;
  recoverable: boolean;
}
```

### 错误处理策略

1. **AI 服务错误**：
   - 网络超时：自动重试 3 次，指数退避
   - API 限制：显示用户友好的错误信息
   - 服务不可用：切换到备用服务或离线模式

2. **存储错误**：
   - 自动备份到多个存储位置
   - 数据损坏时从历史版本恢复
   - 存储空间不足时清理旧数据

3. **用户界面错误**：
   - 使用 Error Boundary 捕获组件错误
   - 提供错误恢复选项
   - 保存用户数据防止丢失

## 测试策略

### 测试层次

1. **单元测试**：
   - AI 服务适配器
   - 文档操作逻辑
   - 工具函数
   - 使用 Jest + Testing Library

2. **集成测试**：
   - AI 服务集成
   - 存储服务集成
   - 组件交互测试

3. **端到端测试**：
   - 用户工作流测试
   - AI 功能完整流程
   - 使用 Playwright

### 测试数据管理

```typescript
// 测试用的 Mock AI 服务
class MockAIService implements AIService {
  async generateText(prompt: string): Promise<string> {
    return `Generated response for: ${prompt}`;
  }
  
  async rewriteText(text: string): Promise<string[]> {
    return [`Rewritten: ${text}`, `Alternative: ${text}`];
  }
}

// 测试文档工厂
class DocumentFactory {
  static createSample(): Document {
    return {
      id: 'test-doc-1',
      title: 'Test Document',
      content: 'Sample content',
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        wordCount: 2,
        characterCount: 14,
        tags: ['test']
      }
    };
  }
}
```

### 性能测试

1. **编辑器性能**：
   - 大文档加载时间 < 2 秒
   - 输入响应延迟 < 50ms
   - 内存使用监控

2. **AI 服务性能**：
   - API 响应时间监控
   - 并发请求处理
   - 错误率监控

## 安全考虑

### API 密钥管理
- 客户端加密存储
- 不在日志中记录敏感信息
- 支持环境变量配置

### 数据隐私
- 本地优先的数据存储
- 用户控制 AI 服务使用
- 清晰的数据使用说明

### 内容安全
- 输入验证和清理
- XSS 防护
- CSP 策略配置

## 部署和扩展

### 部署架构
- 静态站点生成 (SSG) 用于核心页面
- 客户端渲染 (CSR) 用于编辑器
- 支持 Vercel、Netlify 等平台

### 扩展性设计
- 插件系统架构预留
- 主题系统支持
- 多语言国际化准备
- 协作功能接口预留