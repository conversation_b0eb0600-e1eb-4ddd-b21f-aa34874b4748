/**
 * AI 处理状态管理器
 * 管理 AI 请求的处理状态、进度和结果
 */

import { EventEmitter } from 'events';
import {
  AIProcessingStatus,
  type AIProcessingProgress,
  type AIProcessingResult,
  type AIProcessingContext,
  type AIProcessingManager,
  type AIProcessingStage,
  type AIProcessingEvent,
  type AIProcessingStats,
} from '@/types/ai-status.types';

/**
 * AI 处理状态管理器实现
 */
export class AIProcessingManagerImpl extends EventEmitter implements AIProcessingManager {
  private processingMap = new Map<string, AIProcessingProgress>();
  private contextMap = new Map<string, AIProcessingContext>();
  private statsData: AIProcessingStats = {
    totalProcessed: 0,
    successCount: 0,
    errorCount: 0,
    cancelledCount: 0,
    averageProcessingTime: 0,
    totalTokensUsed: 0,
    byType: {},
    byProvider: {},
  };

  /**
   * 开始处理
   */
  async startProcessing(context: AIProcessingContext): Promise<void> {
    const { id, type } = context;

    // 创建初始进度状态
    const initialProgress: AIProcessingProgress = {
      status: AIProcessingStatus.PREPARING,
      overallProgress: 0,
      currentStage: 'preparing',
      stages: this.createProcessingStages(type),
      startTime: new Date(),
      cancellable: true,
    };

    // 存储上下文和进度
    this.contextMap.set(id, context);
    this.processingMap.set(id, initialProgress);

    // 发送开始事件
    this.emitEvent(id, {
      type: 'progress',
      data: initialProgress,
      timestamp: new Date(),
    });

    // 模拟处理阶段
    await this.simulateProcessingStages(id);
  }

  /**
   * 更新进度
   */
  updateProgress(id: string, progress: Partial<AIProcessingProgress>): void {
    const currentProgress = this.processingMap.get(id);
    if (!currentProgress) return;

    const updatedProgress = { ...currentProgress, ...progress };
    this.processingMap.set(id, updatedProgress);

    // 发送进度更新事件
    this.emitEvent(id, {
      type: 'progress',
      data: updatedProgress,
      timestamp: new Date(),
    });
  }

  /**
   * 完成处理
   */
  completeProcessing(id: string, result: AIProcessingResult): void {
    const currentProgress = this.processingMap.get(id);
    const context = this.contextMap.get(id);
    
    if (!currentProgress || !context) return;

    // 更新最终状态
    const completedProgress: AIProcessingProgress = {
      ...currentProgress,
      status: AIProcessingStatus.COMPLETED,
      overallProgress: 100,
      currentStage: 'completed',
      stages: currentProgress.stages.map(stage => ({
        ...stage,
        completed: true,
        progress: 100,
      })),
      tokensProcessed: result.tokensUsed,
    };

    this.processingMap.set(id, completedProgress);

    // 更新统计信息
    this.updateStats(context, result, 'success');

    // 发送完成事件
    this.emitEvent(id, {
      type: 'complete',
      data: { progress: completedProgress, result },
      timestamp: new Date(),
    });

    // 延迟清理
    setTimeout(() => {
      this.cleanup(id);
    }, context.options.autoHideDelay || 5000);
  }

  /**
   * 处理错误
   */
  handleError(id: string, error: Error): void {
    const currentProgress = this.processingMap.get(id);
    const context = this.contextMap.get(id);
    
    if (!currentProgress || !context) return;

    // 更新错误状态
    const errorProgress: AIProcessingProgress = {
      ...currentProgress,
      status: AIProcessingStatus.ERROR,
      error: error.message,
      cancellable: false,
    };

    this.processingMap.set(id, errorProgress);

    // 更新统计信息
    this.updateStats(context, null, 'error');

    // 发送错误事件
    this.emitEvent(id, {
      type: 'error',
      data: { progress: errorProgress, error },
      timestamp: new Date(),
    });
  }

  /**
   * 取消处理
   */
  cancelProcessing(id: string): void {
    const currentProgress = this.processingMap.get(id);
    const context = this.contextMap.get(id);
    
    if (!currentProgress || !context) return;

    // 取消请求
    if (context.cancelToken) {
      context.cancelToken.abort();
    }

    // 更新取消状态
    const cancelledProgress: AIProcessingProgress = {
      ...currentProgress,
      status: AIProcessingStatus.CANCELLED,
      cancellable: false,
    };

    this.processingMap.set(id, cancelledProgress);

    // 更新统计信息
    this.updateStats(context, null, 'cancelled');

    // 发送取消事件
    this.emitEvent(id, {
      type: 'cancel',
      data: cancelledProgress,
      timestamp: new Date(),
    });

    // 立即清理
    setTimeout(() => {
      this.cleanup(id);
    }, 1000);
  }

  /**
   * 获取处理状态
   */
  getProcessingStatus(id: string): AIProcessingProgress | null {
    return this.processingMap.get(id) || null;
  }

  /**
   * 订阅状态变化
   */
  subscribe(id: string, callback: (progress: AIProcessingProgress) => void): () => void {
    const eventHandler = (event: AIProcessingEvent) => {
      if (event.type === 'progress' || event.type === 'complete') {
        callback(event.data.progress || event.data);
      }
    };

    this.on(`progress:${id}`, eventHandler);
    this.on(`complete:${id}`, eventHandler);
    this.on(`error:${id}`, eventHandler);
    this.on(`cancel:${id}`, eventHandler);

    // 返回取消订阅函数
    return () => {
      this.off(`progress:${id}`, eventHandler);
      this.off(`complete:${id}`, eventHandler);
      this.off(`error:${id}`, eventHandler);
      this.off(`cancel:${id}`, eventHandler);
    };
  }

  /**
   * 获取统计信息
   */
  getStats(): AIProcessingStats {
    return { ...this.statsData };
  }

  /**
   * 获取所有活动的处理状态
   */
  getAllActiveProcessing(): Map<string, AIProcessingProgress> {
    return new Map(this.processingMap);
  }

  /**
   * 创建处理阶段
   */
  private createProcessingStages(type: string): AIProcessingStage[] {
    const baseStages: AIProcessingStage[] = [
      {
        id: 'preparing',
        name: '准备中',
        description: '正在准备 AI 请求',
        completed: false,
        current: true,
        progress: 0,
      },
      {
        id: 'connecting',
        name: '连接中',
        description: '正在连接 AI 服务',
        completed: false,
        current: false,
        progress: 0,
      },
      {
        id: 'processing',
        name: '处理中',
        description: 'AI 正在处理您的请求',
        completed: false,
        current: false,
        progress: 0,
      },
      {
        id: 'generating',
        name: '生成中',
        description: '正在生成响应内容',
        completed: false,
        current: false,
        progress: 0,
      },
      {
        id: 'completed',
        name: '完成',
        description: '处理完成',
        completed: false,
        current: false,
        progress: 0,
      },
    ];

    // 根据类型调整阶段
    switch (type) {
      case 'translate':
        baseStages[2].description = 'AI 正在翻译文本';
        baseStages[3].description = '正在生成翻译结果';
        break;
      case 'rewrite':
        baseStages[2].description = 'AI 正在改写文本';
        baseStages[3].description = '正在生成改写结果';
        break;
      case 'summarize':
        baseStages[2].description = 'AI 正在分析文档';
        baseStages[3].description = '正在生成摘要';
        break;
      case 'generate':
        baseStages[2].description = 'AI 正在理解上下文';
        baseStages[3].description = '正在生成内容';
        break;
    }

    return baseStages;
  }

  /**
   * 模拟处理阶段
   */
  private async simulateProcessingStages(id: string): Promise<void> {
    const progress = this.processingMap.get(id);
    const context = this.contextMap.get(id);
    if (!progress || !context) return;

    const stages = progress.stages;
    const totalStages = stages.length - 1; // 排除完成阶段

    for (let i = 0; i < totalStages; i++) {
      // 检查是否被取消
      if (context.cancelToken?.signal.aborted) {
        break;
      }

      // 更新当前阶段
      const updatedStages = stages.map((stage, index) => ({
        ...stage,
        completed: index < i,
        current: index === i,
        progress: index < i ? 100 : index === i ? 0 : 0,
        startTime: index === i ? new Date() : stage.startTime,
      }));

      this.updateProgress(id, {
        status: this.getStatusForStage(stages[i].id),
        currentStage: stages[i].id,
        stages: updatedStages,
        overallProgress: (i / totalStages) * 100,
      });

      // 模拟阶段处理时间
      const stageTime = this.getStageProcessingTime(stages[i].id);
      await this.simulateStageProgress(id, i, stageTime);
    }

    // 如果没有被取消，自动完成处理
    if (!context.cancelToken?.signal.aborted) {
      const mockResult = {
        id,
        type: context.type,
        input: context.input,
        output: `模拟的 ${context.type} 输出结果`,
        provider: 'mock',
        model: 'mock-model',
        tokensUsed: Math.floor(Math.random() * 200) + 50,
        processingTime: Date.now() - context.startTime.getTime(),
        createdAt: new Date(),
      };

      this.completeProcessing(id, mockResult);
    }
  }

  /**
   * 模拟阶段进度
   */
  private async simulateStageProgress(id: string, stageIndex: number, duration: number): Promise<void> {
    const steps = 10;
    const stepDuration = duration / steps;

    for (let step = 0; step <= steps; step++) {
      const context = this.contextMap.get(id);
      if (!context || context.cancelToken?.signal.aborted) {
        break;
      }

      const progress = this.processingMap.get(id);
      if (!progress) break;

      const stageProgress = (step / steps) * 100;
      const overallProgress = ((stageIndex + step / steps) / (progress.stages.length - 1)) * 100;

      // 更新阶段进度
      const updatedStages = progress.stages.map((stage, index) => ({
        ...stage,
        progress: index === stageIndex ? stageProgress : stage.progress,
      }));

      this.updateProgress(id, {
        stages: updatedStages,
        overallProgress,
        estimatedTimeRemaining: duration - (step * stepDuration),
      });

      if (step < steps) {
        await new Promise(resolve => setTimeout(resolve, stepDuration));
      }
    }
  }

  /**
   * 获取阶段对应的状态
   */
  private getStatusForStage(stageId: string): AIProcessingStatus {
    switch (stageId) {
      case 'preparing':
        return AIProcessingStatus.PREPARING;
      case 'connecting':
        return AIProcessingStatus.CONNECTING;
      case 'processing':
        return AIProcessingStatus.PROCESSING;
      case 'generating':
        return AIProcessingStatus.GENERATING;
      default:
        return AIProcessingStatus.PROCESSING;
    }
  }

  /**
   * 获取阶段处理时间
   */
  private getStageProcessingTime(stageId: string): number {
    switch (stageId) {
      case 'preparing':
        return 500;
      case 'connecting':
        return 800;
      case 'processing':
        return 2000;
      case 'generating':
        return 1500;
      default:
        return 1000;
    }
  }

  /**
   * 发送事件
   */
  private emitEvent(id: string, event: AIProcessingEvent): void {
    this.emit(`${event.type}:${id}`, event);
    this.emit('global', { id, ...event });
  }

  /**
   * 更新统计信息
   */
  private updateStats(
    context: AIProcessingContext,
    result: AIProcessingResult | null,
    outcome: 'success' | 'error' | 'cancelled'
  ): void {
    this.statsData.totalProcessed++;

    switch (outcome) {
      case 'success':
        this.statsData.successCount++;
        if (result) {
          this.statsData.totalTokensUsed += result.tokensUsed;
          this.statsData.averageProcessingTime = 
            (this.statsData.averageProcessingTime * (this.statsData.successCount - 1) + result.processingTime) / 
            this.statsData.successCount;
        }
        break;
      case 'error':
        this.statsData.errorCount++;
        break;
      case 'cancelled':
        this.statsData.cancelledCount++;
        break;
    }

    // 按类型统计
    this.statsData.byType[context.type] = (this.statsData.byType[context.type] || 0) + 1;

    // 按提供商统计（如果有结果）
    if (result) {
      this.statsData.byProvider[result.provider] = (this.statsData.byProvider[result.provider] || 0) + 1;
    }
  }

  /**
   * 清理资源
   */
  private cleanup(id: string): void {
    this.processingMap.delete(id);
    this.contextMap.delete(id);
    this.removeAllListeners(`progress:${id}`);
    this.removeAllListeners(`complete:${id}`);
    this.removeAllListeners(`error:${id}`);
    this.removeAllListeners(`cancel:${id}`);
  }
}

// 单例实例
export const aiProcessingManager = new AIProcessingManagerImpl();