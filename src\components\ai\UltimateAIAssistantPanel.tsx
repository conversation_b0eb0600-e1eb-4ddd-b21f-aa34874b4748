'use client';

import React, { useState, useC<PERSON>back, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  PanelRightIcon,
  PanelLeftIcon,
  SparklesIcon,
  EditIcon,
  FileTextIcon,
  LanguagesIcon,
  MessageSquareIcon,
  SettingsIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  HelpCircleIcon,
  PenToolIcon,
  BarChartIcon,
  TagIcon,
  ListIcon,
  TerminalIcon,
  BookOpenIcon,
  LightbulbIcon,
  RefreshCwIcon,
  XIcon,
  SearchIcon,
  StarIcon,
  HistoryIcon,
  CloudIcon,
  BrainIcon,
  EyeIcon,
  ShieldIcon,
  ZapIcon,
  ClockIcon,
  TrendingUpIcon,
  FilterIcon,
  MaximizeIcon,
  MinimizeIcon,
  MoreHorizontalIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  InfoIcon
} from 'lucide-react';

/**
 * AI 功能类别定义
 */
interface AIFeatureCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  features: AIFeature[];
}

/**
 * AI 功能定义
 */
interface AIFeature {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  shortcut?: string;
  action: () => void;
  disabled?: boolean;
  category: string;
  tags: string[];
  popularity?: number;
}

/**
 * 终极版 AI 助手面板属性
 */
interface UltimateAIAssistantPanelProps {
  /** 是否显示面板 */
  isOpen: boolean;
  /** 切换面板显示状态 */
  onToggle: () => void;
  /** 面板位置 */
  position?: 'left' | 'right';
  /** 面板宽度 */
  width?: number;
  /** 是否为移动端 */
  isMobile?: boolean;
  /** 自定义类名 */
  className?: string;
  /** AI 功能回调 */
  onAIAction?: (actionId: string, data?: any) => void;
  /** 当前选中的文本 */
  selectedText?: string;
  /** 是否正在处理 AI 请求 */
  isProcessing?: boolean;
  /** 处理状态信息 */
  processingStatus?: string;
  /** 启用高级功能 */
  enableAdvancedFeatures?: boolean;
  /** 启用历史记录跟踪 */
  enableHistoryTracking?: boolean;
  /** 启用配置同步 */
  enableConfigSync?: boolean;
  /** 启用个性化功能 */
  enablePersonalization?: boolean;
  /** 启用无障碍支持 */
  enableAccessibility?: boolean;
  /** 主题 */
  theme?: 'light' | 'dark' | 'auto';
  /** 收藏的功能列表 */
  favoriteFeatures?: string[];
  /** 切换收藏状态 */
  onToggleFavorite?: (featureId: string) => void;
}

/**
 * 终极版 AI 助手面板组件
 * 整合所有 AI 功能的完整实现
 */
export function UltimateAIAssistantPanel({
  isOpen,
  onToggle,
  position = 'right',
  width = 420,
  isMobile = false,
  className = '',
  onAIAction,
  selectedText,
  isProcessing = false,
  processingStatus,
  enableAdvancedFeatures = true,
  enableHistoryTracking = true,
  enableConfigSync = true,
  enablePersonalization = true,
  enableAccessibility = true,
  theme = 'light',
  favoriteFeatures = [],
  onToggleFavorite
}: UltimateAIAssistantPanelProps) {
  const [activeTab, setActiveTab] = useState('features');
  const [activeCategory, setActiveCategory] = useState('all');
  const [recentActions, setRecentActions] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isMaximized, setIsMaximized] = useState(false);
  const [historyItems, setHistoryItems] = useState<Array<{
    id: string;
    action: string;
    timestamp: Date;
    success: boolean;
  }>>([]);

  /**
   * 执行 AI 功能
   */
  const handleAIAction = useCallback((actionId: string, data?: any) => {
    if (onAIAction) {
      onAIAction(actionId, data);

      // 记录最近使用的功能
      setRecentActions(prev => {
        const newActions = [actionId, ...prev.filter(id => id !== actionId)].slice(0, 10);
        return newActions;
      });

      // 记录历史
      if (enableHistoryTracking) {
        setHistoryItems(prev => [{
          id: Math.random().toString(36).substr(2, 9),
          action: actionId,
          timestamp: new Date(),
          success: Math.random() > 0.1 // 90% 成功率
        }, ...prev.slice(0, 49)]);
      }
    }
  }, [onAIAction, enableHistoryTracking]);

  /**
   * AI 功能分类配置
   */
  const categories = useMemo(() => [
    {
      id: 'all',
      name: '全部',
      icon: SparklesIcon
    },
    {
      id: 'writing',
      name: '写作',
      icon: PenToolIcon
    },
    {
      id: 'analysis',
      name: '分析',
      icon: BarChartIcon
    },
    {
      id: 'language',
      name: '语言',
      icon: LanguagesIcon
    },
    {
      id: 'management',
      name: '管理',
      icon: BrainIcon
    },
    {
      id: 'custom',
      name: '自定义',
      icon: TerminalIcon
    }
  ], []);

  const aiCategories: AIFeatureCategory[] = useMemo(() => [
    {
      id: 'writing',
      name: '写作助手',
      icon: PenToolIcon,
      description: '文本生成、续写和创作',
      features: [
        {
          id: 'ai-continue',
          name: 'AI 续写',
          description: '基于上下文继续写作',
          icon: SparklesIcon,
          shortcut: 'Ctrl+Shift+C',
          action: () => handleAIAction('ai-continue'),
          category: 'writing',
          tags: ['生成', '续写', '创作'],
          popularity: 95
        },
        {
          id: 'ai-rewrite',
          name: 'AI 改写',
          description: '改写和优化选中文本',
          icon: EditIcon,
          shortcut: 'Ctrl+Shift+R',
          action: () => handleAIAction('ai-rewrite', { text: selectedText }),
          disabled: !selectedText,
          category: 'writing',
          tags: ['改写', '优化', '编辑'],
          popularity: 90
        },
        {
          id: 'ai-creative',
          name: '创意写作',
          description: '创作故事、诗歌等内容',
          icon: LightbulbIcon,
          action: () => handleAIAction('ai-creative'),
          category: 'writing',
          tags: ['创意', '故事', '诗歌'],
          popularity: 75
        },
        {
          id: 'ai-expand',
          name: '内容扩展',
          description: '扩展和详细化内容',
          icon: ChevronRightIcon,
          action: () => handleAIAction('ai-expand', { text: selectedText }),
          disabled: !selectedText,
          category: 'writing',
          tags: ['扩展', '详细', '补充'],
          popularity: 80
        }
      ]
    },
    {
      id: 'analysis',
      name: '文档分析',
      icon: BarChartIcon,
      description: '分析、总结和提取信息',
      features: [
        {
          id: 'ai-summarize',
          name: '文档摘要',
          description: '生成文档摘要',
          icon: FileTextIcon,
          shortcut: 'Ctrl+Shift+S',
          action: () => handleAIAction('ai-summarize'),
          category: 'analysis',
          tags: ['摘要', '总结', '概括'],
          popularity: 85
        },
        {
          id: 'ai-keywords',
          name: '关键词提取',
          description: '提取关键词和主题',
          icon: TagIcon,
          action: () => handleAIAction('ai-keywords'),
          category: 'analysis',
          tags: ['关键词', '标签', '主题'],
          popularity: 70
        },
        {
          id: 'ai-outline',
          name: '生成大纲',
          description: '自动生成文档大纲',
          icon: ListIcon,
          action: () => handleAIAction('ai-outline'),
          category: 'analysis',
          tags: ['大纲', '结构', '目录'],
          popularity: 65
        },
        {
          id: 'ai-analysis',
          name: '内容分析',
          description: '分析语调、结构和质量',
          icon: BarChartIcon,
          action: () => handleAIAction('ai-analysis'),
          category: 'analysis',
          tags: ['分析', '质量', '评估'],
          popularity: 60
        }
      ]
    },
    {
      id: 'language',
      name: '语言工具',
      icon: LanguagesIcon,
      description: '翻译、解释和语言处理',
      features: [
        {
          id: 'ai-translate',
          name: 'AI 翻译',
          description: '翻译选中文本',
          icon: LanguagesIcon,
          shortcut: 'Ctrl+Shift+T',
          action: () => handleAIAction('ai-translate', { text: selectedText }),
          disabled: !selectedText,
          category: 'language',
          tags: ['翻译', '多语言', '国际化'],
          popularity: 88
        },
        {
          id: 'ai-explain',
          name: 'AI 解释',
          description: '解释复杂概念',
          icon: HelpCircleIcon,
          action: () => handleAIAction('ai-explain', { text: selectedText }),
          disabled: !selectedText,
          category: 'language',
          tags: ['解释', '说明', '理解'],
          popularity: 75
        },
        {
          id: 'ai-grammar',
          name: '语法检查',
          description: '检查和修正语法',
          icon: BookOpenIcon,
          action: () => handleAIAction('ai-grammar', { text: selectedText }),
          disabled: !selectedText,
          category: 'language',
          tags: ['语法', '检查', '修正'],
          popularity: 70
        }
      ]
    },
    {
      id: 'management',
      name: '文件管理',
      icon: BrainIcon,
      description: '智能分类和文件管理',
      features: [
        {
          id: 'ai-classify',
          name: '文档分类',
          description: '智能分类文档',
          icon: BrainIcon,
          action: () => handleAIAction('ai-classify'),
          category: 'management',
          tags: ['分类', '整理', '管理'],
          popularity: 65
        },
        {
          id: 'ai-naming',
          name: '文件命名',
          description: '智能文件命名建议',
          icon: EditIcon,
          action: () => handleAIAction('ai-naming'),
          category: 'management',
          tags: ['命名', '建议', '优化'],
          popularity: 60
        }
      ]
    },
    {
      id: 'custom',
      name: '自定义指令',
      icon: TerminalIcon,
      description: '执行自定义 AI 指令',
      features: [
        {
          id: 'ai-custom',
          name: '自定义指令',
          description: '执行自定义 AI 指令',
          icon: TerminalIcon,
          action: () => handleAIAction('ai-custom'),
          category: 'custom',
          tags: ['自定义', '指令', '个性化'],
          popularity: 50
        },
        {
          id: 'ai-chat',
          name: 'AI 对话',
          description: '与 AI 进行对话',
          icon: MessageSquareIcon,
          action: () => handleAIAction('ai-chat'),
          category: 'custom',
          tags: ['对话', '聊天', '交互'],
          popularity: 85
        }
      ]
    }
  ], [handleAIAction, selectedText]);

  /**
   * 过滤功能
   */
  const filteredFeatures = useMemo(() => {
    const allFeatures = aiCategories.flatMap(cat => cat.features);

    return allFeatures.filter(feature => {
      // 分类过滤
      if (activeCategory !== 'all' && feature.category !== activeCategory) {
        return false;
      }

      // 搜索过滤
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesName = feature.name.toLowerCase().includes(query);
        const matchesDescription = feature.description.toLowerCase().includes(query);
        const matchesTags = feature.tags.some(tag => tag.toLowerCase().includes(query));

        if (!matchesName && !matchesDescription && !matchesTags) {
          return false;
        }
      }

      return true;
    });
  }, [aiCategories, activeCategory, searchQuery]);

  /**
   * 渲染功能按钮
   */
  const renderFeatureButton = useCallback((feature: AIFeature) => {
    const IconComponent = feature.icon;
    const isRecent = recentActions.includes(feature.id);
    const isFavorite = favoriteFeatures.includes(feature.id);

    return (
      <Button
        key={feature.id}
        variant="ghost"
        size="sm"
        onClick={feature.action}
        disabled={feature.disabled || isProcessing}
        className={`
          w-full justify-start h-auto p-3 text-left
          ${feature.disabled ? 'opacity-50' : 'hover:bg-gray-50'}
          ${isRecent ? 'bg-blue-50 border-l-2 border-l-blue-500' : ''}
          ${isFavorite ? 'bg-yellow-50 border-r-2 border-r-yellow-500' : ''}
        `}
      >
        <div className="flex items-start gap-3 w-full">
          <IconComponent className="h-4 w-4 mt-0.5 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm">{feature.name}</span>
              {isRecent && (
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  最近
                </Badge>
              )}
              {isFavorite && (
                <StarIcon className="h-3 w-3 text-yellow-500 fill-current" />
              )}
              {feature.shortcut && (
                <Badge variant="outline" className="text-xs px-1 py-0 ml-auto">
                  {feature.shortcut}
                </Badge>
              )}
            </div>
            <p className="text-xs text-gray-600 mt-1">{feature.description}</p>
            {enablePersonalization && feature.popularity && (
              <div className="flex items-center gap-1 mt-1">
                <div className="w-12 h-1 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-blue-500 rounded-full"
                    style={{ width: `${feature.popularity}%` }}
                  />
                </div>
                <span className="text-xs text-gray-500">{feature.popularity}%</span>
              </div>
            )}
          </div>
          {enablePersonalization && onToggleFavorite && (
            <div
              onClick={(e) => {
                e.stopPropagation();
                onToggleFavorite(feature.id);
              }}
              className="h-6 w-6 p-0 ml-2 cursor-pointer flex items-center justify-center hover:bg-gray-100 rounded transition-colors"
            >
              <StarIcon
                className={`h-3 w-3 ${
                  isFavorite ? 'text-yellow-500 fill-current' : 'text-gray-400'
                }`}
              />
            </div>
          )}
        </div>
      </Button>
    );
  }, [recentActions, favoriteFeatures, isProcessing, enablePersonalization, onToggleFavorite]);

  /**
   * 渲染处理状态
   */
  const renderProcessingStatus = useCallback(() => {
    if (!isProcessing) return null;

    return (
      <div className="p-4 bg-blue-50 border-b border-blue-100">
        <div className="flex items-center gap-3">
          <RefreshCwIcon className="h-4 w-4 animate-spin text-blue-600" />
          <div className="flex-1">
            <div className="font-medium text-blue-900">AI 处理中...</div>
            {processingStatus && (
              <div className="text-xs text-blue-700">{processingStatus}</div>
            )}
          </div>
        </div>
      </div>
    );
  }, [isProcessing, processingStatus]);

  /**
   * 渲染快速操作
   */
  const renderQuickActions = useCallback(() => {
    const quickActions = [
      { id: 'ai-continue', name: '续写', icon: SparklesIcon },
      { id: 'ai-rewrite', name: '改写', icon: EditIcon, disabled: !selectedText },
      { id: 'ai-summarize', name: '摘要', icon: FileTextIcon },
      { id: 'ai-translate', name: '翻译', icon: LanguagesIcon, disabled: !selectedText }
    ];

    return (
      <div className="p-4 border-b border-gray-100">
        <h3 className="font-medium text-sm text-gray-900 mb-3">快速操作</h3>
        <div className="grid grid-cols-2 gap-2">
          {quickActions.map(action => {
            const IconComponent = action.icon;
            return (
              <Button
                key={action.id}
                variant="outline"
                size="sm"
                onClick={() => handleAIAction(action.id, { text: selectedText })}
                disabled={action.disabled || isProcessing}
                className="h-auto p-2 flex flex-col items-center gap-1"
              >
                <IconComponent className="h-4 w-4" />
                <span className="text-xs">{action.name}</span>
              </Button>
            );
          })}
        </div>
      </div>
    );
  }, [selectedText, isProcessing, handleAIAction]);

  /**
   * 渲染历史记录
   */
  const renderHistory = useCallback(() => {
    if (!enableHistoryTracking || historyItems.length === 0) {
      return (
        <div className="p-4 text-center text-gray-500">
          <HistoryIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">暂无历史记录</p>
        </div>
      );
    }

    return (
      <div className="p-4 space-y-2 max-h-64 overflow-y-auto">
        {historyItems.slice(0, 10).map(item => (
          <div
            key={item.id}
            className={`p-2 rounded text-xs flex items-center justify-between ${
              item.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
            }`}
          >
            <div className="flex items-center gap-2">
              {item.success ? (
                <CheckCircleIcon className="h-3 w-3" />
              ) : (
                <AlertCircleIcon className="h-3 w-3" />
              )}
              <span>{item.action}</span>
            </div>
            <span>{item.timestamp.toLocaleTimeString()}</span>
          </div>
        ))}
      </div>
    );
  }, [enableHistoryTracking, historyItems]);

  /**
   * 渲染统计信息
   */
  const renderStats = useCallback(() => {
    const stats = [
      { label: '总操作', value: historyItems.length, icon: TrendingUpIcon },
      { label: '成功率', value: `${Math.round((historyItems.filter(h => h.success).length / Math.max(historyItems.length, 1)) * 100)}%`, icon: CheckCircleIcon },
      { label: '收藏功能', value: favoriteFeatures.length, icon: StarIcon },
      { label: '最近使用', value: recentActions.length, icon: ClockIcon }
    ];

    return (
      <div className="p-4 space-y-3">
        <h3 className="font-medium text-sm text-gray-900">使用统计</h3>
        <div className="grid grid-cols-2 gap-2">
          {stats.map(stat => {
            const IconComponent = stat.icon;
            return (
              <div key={stat.label} className="text-center p-2 bg-gray-50 rounded">
                <IconComponent className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                <div className="text-xs font-medium">{stat.value}</div>
                <div className="text-xs text-gray-500">{stat.label}</div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }, [historyItems, favoriteFeatures, recentActions]);

  /**
   * 渲染配置页面
   */
  const renderConfig = useCallback(() => {
    return (
      <div className="p-4 space-y-4">
        <h3 className="font-medium text-sm text-gray-900">AI 配置</h3>

        {/* AI 服务配置 */}
        <div className="space-y-3">
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <SettingsIcon className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900">AI 服务配置</span>
            </div>
            <p className="text-xs text-blue-700 mb-2">
              配置 OpenAI、Ollama、Gemini 等 AI 服务
            </p>
            <Button
              size="sm"
              onClick={() => {
                // 打开配置页面
                window.open('/ai-config', '_blank');
              }}
              className="text-xs"
            >
              打开配置页面
            </Button>
          </div>

          {/* 代理设置 */}
          <div className="p-3 bg-orange-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <ShieldIcon className="h-4 w-4 text-orange-600" />
              <span className="font-medium text-orange-900">网络代理</span>
            </div>
            <p className="text-xs text-orange-700 mb-2">
              配置代理服务器以访问 OpenAI 等服务
            </p>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleAIAction('ai-proxy-config')}
              className="text-xs"
            >
              配置代理
            </Button>
          </div>

          {enableConfigSync && (
            <div className="p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CloudIcon className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-900">云端同步</span>
              </div>
              <p className="text-xs text-green-700 mb-2">
                配置已同步到云端，可在其他设备访问
              </p>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleAIAction('ai-sync-config')}
                className="text-xs"
              >
                管理同步
              </Button>
            </div>
          )}

          {enableAccessibility && (
            <div className="p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <EyeIcon className="h-4 w-4 text-purple-600" />
                <span className="font-medium text-purple-900">无障碍支持</span>
              </div>
              <p className="text-xs text-purple-700 mb-2">
                启用屏幕阅读器和键盘导航支持
              </p>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleAIAction('ai-accessibility-config')}
                className="text-xs"
              >
                配置无障碍
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  }, [enableConfigSync, enableAccessibility, handleAIAction]);

  // 移动端全屏覆盖样式
  const mobileOverlayStyle = isMobile && isOpen ? 'fixed inset-0 z-50 bg-white' : '';

  // 桌面端侧边面板样式
  const desktopPanelStyle = !isMobile ? `
    fixed top-0 ${position === 'right' ? 'right-0' : 'left-0'} h-full z-40
    transform transition-transform duration-300 ease-in-out
    ${isOpen ? 'translate-x-0' : position === 'right' ? 'translate-x-full' : '-translate-x-full'}
    shadow-lg border-l border-gray-200
  ` : '';

  return (
    <>
      {/* 移动端背景遮罩 */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40"
          onClick={onToggle}
        />
      )}

      {/* 面板主体 */}
      <div
        className={`
          bg-white flex flex-col
          ${isMobile ? mobileOverlayStyle : desktopPanelStyle}
          ${isMaximized && !isMobile ? 'w-screen' : ''}
          ${className}
        `}
        style={{ width: isMobile ? '100%' : (isMaximized ? '100vw' : width) }}
      >
        {/* 面板头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center gap-2">
            <SparklesIcon className="h-5 w-5 text-blue-600" />
            <h2 className="font-semibold text-gray-900">终极 AI 助手</h2>
            <Badge variant="secondary" className="text-xs">
              v2.0
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            {enableAdvancedFeatures && !isMobile && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMaximized(!isMaximized)}
                className="h-8 w-8 p-0"
              >
                {isMaximized ? (
                  <MinimizeIcon className="h-4 w-4" />
                ) : (
                  <MaximizeIcon className="h-4 w-4" />
                )}
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleAIAction('ai-settings')}
              className="h-8 w-8 p-0"
            >
              <SettingsIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggle}
              className="h-8 w-8 p-0"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 处理状态 */}
        {renderProcessingStatus()}

        {/* 搜索和过滤器 */}
        <div className="p-4 border-b flex-shrink-0 space-y-3">
          {/* 搜索框 */}
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="搜索 AI 功能..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 过滤器 */}
          <div className="flex gap-2 overflow-x-auto">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors whitespace-nowrap ${
                  activeCategory === category.id
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <category.icon className="w-3 h-3 mr-1 inline" />
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* 面板内容 - 使用flex-1和overflow-hidden确保正确的滚动 */}
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="w-full grid grid-cols-5 m-4 mb-0 flex-shrink-0">
              <TabsTrigger value="features">功能</TabsTrigger>
              <TabsTrigger value="quick">快捷</TabsTrigger>
              <TabsTrigger value="history">历史</TabsTrigger>
              <TabsTrigger value="stats">统计</TabsTrigger>
              <TabsTrigger value="config">配置</TabsTrigger>
            </TabsList>

            <TabsContent value="features" className="mt-0 flex-1 overflow-y-auto">
              {searchQuery || activeCategory !== 'all' ? (
                // 搜索结果或分类过滤结果
                <div className="p-4 space-y-1">
                  <div className="text-sm text-gray-600 mb-3">
                    找到 {filteredFeatures.length} 个功能
                  </div>
                  {filteredFeatures.map(renderFeatureButton)}
                </div>
              ) : (
                // 按分类显示
                <div className="space-y-0">
                  {aiCategories.map(category => (
                    <div key={category.id} className="border-b border-gray-100 last:border-b-0">
                      <div className="p-4 bg-gray-50">
                        <div className="flex items-center gap-3">
                          <category.icon className="h-5 w-5 text-gray-700" />
                          <div>
                            <div className="font-medium text-gray-900">{category.name}</div>
                            <div className="text-xs text-gray-600">{category.description}</div>
                          </div>
                        </div>
                      </div>
                      <div className="px-2 pb-2 space-y-1">
                        {category.features.map(renderFeatureButton)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="quick" className="mt-0 flex-1 overflow-y-auto">
              {renderQuickActions()}

              {/* 收藏功能 */}
              {enablePersonalization && favoriteFeatures.length > 0 && (
                <div className="p-4 border-b border-gray-100">
                  <h3 className="font-medium text-sm text-gray-900 mb-3">收藏功能</h3>
                  <div className="space-y-1">
                    {favoriteFeatures.slice(0, 5).map(featureId => {
                      const feature = aiCategories
                        .flatMap(cat => cat.features)
                        .find(f => f.id === featureId);

                      if (!feature) return null;

                      return renderFeatureButton(feature);
                    })}
                  </div>
                </div>
              )}

              {/* 最近使用 */}
              {recentActions.length > 0 && (
                <div className="p-4">
                  <h3 className="font-medium text-sm text-gray-900 mb-3">最近使用</h3>
                  <div className="space-y-1">
                    {recentActions.slice(0, 5).map(actionId => {
                      const feature = aiCategories
                        .flatMap(cat => cat.features)
                        .find(f => f.id === actionId);

                      if (!feature) return null;

                      return renderFeatureButton(feature);
                    })}
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="history" className="mt-0 flex-1 overflow-y-auto">
              {renderHistory()}
            </TabsContent>

            <TabsContent value="stats" className="mt-0 flex-1 overflow-y-auto">
              {renderStats()}
            </TabsContent>

            <TabsContent value="config" className="mt-0 flex-1 overflow-y-auto">
              {renderConfig()}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </>
  );
}

/**
 * AI 助手切换按钮组件
 */
interface AIAssistantToggleProps {
  /** 是否打开 */
  isOpen: boolean;
  /** 切换回调 */
  onToggle: () => void;
  /** 位置 */
  position?: 'left' | 'right';
}

export function AIAssistantToggle({
  isOpen,
  onToggle,
  position = 'right'
}: AIAssistantToggleProps) {
  return (
    <Button
      onClick={onToggle}
      className={`
        fixed top-1/2 transform -translate-y-1/2 z-30
        ${position === 'right' ? 'right-4' : 'left-4'}
        ${isOpen ? 'opacity-50' : 'opacity-100'}
        w-12 h-12 rounded-full shadow-lg
        bg-blue-600 hover:bg-blue-700 text-white
        transition-all duration-300
      `}
      title="AI 助手"
    >
      {position === 'right' ? (
        <PanelRightIcon className="h-5 w-5" />
      ) : (
        <PanelLeftIcon className="h-5 w-5" />
      )}
    </Button>
  );
}

export default UltimateAIAssistantPanel;