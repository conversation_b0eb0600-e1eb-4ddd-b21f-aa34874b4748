import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';
import { nanoid } from 'nanoid';

/**
 * POST /api/documents/[id]/share - 创建或更新文档分享链接
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const body = await request.json();
    const { isPublic } = body;

    // 验证文档是否存在且属于当前用户
    const document = await prisma.document.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!document) {
      return NextResponse.json({ error: '文档未找到' }, { status: 404 });
    }

    let shareToken = document.shareToken;

    // 如果要公开分享且还没有分享令牌，则生成一个
    if (isPublic && !shareToken) {
      shareToken = nanoid(16);
    }

    // 如果要取消分享，则清除分享令牌
    if (!isPublic) {
      shareToken = null;
    }

    // 更新文档的分享状态
    const updatedDocument = await prisma.document.update({
      where: {
        id: params.id,
      },
      data: {
        isPublic,
        shareToken,
      },
    });

    return NextResponse.json({
      document: {
        id: updatedDocument.id,
        isPublic: updatedDocument.isPublic,
        shareToken: updatedDocument.shareToken,
        shareUrl: shareToken ? `${process.env.NEXTAUTH_URL}/shared/${shareToken}` : null,
      },
    });
  } catch (error) {
    console.error('更新文档分享状态失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/documents/[id]/share - 取消文档分享
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    // 验证文档是否存在且属于当前用户
    const document = await prisma.document.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!document) {
      return NextResponse.json({ error: '文档未找到' }, { status: 404 });
    }

    // 取消分享
    await prisma.document.update({
      where: {
        id: params.id,
      },
      data: {
        isPublic: false,
        shareToken: null,
      },
    });

    return NextResponse.json({ message: '文档分享已取消' });
  } catch (error) {
    console.error('取消文档分享失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}