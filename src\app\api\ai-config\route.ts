/**
 * AI 配置管理 API
 * 处理用户的 AI 服务配置的 CRUD 操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';
import { validateAIConfig, testAIConnection } from '@/lib/services/ai';
import type { AIServiceConfig } from '@/types/ai.types';

/**
 * 获取用户的 AI 配置列表
 */
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const configs = await prisma.aIConfiguration.findMany({
      where: {
        userId: session.user.id
      },
      orderBy: [
        { isDefault: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    // 转换为前端需要的格式，隐藏敏感信息
    const safeConfigs = configs.map(config => ({
      id: config.id,
      provider: config.provider,
      model: config.model,
      endpoint: config.endpoint,
      maxTokens: config.maxTokens,
      temperature: config.temperature,
      isDefault: config.isDefault,
      hasApiKey: !!config.apiKey,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt
    }));

    return NextResponse.json({ configs: safeConfigs });
  } catch (error) {
    console.error('获取 AI 配置失败:', error);
    return NextResponse.json(
      { error: '获取配置失败' },
      { status: 500 }
    );
  }
}

/**
 * 创建新的 AI 配置
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      provider,
      apiKey,
      endpoint,
      model,
      maxTokens = 2000,
      temperature = 0.7,
      isDefault = false
    } = body;

    // 验证配置
    const config: AIServiceConfig = {
      provider,
      apiKey,
      endpoint,
      model,
      maxTokens,
      temperature
    };

    const validation = validateAIConfig(config);
    if (!validation.valid) {
      return NextResponse.json(
        { error: '配置验证失败', details: validation.errors },
        { status: 400 }
      );
    }

    // 如果设置为默认配置，先取消其他默认配置
    if (isDefault) {
      await prisma.aIConfiguration.updateMany({
        where: {
          userId: session.user.id,
          isDefault: true
        },
        data: {
          isDefault: false
        }
      });
    }

    // 创建新配置
    const newConfig = await prisma.aIConfiguration.create({
      data: {
        userId: session.user.id,
        provider,
        apiKey,
        endpoint,
        model,
        maxTokens,
        temperature,
        isDefault
      }
    });

    // 返回安全的配置信息
    const safeConfig = {
      id: newConfig.id,
      provider: newConfig.provider,
      model: newConfig.model,
      endpoint: newConfig.endpoint,
      maxTokens: newConfig.maxTokens,
      temperature: newConfig.temperature,
      isDefault: newConfig.isDefault,
      hasApiKey: !!newConfig.apiKey,
      createdAt: newConfig.createdAt,
      updatedAt: newConfig.updatedAt
    };

    return NextResponse.json({ config: safeConfig }, { status: 201 });
  } catch (error) {
    console.error('创建 AI 配置失败:', error);
    return NextResponse.json(
      { error: '创建配置失败' },
      { status: 500 }
    );
  }
}
