/**
 * AI 配置同步 API
 * 处理配置的云端同步操�?
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { aiConfigSyncService } from '@/lib/services/ai-config-sync';

/**
 * 获取同步状态和历史
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'devices':
        // 获取用户设备列表
        const devices = await aiConfigSyncService.getUserDevices(session.user.id);
        return NextResponse.json({ devices });

      case 'history':
        // 获取同步历史
        const limit = parseInt(searchParams.get('limit') || '50');
        const history = await aiConfigSyncService.getSyncHistory(session.user.id, limit);
        return NextResponse.json({ history });

      case 'conflicts':
        // 获取配置冲突
        const conflicts = await aiConfigSyncService.detectConfigConflicts(session.user.id);
        return NextResponse.json({ conflicts });

      case 'status':
        // 获取整体同步状态
        const configId = searchParams.get('configId');
        if (configId) {
          const status = await aiConfigSyncService.getConfigSyncStatus(configId, session.user.id);
          return NextResponse.json({ status });
        } else {
          return NextResponse.json(
            { error: '缺少配置ID参数' },
            { status: 400 }
          );
        }

      default:
        return NextResponse.json(
          { error: '无效的操作类型' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('获取同步信息失败:', error);
    return NextResponse.json(
      { error: '获取同步信息失败' },
      { status: 500 }
    );
  }
}

/**
 * 执行同步操作
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, deviceId, configId, data } = body;

    switch (action) {
      case 'register-device':
        // 注册新设备
        const { name, type, platform } = data;
        const newDeviceId = await aiConfigSyncService.registerDevice(session.user.id, {
          name,
          type,
          platform
        });
        return NextResponse.json({ deviceId: newDeviceId });

      case 'sync-all':
        // 同步所有配置
        if (!deviceId) {
          return NextResponse.json(
            { error: '缺少设备ID参数' },
            { status: 400 }
          );
        }
        const syncResult = await aiConfigSyncService.syncAllConfigs(session.user.id, deviceId);
        return NextResponse.json({ result: syncResult });

      case 'sync-config':
        // 同步单个配置
        if (!configId || !deviceId) {
          return NextResponse.json(
            { error: '缺少必要参数' },
            { status: 400 }
          );
        }
        const configData = await aiConfigSyncService.prepareConfigForSync(configId, session.user.id);
        if (configData) {
          return NextResponse.json({ config: configData });
        } else {
          return NextResponse.json(
            { error: '配置不存在' },
            { status: 404 }
          );
        }

      case 'apply-sync':
        // 应用同步的配置
        if (!deviceId || !data) {
          return NextResponse.json(
            { error: '缺少必要参数' },
            { status: 400 }
          );
        }
        const applyResult = await aiConfigSyncService.applySyncedConfig(data, session.user.id, deviceId);
        return NextResponse.json({ success: applyResult });

      case 'resolve-conflict':
        // 解决配置冲突
        const { resolution, mergeData } = data;
        if (!configId || !resolution) {
          return NextResponse.json(
            { error: '缺少必要参数' },
            { status: 400 }
          );
        }
        const resolveResult = await aiConfigSyncService.resolveConfigConflict(
          configId,
          session.user.id,
          resolution,
          mergeData
        );
        return NextResponse.json({ success: resolveResult });

      case 'cleanup':
        // 清理同步历史
        const days = data?.days || 30;
        const cleanupCount = await aiConfigSyncService.cleanupSyncHistory(session.user.id, days);
        return NextResponse.json({ cleanedCount: cleanupCount });

      default:
        return NextResponse.json(
          { error: '无效的操作类型' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('同步操作失败:', error);
    return NextResponse.json(
      { error: '同步操作失败' },
      { status: 500 }
    );
  }
}
