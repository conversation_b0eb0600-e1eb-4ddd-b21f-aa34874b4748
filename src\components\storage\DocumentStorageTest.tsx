'use client';

import { useState } from 'react';
import { useDocuments } from '@/hooks/useDocuments';
import { JSONContent } from '@tiptap/react';
import { Button } from '@/components/ui/Button';

interface DocumentStorageTestProps {
  userId: string;
}

export function DocumentStorageTest({ userId }: DocumentStorageTestProps) {
  const {
    documents,
    loading,
    error,
    stats,
    createDocument,
    updateDocument,
    deleteDocument,
    searchDocuments,
    refreshDocuments,
    clearError
  } = useDocuments({ userId, autoLoad: true });

  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);

  const handleCreateTestDocument = async () => {
    const testContent: JSONContent = {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: 'This is a test document created to verify the local storage functionality.'
            }
          ]
        },
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: 'It contains multiple paragraphs to test word and character counting.'
            }
          ]
        }
      ]
    };

    await createDocument({
      title: `Test Document ${Date.now()}`,
      content: testContent,
      userId,
      metadata: {
        tags: ['test', 'storage'],
        wordCount: 0,
        characterCount: 0,
        isPublic: false
      }
    });
  };

  const handleUpdateDocument = async (id: string) => {
    const updatedContent: JSONContent = {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: `Updated at ${new Date().toLocaleTimeString()}`
            }
          ]
        }
      ]
    };

    await updateDocument(id, {
      content: updatedContent,
      title: `Updated Document ${Date.now()}`
    });
  };

  const handleSearch = async () => {
    if (searchQuery.trim()) {
      const results = await searchDocuments(searchQuery);
      setSearchResults(results);
    }
  };

  if (loading) {
    return <div className="p-4">Loading documents...</div>;
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Document Storage Test</h2>
      
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-red-800 font-medium">Error</h3>
              <p className="text-red-700 text-sm mt-1">{error.message}</p>
            </div>
            <Button
              onClick={clearError}
              variant="ghost"
              size="sm"
              className="text-red-600 hover:text-red-800"
            >
              ×
            </Button>
          </div>
        </div>
      )}

      {/* Statistics */}
      {stats && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
          <h3 className="text-blue-800 font-medium mb-2">Document Statistics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-blue-600">Total Documents:</span>
              <span className="ml-2 font-medium">{stats.totalDocuments}</span>
            </div>
            <div>
              <span className="text-blue-600">Total Words:</span>
              <span className="ml-2 font-medium">{stats.totalWords.toLocaleString()}</span>
            </div>
            <div>
              <span className="text-blue-600">Total Characters:</span>
              <span className="ml-2 font-medium">{stats.totalCharacters.toLocaleString()}</span>
            </div>
            <div>
              <span className="text-blue-600">Last Modified:</span>
              <span className="ml-2 font-medium">
                {stats.lastModified ? stats.lastModified.toLocaleDateString() : 'N/A'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex flex-wrap gap-4 mb-6">
        <Button onClick={handleCreateTestDocument}>
          Create Test Document
        </Button>
        <Button onClick={refreshDocuments} variant="outline">
          Refresh Documents
        </Button>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="flex gap-2">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search documents..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <Button onClick={handleSearch}>Search</Button>
        </div>
        
        {searchResults.length > 0 && (
          <div className="mt-4">
            <h4 className="font-medium mb-2">Search Results ({searchResults.length})</h4>
            <div className="space-y-2">
              {searchResults.map((doc) => (
                <div key={doc.id} className="p-3 bg-gray-50 rounded-md">
                  <h5 className="font-medium">{doc.title}</h5>
                  <p className="text-sm text-gray-600">
                    {doc.metadata.wordCount} words, {doc.metadata.characterCount} characters
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Documents List */}
      <div>
        <h3 className="text-lg font-medium mb-4">Documents ({documents.length})</h3>
        
        {documents.length === 0 ? (
          <p className="text-gray-500 text-center py-8">
            No documents found. Create a test document to get started.
          </p>
        ) : (
          <div className="space-y-4">
            {documents.map((doc) => (
              <div key={doc.id} className="border border-gray-200 rounded-md p-4">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-lg">{doc.title}</h4>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => handleUpdateDocument(doc.id)}
                      size="sm"
                      variant="outline"
                    >
                      Update
                    </Button>
                    <Button
                      onClick={() => deleteDocument(doc.id)}
                      size="sm"
                      variant="outline"
                      className="text-red-600 hover:text-red-800"
                    >
                      Delete
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                  <div>Words: {doc.metadata.wordCount}</div>
                  <div>Characters: {doc.metadata.characterCount}</div>
                  <div>Created: {doc.createdAt.toLocaleDateString()}</div>
                  <div>Updated: {doc.updatedAt.toLocaleDateString()}</div>
                </div>
                
                {doc.metadata.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-2">
                    {doc.metadata.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
                
                <div className="text-sm text-gray-500">
                  ID: {doc.id} | Dirty: {doc.isDirty ? 'Yes' : 'No'}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}