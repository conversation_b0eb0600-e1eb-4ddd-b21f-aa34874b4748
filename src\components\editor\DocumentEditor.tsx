'use client';

import { useEditor, EditorContent, JSONContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import { useCallback, useEffect, useState, useRef } from 'react';
import { useDocument } from '@/hooks/useDocuments';
import { LocalDocument } from '@/lib/storage/database';
import { Button } from '@/components/ui/Button';
import { updateDocumentMetadata } from '@/lib/storage/document-utils';

interface DocumentEditorProps {
  documentId?: string;
  userId: string;
  placeholder?: string;
  className?: string;
  autoSave?: boolean;
  autoSaveDelay?: number;
  onDocumentChange?: (document: LocalDocument | null) => void;
}

export function DocumentEditor({
  documentId,
  userId,
  placeholder = '开始写作...',
  className = '',
  autoSave = true,
  autoSaveDelay = 2000,
  onDocumentChange,
}: DocumentEditorProps) {
  const {
    document,
    loading,
    error,
    updateDocument,
    clearError
  } = useDocument({ documentId, autoLoad: !!documentId });

  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
        codeBlock: {
          HTMLAttributes: {
            class: 'code-block',
          },
        },
      }),
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
      CharacterCount.configure({
        limit: null,
      }),
    ],
    content: document?.content || {
      type: 'doc',
      content: [{ type: 'paragraph', content: [] }]
    },
    onUpdate: ({ editor }) => {
      setHasUnsavedChanges(true);
      
      if (autoSave && documentId) {
        // Clear existing timeout
        if (autoSaveTimeoutRef.current) {
          clearTimeout(autoSaveTimeoutRef.current);
        }
        
        // Set new timeout for auto-save
        autoSaveTimeoutRef.current = setTimeout(() => {
          handleSave();
        }, autoSaveDelay);
      }
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[400px] px-4 py-6',
      },
    },
  });

  // Update editor content when document changes
  useEffect(() => {
    if (editor && document && document.content) {
      const currentContent = editor.getJSON();
      if (JSON.stringify(currentContent) !== JSON.stringify(document.content)) {
        editor.commands.setContent(document.content);
        setHasUnsavedChanges(false);
      }
    }
  }, [document, editor]);

  // Notify parent component when document changes
  useEffect(() => {
    onDocumentChange?.(document);
  }, [document, onDocumentChange]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  const handleSave = useCallback(async () => {
    if (!editor || !documentId || isSaving) return;

    setIsSaving(true);
    
    try {
      const content = editor.getJSON() as JSONContent;
      const currentMetadata = document?.metadata || {
        wordCount: 0,
        characterCount: 0,
        tags: [],
        isPublic: false
      };
      
      const updatedMetadata = updateDocumentMetadata(content, currentMetadata);
      
      const success = await updateDocument({
        content,
        metadata: updatedMetadata
      });
      
      if (success) {
        setHasUnsavedChanges(false);
        setLastSaved(new Date());
      }
    } catch (error) {
      console.error('Failed to save document:', error);
    } finally {
      setIsSaving(false);
    }
  }, [editor, documentId, document, updateDocument, isSaving]);

  const handleManualSave = useCallback(() => {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }
    handleSave();
  }, [handleSave]);

  const getWordCount = useCallback(() => {
    if (!editor) return 0;
    return editor.storage.characterCount.words();
  }, [editor]);

  const getCharacterCount = useCallback(() => {
    if (!editor) return 0;
    return editor.storage.characterCount.characters();
  }, [editor]);

  const formatLastSaved = useCallback(() => {
    if (!lastSaved) return '';
    const now = new Date();
    const diff = now.getTime() - lastSaved.getTime();
    
    if (diff < 60000) { // Less than 1 minute
      return '刚刚保存';
    } else if (diff < 3600000) { // Less than 1 hour
      const minutes = Math.floor(diff / 60000);
      return `${minutes}分钟前保存`;
    } else {
      return lastSaved.toLocaleTimeString();
    }
  }, [lastSaved]);

  if (loading) {
    return (
      <div className={`min-h-[400px] w-full ${className}`}>
        <div className="flex items-center justify-center h-full">
          <div className="text-muted-foreground">加载文档...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`min-h-[400px] w-full ${className}`}>
        <div className="flex flex-col items-center justify-center h-full space-y-4">
          <div className="text-red-600">加载文档时出错</div>
          <div className="text-sm text-muted-foreground">{error.message}</div>
          <Button onClick={clearError} variant="outline" size="sm">
            重试
          </Button>
        </div>
      </div>
    );
  }

  if (!editor) {
    return (
      <div className={`min-h-[400px] w-full ${className}`}>
        <div className="flex items-center justify-center h-full">
          <div className="text-muted-foreground">初始化编辑器...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      {/* Save status bar */}
      {documentId && (
        <div className="flex items-center justify-between px-4 py-2 bg-gray-50 border-b">
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span>
              {getWordCount()} 词 · {getCharacterCount()} 字符
            </span>
            {document && (
              <span>
                创建于 {document.createdAt.toLocaleDateString()}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              {isSaving ? (
                <span className="flex items-center">
                  <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                  保存中...
                </span>
              ) : hasUnsavedChanges ? (
                <span className="text-orange-600">有未保存的更改</span>
              ) : lastSaved ? (
                <span className="text-green-600">{formatLastSaved()}</span>
              ) : null}
            </div>
            
            {!autoSave && (
              <Button
                onClick={handleManualSave}
                disabled={isSaving || !hasUnsavedChanges}
                size="sm"
              >
                保存
              </Button>
            )}
          </div>
        </div>
      )}
      
      {/* Editor */}
      <div className="relative">
        <EditorContent editor={editor} />
      </div>
    </div>
  );
}

export { useEditor };