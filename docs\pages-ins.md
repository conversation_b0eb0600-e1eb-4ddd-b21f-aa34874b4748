演示和测试页面
AI 功能演示
http://localhost:4501/ai-demo - AI 基础功能演示
http://localhost:4501/ai-text-generation-demo - 文本生成演示
http://localhost:4501/ai-rewrite-demo - 文本改写演示
http://localhost:4501/ai-document-analysis-demo - 文档分析演示
http://localhost:4501/ai-translation-explanation-demo - 翻译解释演示
http://localhost:4501/ai-assistant-showcase - AI 助手完整展示
编辑器功能演示
http://localhost:4501/slash-command-demo - 斜杠命令演示
http://localhost:4501/text-selection-demo - 文本选择功能演示
http://localhost:4501/chat-edit-demo - Chat/Edit 模式演示
系统功能演示
http://localhost:4501/file-tree-demo - 文件树功能演示

http://localhost:4501/sync-demo - 同步功能演示
http://localhost:4501/conflict-demo - 冲突解决演示
http://localhost:4501/storage-test - 存储功能测试
🔧 API 接口
认证 API
/api/auth/* - NextAuth.js 认证端点
文档管理 API
/api/documents/* - 文档 CRUD 操作
/api/folders/* - 文件夹管理
AI 服务 API
/api/ai/* - AI 功能调用接口
/api/ai-config/* - AI 配置管理
/api/ai-interactions/* - AI 交互历史
系统 API
/api/db/* - 数据库操作测试
/api/shared/* - 共享功能接口

---

http://localhost:4501/ai-assistant-advanced
http://localhost:4501/ai-assistant-comprehensive
http://localhost:4501/ai-assistant-demo
http://localhost:4501/ai-assistant-enhanced
http://localhost:4501/ai-assistant-final
http://localhost:4501/ai-assistant-integrated
http://localhost:4501/ai-assistant-mobile
http://localhost:4501/ai-assistant-optimized
http://localhost:4501/ai-assistant-showcase

http://localhost:4501/ai-classification
http://localhost:4501/ai-config

http://localhost:4501/ai-config-demo
http://localhost:4501/ai-config-sync-demo
http://localhost:4501/ai-demo
http://localhost:4501/ai-document-analysis-demo
http://localhost:4501/ai-file-naming
http://localhost:4501/ai-history-demo
http://localhost:4501/ai-processing-demo
http://localhost:4501/ai-rewrite-demo
http://localhost:4501/ai-text-generation-demo
http://localhost:4501/ai-translation-explanation-demo

http://localhost:4501/api
http://localhost:4501/auth
http://localhost:4501/auth-demo
http://localhost:4501/auth-test
http://localhost:4501/chat-edit-demo
http://localhost:4501/conflict-demo
http://localhost:4501/dashboard
http://localhost:4501/db-test
http://localhost:4501/document-manager
http://localhost:4501/editor
http://localhost:4501/enhanced-document-manager
http://localhost:4501/file-tree-demo
http://localhost:4501/mobile-editor-test
http://localhost:4501/selection-test
http://localhost:4501/slash-command-demo
http://localhost:4501/storage-test
http://localhost:4501/sync-demo
http://localhost:4501/test-hierarchy
http://localhost:4501/text-selection-demo