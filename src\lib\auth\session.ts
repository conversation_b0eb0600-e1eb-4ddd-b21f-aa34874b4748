import { getServerSession } from "next-auth/next";
import { authOptions } from "./config";
import { prisma } from "@/lib/db/prisma";
import { AuthUser } from "@/types/auth";

/**
 * 获取服务端会话信息
 * 在服务端组件中获取当前用户会话
 * @returns 会话信息或 null
 */
export async function getSession() {
  return await getServerSession(authOptions);
}

/**
 * 获取当前登录用户信息
 * 从会话中提取用户信息
 * @returns 用户信息或 null
 */
export async function getCurrentUser(): Promise<AuthUser | null> {
  const session = await getSession();
  return session?.user || null;
}

/**
 * 根据用户ID获取用户详细信息
 * 从数据库中查询用户的完整信息
 * @param id 用户ID
 * @returns 用户信息或 null
 */
export async function getUserById(id: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        subscription: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return user;
  } catch (error) {
    console.error("获取用户信息失败:", error);
    return null;
  }
}

/**
 * 更新用户个人资料
 * 更新用户的姓名和头像信息
 * @param userId 用户ID
 * @param data 要更新的数据
 * @returns 更新后的用户信息
 */
export async function updateUserProfile(
  userId: string,
  data: {
    name?: string;
    image?: string;
  }
) {
  try {
    const user = await prisma.user.update({
      where: { id: userId },
      data,
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        subscription: true,
        updatedAt: true,
      },
    });

    return user;
  } catch (error) {
    console.error("更新用户资料失败:", error);
    throw new Error("更新个人资料失败");
  }
}

/**
 * 删除用户账户
 * 删除用户及其所有相关数据（级联删除）
 * @param userId 用户ID
 * @returns 删除是否成功
 */
export async function deleteUserAccount(userId: string) {
  try {
    // 由于 Prisma schema 中的级联删除约束，这将删除所有相关数据
    await prisma.user.delete({
      where: { id: userId },
    });

    return true;
  } catch (error) {
    console.error("删除用户账户失败:", error);
    throw new Error("删除账户失败");
  }
}