import { JSONContent } from '@tiptap/react';

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  subscription: 'free' | 'pro' | 'enterprise';
}

export interface AuthSession {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}

// Document types
export interface Document {
  id: string;
  title: string;
  content: JSONContent;
  folderId?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  lastSyncAt?: Date;
  metadata: DocumentMetadata;
}

export interface DocumentMetadata {
  wordCount: number;
  characterCount: number;
  lastAIInteraction?: Date;
  tags: string[];
  isPublic: boolean;
  shareToken?: string;
}

// Folder types
export interface Folder {
  id: string;
  name: string;
  parentId?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  children: (Folder | Document)[];
}

// AI types
export interface AIService {
  name: string;
  type: 'openai' | 'ollama' | 'gemini';
  generateText(prompt: string, context?: string): Promise<string>;
  rewriteText(text: string, style?: string): Promise<string[]>;
  summarizeText(text: string): Promise<string>;
  analyzeText(text: string): Promise<TextAnalysis>;
  translateText(text: string, targetLanguage: string): Promise<string>;
  explainText(text: string): Promise<string>;
}

export interface AIConfiguration {
  id: string;
  userId: string;
  provider: string;
  apiKey?: string;
  endpoint?: string;
  model: string;
  maxTokens: number;
  temperature: number;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TextAnalysis {
  sentiment: 'positive' | 'negative' | 'neutral';
  keyWords: string[];
  topics: string[];
  readabilityScore: number;
  suggestions: string[];
}

// Slash command types
export interface SlashCommand {
  id: string;
  label: string;
  description: string;
  icon: string;
  category: 'basic' | 'ai' | 'media' | 'advanced';
  shortcut?: string;
  action: (editor: any, range: any) => void;
}

export interface SlashCommandCategory {
  name: string;
  commands: SlashCommand[];
}

// Export sync types from separate file
export * from './sync';

// Error types
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AI_SERVICE_ERROR = 'AI_SERVICE_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
}

export interface AppError {
  type: ErrorType;
  message: string;
  details?: any;
  timestamp: Date;
  recoverable: boolean;
}