'use client';

import React, { useState, useCallback } from 'react';
import { 
  TranslationResult, 
  ExplanationResult, 
  CustomInstructionResult, 
  CreativeWritingResult,
  SUPPORTED_LANGUAGES,
  SupportedLanguage
} from '@/lib/services/ai/translation-explanation-service';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  LanguagesIcon, 
  HelpCircleIcon, 
  TerminalIcon,
  PenToolIcon,
  CopyIcon,
  RefreshCwIcon,
  XIcon,
  CheckIcon,
  StarIcon,
  LightbulbIcon,
  BookOpenIcon
} from 'lucide-react';

/**
 * 翻译和解释面板的属性
 */
interface AITranslationExplanationPanelProps {
  /** 翻译结果 */
  translationResult?: TranslationResult;
  /** 解释结果 */
  explanationResult?: ExplanationResult;
  /** 自定义指令结果 */
  instructionResult?: CustomInstructionResult;
  /** 创意写作结果 */
  creativeResult?: CreativeWritingResult;
  /** 是否显示 */
  visible: boolean;
  /** 关闭面板的回调 */
  onClose: () => void;
  /** 重新执行的回调 */
  onRegenerate: () => void;
  /** 应用结果的回调 */
  onApply?: (content: string) => void;
  /** 是否正在处理 */
  isProcessing?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * AI 翻译和解释面板组件
 * 显示翻译、解释、自定义指令和创意写作的结果
 */
export function AITranslationExplanationPanel({
  translationResult,
  explanationResult,
  instructionResult,
  creativeResult,
  visible,
  onClose,
  onRegenerate,
  onApply,
  isProcessing = false,
  className = ''
}: AITranslationExplanationPanelProps) {
  const [copiedText, setCopiedText] = useState<string | null>(null);

  /**
   * 复制文本到剪贴板
   */
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(text);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  }, []);

  /**
   * 应用结果到编辑器
   */
  const handleApply = useCallback((content: string) => {
    if (onApply) {
      onApply(content);
      onClose();
    }
  }, [onApply, onClose]);

  /**
   * 渲染翻译结果
   */
  const renderTranslationResult = useCallback((result: TranslationResult) => {
    const sourceLangName = SUPPORTED_LANGUAGES[result.detectedSourceLanguage] || result.detectedSourceLanguage;
    const targetLangName = SUPPORTED_LANGUAGES[result.targetLanguage] || result.targetLanguage;

    return (
      <div className="space-y-4">
        {/* 语言信息 */}
        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center gap-2">
            <LanguagesIcon className="h-5 w-5 text-blue-600" />
            <span className="text-blue-800 font-medium">
              {sourceLangName} → {targetLangName}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <StarIcon className="h-4 w-4 text-yellow-500" />
            <span className="text-sm text-blue-700">质量评分: {result.qualityScore}/100</span>
          </div>
        </div>

        {/* 翻译结果 */}
        <div className="p-4 bg-gray-50 rounded-lg border">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-gray-900">翻译结果</h4>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(result.translatedText)}
                className="h-6 w-6 p-0"
              >
                <CopyIcon className="h-3 w-3" />
              </Button>
              {onApply && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleApply(result.translatedText)}
                  className="h-6 w-6 p-0"
                >
                  <CheckIcon className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
          <p className="text-gray-800 leading-relaxed">{result.translatedText}</p>
        </div>

        {/* 备选翻译 */}
        {result.alternatives && result.alternatives.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">备选翻译</h4>
            <div className="space-y-2">
              {result.alternatives.map((alt, index) => (
                <div key={index} className="p-3 bg-white rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <p className="text-gray-700 flex-1">{alt}</p>
                    <div className="flex items-center gap-1 ml-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(alt)}
                        className="h-6 w-6 p-0"
                      >
                        <CopyIcon className="h-3 w-3" />
                      </Button>
                      {onApply && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleApply(alt)}
                          className="h-6 w-6 p-0"
                        >
                          <CheckIcon className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 翻译说明 */}
        {result.notes && result.notes.length > 0 && (
          <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <h4 className="font-medium text-yellow-800 mb-2">翻译说明</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              {result.notes.map((note, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-yellow-600 mt-1">•</span>
                  <span>{note}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  }, [copyToClipboard, handleApply, onApply]);

  /**
   * 渲染解释结果
   */
  const renderExplanationResult = useCallback((result: ExplanationResult) => {
    return (
      <div className="space-y-4">
        {/* 解释内容 */}
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-blue-900">详细解释</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(result.explanation)}
              className="h-6 w-6 p-0"
            >
              <CopyIcon className="h-3 w-3" />
            </Button>
          </div>
          <p className="text-blue-800 leading-relaxed">{result.explanation}</p>
        </div>

        {/* 关键要点 */}
        {result.keyPoints.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
              <LightbulbIcon className="h-4 w-4 text-yellow-500" />
              关键要点
            </h4>
            <div className="space-y-2">
              {result.keyPoints.map((point, index) => (
                <div key={index} className="flex items-start gap-2 p-2 bg-yellow-50 rounded">
                  <span className="text-yellow-600 font-bold mt-1">{index + 1}</span>
                  <span className="text-yellow-800">{point}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 相关概念 */}
        {result.relatedConcepts && result.relatedConcepts.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">相关概念</h4>
            <div className="flex flex-wrap gap-2">
              {result.relatedConcepts.map((concept, index) => (
                <Badge key={index} variant="secondary" className="bg-purple-100 text-purple-800">
                  {concept}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* 举例说明 */}
        {result.examples && result.examples.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">举例说明</h4>
            <div className="space-y-2">
              {result.examples.map((example, index) => (
                <div key={index} className="p-3 bg-green-50 rounded-lg border border-green-200">
                  <p className="text-green-800">{example}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 参考建议 */}
        {result.references && result.references.length > 0 && (
          <div className="p-3 bg-gray-50 rounded-lg border">
            <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
              <BookOpenIcon className="h-4 w-4 text-blue-500" />
              进一步学习建议
            </h4>
            <ul className="text-sm text-gray-700 space-y-1">
              {result.references.map((ref, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-blue-600 mt-1">→</span>
                  <span>{ref}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  }, [copyToClipboard]);

  /**
   * 渲染自定义指令结果
   */
  const renderInstructionResult = useCallback((result: CustomInstructionResult) => {
    return (
      <div className="space-y-4">
        {/* 执行的指令 */}
        <div className="p-3 bg-gray-100 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-1">执行指令</h4>
          <p className="text-sm text-gray-700 font-mono">{result.instruction}</p>
        </div>

        {/* 执行结果 */}
        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-green-900">执行结果</h4>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(result.content)}
                className="h-6 w-6 p-0"
              >
                <CopyIcon className="h-3 w-3" />
              </Button>
              {onApply && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleApply(result.content)}
                  className="h-6 w-6 p-0"
                >
                  <CheckIcon className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
          <div className="text-green-800 leading-relaxed whitespace-pre-wrap">{result.content}</div>
        </div>

        {/* 处理说明 */}
        {result.processingNotes && result.processingNotes.length > 0 && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-800 mb-2">处理说明</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              {result.processingNotes.map((note, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-blue-600 mt-1">•</span>
                  <span>{note}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 格式信息 */}
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            格式: {result.format}
          </Badge>
        </div>
      </div>
    );
  }, [copyToClipboard, handleApply, onApply]);

  /**
   * 渲染创意写作结果
   */
  const renderCreativeResult = useCallback((result: CreativeWritingResult) => {
    return (
      <div className="space-y-4">
        {/* 创作主题 */}
        <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
          <h4 className="font-medium text-purple-900 mb-1">创作主题</h4>
          <p className="text-purple-800">{result.theme}</p>
        </div>

        {/* 创作内容 */}
        <div className="p-4 bg-gradient-to-br from-pink-50 to-purple-50 rounded-lg border">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-gray-900">创作内容</h4>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(result.content)}
                className="h-6 w-6 p-0"
              >
                <CopyIcon className="h-3 w-3" />
              </Button>
              {onApply && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleApply(result.content)}
                  className="h-6 w-6 p-0"
                >
                  <CheckIcon className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
          <div className="text-gray-800 leading-relaxed whitespace-pre-wrap">{result.content}</div>
        </div>

        {/* 风格特点 */}
        {result.styleFeatures.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">风格特点</h4>
            <div className="flex flex-wrap gap-2">
              {result.styleFeatures.map((feature, index) => (
                <Badge key={index} variant="secondary" className="bg-pink-100 text-pink-800">
                  {feature}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* 创作说明 */}
        {result.creationNotes && result.creationNotes.length > 0 && (
          <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
            <h4 className="font-medium text-orange-800 mb-2">创作说明</h4>
            <ul className="text-sm text-orange-700 space-y-1">
              {result.creationNotes.map((note, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-orange-600 mt-1">✨</span>
                  <span>{note}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 类型信息 */}
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            类型: {result.type}
          </Badge>
        </div>
      </div>
    );
  }, [copyToClipboard, handleApply, onApply]);

  if (!visible) return null;

  // 确定显示的结果类型和图标
  let title = '';
  let icon = null;
  let result = null;

  if (translationResult) {
    title = '翻译结果';
    icon = <LanguagesIcon className="h-5 w-5" />;
    result = renderTranslationResult(translationResult);
  } else if (explanationResult) {
    title = '解释说明';
    icon = <HelpCircleIcon className="h-5 w-5" />;
    result = renderExplanationResult(explanationResult);
  } else if (instructionResult) {
    title = '指令执行结果';
    icon = <TerminalIcon className="h-5 w-5" />;
    result = renderInstructionResult(instructionResult);
  } else if (creativeResult) {
    title = '创意写作';
    icon = <PenToolIcon className="h-5 w-5" />;
    result = renderCreativeResult(creativeResult);
  }

  return (
    <div
      className={`
        fixed inset-0 bg-black/50 flex items-center justify-center z-50
        animate-in fade-in duration-200
        ${className}
      `}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden mx-4">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
              {icon}
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
              <p className="text-sm text-gray-600">
                处理时间: {translationResult?.responseTime || explanationResult?.responseTime || instructionResult?.responseTime || creativeResult?.responseTime}ms · 
                令牌: {translationResult?.tokensUsed || explanationResult?.tokensUsed || instructionResult?.tokensUsed || creativeResult?.tokensUsed}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRegenerate}
              disabled={isProcessing}
              className="flex items-center gap-1"
            >
              <RefreshCwIcon className={`h-4 w-4 ${isProcessing ? 'animate-spin' : ''}`} />
              {isProcessing ? '处理中...' : '重新生成'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* 内容 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {result}
        </div>
      </div>
    </div>
  );
}

/**
 * 处理加载状态组件
 */
interface AITranslationExplanationLoadingProps {
  visible: boolean;
  onCancel: () => void;
  message?: string;
  type?: 'translation' | 'explanation' | 'instruction' | 'creative';
}

export function AITranslationExplanationLoading({
  visible,
  onCancel,
  message = 'AI 正在处理...',
  type
}: AITranslationExplanationLoadingProps) {
  if (!visible) return null;

  const getIcon = () => {
    switch (type) {
      case 'translation': return <LanguagesIcon className="h-4 w-4 text-blue-600" />;
      case 'explanation': return <HelpCircleIcon className="h-4 w-4 text-green-600" />;
      case 'instruction': return <TerminalIcon className="h-4 w-4 text-purple-600" />;
      case 'creative': return <PenToolIcon className="h-4 w-4 text-pink-600" />;
      default: return null;
    }
  };

  const getTypeMessage = () => {
    switch (type) {
      case 'translation': return '正在翻译文本...';
      case 'explanation': return '正在生成解释...';
      case 'instruction': return '正在执行指令...';
      case 'creative': return '正在创作内容...';
      default: return message;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
        <div className="flex items-center gap-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 flex items-center justify-center">
            {getIcon()}
          </div>
          <div className="flex-1">
            <div className="font-medium text-gray-900">{getTypeMessage()}</div>
            <div className="text-sm text-gray-600">{message}</div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="h-8 w-8 p-0"
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}