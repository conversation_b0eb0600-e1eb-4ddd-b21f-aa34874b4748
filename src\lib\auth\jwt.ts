import jwt from "jsonwebtoken";

/**
 * JWT 签名密钥
 * 从环境变量获取，如果未设置则使用默认值（仅用于开发环境）
 */
const JWT_SECRET = process.env.NEXTAUTH_SECRET || "fallback-secret";

/**
 * JWT 令牌载荷接口
 * 定义令牌中包含的用户信息
 */
export interface TokenPayload {
  userId: string; // 用户ID
  email: string;  // 用户邮箱
  name?: string;  // 用户姓名（可选）
}

/**
 * 签发 JWT 令牌
 * 使用用户信息创建一个新的 JWT 令牌
 * @param payload 令牌载荷数据
 * @returns 签名后的 JWT 令牌字符串
 */
export function signToken(payload: TokenPayload): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: "30d", // 令牌有效期：30天
    issuer: "nextjs-document-editor", // 令牌签发者
  });
}

/**
 * 验证 JWT 令牌
 * 验证令牌的有效性并解析载荷数据
 * @param token JWT 令牌字符串
 * @returns 解析后的载荷数据，验证失败返回 null
 */
export function verifyToken(token: string): TokenPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as TokenPayload;
    return decoded;
  } catch (error) {
    console.error("令牌验证失败:", error);
    return null;
  }
}

/**
 * 刷新 JWT 令牌
 * 使用现有令牌创建一个新的令牌（延长有效期）
 * @param token 现有的 JWT 令牌
 * @returns 新的 JWT 令牌，验证失败返回 null
 */
export function refreshToken(token: string): string | null {
  const payload = verifyToken(token);
  if (!payload) {
    return null;
  }

  // 使用现有载荷创建新令牌，重置过期时间
  return signToken({
    userId: payload.userId,
    email: payload.email,
    name: payload.name,
  });
}