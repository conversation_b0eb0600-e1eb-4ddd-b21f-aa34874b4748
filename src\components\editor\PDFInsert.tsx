'use client';

import { useState, useRef, useCallback } from 'react';
import { Editor } from '@tiptap/react';
import { FileText, Upload, X, Check, AlertCircle, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/Button';

interface PDFInsertProps {
  editor: Editor | null;
  onClose: () => void;
  className?: string;
}

interface PDFData {
  url: string;
  title: string;
  description?: string;
  embedType: 'link' | 'embed';
}

interface PDFUploadState {
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
}

/**
 * PDF 文件插入组件
 * 支持 PDF 文件上传、URL 链接和嵌入显示
 */
export function PDFInsert({ editor, onClose, className }: PDFInsertProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [pdfData, setPdfData] = useState<PDFData>({
    url: '',
    title: '',
    description: '',
    embedType: 'link',
  });
  const [uploadState, setUploadState] = useState<PDFUploadState>({
    isUploading: false,
    uploadProgress: 0,
    error: null,
  });
  const [uploadMethod, setUploadMethod] = useState<'file' | 'url'>('url');

  /**
   * 处理文件选择
   */
  const handleFileSelect = useCallback((file: File) => {
    if (file.type !== 'application/pdf') {
      setUploadState(prev => ({
        ...prev,
        error: '请选择 PDF 文件',
      }));
      return;
    }

    // 检查文件大小 (10MB 限制)
    if (file.size > 10 * 1024 * 1024) {
      setUploadState(prev => ({
        ...prev,
        error: 'PDF 文件大小不能超过 10MB',
      }));
      return;
    }

    setUploadState(prev => ({
      ...prev,
      isUploading: true,
      error: null,
      uploadProgress: 0,
    }));

    // 模拟上传过程 (实际项目中应该上传到服务器)
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      
      // 模拟上传进度
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        setUploadState(prev => ({
          ...prev,
          uploadProgress: progress,
        }));
        
        if (progress >= 100) {
          clearInterval(interval);
          setUploadState(prev => ({
            ...prev,
            isUploading: false,
          }));
          
          // 设置 PDF 数据 (使用 data URL，实际项目中应该是服务器 URL)
          setPdfData(prev => ({
            ...prev,
            url: result,
            title: file.name.replace(/\.pdf$/i, ''),
          }));
        }
      }, 100);
    };

    reader.onerror = () => {
      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        error: '读取文件失败',
      }));
    };

    reader.readAsDataURL(file);
  }, []);

  /**
   * 处理文件输入变化
   */
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  /**
   * 处理拖拽上传
   */
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  /**
   * 验证 PDF URL
   */
  const validatePdfUrl = useCallback((url: string): boolean => {
    if (!url.trim()) return false;
    
    try {
      const urlObj = new URL(url);
      // 检查是否是 PDF 文件或支持的 PDF 服务
      return url.toLowerCase().includes('.pdf') || 
             url.includes('drive.google.com') ||
             url.includes('dropbox.com') ||
             url.includes('onedrive.com');
    } catch {
      return false;
    }
  }, []);

  /**
   * 处理输入变化
   */
  const handleInputChange = useCallback((field: keyof PDFData, value: string) => {
    setPdfData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // 清除错误
    if (uploadState.error) {
      setUploadState(prev => ({
        ...prev,
        error: null,
      }));
    }
  }, [uploadState.error]);

  /**
   * 插入 PDF
   */
  const handleInsertPDF = useCallback(() => {
    if (!editor) return;

    const { url, title, description, embedType } = pdfData;

    // 验证数据
    if (!url.trim()) {
      setUploadState(prev => ({
        ...prev,
        error: '请提供 PDF 文件或 URL',
      }));
      return;
    }

    if (!title.trim()) {
      setUploadState(prev => ({
        ...prev,
        error: '请输入 PDF 标题',
      }));
      return;
    }

    if (uploadMethod === 'url' && !validatePdfUrl(url)) {
      setUploadState(prev => ({
        ...prev,
        error: '请输入有效的 PDF URL',
      }));
      return;
    }

    // 根据嵌入类型插入不同的内容
    if (embedType === 'embed') {
      // 嵌入式 PDF 显示
      const embedHtml = `
        <div class="pdf-embed" data-url="${url}" data-title="${title}">
          <div class="pdf-embed-header">
            <div class="pdf-embed-icon">📄</div>
            <div class="pdf-embed-info">
              <div class="pdf-embed-title">${title}</div>
              ${description ? `<div class="pdf-embed-description">${description}</div>` : ''}
            </div>
            <a href="${url}" target="_blank" class="pdf-embed-link" title="在新窗口中打开">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                <polyline points="15,3 21,3 21,9"></polyline>
                <line x1="10" y1="14" x2="21" y2="3"></line>
              </svg>
            </a>
          </div>
          <div class="pdf-embed-preview">
            <iframe src="${url}" width="100%" height="400" frameborder="0"></iframe>
          </div>
        </div>
      `;
      
      editor
        .chain()
        .focus()
        .insertContent(embedHtml)
        .run();
    } else {
      // 链接式 PDF 显示
      const linkHtml = `
        <div class="pdf-link" data-url="${url}" data-title="${title}">
          <a href="${url}" target="_blank" class="pdf-link-content">
            <div class="pdf-link-icon">📄</div>
            <div class="pdf-link-info">
              <div class="pdf-link-title">${title}</div>
              ${description ? `<div class="pdf-link-description">${description}</div>` : ''}
              <div class="pdf-link-url">${url.length > 50 ? url.substring(0, 50) + '...' : url}</div>
            </div>
            <div class="pdf-link-arrow">→</div>
          </a>
        </div>
      `;
      
      editor
        .chain()
        .focus()
        .insertContent(linkHtml)
        .run();
    }

    onClose();
  }, [editor, pdfData, uploadMethod, validatePdfUrl, onClose]);

  /**
   * 重置状态
   */
  const handleReset = useCallback(() => {
    setPdfData({
      url: '',
      title: '',
      description: '',
      embedType: 'link',
    });
    setUploadState({
      isUploading: false,
      uploadProgress: 0,
      error: null,
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  return (
    <div className={`bg-background border border-border rounded-lg shadow-lg p-6 w-96 ${className}`}>
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <FileText className="h-5 w-5" />
          插入 PDF
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* 上传方式选择 */}
      <div className="flex gap-2 mb-4">
        <Button
          variant={uploadMethod === 'url' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setUploadMethod('url')}
        >
          URL 链接
        </Button>
        <Button
          variant={uploadMethod === 'file' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setUploadMethod('file')}
        >
          文件上传
        </Button>
      </div>

      {/* URL 输入区域 */}
      {uploadMethod === 'url' && (
        <div className="space-y-3 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">PDF URL</label>
            <div className="flex gap-2">
              <input
                type="url"
                value={pdfData.url}
                onChange={(e) => handleInputChange('url', e.target.value)}
                placeholder="https://example.com/document.pdf"
                className="flex-1 px-3 py-2 border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
              />
              {pdfData.url && validatePdfUrl(pdfData.url) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(pdfData.url, '_blank')}
                  title="在新窗口中预览"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 文件上传区域 */}
      {uploadMethod === 'file' && (
        <div
          className="border-2 border-dashed border-border rounded-lg p-6 text-center mb-4 transition-colors hover:border-primary/50"
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          {uploadState.isUploading ? (
            <div className="space-y-2">
              <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto" />
              <p className="text-sm text-muted-foreground">上传中...</p>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadState.uploadProgress}%` }}
                />
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <Upload className="h-8 w-8 text-muted-foreground mx-auto" />
              <p className="text-sm text-muted-foreground">
                拖拽 PDF 文件到此处或
                <button
                  className="text-primary hover:underline ml-1"
                  onClick={() => fileInputRef.current?.click()}
                >
                  点击选择文件
                </button>
              </p>
              <p className="text-xs text-muted-foreground">
                支持 PDF 格式，最大 10MB
              </p>
            </div>
          )}
        </div>
      )}

      {/* PDF 信息输入 */}
      <div className="space-y-3 mb-4">
        <div>
          <label className="block text-sm font-medium mb-1">标题</label>
          <input
            type="text"
            value={pdfData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="PDF 文档标题"
            className="w-full px-3 py-2 border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">描述 (可选)</label>
          <textarea
            value={pdfData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="简要描述 PDF 内容..."
            rows={2}
            className="w-full px-3 py-2 border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary resize-none"
          />
        </div>

        {/* 显示方式 */}
        <div>
          <label className="block text-sm font-medium mb-1">显示方式</label>
          <div className="flex gap-4">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="embedType"
                value="link"
                checked={pdfData.embedType === 'link'}
                onChange={(e) => handleInputChange('embedType', e.target.value)}
                className="w-4 h-4 text-primary"
              />
              <span className="text-sm">链接卡片</span>
            </label>
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="embedType"
                value="embed"
                checked={pdfData.embedType === 'embed'}
                onChange={(e) => handleInputChange('embedType', e.target.value)}
                className="w-4 h-4 text-primary"
              />
              <span className="text-sm">嵌入预览</span>
            </label>
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {uploadState.error && (
        <div className="flex items-center gap-2 p-3 bg-destructive/10 text-destructive rounded-md mb-4">
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span className="text-sm">{uploadState.error}</span>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex gap-2 justify-end">
        <Button
          variant="outline"
          size="sm"
          onClick={handleReset}
          disabled={uploadState.isUploading}
        >
          重置
        </Button>
        <Button
          size="sm"
          onClick={handleInsertPDF}
          disabled={!pdfData.url.trim() || !pdfData.title.trim() || uploadState.isUploading}
          className="flex items-center gap-2"
        >
          <Check className="h-4 w-4" />
          插入 PDF
        </Button>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,application/pdf"
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  );
}