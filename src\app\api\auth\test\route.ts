import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth/session";
import { prisma } from "@/lib/db/prisma";

/**
 * 认证系统测试 API 路由
 * GET /api/auth/test
 * 
 * 用于测试认证系统的各个组件是否正常工作：
 * - 数据库连接状态
 * - 用户会话状态
 * - 系统整体健康状况
 */
export async function GET(request: NextRequest) {
  try {
    // 测试数据库连接 - 获取用户总数
    const userCount = await prisma.user.count();
    
    // 测试会话管理 - 获取当前登录用户
    const currentUser = await getCurrentUser();
    
    // 返回测试结果
    return NextResponse.json({
      message: "认证系统测试成功",
      database: {
        connected: true,    // 数据库连接状态
        userCount,          // 用户总数
      },
      session: {
        authenticated: !!currentUser, // 是否已认证
        user: currentUser || null,    // 当前用户信息
      },
      timestamp: new Date().toISOString(), // 测试时间戳
    });
  } catch (error) {
    console.error("认证系统测试失败:", error);
    return NextResponse.json(
      { 
        error: "认证系统测试失败",
        details: error instanceof Error ? error.message : "未知错误"
      },
      { status: 500 }
    );
  }
}
