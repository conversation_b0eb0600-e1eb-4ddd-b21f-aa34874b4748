/**
 * 测试修复后的 OpenAI 服务
 */

require('dotenv').config();

async function testOpenAIFixed() {
  console.log('🧪 测试修复后的 OpenAI 服务...');

  try {
    const { OpenAIService } = require('./src/lib/services/ai/openai-service.ts');

    const config = {
      provider: 'openai',
      apiKey: '***************************************************',
      endpoint: 'http://127.0.0.1:57800', // 代理地址
      model: 'gpt-3.5-turbo',
      maxTokens: 10,
      temperature: 0.7,
      timeout: 60000 // 60秒超时
    };

    console.log('配置:', config);

    const service = new OpenAIService(config);
    console.log('OpenAI 服务创建成功');

    const connected = await service.testConnection();

    if (connected) {
      console.log('✅ OpenAI 连接测试成功!');

      // 尝试生成文本
      try {
        const response = await service._generateText({
          prompt: '你好，请简单回复',
          maxTokens: 10
        });
        console.log('📝 生成文本成功:', response.content);
        console.log('🔢 使用令牌数:', response.tokensUsed);
        console.log('⏱️ 响应时间:', response.responseTime + 'ms');
      } catch (error) {
        console.log('📝 生成文本失败:', error.message);
      }
    } else {
      console.log('❌ OpenAI 连接测试失败');
    }
  } catch (error) {
    console.log('❌ 测试异常:', error.message);
    console.log('错误详情:', error);
  }
}

testOpenAIFixed();