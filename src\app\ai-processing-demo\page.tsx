/**
 * AI 处理状态演示页面
 */

'use client';

import React, { useState } from 'react';
import { AIProcessingIndicator } from '@/components/ai/AIProcessingIndicator';
import { AIProcessingStatusBar } from '@/components/ai/AIProcessingStatusBar';
import { AIProcessingManager } from '@/components/ai/AIProcessingManager';
import { useAIProcessing, useMultipleAIProcessing } from '@/hooks/use-ai-processing';
import { 
  Play, 
  Pause, 
  Square, 
  RotateCcw,
  Activity,
  BarChart3,
  Settings,
  Zap,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

/**
 * 演示类型
 */
type DemoType = 'single' | 'multiple' | 'statusbar' | 'manager';

/**
 * AI 处理状态演示页面
 */
export default function AIProcessingDemoPage() {
  const [currentDemo, setCurrentDemo] = useState<DemoType>('single');
  const [isRunning, setIsRunning] = useState(false);

  // 单个处理演示
  const singleProcessing = useAIProcessing('demo-doc-1');
  
  // 多个处理演示
  const multipleProcessing = useMultipleAIProcessing('demo-doc-1');

  /**
   * 开始单个处理演示
   */
  const startSingleDemo = async () => {
    if (isRunning) return;
    
    setIsRunning(true);
    try {
      await singleProcessing.startProcessing(
        'generate',
        '请帮我写一段关于人工智能的介绍',
        {
          showDetailedProgress: true,
          showTokenCount: true,
          showTimeEstimate: true,
          allowCancel: true,
        }
      );
    } catch (error) {
      console.error('处理失败:', error);
    } finally {
      setIsRunning(false);
    }
  };

  /**
   * 开始多个处理演示
   */
  const startMultipleDemo = async () => {
    const tasks = [
      { type: 'generate', input: '生成一段关于机器学习的介绍' },
      { type: 'rewrite', input: '改写这段文字使其更正式' },
      { type: 'summarize', input: '总结这篇文章的主要内容' },
      { type: 'translate', input: '将这段中文翻译成英文' },
    ];

    for (const task of tasks) {
      try {
        await multipleProcessing.startProcessing(task.type, task.input);
        // 稍微延迟以便观察效果
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.error(`${task.type} 处理失败:`, error);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 头部导航 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">
                AI 处理状态演示
              </h1>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentDemo('single')}
                  className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${
                    currentDemo === 'single'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <Activity className="w-4 h-4 inline mr-1" />
                  单个处理
                </button>
                
                <button
                  onClick={() => setCurrentDemo('multiple')}
                  className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${
                    currentDemo === 'multiple'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <BarChart3 className="w-4 h-4 inline mr-1" />
                  多个处理
                </button>
                
                <button
                  onClick={() => setCurrentDemo('statusbar')}
                  className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${
                    currentDemo === 'statusbar'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <Settings className="w-4 h-4 inline mr-1" />
                  状态栏
                </button>
                
                <button
                  onClick={() => setCurrentDemo('manager')}
                  className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${
                    currentDemo === 'manager'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <Activity className="w-4 h-4 inline mr-1" />
                  管理器
                </button>
              </div>
            </div>

            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Activity className="w-4 h-4" />
              <span>演示环境</span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentDemo === 'single' && (
          <SingleProcessingDemo
            processing={singleProcessing}
            isRunning={isRunning}
            onStart={startSingleDemo}
          />
        )}

        {currentDemo === 'multiple' && (
          <MultipleProcessingDemo
            processing={multipleProcessing}
            onStart={startMultipleDemo}
          />
        )}

        {currentDemo === 'statusbar' && (
          <StatusBarDemo onStart={startMultipleDemo} />
        )}

        {currentDemo === 'manager' && (
          <ManagerDemo onStart={startMultipleDemo} />
        )}
      </div>

      {/* 状态栏（在状态栏演示模式下显示） */}
      {currentDemo === 'statusbar' && (
        <AIProcessingStatusBar documentId="demo-doc-1" />
      )}
    </div>
  );
}

/**
 * 单个处理演示组件
 */
interface SingleProcessingDemoProps {
  processing: ReturnType<typeof useAIProcessing>;
  isRunning: boolean;
  onStart: () => void;
}

function SingleProcessingDemo({ processing, isRunning, onStart }: SingleProcessingDemoProps) {
  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          单个 AI 处理演示
        </h3>
        
        <div className="flex items-center space-x-4 mb-4">
          <button
            onClick={onStart}
            disabled={isRunning || processing.isProcessing}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Play className="w-4 h-4 mr-2" />
            开始处理
          </button>

          <button
            onClick={processing.cancelProcessing}
            disabled={!processing.isProcessing}
            className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Square className="w-4 h-4 mr-2" />
            取消处理
          </button>

          <button
            onClick={processing.retryProcessing}
            disabled={!processing.error}
            className="flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            重试
          </button>

          <button
            onClick={processing.clearState}
            className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            清除状态
          </button>
        </div>

        {/* 状态信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">
              {processing.isProcessing ? '处理中' : '空闲'}
            </div>
            <div className="text-sm text-gray-600">状态</div>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">
              {processing.progress?.overallProgress.toFixed(0) || 0}%
            </div>
            <div className="text-sm text-gray-600">进度</div>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">
              {processing.progress?.currentStage || '-'}
            </div>
            <div className="text-sm text-gray-600">当前阶段</div>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">
              {processing.result ? '有结果' : '无结果'}
            </div>
            <div className="text-sm text-gray-600">结果</div>
          </div>
        </div>
      </div>

      {/* 处理指示器 */}
      {processing.progress && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h4 className="text-md font-semibold text-gray-900 mb-4">处理进度指示器</h4>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 详细模式 */}
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">详细模式</h5>
              <AIProcessingIndicator
                progress={processing.progress}
                onCancel={processing.cancelProcessing}
                onRetry={processing.retryProcessing}
                options={{
                  showDetailedProgress: true,
                  showTokenCount: true,
                  showTimeEstimate: true,
                  allowCancel: true,
                }}
              />
            </div>
            
            {/* 紧凑模式 */}
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">紧凑模式</h5>
              <AIProcessingIndicator
                progress={processing.progress}
                compact={true}
                onCancel={processing.cancelProcessing}
              />
            </div>
          </div>
        </div>
      )}

      {/* 结果显示 */}
      {processing.result && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h4 className="text-md font-semibold text-gray-900 mb-4">处理结果</h4>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">输入</label>
              <div className="p-3 bg-gray-50 rounded-lg text-sm text-gray-700">
                {processing.result.input}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">输出</label>
              <div className="p-3 bg-green-50 rounded-lg text-sm text-gray-700">
                {processing.result.output}
              </div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium">提供商:</span> {processing.result.provider}
              </div>
              <div>
                <span className="font-medium">模型:</span> {processing.result.model}
              </div>
              <div>
                <span className="font-medium">令牌:</span> {processing.result.tokensUsed}
              </div>
              <div>
                <span className="font-medium">耗时:</span> {processing.result.processingTime}ms
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 错误显示 */}
      {processing.error && (
        <div className="bg-white rounded-lg shadow-sm border border-red-200 p-6">
          <div className="flex items-center mb-2">
            <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
            <h4 className="text-md font-semibold text-red-900">处理错误</h4>
          </div>
          <div className="text-sm text-red-700 bg-red-50 p-3 rounded-lg">
            {processing.error}
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * 多个处理演示组件
 */
interface MultipleProcessingDemoProps {
  processing: ReturnType<typeof useMultipleAIProcessing>;
  onStart: () => void;
}

function MultipleProcessingDemo({ processing, onStart }: MultipleProcessingDemoProps) {
  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          多个 AI 处理演示
        </h3>
        
        <div className="flex items-center space-x-4 mb-4">
          <button
            onClick={onStart}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Play className="w-4 h-4 mr-2" />
            开始批量处理
          </button>

          <button
            onClick={processing.cancelAllProcessing}
            disabled={processing.activeCount === 0}
            className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Square className="w-4 h-4 mr-2" />
            取消所有
          </button>

          <button
            onClick={processing.clearCompleted}
            className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            清除已完成
          </button>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-lg font-semibold text-blue-700">
              {processing.activeCount}
            </div>
            <div className="text-sm text-blue-600">活动任务</div>
          </div>
          
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-lg font-semibold text-green-700">
              {processing.stats.successCount}
            </div>
            <div className="text-sm text-green-600">成功任务</div>
          </div>
          
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-lg font-semibold text-red-700">
              {processing.stats.errorCount}
            </div>
            <div className="text-sm text-red-600">失败任务</div>
          </div>
          
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-lg font-semibold text-purple-700">
              {processing.stats.totalTokensUsed.toLocaleString()}
            </div>
            <div className="text-sm text-purple-600">总令牌</div>
          </div>
        </div>
      </div>

      {/* 处理列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h4 className="text-md font-semibold text-gray-900">当前处理任务</h4>
        </div>
        
        <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
          {Array.from(processing.allProgress.entries()).map(([id, progress]) => (
            <div key={id} className="p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900">
                  任务 {id.slice(-8)}
                </span>
                <button
                  onClick={() => processing.cancelProcessing(id)}
                  disabled={!progress.cancellable}
                  className="px-2 py-1 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  取消
                </button>
              </div>
              
              <AIProcessingIndicator
                progress={progress}
                compact={true}
                onCancel={() => processing.cancelProcessing(id)}
              />
            </div>
          ))}
          
          {processing.allProgress.size === 0 && (
            <div className="p-8 text-center text-gray-500">
              <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>暂无处理任务</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * 状态栏演示组件
 */
interface StatusBarDemoProps {
  onStart: () => void;
}

function StatusBarDemo({ onStart }: StatusBarDemoProps) {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          AI 处理状态栏演示
        </h3>
        
        <div className="space-y-4">
          <p className="text-gray-600">
            状态栏会在页面底部显示当前的 AI 处理状态。点击下面的按钮开始处理任务，然后观察页面底部的状态栏。
          </p>
          
          <button
            onClick={onStart}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Play className="w-4 h-4 mr-2" />
            开始处理任务
          </button>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">状态栏功能特性：</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 显示当前活动的处理任务数量和总体进度</li>
              <li>• 可以展开查看详细的处理信息</li>
              <li>• 支持取消单个或所有处理任务</li>
              <li>• 显示处理统计信息（令牌消耗、平均时间等）</li>
              <li>• 自动隐藏已完成的任务</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * 管理器演示组件
 */
interface ManagerDemoProps {
  onStart: () => void;
}

function ManagerDemo({ onStart }: ManagerDemoProps) {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          AI 处理管理器演示
        </h3>
        
        <div className="space-y-4 mb-6">
          <p className="text-gray-600">
            处理管理器提供了完整的 AI 处理任务管理功能，包括任务监控、批量操作、统计分析等。
          </p>
          
          <button
            onClick={onStart}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Play className="w-4 h-4 mr-2" />
            创建演示任务
          </button>
        </div>
      </div>

      {/* AI 处理管理器 */}
      <AIProcessingManager
        documentId="demo-doc-1"
        showStats={true}
        showHistory={true}
      />
    </div>
  );
}