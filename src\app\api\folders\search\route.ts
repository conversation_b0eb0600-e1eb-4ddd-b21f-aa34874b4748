import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';

/**
 * GET /api/folders/search - 搜索文件夹
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const parentId = searchParams.get('parentId');
    const includeDocuments = searchParams.get('includeDocuments') === 'true';
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);

    if (!query || query.trim().length === 0) {
      return NextResponse.json({ folders: [] });
    }

    const whereClause: any = {
      userId: session.user.id,
      name: {
        contains: query,
        mode: 'insensitive',
      },
    };

    // 如果指定了父文件夹，只在该文件夹下搜索
    if (parentId) {
      whereClause.parentId = parentId;
    }

    const includeOptions: any = {
      parent: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          documents: true,
          children: true,
        },
      },
    };

    // 是否包含文档信息
    if (includeDocuments) {
      includeOptions.documents = {
        select: {
          id: true,
          title: true,
          createdAt: true,
          updatedAt: true,
          wordCount: true,
          charCount: true,
        },
        orderBy: {
          updatedAt: 'desc',
        },
        take: 10, // 每个文件夹最多返回10个文档
      };
    }

    const folders = await prisma.folder.findMany({
      where: whereClause,
      include: includeOptions,
      orderBy: [
        {
          name: 'asc',
        },
        {
          createdAt: 'desc',
        },
      ],
      take: limit,
    });

    // 为每个文件夹添加路径信息
    const foldersWithPath = await Promise.all(
      folders.map(async (folder) => {
        const path = await getFolderPath(folder.id);
        return {
          ...folder,
          path,
          pathString: path.join(' / '),
        };
      })
    );

    return NextResponse.json({ 
      folders: foldersWithPath,
      total: folders.length,
      query,
    });
  } catch (error) {
    console.error('搜索文件夹失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 获取文件夹的完整路径
 */
async function getFolderPath(folderId: string): Promise<string[]> {
  const folder = await prisma.folder.findUnique({
    where: { id: folderId },
    include: {
      parent: true,
    },
  });

  if (!folder) {
    return [];
  }

  const path = [folder.name];

  if (folder.parent) {
    const parentPath = await getFolderPath(folder.parent.id);
    path.unshift(...parentPath);
  }

  return path;
}