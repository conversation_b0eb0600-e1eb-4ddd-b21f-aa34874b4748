'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { 
  Bot,
  MessageCircle,
  Edit3,
  FileText,
  Languages,
  Lightbulb,
  X,
  ChevronUp,
  ChevronDown
} from 'lucide-react';

interface MobileAIPanelProps {
  visible?: boolean;
  onClose?: () => void;
  onAction?: (action: string) => void;
}

/**
 * 移动端优化的AI助手面板
 * 提供触摸友好的界面和简化的功能访问
 */
export function MobileAIPanel({
  visible = false,
  onClose,
  onAction
}: MobileAIPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  // AI功能分类
  const aiCategories = [
    {
      id: 'chat',
      name: '对话',
      icon: MessageCircle,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      actions: [
        { id: 'explain', name: '解释', description: '解释选中内容' },
        { id: 'summarize', name: '总结', description: '总结文档内容' },
        { id: 'qa', name: '问答', description: '回答相关问题' },
      ]
    },
    {
      id: 'edit',
      name: '编辑',
      icon: Edit3,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      actions: [
        { id: 'improve', name: '优化', description: '改进文本质量' },
        { id: 'rewrite', name: '改写', description: '重新表达内容' },
        { id: 'formal', name: '正式化', description: '转为正式语调' },
        { id: 'simplify', name: '简化', description: '简化表达' },
      ]
    },
    {
      id: 'generate',
      name: '生成',
      icon: FileText,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      actions: [
        { id: 'continue', name: '续写', description: '继续写作' },
        { id: 'expand', name: '扩展', description: '扩展内容' },
        { id: 'outline', name: '大纲', description: '生成大纲' },
      ]
    },
    {
      id: 'translate',
      name: '翻译',
      icon: Languages,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      actions: [
        { id: 'translate-en', name: '译英文', description: '翻译为英文' },
        { id: 'translate-zh', name: '译中文', description: '翻译为中文' },
        { id: 'translate-other', name: '其他语言', description: '翻译为其他语言' },
      ]
    }
  ];

  // 处理动作点击
  const handleActionClick = (actionId: string) => {
    onAction?.(actionId);
    onClose?.();
  };

  // 处理分类点击
  const handleCategoryClick = (categoryId: string) => {
    if (activeCategory === categoryId) {
      setActiveCategory(null);
    } else {
      setActiveCategory(categoryId);
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <>
      {/* 遮罩层 */}
      <div 
        className="fixed inset-0 bg-black/50 z-40"
        onClick={onClose}
      />
      
      {/* 面板 */}
      <div className={`
        fixed bottom-0 left-0 right-0 z-50
        bg-background border-t border-border rounded-t-2xl
        transition-transform duration-300 ease-out
        ${visible ? 'transform translate-y-0' : 'transform translate-y-full'}
        max-h-[80vh] overflow-hidden
      `}>
        {/* 拖拽指示器 */}
        <div className="flex justify-center py-2">
          <div className="w-12 h-1 bg-muted-foreground/30 rounded-full" />
        </div>
        
        {/* 头部 */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-border">
          <div className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-semibold">AI 助手</h2>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-8 w-8 p-0"
            >
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* 内容区域 */}
        <div className={`
          overflow-y-auto
          ${isExpanded ? 'max-h-[60vh]' : 'max-h-[40vh]'}
          transition-all duration-300
        `}>
          <div className="p-4 space-y-3">
            {aiCategories.map((category) => {
              const Icon = category.icon;
              const isActive = activeCategory === category.id;
              
              return (
                <div key={category.id} className="space-y-2">
                  {/* 分类按钮 */}
                  <Button
                    variant="ghost"
                    onClick={() => handleCategoryClick(category.id)}
                    className={`
                      w-full h-12 justify-between p-3
                      ${isActive ? category.bgColor : 'hover:bg-muted'}
                      touch-manipulation
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <Icon className={`h-5 w-5 ${category.color}`} />
                      <span className="font-medium">{category.name}</span>
                    </div>
                    <ChevronDown className={`
                      h-4 w-4 transition-transform duration-200
                      ${isActive ? 'rotate-180' : ''}
                    `} />
                  </Button>
                  
                  {/* 动作列表 */}
                  {isActive && (
                    <div className="ml-4 space-y-1 animate-in slide-in-from-top-2 duration-200">
                      {category.actions.map((action) => (
                        <Button
                          key={action.id}
                          variant="ghost"
                          onClick={() => handleActionClick(action.id)}
                          className="w-full h-10 justify-start p-3 text-left touch-manipulation hover:bg-muted/50"
                        >
                          <div className="flex-1">
                            <div className="font-medium text-sm">{action.name}</div>
                            <div className="text-xs text-muted-foreground">{action.description}</div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          
          {/* 快捷操作 */}
          <div className="border-t border-border p-4">
            <div className="text-sm font-medium text-muted-foreground mb-3">快捷操作</div>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleActionClick('quick-improve')}
                className="h-10 touch-manipulation"
              >
                <Lightbulb className="h-4 w-4 mr-2" />
                快速优化
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleActionClick('quick-translate')}
                className="h-10 touch-manipulation"
              >
                <Languages className="h-4 w-4 mr-2" />
                快速翻译
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}export
 default MobileAIPanel;