import { prisma } from '@/lib/db/prisma';
import { JSONContent } from '@tiptap/react';

/**
 * 版本历史记录类型
 */
export interface DocumentVersion {
  id: string;
  documentId: string;
  version: number;
  content: JSONContent;
  title: string;
  changeType: 'user' | 'ai' | 'sync' | 'merge';
  createdAt: Date;
  metadata: {
    wordCount: number;
    characterCount: number;
    changesSummary?: string;
    conflictResolution?: 'local' | 'remote' | 'merge';
  };
}

/**
 * 版本比较结果
 */
export interface VersionComparison {
  additions: string[];
  deletions: string[];
  modifications: string[];
  summary: string;
  similarity: number;
}

/**
 * 文档版本历史服务
 */
export class VersionHistoryService {
  /**
   * 创建新的版本记录
   */
  static async createVersion(
    documentId: string,
    content: JSONContent,
    title: string,
    changeType: 'user' | 'ai' | 'sync' | 'merge',
    conflictResolution?: 'local' | 'remote' | 'merge'
  ): Promise<DocumentVersion> {
    // 获取当前最大版本号
    const lastVersion = await prisma.documentHistory.findFirst({
      where: { documentId },
      orderBy: { version: 'desc' },
    });

    const nextVersion = (lastVersion?.version || 0) + 1;

    // 计算文本统计
    const textContent = this.extractTextFromContent(content);
    const wordCount = textContent.trim().split(/\s+/).filter(word => word.length > 0).length;
    const characterCount = textContent.length;

    // 生成变更摘要
    let changesSummary = '';
    if (lastVersion) {
      const lastContent = typeof lastVersion.content === 'string' 
        ? JSON.parse(lastVersion.content) as JSONContent 
        : lastVersion.content as unknown as JSONContent;
      const comparison = await this.compareVersions(lastContent, content);
      changesSummary = comparison.summary;
    }

    // 创建版本记录
    const versionRecord = await prisma.documentHistory.create({
      data: {
        documentId,
        version: nextVersion,
        content: JSON.stringify(content),
        changeType,
        // 注意：changeDescription 和 metadata 字段在当前 schema 中不存在
        // 这些信息可以存储在 content 的 JSON 结构中或者需要更新 schema
      },
    });

    return {
      id: versionRecord.id,
      documentId: versionRecord.documentId,
      version: versionRecord.version,
      content,
      title,
      changeType: versionRecord.changeType as any,
      changeDescription: undefined, // 字段不存在
      createdAt: versionRecord.createdAt,
      metadata: {
        wordCount,
        characterCount,
        changesSummary,
        conflictResolution
      }
    };
  }

  /**
   * 获取文档的版本历史
   */
  static async getVersionHistory(
    documentId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<DocumentVersion[]> {
    const versions = await prisma.documentHistory.findMany({
      where: { documentId },
      orderBy: { version: 'desc' },
      take: limit,
      skip: offset,
    });

    return versions.map(version => {
      // metadata 字段在当前 schema 中不存在，使用默认值
      const metadata = {};

      return {
        id: version.id,
        documentId: version.documentId,
        version: version.version,
        content: typeof version.content === 'string' 
          ? JSON.parse(version.content) 
          : version.content as JSONContent,
        title: '未命名文档', // metadata 字段不存在，使用默认值
        changeType: version.changeType as any,
        // changeDescription: undefined, // 字段不存在于当前 schema
        createdAt: version.createdAt,
        metadata: {
          wordCount: 0, // metadata 字段不存在，使用默认值
          characterCount: 0, // metadata 字段不存在，使用默认值
          changesSummary: undefined, // metadata 字段不存在
          conflictResolution: undefined // metadata 字段不存在
        }
      };
    });
  }

  /**
   * 获取特定版本
   */
  static async getVersion(documentId: string, version: number): Promise<DocumentVersion | null> {
    const versionRecord = await prisma.documentHistory.findFirst({
      where: {
        documentId,
        version
      },
    });

    if (!versionRecord) return null;

    // metadata 字段在当前 schema 中不存在，使用默认值
    const metadata = {};

    return {
      id: versionRecord.id,
      documentId: versionRecord.documentId,
      version: versionRecord.version,
      content: typeof versionRecord.content === 'string' 
        ? JSON.parse(versionRecord.content) 
        : versionRecord.content as JSONContent,
      title: '未命名文档', // metadata 字段不存在，使用默认值
      changeType: versionRecord.changeType as any,
      createdAt: versionRecord.createdAt,
      metadata: {
        wordCount: 0, // metadata 字段不存在，使用默认值
        characterCount: 0, // metadata 字段不存在，使用默认值
        changesSummary: undefined, // metadata 字段不存在
        conflictResolution: undefined // metadata 字段不存在
      }
    };
  }

  /**
   * 比较两个版本
   */
  static async compareVersions(
    oldContent: JSONContent,
    newContent: JSONContent
  ): Promise<VersionComparison> {
    const oldText = this.extractTextFromContent(oldContent);
    const newText = this.extractTextFromContent(newContent);

    // 简化的文本比较 - 实际实现中可以使用更复杂的diff算法
    const oldLines = oldText.split('\n').filter(line => line.trim());
    const newLines = newText.split('\n').filter(line => line.trim());

    const additions: string[] = [];
    const deletions: string[] = [];
    const modifications: string[] = [];

    // 简单的行级比较
    const oldSet = new Set(oldLines);
    const newSet = new Set(newLines);

    // 找出新增的行
    for (const line of newLines) {
      if (!oldSet.has(line)) {
        additions.push(line);
      }
    }

    // 找出删除的行
    for (const line of oldLines) {
      if (!newSet.has(line)) {
        deletions.push(line);
      }
    }

    // 计算相似度
    const totalLines = Math.max(oldLines.length, newLines.length);
    const changedLines = additions.length + deletions.length;
    const similarity = totalLines > 0 ? (totalLines - changedLines) / totalLines : 1;

    // 生成摘要
    let summary = '';
    if (additions.length > 0) {
      summary += `新增 ${additions.length} 行`;
    }
    if (deletions.length > 0) {
      if (summary) summary += '，';
      summary += `删除 ${deletions.length} 行`;
    }
    if (modifications.length > 0) {
      if (summary) summary += '，';
      summary += `修改 ${modifications.length} 行`;
    }
    if (!summary) {
      summary = '无变更';
    }

    return {
      additions,
      deletions,
      modifications,
      summary,
      similarity
    };
  }

  /**
   * 恢复到指定版本
   */
  static async restoreToVersion(
    documentId: string,
    version: number,
    userId: string
  ): Promise<DocumentVersion | null> {
    const targetVersion = await this.getVersion(documentId, version);
    if (!targetVersion) return null;

    // 验证用户权限（这里可以添加权限检查逻辑）
    console.log(`用户 ${userId} 正在恢复文档 ${documentId} 到版本 ${version}`);

    // 更新文档到指定版本
    await prisma.document.update({
      where: { id: documentId },
      data: {
        title: targetVersion.title,
        content: JSON.stringify(targetVersion.content),
        wordCount: 0, // metadata 字段不存在，使用默认值
        charCount: 0, // metadata 字段不存在，使用默认值
        updatedAt: new Date(),
      },
    });

    // 创建恢复操作的版本记录
    const restoreVersion = await this.createVersion(
      documentId,
      targetVersion.content,
      targetVersion.title,
      'user'
    );

    return restoreVersion;
  }

  /**
   * 清理旧版本
   */
  static async cleanupOldVersions(
    documentId: string,
    keepCount: number = 100
  ): Promise<number> {
    // 获取需要删除的版本
    const versionsToDelete = await prisma.documentHistory.findMany({
      where: { documentId },
      orderBy: { version: 'desc' },
      skip: keepCount,
      select: { id: true },
    });

    if (versionsToDelete.length === 0) return 0;

    // 删除旧版本
    const deleteResult = await prisma.documentHistory.deleteMany({
      where: {
        id: {
          in: versionsToDelete.map(v => v.id)
        }
      }
    });

    return deleteResult.count;
  }

  /**
   * 获取版本统计信息
   */
  static async getVersionStats(documentId: string): Promise<{
    totalVersions: number;
    oldestVersion: Date | null;
    newestVersion: Date | null;
    changeTypes: Record<string, number>;
  }> {
    const [totalCount, oldestVersion, newestVersion, changeTypeCounts] = await Promise.all([
      prisma.documentHistory.count({
        where: { documentId }
      }),
      prisma.documentHistory.findFirst({
        where: { documentId },
        orderBy: { version: 'asc' },
        select: { createdAt: true }
      }),
      prisma.documentHistory.findFirst({
        where: { documentId },
        orderBy: { version: 'desc' },
        select: { createdAt: true }
      }),
      prisma.documentHistory.groupBy({
        by: ['changeType'],
        where: { documentId },
        _count: { changeType: true }
      })
    ]);

    const changeTypes: Record<string, number> = {};
    for (const group of changeTypeCounts) {
      changeTypes[group.changeType] = group._count.changeType;
    }

    return {
      totalVersions: totalCount,
      oldestVersion: oldestVersion?.createdAt || null,
      newestVersion: newestVersion?.createdAt || null,
      changeTypes
    };
  }

  /**
   * 从内容中提取文本
   */
  private static extractTextFromContent(content: JSONContent): string {
    if (!content) return '';

    let text = '';

    if (content.text) {
      text += content.text;
    }

    if (content.content && Array.isArray(content.content)) {
      for (const child of content.content) {
        text += this.extractTextFromContent(child);
        if (child.type === 'paragraph' || child.type === 'heading') {
          text += '\n';
        }
      }
    }

    return text;
  }

  /**
   * 搜索版本历史
   */
  static async searchVersionHistory(
    documentId: string,
    query: string,
    limit: number = 20
  ): Promise<DocumentVersion[]> {
    // 获取所有版本
    const versions = await this.getVersionHistory(documentId, 200); // 搜索最近200个版本

    // 在内容中搜索
    const matchingVersions = versions.filter(version => {
      const text = this.extractTextFromContent(version.content).toLowerCase();
      const title = version.title.toLowerCase();
      const searchQuery = query.toLowerCase();
      
      return text.includes(searchQuery) || 
             title.includes(searchQuery) ||
             false; // changeDescription 字段不存在
    });

    return matchingVersions.slice(0, limit);
  }
}
