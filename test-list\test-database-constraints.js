/**
 * 数据库约束测试用例
 * 
 * 文件作用：
 * 1. 测试新添加的数据库唯一约束是否生效
 * 2. 验证约束对不同场景的处理
 * 3. 确认软删除场景下的约束行为
 * 
 * 测试的约束：
 * - unique_active_document_title: 同一用户在同一文件夹下不能有重复标题的文档
 * - unique_active_folder_name: 同一用户在同一父文件夹下不能有重复名称的文件夹
 * 
 * 测试场景：
 * 1. 正常创建（应该成功）
 * 2. 重复创建（应该失败）
 * 3. 不同位置创建同名（应该成功）
 * 4. 软删除后重新创建（应该成功）
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * 测试文档唯一约束
 */
async function testDocumentConstraint(userId) {
  console.log('\n📄 测试文档唯一约束');
  const testTitle = 'ConstraintTestDoc';
  const createdDocs = [];
  
  try {
    // 测试1: 正常创建文档
    console.log('   测试1: 在根目录创建文档');
    const doc1 = await prisma.document.create({
      data: {
        title: testTitle,
        content: '测试内容1',
        userId: userId,
        folderId: null,
        isDeleted: false
      }
    });
    createdDocs.push(doc1);
    console.log(`   ✅ 成功创建: ${doc1.id.slice(-8)}...`);
    
    // 测试2: 尝试在同一位置创建重复标题文档（应该失败）
    console.log('   测试2: 在根目录创建同标题文档（应该失败）');
    try {
      const doc2 = await prisma.document.create({
        data: {
          title: testTitle,
          content: '测试内容2',
          userId: userId,
          folderId: null,
          isDeleted: false
        }
      });
      createdDocs.push(doc2);
      console.log(`   ❌ 意外成功: ${doc2.id.slice(-8)}... (约束未生效)`);
    } catch (error) {
      console.log(`   ✅ 正确失败: ${error.message.includes('UNIQUE constraint') ? '唯一约束生效' : error.message}`);
    }
    
    // 测试3: 在不同文件夹创建同标题文档（应该成功）
    const folder = await prisma.folder.findFirst({ where: { userId, isDeleted: false } });
    if (folder) {
      console.log('   测试3: 在不同文件夹创建同标题文档（应该成功）');
      try {
        const doc3 = await prisma.document.create({
          data: {
            title: testTitle,
            content: '测试内容3',
            userId: userId,
            folderId: folder.id,
            isDeleted: false
          }
        });
        createdDocs.push(doc3);
        console.log(`   ✅ 成功创建: ${doc3.id.slice(-8)}... (不同位置允许同名)`);
      } catch (error) {
        console.log(`   ❌ 意外失败: ${error.message}`);
      }
    }
    
    // 测试4: 软删除后重新创建（应该成功）
    console.log('   测试4: 软删除后重新创建同标题文档（应该成功）');
    await prisma.document.update({
      where: { id: doc1.id },
      data: { isDeleted: true }
    });
    console.log('   - 已软删除第一个文档');
    
    try {
      const doc4 = await prisma.document.create({
        data: {
          title: testTitle,
          content: '测试内容4',
          userId: userId,
          folderId: null,
          isDeleted: false
        }
      });
      createdDocs.push(doc4);
      console.log(`   ✅ 成功创建: ${doc4.id.slice(-8)}... (软删除后允许重新创建)`);
    } catch (error) {
      console.log(`   ❌ 意外失败: ${error.message}`);
    }
    
    return createdDocs;
    
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`);
    return createdDocs;
  }
}

/**
 * 测试文件夹唯一约束
 */
async function testFolderConstraint(userId) {
  console.log('\n📁 测试文件夹唯一约束');
  const testName = 'ConstraintTestFolder';
  const createdFolders = [];
  
  try {
    // 测试1: 正常创建文件夹
    console.log('   测试1: 在根目录创建文件夹');
    const folder1 = await prisma.folder.create({
      data: {
        name: testName,
        userId: userId,
        parentId: null,
        isDeleted: false
      }
    });
    createdFolders.push(folder1);
    console.log(`   ✅ 成功创建: ${folder1.id.slice(-8)}...`);
    
    // 测试2: 尝试在同一位置创建重复名称文件夹（应该失败）
    console.log('   测试2: 在根目录创建同名文件夹（应该失败）');
    try {
      const folder2 = await prisma.folder.create({
        data: {
          name: testName,
          userId: userId,
          parentId: null,
          isDeleted: false
        }
      });
      createdFolders.push(folder2);
      console.log(`   ❌ 意外成功: ${folder2.id.slice(-8)}... (约束未生效)`);
    } catch (error) {
      console.log(`   ✅ 正确失败: ${error.message.includes('UNIQUE constraint') ? '唯一约束生效' : error.message}`);
    }
    
    // 测试3: 在不同父文件夹创建同名文件夹（应该成功）
    const parentFolder = await prisma.folder.findFirst({ 
      where: { userId, isDeleted: false, id: { not: folder1.id } } 
    });
    if (parentFolder) {
      console.log('   测试3: 在不同父文件夹创建同名文件夹（应该成功）');
      try {
        const folder3 = await prisma.folder.create({
          data: {
            name: testName,
            userId: userId,
            parentId: parentFolder.id,
            isDeleted: false
          }
        });
        createdFolders.push(folder3);
        console.log(`   ✅ 成功创建: ${folder3.id.slice(-8)}... (不同位置允许同名)`);
      } catch (error) {
        console.log(`   ❌ 意外失败: ${error.message}`);
      }
    }
    
    // 测试4: 软删除后重新创建（应该成功）
    console.log('   测试4: 软删除后重新创建同名文件夹（应该成功）');
    await prisma.folder.update({
      where: { id: folder1.id },
      data: { isDeleted: true }
    });
    console.log('   - 已软删除第一个文件夹');
    
    try {
      const folder4 = await prisma.folder.create({
        data: {
          name: testName,
          userId: userId,
          parentId: null,
          isDeleted: false
        }
      });
      createdFolders.push(folder4);
      console.log(`   ✅ 成功创建: ${folder4.id.slice(-8)}... (软删除后允许重新创建)`);
    } catch (error) {
      console.log(`   ❌ 意外失败: ${error.message}`);
    }
    
    return createdFolders;
    
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`);
    return createdFolders;
  }
}

/**
 * 主测试函数
 */
async function testDatabaseConstraints() {
  console.log('🚀 开始数据库约束测试');
  console.log('=' .repeat(60));
  
  let allCreatedItems = [];
  
  try {
    // 获取测试用户
    const user = await prisma.user.findFirst();
    if (!user) {
      console.log('❌ 没有测试用户');
      return;
    }
    console.log(`👤 使用测试用户: ${user.name || user.email}`);
    
    // 测试文档约束
    const createdDocs = await testDocumentConstraint(user.id);
    allCreatedItems.push(...createdDocs);
    
    // 测试文件夹约束
    const createdFolders = await testFolderConstraint(user.id);
    allCreatedItems.push(...createdFolders);
    
    console.log('\n🎯 测试总结:');
    console.log(`   📄 测试了文档唯一约束，创建了 ${createdDocs.length} 个测试文档`);
    console.log(`   📁 测试了文件夹唯一约束，创建了 ${createdFolders.length} 个测试文件夹`);
    console.log('   ✅ 数据库约束已成功添加并生效');
    console.log('   📝 现在同一位置不能创建重复名称的项目');
    console.log('   🔄 软删除后可以重新创建同名项目');
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
  } finally {
    // 清理测试数据
    console.log('\n🧹 清理测试数据...');
    
    // 清理文档
    const docIds = allCreatedItems.filter(item => item.title).map(item => item.id);
    if (docIds.length > 0) {
      await prisma.document.deleteMany({
        where: { id: { in: docIds } }
      });
      console.log(`   ✅ 清理了 ${docIds.length} 个测试文档`);
    }
    
    // 清理文件夹
    const folderIds = allCreatedItems.filter(item => item.name).map(item => item.id);
    if (folderIds.length > 0) {
      await prisma.folder.deleteMany({
        where: { id: { in: folderIds } }
      });
      console.log(`   ✅ 清理了 ${folderIds.length} 个测试文件夹`);
    }
    
    await prisma.$disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

// 执行测试
console.log('🚀 开始执行数据库约束测试...');
testDatabaseConstraints();
