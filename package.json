{"name": "nextjs-document-editor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4501", "build": "next build", "start": "next start -p 4501", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset --force", "db:test": "tsx scripts/test-db.ts", "db:deploy": "prisma migrate deploy", "test:ai-config": "node scripts/test-ai-config-page.js", "test:install": "npx playwright install"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.22.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tiptap/extension-character-count": "^2.1.13", "@tiptap/extension-horizontal-rule": "^2.26.1", "@tiptap/extension-image": "^2.26.1", "@tiptap/extension-link": "^2.26.1", "@tiptap/extension-placeholder": "^2.1.13", "@tiptap/extension-table": "^2.26.1", "@tiptap/extension-table-cell": "^2.26.1", "@tiptap/extension-table-header": "^2.26.1", "@tiptap/extension-table-row": "^2.26.1", "@tiptap/extension-task-item": "^2.26.1", "@tiptap/extension-task-list": "^2.26.1", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^4.1.0", "dexie": "^3.2.4", "dotenv": "^17.2.1", "https-proxy-agent": "^7.0.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "nanoid": "^5.1.5", "next": "14.0.4", "next-auth": "^4.24.5", "node-fetch": "^2.7.0", "openai": "^5.10.2", "prisma": "^5.22.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "tailwind-merge": "^2.2.0", "zod": "^3.25.76", "zustand": "^4.4.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/node-fetch": "^2.6.12", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "playwright": "^1.40.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^3.4.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}}