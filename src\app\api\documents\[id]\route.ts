import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';
import { z } from 'zod';

// 文档更新的验证模式
const updateDocumentSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(255, '标题长度不能超过255个字符').optional(),
  content: z.string().optional(),
  folderId: z.string().nullable().optional(),
  createHistory: z.boolean().optional().default(false), // 是否创建历史记录
});

/**
 * GET /api/documents/[id] - 获取指定文档
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const includeHistory = searchParams.get('includeHistory') === 'true';

    const document = await prisma.document.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
        isDeleted: false, // 不返回已删除的文档
      },
      include: {
        folder: {
          select: {
            id: true,
            name: true,
          },
        },
        ...(includeHistory && {
          history: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 10, // 最近10条历史记录
          },
        }),
      },
    });

    if (!document) {
      return NextResponse.json({ error: '文档未找到' }, { status: 404 });
    }

    // 更新最后同步时间
    await prisma.document.update({
      where: { id: params.id },
      data: { lastSyncAt: new Date() },
    });

    return NextResponse.json({ document });
  } catch (error) {
    console.error('获取文档失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/documents/[id] - 更新文档
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = updateDocumentSchema.parse(body);

    // 检查文档是否存在且属于当前用户
    const existingDocument = await prisma.document.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!existingDocument) {
      return NextResponse.json({ error: '文档未找到' }, { status: 404 });
    }

    // 如果要移动到文件夹，验证文件夹是否存在且属于当前用户
    if (validatedData.folderId !== undefined && validatedData.folderId) {
      const folder = await prisma.folder.findFirst({
        where: {
          id: validatedData.folderId,
          userId: session.user.id,
        },
      });

      if (!folder) {
        return NextResponse.json(
          { error: '文件夹未找到' },
          { status: 404 }
        );
      }
    }

    // 如果内容有更新，计算字数和字符数
    let wordCount = existingDocument.wordCount;
    let charCount = existingDocument.charCount;

    if (validatedData.content !== undefined) {
      const plainText = validatedData.content.replace(/<[^>]*>/g, '').trim();
      wordCount = plainText ? plainText.split(/\s+/).length : 0;
      charCount = plainText.length;
    }

    // 使用事务更新文档和创建历史记录
    const result = await prisma.$transaction(async (tx) => {
      // 更新文档
      const updatedDocument = await tx.document.update({
        where: {
          id: params.id,
        },
        data: {
          ...(validatedData.title && { title: validatedData.title }),
          ...(validatedData.content !== undefined && { content: validatedData.content }),
          ...(validatedData.folderId !== undefined && { folderId: validatedData.folderId }),
          wordCount,
          charCount,
          lastSyncAt: new Date(),
        },
        include: {
          folder: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // 如果内容有更新且需要创建历史记录
      if (validatedData.content !== undefined && validatedData.createHistory) {
        // 获取当前最大版本号
        const lastHistory = await tx.documentHistory.findFirst({
          where: { documentId: params.id },
          orderBy: { version: 'desc' },
        });

        const nextVersion = (lastHistory?.version || 0) + 1;

        // 创建历史记录
        await tx.documentHistory.create({
          data: {
            documentId: params.id,
            version: nextVersion,
            content: validatedData.content,
            changeType: 'user',
          },
        });
      }

      return updatedDocument;
    });

    console.log(`用户 ${session.user.id} 更新了文档: ${result.title}`);

    return NextResponse.json({ document: result });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '输入参数无效', details: error.issues },
        { status: 400 }
      );
    }

    console.error('更新文档失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/documents/[id] - 删除文档
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    // 检查文档是否存在且属于当前用户
    const document = await prisma.document.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!document) {
      return NextResponse.json({ error: '文档未找到' }, { status: 404 });
    }

    // 使用事务删除文档及其相关数据
    await prisma.$transaction(async (tx) => {
      // 删除文档历史记录
      await tx.documentHistory.deleteMany({
        where: { documentId: params.id },
      });

      // 删除AI交互记录
      await tx.aIInteraction.deleteMany({
        where: { documentId: params.id },
      });

      // 删除文档
      await tx.document.delete({
        where: { id: params.id },
      });
    });

    console.log(`用户 ${session.user.id} 删除了文档: ${document.title}`);

    return NextResponse.json({ message: '文档删除成功' });
  } catch (error) {
    console.error('删除文档失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}