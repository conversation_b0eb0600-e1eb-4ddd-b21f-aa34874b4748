import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { VersionHistoryService } from '@/lib/services/version-history';
import { DocumentService } from '@/lib/services/document-service';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * 比较两个版本
 * POST /api/documents/[id]/versions/compare
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const documentId = params.id;
    const { version1, version2 } = await request.json();

    if (!version1 || !version2) {
      return NextResponse.json(
        { error: '需要提供两个版本号进行比较' },
        { status: 400 }
      );
    }

    // 验证文档访问权限
    const document = await DocumentService.validateDocumentAccess(
      documentId,
      session.user.id
    );

    if (!document) {
      return NextResponse.json(
        { error: '文档不存在或无权限访问' },
        { status: 404 }
      );
    }

    // 获取两个版本
    const [versionData1, versionData2] = await Promise.all([
      VersionHistoryService.getVersion(documentId, version1),
      VersionHistoryService.getVersion(documentId, version2)
    ]);

    if (!versionData1 || !versionData2) {
      return NextResponse.json(
        { error: '指定的版本不存在' },
        { status: 404 }
      );
    }

    // 比较版本
    const comparison = await VersionHistoryService.compareVersions(
      versionData1.content,
      versionData2.content
    );

    return NextResponse.json({
      version1: versionData1,
      version2: versionData2,
      comparison,
      success: true
    });

  } catch (error) {
    console.error('版本比较失败:', error);
    return NextResponse.json(
      { error: '版本比较失败' },
      { status: 500 }
    );
  }
}