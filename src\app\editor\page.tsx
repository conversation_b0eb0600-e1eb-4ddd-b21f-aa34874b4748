'use client';

import { ResponsiveEditor } from '@/components/editor/ResponsiveEditor';
import { useState } from 'react';

export default function EditorPage() {
  const [content, setContent] = useState('');

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
  };

  const handleSave = (content: string) => {
    console.log('Saving content:', content);
    // TODO: Implement actual save functionality
  };

  return (
    <ResponsiveEditor
      initialContent="<h1>欢迎使用智能文档编辑器</h1><p>开始在这里写作...</p><p>这是一个功能完整的富文本编辑器，支持：</p><ul><li><strong>粗体</strong>和<em>斜体</em>文本</li><li>多级标题</li><li>有序和无序列表</li><li>引用块</li><li>代码块</li></ul><blockquote><p>这是一个引用示例</p></blockquote><p>您可以使用工具栏中的按钮来格式化文本，或者使用键盘快捷键。</p>"
      placeholder="开始写作..."
      onChange={handleContentChange}
      onSave={handleSave}
    />
  );
}