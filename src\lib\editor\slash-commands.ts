import { Editor, Range } from '@tiptap/react';
import { SlashCommand, SlashCommandCategory } from '@/types/slash-command.types';

/**
 * 基础格式化命令
 */
const basicCommands: SlashCommand[] = [
  {
    id: 'heading1',
    label: '标题 1',
    description: '大标题',
    icon: 'H1',
    category: 'basic',
    shortcut: 'Ctrl+Alt+1',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setHeading({ level: 1 })
        .run();
    },
  },
  {
    id: 'heading2',
    label: '标题 2',
    description: '中标题',
    icon: 'H2',
    category: 'basic',
    shortcut: 'Ctrl+Alt+2',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setHeading({ level: 2 })
        .run();
    },
  },
  {
    id: 'heading3',
    label: '标题 3',
    description: '小标题',
    icon: 'H3',
    category: 'basic',
    shortcut: 'Ctrl+Alt+3',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setHeading({ level: 3 })
        .run();
    },
  },
  {
    id: 'paragraph',
    label: '正文',
    description: '普通段落文本',
    icon: 'P',
    category: 'basic',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setParagraph()
        .run();
    },
  },
  {
    id: 'bullet-list',
    label: '无序列表',
    description: '创建无序列表',
    icon: '•',
    category: 'basic',
    shortcut: 'Ctrl+Shift+8',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleBulletList()
        .run();
    },
  },
  {
    id: 'numbered-list',
    label: '有序列表',
    description: '创建有序列表',
    icon: '1.',
    category: 'basic',
    shortcut: 'Ctrl+Shift+7',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleOrderedList()
        .run();
    },
  },
  {
    id: 'quote',
    label: '引用',
    description: '创建引用块',
    icon: '"',
    category: 'basic',
    shortcut: 'Ctrl+Shift+B',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleBlockquote()
        .run();
    },
  },
  {
    id: 'code-block',
    label: '代码块',
    description: '插入代码块',
    icon: '</>',
    category: 'basic',
    shortcut: 'Ctrl+Alt+C',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleCodeBlock()
        .run();
    },
  },
  {
    id: 'inline-code',
    label: '行内代码',
    description: '添加行内代码格式',
    icon: '`',
    category: 'basic',
    shortcut: 'Ctrl+E',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleCode()
        .run();
    },
  },
  {
    id: 'bold',
    label: '粗体',
    description: '添加粗体格式',
    icon: 'B',
    category: 'basic',
    shortcut: 'Ctrl+B',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleBold()
        .run();
    },
  },
  {
    id: 'italic',
    label: '斜体',
    description: '添加斜体格式',
    icon: 'I',
    category: 'basic',
    shortcut: 'Ctrl+I',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleItalic()
        .run();
    },
  },
  {
    id: 'strikethrough',
    label: '删除线',
    description: '添加删除线格式',
    icon: 'S',
    category: 'basic',
    shortcut: 'Ctrl+Shift+X',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleStrike()
        .run();
    },
  },
];

/**
 * AI 功能命令（暂时为占位符，后续任务中实现）
 */
const aiCommands: SlashCommand[] = [
  {
    id: 'ai-continue',
    label: 'AI 续写',
    description: '让 AI 继续写作',
    icon: '✨',
    category: 'ai',
    shortcut: 'Ctrl+Shift+G',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 文本生成功能
      if ((editor as any).aiTextGeneration) {
        await (editor as any).aiTextGeneration.continueText();
      } else {
        console.warn('AI 文本生成功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-rewrite',
    label: 'AI 改写',
    description: '改写选中文本',
    icon: '🔄',
    category: 'ai',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 文本改写功能
      if ((editor as any).aiTextRewrite) {
        await (editor as any).aiTextRewrite.improve();
      } else {
        console.warn('AI 文本改写功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-grammar',
    label: 'AI 语法检查',
    description: '检查并修正语法错误',
    icon: '📝',
    category: 'ai',
    shortcut: 'Ctrl+Shift+G',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 语法检查功能
      if ((editor as any).aiTextRewrite) {
        await (editor as any).aiTextRewrite.checkGrammar();
      } else {
        console.warn('AI 文本改写功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-formal',
    label: 'AI 正式化',
    description: '调整为正式风格',
    icon: '🎩',
    category: 'ai',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 正式化功能
      if ((editor as any).aiTextRewrite) {
        await (editor as any).aiTextRewrite.makeFormal();
      } else {
        console.warn('AI 文本改写功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-casual',
    label: 'AI 口语化',
    description: '调整为轻松风格',
    icon: '😊',
    category: 'ai',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 口语化功能
      if ((editor as any).aiTextRewrite) {
        await (editor as any).aiTextRewrite.makeCasual();
      } else {
        console.warn('AI 文本改写功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-simplify',
    label: 'AI 简化',
    description: '简化文本表达',
    icon: '🎯',
    category: 'ai',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 简化功能
      if ((editor as any).aiTextRewrite) {
        await (editor as any).aiTextRewrite.simplify();
      } else {
        console.warn('AI 文本改写功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-summarize',
    label: 'AI 总结',
    description: '生成文档摘要',
    icon: '📝',
    category: 'ai',
    shortcut: 'Ctrl+Shift+S',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 文档分析功能
      if ((editor as any).aiDocumentAnalysis) {
        await (editor as any).aiDocumentAnalysis.generateSummary();
      } else {
        console.warn('AI 文档分析功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-keywords',
    label: 'AI 关键词',
    description: '提取文档关键词',
    icon: '🏷️',
    category: 'ai',
    shortcut: 'Ctrl+Shift+K',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 关键词提取功能
      if ((editor as any).aiDocumentAnalysis) {
        await (editor as any).aiDocumentAnalysis.extractKeywords();
      } else {
        console.warn('AI 文档分析功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-outline',
    label: 'AI 大纲',
    description: '生成文档大纲',
    icon: '📋',
    category: 'ai',
    shortcut: 'Ctrl+Shift+O',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 大纲生成功能
      if ((editor as any).aiDocumentAnalysis) {
        await (editor as any).aiDocumentAnalysis.generateOutline();
      } else {
        console.warn('AI 文档分析功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-analysis',
    label: 'AI 分析',
    description: '分析文档内容',
    icon: '📊',
    category: 'ai',
    shortcut: 'Ctrl+Shift+A',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 内容分析功能
      if ((editor as any).aiDocumentAnalysis) {
        await (editor as any).aiDocumentAnalysis.analyzeContent();
      } else {
        console.warn('AI 文档分析功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-translate',
    label: 'AI 翻译',
    description: '翻译选中文本',
    icon: '🌐',
    category: 'ai',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 翻译功能（翻译为英语）
      if ((editor as any).aiTranslationExplanation) {
        await (editor as any).aiTranslationExplanation.translateToEnglish();
      } else {
        console.warn('AI 翻译功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-translate-chinese',
    label: 'AI 翻译中文',
    description: '翻译为中文',
    icon: '🇨🇳',
    category: 'ai',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 翻译功能（翻译为中文）
      if ((editor as any).aiTranslationExplanation) {
        await (editor as any).aiTranslationExplanation.translateToChinese();
      } else {
        console.warn('AI 翻译功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-translate-english',
    label: 'AI 翻译英文',
    description: '翻译为英文',
    icon: '🇺🇸',
    category: 'ai',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 翻译功能（翻译为英文）
      if ((editor as any).aiTranslationExplanation) {
        await (editor as any).aiTranslationExplanation.translateToEnglish();
      } else {
        console.warn('AI 翻译功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-expand',
    label: 'AI 扩展',
    description: '扩展选中文本',
    icon: '📝',
    category: 'ai',
    shortcut: 'Ctrl+Shift+E',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 文本扩展功能（需要选中文本）
      const { from, to } = editor.state.selection;
      if (from !== to) {
        if ((editor as any).aiTextGeneration) {
          await (editor as any).aiTextGeneration.expandText();
        } else {
          console.warn('AI 文本生成功能未启用或未配置');
        }
      } else {
        console.log('请先选择要扩展的文本');
      }
    },
  },
  {
    id: 'ai-explain',
    label: 'AI 解释',
    description: '解释复杂概念',
    icon: '💡',
    category: 'ai',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 解释功能
      if ((editor as any).aiTranslationExplanation) {
        await (editor as any).aiTranslationExplanation.explainConcept();
      } else {
        console.warn('AI 解释功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-explain-simple',
    label: 'AI 简单解释',
    description: '用简单语言解释',
    icon: '🔍',
    category: 'ai',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 简单解释功能
      if ((editor as any).aiTranslationExplanation) {
        await (editor as any).aiTranslationExplanation.explainSimply();
      } else {
        console.warn('AI 解释功能未启用或未配置');
      }
    },
  },
  {
    id: 'ai-explain-example',
    label: 'AI 举例解释',
    description: '通过例子解释',
    icon: '📚',
    category: 'ai',
    action: async (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 调用 AI 举例解释功能
      if ((editor as any).aiTranslationExplanation) {
        await (editor as any).aiTranslationExplanation.explainWithExample();
      } else {
        console.warn('AI 解释功能未启用或未配置');
      }
    },
  },
];

/**
 * 媒体插入命令
 */
const mediaCommands: SlashCommand[] = [
  {
    id: 'image',
    label: '图片',
    description: '上传或插入图片',
    icon: '🖼️',
    category: 'media',
    action: (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 打开图片插入界面
      if (typeof window !== 'undefined' && (window as any).mediaInsertManager) {
        (window as any).mediaInsertManager.openMediaInsert('image');
      }
    },
  },
  {
    id: 'link',
    label: '链接',
    description: '插入或编辑链接',
    icon: '🔗',
    category: 'media',
    shortcut: 'Ctrl+K',
    action: (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 打开链接插入界面
      if (typeof window !== 'undefined' && (window as any).mediaInsertManager) {
        (window as any).mediaInsertManager.openMediaInsert('link');
      }
    },
  },
  {
    id: 'pdf',
    label: 'PDF',
    description: '插入 PDF 文档',
    icon: '📄',
    category: 'media',
    action: (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 打开 PDF 插入界面
      if (typeof window !== 'undefined' && (window as any).mediaInsertManager) {
        (window as any).mediaInsertManager.openMediaInsert('pdf');
      }
    },
  },
  {
    id: 'video',
    label: '视频',
    description: '插入视频 (即将推出)',
    icon: '🎥',
    category: 'media',
    action: (editor: Editor, range: Range) => {
      editor.chain().focus().deleteRange(range).run();
      
      // 打开视频插入界面 (占位符)
      if (typeof window !== 'undefined' && (window as any).mediaInsertManager) {
        (window as any).mediaInsertManager.openMediaInsert('video');
      }
    },
  },
];

/**
 * 高级功能命令
 */
const advancedCommands: SlashCommand[] = [
  {
    id: 'table',
    label: '表格',
    description: '插入 3x3 表格',
    icon: '📊',
    category: 'advanced',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
        .run();
    },
  },
  {
    id: 'divider',
    label: '分割线',
    description: '插入水平分割线',
    icon: '—',
    category: 'advanced',
    shortcut: 'Ctrl+Alt+H',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setHorizontalRule()
        .run();
    },
  },
  {
    id: 'task-list',
    label: '任务列表',
    description: '创建待办事项列表',
    icon: '☑️',
    category: 'advanced',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleTaskList()
        .run();
    },
  },
  {
    id: 'callout-info',
    label: '信息提示',
    description: '插入信息提示框',
    icon: 'ℹ️',
    category: 'advanced',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertContent('<div class="callout callout-info"><div class="callout-icon">ℹ️</div><div class="callout-content"><p>这是一个信息提示</p></div></div>')
        .run();
    },
  },
  {
    id: 'callout-warning',
    label: '警告提示',
    description: '插入警告提示框',
    icon: '⚠️',
    category: 'advanced',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertContent('<div class="callout callout-warning"><div class="callout-icon">⚠️</div><div class="callout-content"><p>这是一个警告提示</p></div></div>')
        .run();
    },
  },
  {
    id: 'callout-success',
    label: '成功提示',
    description: '插入成功提示框',
    icon: '✅',
    category: 'advanced',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertContent('<div class="callout callout-success"><div class="callout-icon">✅</div><div class="callout-content"><p>这是一个成功提示</p></div></div>')
        .run();
    },
  },
  {
    id: 'callout-error',
    label: '错误提示',
    description: '插入错误提示框',
    icon: '❌',
    category: 'advanced',
    action: (editor: Editor, range: Range) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertContent('<div class="callout callout-error"><div class="callout-icon">❌</div><div class="callout-content"><p>这是一个错误提示</p></div></div>')
        .run();
    },
  },
  {
    id: 'table-add-row',
    label: '添加表格行',
    description: '在当前行下方添加新行',
    icon: '➕',
    category: 'advanced',
    action: (editor: Editor, range: Range) => {
      if (editor.isActive('table')) {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .addRowAfter()
          .run();
      } else {
        // 如果不在表格中，先插入表格
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
          .run();
      }
    },
  },
  {
    id: 'table-add-column',
    label: '添加表格列',
    description: '在当前列右侧添加新列',
    icon: '📋',
    category: 'advanced',
    action: (editor: Editor, range: Range) => {
      if (editor.isActive('table')) {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .addColumnAfter()
          .run();
      } else {
        // 如果不在表格中，先插入表格
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
          .run();
      }
    },
  },
  {
    id: 'table-delete-row',
    label: '删除表格行',
    description: '删除当前行',
    icon: '🗑️',
    category: 'advanced',
    action: (editor: Editor, range: Range) => {
      if (editor.isActive('table')) {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .deleteRow()
          .run();
      }
    },
  },
  {
    id: 'table-delete-column',
    label: '删除表格列',
    description: '删除当前列',
    icon: '🗂️',
    category: 'advanced',
    action: (editor: Editor, range: Range) => {
      if (editor.isActive('table')) {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .deleteColumn()
          .run();
      }
    },
  },
];

/**
 * 预定义的斜杠命令分类
 */
export const SLASH_COMMANDS: SlashCommandCategory[] = [
  {
    name: '基础',
    commands: basicCommands,
  },
  {
    name: 'AI 功能',
    commands: aiCommands,
  },
  {
    name: '媒体',
    commands: mediaCommands,
  },
  {
    name: '高级',
    commands: advancedCommands,
  },
];

/**
 * 获取所有斜杠命令的扁平列表
 */
export function getAllSlashCommands(): SlashCommand[] {
  return SLASH_COMMANDS.flatMap(category => category.commands);
}

/**
 * 根据查询字符串过滤命令
 */
export function filterSlashCommands(query: string): SlashCommand[] {
  const allCommands = getAllSlashCommands();
  
  if (!query.trim()) {
    return allCommands;
  }
  
  const lowerQuery = query.toLowerCase();
  
  return allCommands.filter(command => 
    command.label.toLowerCase().includes(lowerQuery) ||
    command.description.toLowerCase().includes(lowerQuery) ||
    command.id.toLowerCase().includes(lowerQuery)
  );
}

/**
 * 根据 ID 查找命令
 */
export function findSlashCommand(id: string): SlashCommand | undefined {
  return getAllSlashCommands().find(command => command.id === id);
}