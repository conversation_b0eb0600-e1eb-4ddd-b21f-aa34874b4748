import { Editor, Range } from '@tiptap/react';

/**
 * 斜杠命令接口定义
 */
export interface SlashCommand {
  id: string;
  label: string;
  description: string;
  icon: string;
  category: 'basic' | 'ai' | 'media' | 'advanced';
  shortcut?: string;
  action: (editor: Editor, range: Range) => void | Promise<void>;
}

/**
 * 斜杠命令分类
 */
export interface SlashCommandCategory {
  name: string;
  commands: SlashCommand[];
}

/**
 * 斜杠命令菜单状态
 */
export interface SlashCommandMenuState {
  isOpen: boolean;
  position: { x: number; y: number };
  range: Range | null;
  query: string;
  selectedIndex: number;
  filteredCommands: SlashCommand[];
}

/**
 * 斜杠命令执行上下文
 */
export interface SlashCommandContext {
  editor: Editor;
  range: Range;
  query: string;
  selectedText?: string;
}

/**
 * 斜杠命令插件配置
 */
export interface SlashCommandPluginOptions {
  commands: SlashCommandCategory[];
  trigger: string;
  allowSpaces: boolean;
  startOfLine: boolean;
  char: string;
}