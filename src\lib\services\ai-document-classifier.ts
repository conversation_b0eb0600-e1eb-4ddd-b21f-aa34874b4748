/**
 * AI 文档分类服务
 * 提供基于内容的文档自动分类功能
 */

import { prisma } from '@/lib/db/prisma';
import { aiServiceManager } from './ai/ai-service-factory';
import { aiProcessingManager } from './ai-processing-manager';
import {
  DocumentClassification,
  DocumentContentAnalysis,
  DocumentType,
  ClassificationOptions,
  ClassificationRule,
  BatchClassificationRequest,
  BatchClassificationResult
} from '@/types/ai-classification.types';
import { AIProcessingContext, AIProcessingOptions } from '@/types/ai-status.types';

/**
 * AI 文档分类服务类
 */
export class AIDocumentClassifierService {
  private readonly defaultOptions: ClassificationOptions = {
    enableAutoClassification: true,
    enableFolderSuggestions: true,
    enableRelatedDocuments: true,
    minConfidenceThreshold: 0.7,
    maxSuggestions: 10,
    analysisDepth: 'detailed',
    customRules: []
  };

  /**
   * 分析单个文档内容
   */
  async analyzeDocument(documentId: string, userId: string): Promise<DocumentContentAnalysis> {
    // 获取文档内容
    const document = await prisma.document.findFirst({
      where: { id: documentId, userId },
      select: { id: true, title: true, content: true, createdAt: true }
    });

    if (!document) {
      throw new Error('文档不存在或无权限访问');
    }

    // 解析文档内容
    const textContent = this.extractTextFromContent(document.content);
    
    // 创建AI处理上下文
    const processingId = `analyze_${documentId}_${Date.now()}`;
    const context: AIProcessingContext = {
      id: processingId,
      documentId,
      userId,
      type: 'document_analysis',
      input: textContent,
      options: { showDetailedProgress: true },
      startTime: new Date()
    };

    // 开始AI处理
    await aiProcessingManager.startProcessing(context);

    try {
      // 构建分析提示
      const analysisPrompt = this.buildAnalysisPrompt(document.title, textContent);
      
      // 调用AI服务进行分析
      const aiService = aiServiceManager.getDefaultService();
      const response = await aiService.generateText({
        prompt: analysisPrompt,
        maxTokens: 1000
      });

      // 解析AI响应
      const analysis = this.parseAnalysisResponse(response.content, documentId);
      
      // 完成处理
      aiProcessingManager.completeProcessing(processingId, {
        id: processingId,
        type: 'document_analysis',
        input: textContent,
        output: response.content,
        provider: response.provider,
        model: response.model,
        tokensUsed: response.tokensUsed,
        processingTime: response.responseTime,
        createdAt: new Date()
      });

      return analysis;
    } catch (error) {
      aiProcessingManager.handleError(processingId, error as Error);
      throw error;
    }
  }

  /**
   * 分类单个文档
   */
  async classifyDocument(
    documentId: string, 
    userId: string, 
    options: Partial<ClassificationOptions> = {}
  ): Promise<DocumentClassification> {
    const finalOptions = { ...this.defaultOptions, ...options };
    
    // 首先分析文档内容
    const analysis = await this.analyzeDocument(documentId, userId);
    
    // 应用自定义规则
    const ruleBasedCategory = this.applyCustomRules(analysis, finalOptions.customRules || []);
    
    // 如果规则匹配，使用规则结果
    if (ruleBasedCategory) {
      return {
        documentId,
        primaryCategory: ruleBasedCategory.category,
        secondaryCategories: [],
        confidence: 1.0,
        suggestedTags: analysis.keyConcepts,
        reasoning: `匹配自定义规则: ${ruleBasedCategory.name}`,
        analyzedAt: new Date()
      };
    }

    // 使用AI进行智能分类
    return this.performAIClassification(analysis, finalOptions);
  }

  /**
   * 批量分类文档
   */
  async batchClassifyDocuments(request: BatchClassificationRequest): Promise<BatchClassificationResult> {
    const result: BatchClassificationResult = {
      requestId: `batch_${Date.now()}`,
      status: 'processing',
      totalDocuments: request.documentIds.length,
      processedDocuments: 0,
      classifications: [],
      folderSuggestions: [],
      relatedDocuments: [],
      errors: [],
      startedAt: new Date()
    };

    try {
      // 逐个处理文档
      for (const documentId of request.documentIds) {
        try {
          const classification = await this.classifyDocument(
            documentId, 
            request.userId, 
            request.options
          );
          result.classifications.push(classification);
          result.processedDocuments++;
        } catch (error) {
          result.errors.push(`文档 ${documentId}: ${(error as Error).message}`);
        }
      }

      result.status = 'completed';
      result.completedAt = new Date();
      
      return result;
    } catch (error) {
      result.status = 'failed';
      result.errors.push(`批量处理失败: ${(error as Error).message}`);
      return result;
    }
  }

  /**
   * 获取文档的建议分类
   */
  async getSuggestedCategories(userId: string): Promise<string[]> {
    // 获取用户的所有文档分类
    const documents = await prisma.document.findMany({
      where: { userId },
      select: { title: true, content: true }
    });

    if (documents.length === 0) {
      return this.getDefaultCategories();
    }

    // 分析现有文档，提取常见主题
    const allContent = documents.map(doc => 
      `${doc.title} ${this.extractTextFromContent(doc.content)}`
    ).join(' ');

    const prompt = `
基于以下文档内容，建议10个最适合的文档分类类别：

${allContent.substring(0, 2000)}...

请返回JSON格式的分类列表：
{
  "categories": ["分类1", "分类2", ...]
}
`;

    try {
      const aiService = aiServiceManager.getDefaultService();
      const response = await aiService.generateText({ prompt });
      
      const parsed = JSON.parse(response.content);
      return parsed.categories || this.getDefaultCategories();
    } catch (error) {
      console.error('获取建议分类失败:', error);
      return this.getDefaultCategories();
    }
  }

  /**
   * 构建文档分析提示
   */
  private buildAnalysisPrompt(title: string, content: string): string {
    return `
请分析以下文档的内容，并返回JSON格式的分析结果：

文档标题: ${title}
文档内容: ${content.substring(0, 2000)}${content.length > 2000 ? '...' : ''}

请分析并返回以下信息：
{
  "mainTopics": ["主题1", "主题2", ...],
  "keyConcepts": ["概念1", "概念2", ...],
  "documentType": "note|article|report|meeting_minutes|todo_list|project_plan|research|tutorial|reference|other",
  "complexity": 1-5,
  "domain": "专业领域（如果有）",
  "language": "zh|en|other",
  "summary": "内容摘要（100字以内）"
}

请确保返回有效的JSON格式。
`;
  }

  /**
   * 解析AI分析响应
   */
  private parseAnalysisResponse(response: string, documentId: string): DocumentContentAnalysis {
    try {
      const parsed = JSON.parse(response);
      return {
        documentId,
        mainTopics: parsed.mainTopics || [],
        keyConcepts: parsed.keyConcepts || [],
        documentType: parsed.documentType || DocumentType.OTHER,
        complexity: parsed.complexity || 3,
        domain: parsed.domain,
        language: parsed.language || 'zh',
        summary: parsed.summary || '',
        analyzedAt: new Date()
      };
    } catch (error) {
      // 如果解析失败，返回默认分析结果
      return {
        documentId,
        mainTopics: [],
        keyConcepts: [],
        documentType: DocumentType.OTHER,
        complexity: 3,
        language: 'zh',
        summary: '分析失败',
        analyzedAt: new Date()
      };
    }
  }

  /**
   * 应用自定义分类规则
   */
  private applyCustomRules(
    analysis: DocumentContentAnalysis, 
    rules: ClassificationRule[]
  ): ClassificationRule | null {
    // 按优先级排序
    const sortedRules = rules
      .filter(rule => rule.enabled)
      .sort((a, b) => b.priority - a.priority);

    for (const rule of sortedRules) {
      if (this.matchesRule(analysis, rule)) {
        return rule;
      }
    }

    return null;
  }

  /**
   * 检查是否匹配规则
   */
  private matchesRule(analysis: DocumentContentAnalysis, rule: ClassificationRule): boolean {
    return rule.conditions.every(condition => {
      switch (condition.type) {
        case 'title_contains':
          // 这里需要文档标题，暂时跳过
          return true;
        case 'content_contains':
          return analysis.summary.includes(condition.value as string);
        case 'has_tags':
          const tags = condition.value as string[];
          return tags.some(tag => analysis.keyConcepts.includes(tag));
        default:
          return false;
      }
    });
  }

  /**
   * 执行AI智能分类
   */
  private async performAIClassification(
    analysis: DocumentContentAnalysis,
    options: ClassificationOptions
  ): Promise<DocumentClassification> {
    const classificationPrompt = `
基于以下文档分析结果，请进行智能分类：

主要主题: ${analysis.mainTopics.join(', ')}
关键概念: ${analysis.keyConcepts.join(', ')}
文档类型: ${analysis.documentType}
专业领域: ${analysis.domain || '无'}
内容摘要: ${analysis.summary}

请返回JSON格式的分类结果：
{
  "primaryCategory": "主要分类",
  "secondaryCategories": ["次要分类1", "次要分类2"],
  "confidence": 0.0-1.0,
  "suggestedTags": ["标签1", "标签2"],
  "reasoning": "分类原因说明"
}

常见分类包括但不限于：工作笔记、学习资料、项目文档、会议记录、个人日记、技术文档、研究资料等。
`;

    try {
      const aiService = aiServiceManager.getDefaultService();
      const response = await aiService.generateText({
        prompt: classificationPrompt,
        maxTokens: 500
      });

      const parsed = JSON.parse(response.content);
      
      return {
        documentId: analysis.documentId,
        primaryCategory: parsed.primaryCategory || '未分类',
        secondaryCategories: parsed.secondaryCategories || [],
        confidence: Math.max(parsed.confidence || 0.5, options.minConfidenceThreshold),
        suggestedTags: parsed.suggestedTags || analysis.keyConcepts,
        reasoning: parsed.reasoning || '基于内容分析的智能分类',
        analyzedAt: new Date()
      };
    } catch (error) {
      // 分类失败时返回默认结果
      return {
        documentId: analysis.documentId,
        primaryCategory: this.getDefaultCategoryForType(analysis.documentType),
        secondaryCategories: [],
        confidence: 0.5,
        suggestedTags: analysis.keyConcepts,
        reasoning: '自动分类失败，使用默认分类',
        analyzedAt: new Date()
      };
    }
  }

  /**
   * 从文档内容中提取纯文本
   */
  private extractTextFromContent(content: string): string {
    try {
      const parsed = JSON.parse(content);
      return this.extractTextFromTipTapContent(parsed);
    } catch {
      return content;
    }
  }

  /**
   * 从TipTap内容中提取文本
   */
  private extractTextFromTipTapContent(content: any): string {
    if (!content || !content.content) return '';
    
    let text = '';
    
    const extractFromNode = (node: any): void => {
      if (node.text) {
        text += node.text + ' ';
      }
      
      if (node.content && Array.isArray(node.content)) {
        node.content.forEach(extractFromNode);
      }
    };
    
    content.content.forEach(extractFromNode);
    return text.trim();
  }

  /**
   * 获取默认分类列表
   */
  private getDefaultCategories(): string[] {
    return [
      '工作笔记',
      '学习资料',
      '项目文档',
      '会议记录',
      '个人日记',
      '技术文档',
      '研究资料',
      '待办事项',
      '参考资料',
      '其他'
    ];
  }

  /**
   * 根据文档类型获取默认分类
   */
  private getDefaultCategoryForType(type: DocumentType): string {
    const typeMap: Record<DocumentType, string> = {
      [DocumentType.NOTE]: '工作笔记',
      [DocumentType.ARTICLE]: '文章',
      [DocumentType.REPORT]: '报告',
      [DocumentType.MEETING_MINUTES]: '会议记录',
      [DocumentType.TODO_LIST]: '待办事项',
      [DocumentType.PROJECT_PLAN]: '项目文档',
      [DocumentType.RESEARCH]: '研究资料',
      [DocumentType.TUTORIAL]: '学习资料',
      [DocumentType.REFERENCE]: '参考资料',
      [DocumentType.OTHER]: '其他'
    };
    
    return typeMap[type] || '其他';
  }
}

// 导出单例实例
export const aiDocumentClassifier = new AIDocumentClassifierService();