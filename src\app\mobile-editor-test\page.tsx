'use client';

import { useState } from 'react';
import { ResponsiveEditor } from '@/components/editor/ResponsiveEditor';

/**
 * 移动端编辑器测试页面
 */
export default function MobileEditorTestPage() {
  const [content, setContent] = useState(`
    <h1>移动端编辑器测试</h1>
    <p>这是一个专门为移动设备优化的编辑器测试页面。</p>
    
    <h2>功能特性</h2>
    <ul>
      <li><strong>触摸友好</strong>：所有按钮都针对触摸操作进行了优化</li>
      <li><strong>响应式布局</strong>：根据屏幕尺寸自动调整界面</li>
      <li><strong>浮动工具栏</strong>：不占用编辑空间的工具栏设计</li>
      <li><strong>AI助手集成</strong>：移动端优化的AI功能面板</li>
    </ul>
    
    <h2>测试内容</h2>
    <p>请在不同设备上测试以下功能：</p>
    
    <ol>
      <li>文本选择和编辑</li>
      <li>格式化工具栏的使用</li>
      <li>AI助手面板的交互</li>
      <li>虚拟键盘的适配</li>
    </ol>
    
    <blockquote>
      <p>这是一个引用示例，用于测试移动端的显示效果。</p>
    </blockquote>
    
    <p>您可以尝试选择文本来测试选择菜单的功能。</p>
  `);

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    console.log('Content changed:', newContent);
  };

  const handleSave = (content: string) => {
    console.log('Saving content:', content);
    // 这里可以实现实际的保存逻辑
  };

  return (
    <div className="h-screen">
      <ResponsiveEditor
        initialContent={content}
        placeholder="开始在移动设备上写作..."
        onChange={handleContentChange}
        onSave={handleSave}
      />
    </div>
  );
}