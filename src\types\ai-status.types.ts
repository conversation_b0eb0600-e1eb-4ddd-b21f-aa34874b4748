/**
 * AI 处理状态相关的类型定义
 */

/**
 * AI 处理状态枚举
 */
export enum AIProcessingStatus {
  /** 空闲状态 */
  IDLE = 'idle',
  /** 准备中 */
  PREPARING = 'preparing',
  /** 连接中 */
  CONNECTING = 'connecting',
  /** 处理中 */
  PROCESSING = 'processing',
  /** 生成中 */
  GENERATING = 'generating',
  /** 完成 */
  COMPLETED = 'completed',
  /** 错误 */
  ERROR = 'error',
  /** 已取消 */
  CANCELLED = 'cancelled',
}

/**
 * AI 处理阶段
 */
export interface AIProcessingStage {
  /** 阶段标识 */
  id: string;
  /** 阶段名称 */
  name: string;
  /** 阶段描述 */
  description: string;
  /** 是否完成 */
  completed: boolean;
  /** 是否当前阶段 */
  current: boolean;
  /** 进度百分比 (0-100) */
  progress: number;
  /** 开始时间 */
  startTime?: Date;
  /** 结束时间 */
  endTime?: Date;
  /** 错误信息 */
  error?: string;
}

/**
 * AI 处理进度信息
 */
export interface AIProcessingProgress {
  /** 当前状态 */
  status: AIProcessingStatus;
  /** 总体进度百分比 (0-100) */
  overallProgress: number;
  /** 当前阶段 */
  currentStage?: string;
  /** 所有处理阶段 */
  stages: AIProcessingStage[];
  /** 开始时间 */
  startTime: Date;
  /** 预计剩余时间（毫秒） */
  estimatedTimeRemaining?: number;
  /** 处理的令牌数 */
  tokensProcessed?: number;
  /** 预计总令牌数 */
  estimatedTotalTokens?: number;
  /** 错误信息 */
  error?: string;
  /** 是否可以取消 */
  cancellable: boolean;
}

/**
 * AI 响应结果
 */
export interface AIProcessingResult {
  /** 处理 ID */
  id: string;
  /** 处理类型 */
  type: string;
  /** 输入内容 */
  input: string;
  /** 输出结果 */
  output: string;
  /** 使用的提供商 */
  provider: string;
  /** 使用的模型 */
  model: string;
  /** 消耗的令牌数 */
  tokensUsed: number;
  /** 处理时间（毫秒） */
  processingTime: number;
  /** 质量评分 (0-100) */
  qualityScore?: number;
  /** 置信度 (0-1) */
  confidence?: number;
  /** 元数据 */
  metadata?: Record<string, any>;
  /** 创建时间 */
  createdAt: Date;
}

/**
 * AI 处理选项
 */
export interface AIProcessingOptions {
  /** 是否显示详细进度 */
  showDetailedProgress?: boolean;
  /** 是否显示令牌计数 */
  showTokenCount?: boolean;
  /** 是否显示时间估算 */
  showTimeEstimate?: boolean;
  /** 是否允许取消 */
  allowCancel?: boolean;
  /** 自动隐藏延迟（毫秒） */
  autoHideDelay?: number;
  /** 进度更新间隔（毫秒） */
  progressUpdateInterval?: number;
}

/**
 * AI 处理事件
 */
export interface AIProcessingEvent {
  /** 事件类型 */
  type: 'progress' | 'stage_change' | 'error' | 'complete' | 'cancel';
  /** 事件数据 */
  data: any;
  /** 时间戳 */
  timestamp: Date;
}

/**
 * AI 处理上下文
 */
export interface AIProcessingContext {
  /** 处理 ID */
  id: string;
  /** 文档 ID */
  documentId?: string;
  /** 用户 ID */
  userId: string;
  /** 处理类型 */
  type: string;
  /** 输入内容 */
  input: string;
  /** 处理选项 */
  options: AIProcessingOptions;
  /** 开始时间 */
  startTime: Date;
  /** 取消令牌 */
  cancelToken?: AbortController;
}

/**
 * AI 处理状态管理器接口
 */
export interface AIProcessingManager {
  /** 开始处理 */
  startProcessing(context: AIProcessingContext): Promise<void>;
  /** 更新进度 */
  updateProgress(id: string, progress: Partial<AIProcessingProgress>): void;
  /** 完成处理 */
  completeProcessing(id: string, result: AIProcessingResult): void;
  /** 处理错误 */
  handleError(id: string, error: Error): void;
  /** 取消处理 */
  cancelProcessing(id: string): void;
  /** 获取处理状态 */
  getProcessingStatus(id: string): AIProcessingProgress | null;
  /** 订阅状态变化 */
  subscribe(id: string, callback: (progress: AIProcessingProgress) => void): () => void;
}

/**
 * AI 处理统计信息
 */
export interface AIProcessingStats {
  /** 总处理次数 */
  totalProcessed: number;
  /** 成功次数 */
  successCount: number;
  /** 失败次数 */
  errorCount: number;
  /** 取消次数 */
  cancelledCount: number;
  /** 平均处理时间 */
  averageProcessingTime: number;
  /** 总令牌消耗 */
  totalTokensUsed: number;
  /** 按类型统计 */
  byType: Record<string, number>;
  /** 按提供商统计 */
  byProvider: Record<string, number>;
}