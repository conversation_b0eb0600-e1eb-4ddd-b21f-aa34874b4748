'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { OptimizedAIAssistantPanel } from '@/components/ai/OptimizedAIAssistantPanel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { 
  SparklesIcon,
  CheckCircleIcon,
  PlayIcon,
  PauseIcon,
  SettingsIcon,
  MonitorIcon,
  SmartphoneIcon,
  TabletIcon,
  ZapIcon,
  BarChartIcon,
  ClockIcon,
  StarIcon,
  InfoIcon,
  CheckSquareIcon,
  CheckIcon,
  TrendingUpIcon,
  CpuIcon,
  MemoryStickIcon,
  GaugeIcon,
  ActivityIcon,
  DatabaseIcon,
  RefreshCwIcon,
  EyeIcon,
  LayersIcon,
  RocketIcon
} from 'lucide-react';

/**
 * 性能监控组件
 */
interface PerformanceMonitorProps {
  isActive: boolean;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ isActive }) => {
  const [metrics, setMetrics] = useState({
    memoryUsage: 0,
    renderTime: 0,
    componentCount: 0,
    virtualizedItems: 0,
    cacheHits: 0,
    fps: 60
  });

  useEffect(() => {
    if (!isActive) return;

    const updateMetrics = () => {
      // 模拟性能指标
      setMetrics({
        memoryUsage: Math.random() * 50 + 20, // MB
        renderTime: Math.random() * 10 + 2, // ms
        componentCount: Math.floor(Math.random() * 50) + 100,
        virtualizedItems: Math.floor(Math.random() * 500) + 200,
        cacheHits: Math.random() * 100,
        fps: Math.floor(Math.random() * 10) + 55
      });
    };

    const interval = setInterval(updateMetrics, 1000);
    updateMetrics();

    return () => clearInterval(interval);
  }, [isActive]);

  if (!isActive) return null;

  return (
    <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-green-900">
          <ActivityIcon className="h-5 w-5" />
          性能监控
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="text-center p-3 bg-white rounded-lg border">
            <MemoryStickIcon className="h-6 w-6 text-blue-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-blue-600">{metrics.memoryUsage.toFixed(1)}MB</div>
            <div className="text-xs text-gray-600">内存使用</div>
          </div>
          
          <div className="text-center p-3 bg-white rounded-lg border">
            <ClockIcon className="h-6 w-6 text-green-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-green-600">{metrics.renderTime.toFixed(1)}ms</div>
            <div className="text-xs text-gray-600">渲染时间</div>
          </div>
          
          <div className="text-center p-3 bg-white rounded-lg border">
            <LayersIcon className="h-6 w-6 text-purple-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-purple-600">{metrics.componentCount}</div>
            <div className="text-xs text-gray-600">组件数量</div>
          </div>
          
          <div className="text-center p-3 bg-white rounded-lg border">
            <EyeIcon className="h-6 w-6 text-orange-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-orange-600">{metrics.virtualizedItems}</div>
            <div className="text-xs text-gray-600">虚拟化项目</div>
          </div>
          
          <div className="text-center p-3 bg-white rounded-lg border">
            <DatabaseIcon className="h-6 w-6 text-indigo-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-indigo-600">{metrics.cacheHits.toFixed(0)}%</div>
            <div className="text-xs text-gray-600">缓存命中率</div>
          </div>
          
          <div className="text-center p-3 bg-white rounded-lg border">
            <GaugeIcon className="h-6 w-6 text-red-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-red-600">{metrics.fps}</div>
            <div className="text-xs text-gray-600">FPS</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * 性能优化AI助手面板演示页面
 */
export default function OptimizedAIAssistantPage() {
  const [panelOpen, setPanelOpen] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('');
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 });
  const [performanceMode, setPerformanceMode] = useState(true);
  const [virtualScrollEnabled, setVirtualScrollEnabled] = useState(true);
  const [maxHistoryItems, setMaxHistoryItems] = useState(1000);
  const [userPreferences, setUserPreferences] = useState({
    theme: 'light' as 'light' | 'dark' | 'auto',
    compactMode: false,
    showShortcuts: true,
    enableAnimations: true,
    enableSounds: false,
    autoCollapse: false,
    defaultTab: 'features',
    favoriteFeatures: ['ai-continue', 'ai-rewrite', 'ai-summarize'],
    pinnedCategories: ['writing'],
    customCategoryOrder: {},
    accessibilityMode: false,
    highContrast: false,
    reducedMotion: false
  });

  // 检测设备类型
  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({ width, height });
      
      if (width < 768) {
        setDeviceType('mobile');
      } else if (width < 1024) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    updateDeviceInfo();
    window.addEventListener('resize', updateDeviceInfo);
    
    return () => window.removeEventListener('resize', updateDeviceInfo);
  }, []);

  /**
   * 处理 AI 操作
   */
  const handleAIAction = useCallback(async (actionId: string, data?: any) => {
    console.log('AI Action:', actionId, data);
    
    const startTime = Date.now();
    setIsProcessing(true);
    
    // 设置处理状态消息
    const statusMessages: Record<string, string> = {
      'ai-continue': '正在生成续写内容...',
      'ai-rewrite': '正在改写文本...',
      'ai-summarize': '正在生成摘要...',
      'ai-translate': '正在翻译文本...',
      'ai-explain': '正在生成解释...',
      'ai-keywords': '正在提取关键词...',
      'ai-outline': '正在生成大纲...',
      'ai-analysis': '正在分析内容...',
      'ai-creative': '正在创作内容...',
      'ai-custom': '正在执行自定义指令...',
      'ai-chat': '正在准备对话...',
      'ai-grammar': '正在检查语法...',
      'ai-expand': '正在扩展内容...',
      'ai-settings': '正在打开设置...'
    };
    
    setProcessingStatus(statusMessages[actionId] || '正在处理请求...');
    
    try {
      // 模拟处理延迟
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1500));
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // 模拟成功结果
      console.log(`AI action ${actionId} completed in ${responseTime}ms`);
      
    } catch (error) {
      console.error('AI action failed:', error);
    } finally {
      setIsProcessing(false);
      setProcessingStatus('');
    }
  }, []);

  /**
   * 切换面板状态
   */
  const togglePanel = useCallback(() => {
    setPanelOpen(prev => !prev);
  }, []);

  /**
   * 处理偏好设置变更
   */
  const handlePreferencesChange = useCallback((newPreferences: any) => {
    setUserPreferences(newPreferences);
    console.log('Preferences updated:', newPreferences);
  }, []);

  /**
   * 获取设备信息
   */
  const getDeviceInfo = () => {
    switch (deviceType) {
      case 'mobile':
        return { 
          icon: SmartphoneIcon, 
          name: '移动端', 
          color: 'text-green-600',
          bg: 'bg-green-50'
        };
      case 'tablet':
        return { 
          icon: TabletIcon, 
          name: '平板端', 
          color: 'text-blue-600',
          bg: 'bg-blue-50'
        };
      default:
        return { 
          icon: MonitorIcon, 
          name: '桌面端', 
          color: 'text-purple-600',
          bg: 'bg-purple-50'
        };
    }
  };

  const deviceInfo = getDeviceInfo();
  const DeviceIcon = deviceInfo.icon;

  // 示例文本内容
  const sampleTexts = [
    {
      title: '性能优化特性',
      content: '本版本的AI助手面板采用了多项性能优化技术，包括虚拟滚动、懒加载、防抖搜索、组件记忆化等。这些优化确保即使在处理大量数据时，界面依然保持流畅的用户体验。虚拟滚动技术可以处理数千个项目而不影响性能，懒加载减少了初始加载时间，防抖搜索避免了频繁的重新渲染。',
      category: '性能优化'
    },
    {
      title: '内存管理策略',
      content: '为了防止内存泄漏和过度消耗，我们实现了环形缓冲区来管理操作历史，限制了同时存在的组件数量，并使用了智能的垃圾回收策略。这些措施确保应用程序可以长时间运行而不会出现性能下降。同时，我们还实现了请求取消机制，避免了无效的网络请求堆积。',
      category: '内存管理'
    },
    {
      title: '用户体验优化',
      content: '在保证性能的同时，我们没有牺牲用户体验。通过智能预加载、渐进式渲染、骨架屏等技术，用户感受到的是更快的响应速度和更流畅的交互。所有的优化都是在后台进行的，用户无需了解技术细节就能享受到更好的使用体验。',
      category: '用户体验'
    }
  ];

  // 性能优化特性
  const optimizationFeatures = [
    {
      icon: EyeIcon,
      title: '虚拟滚动',
      description: '只渲染可见区域的项目，支持大量数据',
      color: 'text-blue-600',
      bg: 'bg-blue-50',
      enabled: virtualScrollEnabled
    },
    {
      icon: RefreshCwIcon,
      title: '懒加载',
      description: '按需加载组件和资源',
      color: 'text-green-600',
      bg: 'bg-green-50',
      enabled: true
    },
    {
      icon: DatabaseIcon,
      title: '智能缓存',
      description: '缓存频繁访问的数据和组件',
      color: 'text-purple-600',
      bg: 'bg-purple-50',
      enabled: true
    },
    {
      icon: MemoryStickIcon,
      title: '内存优化',
      description: '环形缓冲区和垃圾回收',
      color: 'text-orange-600',
      bg: 'bg-orange-50',
      enabled: true
    },
    {
      icon: ZapIcon,
      title: '防抖搜索',
      description: '减少不必要的搜索请求',
      color: 'text-yellow-600',
      bg: 'bg-yellow-50',
      enabled: true
    },
    {
      icon: CpuIcon,
      title: '组件记忆化',
      description: 'React.memo 和 useMemo 优化',
      color: 'text-red-600',
      bg: 'bg-red-50',
      enabled: true
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
      <div className="max-w-7xl mx-auto p-4 md:p-6 space-y-6">
        {/* 页面标题 */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <div className="p-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-full">
              <RocketIcon className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
              性能优化 AI 助手面板
            </h1>
          </div>
          <p className="text-base md:text-lg text-gray-600 max-w-4xl mx-auto">
            任务 27 的性能优化版本 - 虚拟滚动、懒加载、内存优化和智能缓存
          </p>
          <div className="flex items-center justify-center gap-2">
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <RocketIcon className="h-3 w-3 mr-1" />
              高性能版本
            </Badge>
            <Badge variant="outline">
              虚拟滚动 + 懒加载 + 内存优化
            </Badge>
          </div>
        </div>

        {/* 实时状态面板 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className={`${deviceInfo.bg} border-2`}>
            <CardContent className="p-4 text-center">
              <DeviceIcon className={`h-8 w-8 ${deviceInfo.color} mx-auto mb-2`} />
              <div className="font-semibold text-gray-900">{deviceInfo.name}</div>
              <div className="text-sm text-gray-600">{screenSize.width} × {screenSize.height}</div>
            </CardContent>
          </Card>
          
          <Card className="bg-blue-50 border-2 border-blue-200">
            <CardContent className="p-4 text-center">
              <ZapIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">面板状态</div>
              <div className="text-sm text-gray-600">
                {panelOpen ? '已打开' : '已关闭'}
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-green-50 border-2 border-green-200">
            <CardContent className="p-4 text-center">
              <EyeIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">虚拟滚动</div>
              <div className="text-sm text-gray-600">
                {virtualScrollEnabled ? '已启用' : '已禁用'}
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-purple-50 border-2 border-purple-200">
            <CardContent className="p-4 text-center">
              <DatabaseIcon className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="font-semibold text-gray-900">历史记录</div>
              <div className="text-sm text-gray-600">
                最多 {maxHistoryItems} 条
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 性能监控 */}
        <PerformanceMonitor isActive={performanceMode} />

        {/* 控制面板 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <SettingsIcon className="h-5 w-5 text-green-600" />
              性能控制与设置
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 基础控制 */}
              <div className="flex flex-wrap items-center gap-3">
                <Button
                  onClick={togglePanel}
                  className={`flex items-center gap-2 ${panelOpen ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'} text-white`}
                >
                  {panelOpen ? (
                    <>
                      <PauseIcon className="h-4 w-4" />
                      关闭面板
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4" />
                      打开面板
                    </>
                  )}
                </Button>
                
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <RocketIcon className="h-4 w-4" />
                  <span>性能优化演示</span>
                </div>
                
                {isProcessing && (
                  <div className="flex items-center gap-2 text-sm text-blue-600">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span>AI 处理中...</span>
                  </div>
                )}
              </div>

              {/* 性能设置 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <Button
                  variant={virtualScrollEnabled ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setVirtualScrollEnabled(!virtualScrollEnabled)}
                  className="flex items-center gap-2"
                >
                  <EyeIcon className="h-4 w-4" />
                  虚拟滚动
                </Button>
                
                <Button
                  variant={performanceMode ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPerformanceMode(!performanceMode)}
                  className="flex items-center gap-2"
                >
                  <ActivityIcon className="h-4 w-4" />
                  性能监控
                </Button>
                
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-700">历史记录上限:</span>
                  <select
                    value={maxHistoryItems}
                    onChange={(e) => setMaxHistoryItems(Number(e.target.value))}
                    className="text-sm border border-gray-200 rounded px-2 py-1"
                  >
                    <option value={100}>100</option>
                    <option value={500}>500</option>
                    <option value={1000}>1000</option>
                    <option value={5000}>5000</option>
                  </select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 性能优化特性展示 */}
        <Card>
          <CardHeader>
            <CardTitle>性能优化特性</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {optimizationFeatures.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div
                    key={index}
                    className={`p-4 rounded-lg border ${feature.bg} border-gray-200 relative`}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <IconComponent className={`h-6 w-6 ${feature.color}`} />
                      <h4 className="font-medium text-gray-900">{feature.title}</h4>
                      {feature.enabled && (
                        <Badge variant="secondary" className="text-xs bg-green-100 text-green-800 ml-auto">
                          已启用
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* 交互测试区域 */}
        <Card>
          <CardHeader>
            <CardTitle>性能测试区域</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600">
              点击下面的文本来测试性能优化版AI助手面板。即使处理大量数据，界面依然保持流畅。
            </p>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {sampleTexts.map((text, index) => (
                <div
                  key={index}
                  className={`
                    p-4 rounded-lg border cursor-pointer transition-all duration-200
                    ${selectedText === text.content
                      ? 'bg-green-50 border-green-300 shadow-lg transform scale-[1.02]' 
                      : 'bg-white border-gray-200 hover:bg-gray-50 hover:shadow-md'
                    }
                  `}
                  onClick={() => setSelectedText(text.content)}
                >
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="outline" className="text-xs">
                      {text.category}
                    </Badge>
                    <h4 className="font-medium text-gray-900">{text.title}</h4>
                  </div>
                  <p className="text-sm text-gray-700 leading-relaxed">
                    {text.content}
                  </p>
                  {selectedText === text.content && (
                    <div className="mt-3 p-2 bg-green-100 rounded text-xs text-green-800 flex items-center gap-2">
                      <CheckCircleIcon className="h-3 w-3" />
                      已选择此文本，AI 功能已激活
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 技术说明 */}
        <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
          <CardHeader>
            <CardTitle className="text-green-900">性能优化技术说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium text-green-900">核心优化技术</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• 虚拟滚动：只渲染可见区域的项目</li>
                  <li>• 懒加载：按需加载组件和资源</li>
                  <li>• 防抖搜索：减少不必要的重新渲染</li>
                  <li>• 组件记忆化：避免无效的重新渲染</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-medium text-green-900">内存管理</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• 环形缓冲区：限制历史记录数量</li>
                  <li>• 请求取消：避免无效网络请求堆积</li>
                  <li>• 智能垃圾回收：及时清理无用数据</li>
                  <li>• 资源池化：复用昂贵的对象实例</li>
                </ul>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-green-100 rounded-lg">
              <div className="flex items-start gap-2">
                <InfoIcon className="h-4 w-4 text-green-600 mt-0.5" />
                <div className="text-sm text-green-800">
                  <strong>性能优化说明：</strong>本版本在保持所有原有功能的基础上，通过多项性能优化技术，
                  确保即使在处理大量数据时也能保持流畅的用户体验。所有优化都是透明的，用户无需了解技术细节
                  就能享受到更快的响应速度和更低的资源消耗。
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 性能优化版 AI 助手面板 */}
      <OptimizedAIAssistantPanel
        isOpen={panelOpen}
        onToggle={togglePanel}
        position="right"
        width={deviceType === 'mobile' ? undefined : 450}
        isMobile={deviceType === 'mobile'}
        onAIAction={handleAIAction}
        selectedText={selectedText}
        isProcessing={isProcessing}
        processingStatus={processingStatus}
        enableAdvancedFeatures={true}
        initialTheme={userPreferences.theme}
        enableSearch={true}
        enableStats={true}
        userPreferences={userPreferences}
        onPreferencesChange={handlePreferencesChange}
        enableVirtualScroll={virtualScrollEnabled}
        maxHistoryItems={maxHistoryItems}
      />
    </div>
  );
}