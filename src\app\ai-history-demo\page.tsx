/**
 * AI 交互历史记录演示页面
 */

'use client';

import React, { useState } from 'react';
import { AIInteractionHistory } from '@/components/ai/AIInteractionHistory';
import { AIHistoryPanel } from '@/components/ai/AIHistoryPanel';
import { AIInteractionHistoryDemo } from '@/components/ai/AIInteractionHistoryDemo';
import { 
  MessageSquare, 
  History, 
  BarChart3,
  Settings,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

/**
 * 视图类型
 */
type ViewType = 'full' | 'panel';

/**
 * AI 交互历史记录演示页面
 */
export default function AIHistoryDemoPage() {
  const [currentView, setCurrentView] = useState<ViewType>('full');

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 头部导航 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">
                AI 交互历史记录演示
              </h1>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentView('full')}
                  className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${
                    currentView === 'full'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <MessageSquare className="w-4 h-4 inline mr-1" />
                  完整视图
                </button>
                
                <button
                  onClick={() => setCurrentView('panel')}
                  className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${
                    currentView === 'panel'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <History className="w-4 h-4 inline mr-1" />
                  面板视图
                </button>
              </div>
            </div>

            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <BarChart3 className="w-4 h-4" />
              <span>演示环境</span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentView === 'full' ? (
          /* 完整历史记录视图 */
          <AIInteractionHistoryDemo />
        ) : (
          /* 面板视图演示 */
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 左侧：面板演示 */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-[600px]">
                <div className="p-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    AI 助手面板中的历史记录
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    这是在 AI 助手面板中显示的简化版本
                  </p>
                </div>
                
                <div className="h-[calc(100%-80px)]">
                  <AIHistoryPanel
                    limit={15}
                    showSearch={true}
                    showStats={true}
                    onInteractionClick={(interaction) => {
                      console.log('点击交互记录:', interaction);
                      alert(`点击了交互记录: ${interaction.type} - ${interaction.document.title}`);
                    }}
                    onViewAll={() => {
                      setCurrentView('full');
                    }}
                  />
                </div>
              </div>
            </div>

            {/* 右侧：功能说明 */}
            <div className="lg:col-span-2 space-y-6">
              {/* 功能特性 */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  AI 交互历史记录功能特性
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <div>
                        <h4 className="font-medium text-gray-900">完整记录存储</h4>
                        <p className="text-sm text-gray-600">
                          保存所有 AI 交互的输入、输出、类型、提供商等详细信息
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div>
                        <h4 className="font-medium text-gray-900">智能搜索</h4>
                        <p className="text-sm text-gray-600">
                          支持按内容、文档标题、交互类型等多维度搜索
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                      <div>
                        <h4 className="font-medium text-gray-900">多重过滤</h4>
                        <p className="text-sm text-gray-600">
                          按类型、提供商、日期范围等条件过滤记录
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                      <div>
                        <h4 className="font-medium text-gray-900">统计分析</h4>
                        <p className="text-sm text-gray-600">
                          提供交互次数、令牌消耗、使用趋势等统计信息
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <h4 className="font-medium text-gray-900">批量管理</h4>
                        <p className="text-sm text-gray-600">
                          支持批量选择、删除和清空历史记录
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-indigo-500 rounded-full mt-2"></div>
                      <div>
                        <h4 className="font-medium text-gray-900">分页浏览</h4>
                        <p className="text-sm text-gray-600">
                          支持分页显示，可调整每页显示数量
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 使用场景 */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  主要使用场景
                </h3>
                
                <div className="space-y-4">
                  <div className="border-l-4 border-blue-500 pl-4">
                    <h4 className="font-medium text-gray-900">写作回顾</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      查看之前的 AI 生成内容，重复使用优质的输出结果，提高写作效率。
                    </p>
                  </div>
                  
                  <div className="border-l-4 border-green-500 pl-4">
                    <h4 className="font-medium text-gray-900">学习参考</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      回顾 AI 的改写建议和解释内容，学习更好的表达方式和写作技巧。
                    </p>
                  </div>
                  
                  <div className="border-l-4 border-purple-500 pl-4">
                    <h4 className="font-medium text-gray-900">使用分析</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      分析 AI 功能的使用情况，了解令牌消耗，优化使用策略。
                    </p>
                  </div>
                  
                  <div className="border-l-4 border-orange-500 pl-4">
                    <h4 className="font-medium text-gray-900">问题追踪</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      当 AI 输出不理想时，可以回顾历史记录，分析问题并改进提示词。
                    </p>
                  </div>
                </div>
              </div>

              {/* 技术实现 */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  技术实现要点
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">数据存储</h4>
                    <ul className="space-y-1 text-gray-600">
                      <li>• 使用 Prisma ORM 管理数据库</li>
                      <li>• 支持 SQLite 和 PostgreSQL</li>
                      <li>• 完整的关系型数据设计</li>
                      <li>• 自动记录时间戳和元数据</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">API 设计</h4>
                    <ul className="space-y-1 text-gray-600">
                      <li>• RESTful API 接口</li>
                      <li>• 支持复杂查询和过滤</li>
                      <li>• 分页和排序功能</li>
                      <li>• 用户权限验证</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">前端组件</h4>
                    <ul className="space-y-1 text-gray-600">
                      <li>• React Hook 状态管理</li>
                      <li>• 响应式界面设计</li>
                      <li>• 实时搜索和过滤</li>
                      <li>• 批量操作支持</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">性能优化</h4>
                    <ul className="space-y-1 text-gray-600">
                      <li>• 数据库索引优化</li>
                      <li>• 分页加载减少内存占用</li>
                      <li>• 缓存常用查询结果</li>
                      <li>• 异步操作提升响应速度</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}