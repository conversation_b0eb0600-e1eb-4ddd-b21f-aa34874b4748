/**
 * 双重保护测试用例
 * 
 * 文件作用：
 * 1. 测试应用层检查和数据库约束的双重保护
 * 2. 验证错误消息的友好性
 * 3. 确保所有 API 端点都有正确的重复检查
 * 
 * 测试场景：
 * - 应用层检查：友好的错误消息
 * - 数据库约束：最后的防线
 * - 软删除支持：删除后可重新创建
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * 测试应用层重复检查
 */
async function testApplicationLayerCheck(userId) {
  console.log('\n🔍 测试应用层重复检查');
  
  // 导入服务
  const DocumentService = {
    async isDocumentTitleDuplicate(title, userId, folderId) {
      const existing = await prisma.document.findFirst({
        where: {
          title,
          userId,
          folderId: folderId || null,
          isDeleted: false
        }
      });
      return !!existing;
    }
  };
  
  const FolderService = {
    async isFolderNameDuplicate(name, userId, parentId) {
      const existing = await prisma.folder.findFirst({
        where: {
          name,
          userId,
          parentId: parentId || null,
          isDeleted: false
        }
      });
      return !!existing;
    }
  };
  
  const testItems = [];
  
  try {
    // 创建测试文档
    const doc = await prisma.document.create({
      data: {
        title: 'DoubleProtectionDoc',
        content: '测试内容',
        userId: userId,
        folderId: null,
        isDeleted: false
      }
    });
    testItems.push({ type: 'document', id: doc.id });
    
    // 创建测试文件夹
    const folder = await prisma.folder.create({
      data: {
        name: 'DoubleProtectionFolder',
        userId: userId,
        parentId: null,
        isDeleted: false
      }
    });
    testItems.push({ type: 'folder', id: folder.id });
    
    // 测试应用层检查
    console.log('   测试文档重复检查:');
    const docDuplicate = await DocumentService.isDocumentTitleDuplicate(
      'DoubleProtectionDoc', userId, null
    );
    console.log(`   - 应用层检查结果: ${docDuplicate ? '发现重复' : '未发现重复'}`);
    
    console.log('   测试文件夹重复检查:');
    const folderDuplicate = await FolderService.isFolderNameDuplicate(
      'DoubleProtectionFolder', userId, null
    );
    console.log(`   - 应用层检查结果: ${folderDuplicate ? '发现重复' : '未发现重复'}`);
    
    if (docDuplicate && folderDuplicate) {
      console.log('   ✅ 应用层检查正常工作');
    } else {
      console.log('   ❌ 应用层检查有问题');
    }
    
    return testItems;
    
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`);
    return testItems;
  }
}

/**
 * 测试数据库约束保护
 */
async function testDatabaseConstraintProtection(userId) {
  console.log('\n🛡️  测试数据库约束保护');
  
  const testItems = [];
  
  try {
    // 创建第一个文档
    const doc1 = await prisma.document.create({
      data: {
        title: 'ConstraintTestDoc',
        content: '第一个文档',
        userId: userId,
        folderId: null,
        isDeleted: false
      }
    });
    testItems.push({ type: 'document', id: doc1.id });
    console.log('   ✅ 第一个文档创建成功');
    
    // 尝试创建重复文档（绕过应用层检查）
    console.log('   尝试直接创建重复文档（绕过应用层）:');
    try {
      const doc2 = await prisma.document.create({
        data: {
          title: 'ConstraintTestDoc',
          content: '第二个文档',
          userId: userId,
          folderId: null,
          isDeleted: false
        }
      });
      testItems.push({ type: 'document', id: doc2.id });
      console.log('   ❌ 数据库约束未生效，重复文档创建成功');
    } catch (error) {
      if (error.message.includes('UNIQUE constraint')) {
        console.log('   ✅ 数据库约束生效，阻止了重复文档');
      } else {
        console.log(`   ⚠️  其他错误: ${error.message}`);
      }
    }
    
    // 创建第一个文件夹
    const folder1 = await prisma.folder.create({
      data: {
        name: 'ConstraintTestFolder',
        userId: userId,
        parentId: null,
        isDeleted: false
      }
    });
    testItems.push({ type: 'folder', id: folder1.id });
    console.log('   ✅ 第一个文件夹创建成功');
    
    // 尝试创建重复文件夹（绕过应用层检查）
    console.log('   尝试直接创建重复文件夹（绕过应用层）:');
    try {
      const folder2 = await prisma.folder.create({
        data: {
          name: 'ConstraintTestFolder',
          userId: userId,
          parentId: null,
          isDeleted: false
        }
      });
      testItems.push({ type: 'folder', id: folder2.id });
      console.log('   ❌ 数据库约束未生效，重复文件夹创建成功');
    } catch (error) {
      if (error.message.includes('UNIQUE constraint')) {
        console.log('   ✅ 数据库约束生效，阻止了重复文件夹');
      } else {
        console.log(`   ⚠️  其他错误: ${error.message}`);
      }
    }
    
    return testItems;
    
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`);
    return testItems;
  }
}

/**
 * 测试软删除后重新创建
 */
async function testSoftDeleteRecreation(userId) {
  console.log('\n🔄 测试软删除后重新创建');
  
  const testItems = [];
  
  try {
    // 创建文档
    const doc = await prisma.document.create({
      data: {
        title: 'SoftDeleteTestDoc',
        content: '测试内容',
        userId: userId,
        folderId: null,
        isDeleted: false
      }
    });
    testItems.push({ type: 'document', id: doc.id });
    console.log('   ✅ 文档创建成功');
    
    // 软删除
    await prisma.document.update({
      where: { id: doc.id },
      data: { isDeleted: true }
    });
    console.log('   ✅ 文档软删除成功');
    
    // 重新创建同名文档
    const newDoc = await prisma.document.create({
      data: {
        title: 'SoftDeleteTestDoc',
        content: '新的测试内容',
        userId: userId,
        folderId: null,
        isDeleted: false
      }
    });
    testItems.push({ type: 'document', id: newDoc.id });
    console.log('   ✅ 软删除后重新创建文档成功');
    
    // 创建文件夹
    const folder = await prisma.folder.create({
      data: {
        name: 'SoftDeleteTestFolder',
        userId: userId,
        parentId: null,
        isDeleted: false
      }
    });
    testItems.push({ type: 'folder', id: folder.id });
    console.log('   ✅ 文件夹创建成功');
    
    // 软删除
    await prisma.folder.update({
      where: { id: folder.id },
      data: { isDeleted: true }
    });
    console.log('   ✅ 文件夹软删除成功');
    
    // 重新创建同名文件夹
    const newFolder = await prisma.folder.create({
      data: {
        name: 'SoftDeleteTestFolder',
        userId: userId,
        parentId: null,
        isDeleted: false
      }
    });
    testItems.push({ type: 'folder', id: newFolder.id });
    console.log('   ✅ 软删除后重新创建文件夹成功');
    
    return testItems;
    
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`);
    return testItems;
  }
}

/**
 * 主测试函数
 */
async function testDoubleProtection() {
  console.log('🚀 开始双重保护测试');
  console.log('=' .repeat(60));
  
  let allTestItems = [];
  
  try {
    // 获取测试用户
    const user = await prisma.user.findFirst();
    if (!user) {
      console.log('❌ 没有测试用户');
      return;
    }
    console.log(`👤 使用测试用户: ${user.name || user.email}`);
    
    // 执行测试
    const appLayerItems = await testApplicationLayerCheck(user.id);
    allTestItems.push(...appLayerItems);
    
    const dbConstraintItems = await testDatabaseConstraintProtection(user.id);
    allTestItems.push(...dbConstraintItems);
    
    const softDeleteItems = await testSoftDeleteRecreation(user.id);
    allTestItems.push(...softDeleteItems);
    
    console.log('\n🎯 双重保护测试总结:');
    console.log('   ✅ 应用层检查：提供友好的错误消息');
    console.log('   ✅ 数据库约束：作为最后的防线');
    console.log('   ✅ 软删除支持：删除后可重新创建同名项目');
    console.log('   📝 双重保护确保数据一致性和用户体验');
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
  } finally {
    // 清理测试数据
    console.log('\n🧹 清理测试数据...');
    
    const docIds = allTestItems.filter(item => item.type === 'document').map(item => item.id);
    const folderIds = allTestItems.filter(item => item.type === 'folder').map(item => item.id);
    
    if (docIds.length > 0) {
      await prisma.document.deleteMany({
        where: { id: { in: docIds } }
      });
      console.log(`   ✅ 清理了 ${docIds.length} 个测试文档`);
    }
    
    if (folderIds.length > 0) {
      await prisma.folder.deleteMany({
        where: { id: { in: folderIds } }
      });
      console.log(`   ✅ 清理了 ${folderIds.length} 个测试文件夹`);
    }
    
    await prisma.$disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

// 执行测试
console.log('🚀 开始执行双重保护测试...');
testDoubleProtection();
