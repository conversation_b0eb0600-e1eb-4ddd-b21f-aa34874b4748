'use client';

import { useState, useEffect, useCallback } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import { Button } from '@/components/ui/Button';
import { 
  Bold, 
  Italic, 
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Quote,
  Undo,
  Redo,
  MoreHorizontal,
  X,
  Bot
} from 'lucide-react';
import { MobileAIPanel } from '../ai/MobileAIPanel';

interface MobileEditorProps {
  content?: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  editable?: boolean;
  className?: string;
}

/**
 * 移动端优化的编辑器组件
 * 提供触摸友好的界面和简化的工具栏
 */
export function MobileEditor({
  content = '',
  placeholder = '开始写作...',
  onChange,
  editable = true,
  className = '',
}: MobileEditorProps) {
  const [showToolbar, setShowToolbar] = useState(false);
  const [showAIPanel, setShowAIPanel] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
      }),
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
      CharacterCount.configure({
        limit: null,
      }),
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange?.(html);
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm mx-auto focus:outline-none touch-manipulation min-h-[300px] p-4',
      },
    },
  });

  // 检测虚拟键盘
  useEffect(() => {
    const handleResize = () => {
      const viewportHeight = window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.innerHeight;
      const heightDiff = windowHeight - viewportHeight;
      
      setIsKeyboardVisible(heightDiff > 150);
    };

    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleResize);
      return () => window.visualViewport?.removeEventListener('resize', handleResize);
    } else {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  // 更新内容
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  // 更新可编辑状态
  useEffect(() => {
    if (editor) {
      editor.setEditable(editable);
    }
  }, [editable, editor]);

  const getStats = useCallback(() => {
    if (!editor) return { words: 0, characters: 0 };
    return {
      words: editor.storage.characterCount.words(),
      characters: editor.storage.characterCount.characters(),
    };
  }, [editor]);

  const ToolbarButton = ({ 
    onClick, 
    isActive = false, 
    disabled = false, 
    children, 
    title 
  }: {
    onClick: () => void;
    isActive?: boolean;
    disabled?: boolean;
    children: React.ReactNode;
    title: string;
  }) => (
    <Button
      variant={isActive ? 'default' : 'ghost'}
      size="sm"
      onClick={onClick}
      disabled={disabled}
      title={title}
      className={`h-10 w-10 p-0 touch-manipulation ${isActive ? 'bg-primary text-primary-foreground' : ''}`}
    >
      {children}
    </Button>
  );

  if (!editor) {
    return (
      <div className={`min-h-[400px] w-full ${className}`}>
        <div className="flex items-center justify-center h-full">
          <div className="text-muted-foreground">加载编辑器...</div>
        </div>
      </div>
    );
  }

  const stats = getStats();

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* 编辑器内容区域 */}
      <div className="flex-1 relative">
        <EditorContent 
          editor={editor} 
          className="h-full w-full focus-within:outline-none"
          onFocus={() => setShowToolbar(true)}
        />
        
        {/* 浮动操作按钮 */}
        {!showToolbar && !showAIPanel && (
          <div className="fixed bottom-6 right-4 flex flex-col gap-3 z-40">
            {/* AI助手按钮 */}
            <Button
              onClick={() => setShowAIPanel(true)}
              className="h-12 w-12 rounded-full shadow-lg touch-manipulation bg-primary hover:bg-primary/90"
              size="sm"
            >
              <Bot className="h-5 w-5" />
            </Button>
            
            {/* 格式化按钮 */}
            <Button
              onClick={() => setShowToolbar(true)}
              className="h-12 w-12 rounded-full shadow-lg touch-manipulation"
              size="sm"
              variant="secondary"
            >
              <MoreHorizontal className="h-5 w-5" />
            </Button>
          </div>
        )}
      </div>

      {/* 移动端工具栏 */}
      {showToolbar && (
        <div className={`
          fixed bottom-0 left-0 right-0 z-50 
          bg-background border-t border-border
          transition-transform duration-300 ease-in-out
          ${isKeyboardVisible ? 'transform translate-y-0' : ''}
        `}>
          <div className="flex items-center justify-between p-2 border-b border-border">
            <div className="text-sm font-medium">格式化</div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowToolbar(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="p-3">
            {/* 第一行：文本格式 */}
            <div className="flex items-center gap-2 mb-3">
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleBold().run()}
                isActive={editor.isActive('bold')}
                title="粗体"
              >
                <Bold className="h-4 w-4" />
              </ToolbarButton>
              
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleItalic().run()}
                isActive={editor.isActive('italic')}
                title="斜体"
              >
                <Italic className="h-4 w-4" />
              </ToolbarButton>
              
              <div className="h-6 w-px bg-border mx-1" />
              
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                isActive={editor.isActive('heading', { level: 1 })}
                title="标题 1"
              >
                <Heading1 className="h-4 w-4" />
              </ToolbarButton>
              
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                isActive={editor.isActive('heading', { level: 2 })}
                title="标题 2"
              >
                <Heading2 className="h-4 w-4" />
              </ToolbarButton>
            </div>
            
            {/* 第二行：列表和其他 */}
            <div className="flex items-center gap-2 mb-3">
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleBulletList().run()}
                isActive={editor.isActive('bulletList')}
                title="无序列表"
              >
                <List className="h-4 w-4" />
              </ToolbarButton>
              
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleOrderedList().run()}
                isActive={editor.isActive('orderedList')}
                title="有序列表"
              >
                <ListOrdered className="h-4 w-4" />
              </ToolbarButton>
              
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleBlockquote().run()}
                isActive={editor.isActive('blockquote')}
                title="引用"
              >
                <Quote className="h-4 w-4" />
              </ToolbarButton>
              
              <div className="h-6 w-px bg-border mx-1" />
              
              <ToolbarButton
                onClick={() => editor.chain().focus().undo().run()}
                disabled={!editor.can().undo()}
                title="撤销"
              >
                <Undo className="h-4 w-4" />
              </ToolbarButton>
              
              <ToolbarButton
                onClick={() => editor.chain().focus().redo().run()}
                disabled={!editor.can().redo()}
                title="重做"
              >
                <Redo className="h-4 w-4" />
              </ToolbarButton>
            </div>
            
            {/* 统计信息 */}
            <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t border-border">
              <div>{stats.words} 词 · {stats.characters} 字符</div>
              <div className="text-green-600">● 自动保存</div>
            </div>
          </div>
        </div>
      )}
      
      {/* 遮罩层 */}
      {showToolbar && (
        <div 
          className="fixed inset-0 bg-black/20 z-40"
          onClick={() => setShowToolbar(false)}
        />
      )}
      
      {/* AI助手面板 */}
      <MobileAIPanel
        visible={showAIPanel}
        onClose={() => setShowAIPanel(false)}
        onAction={(action) => {
          console.log('AI Action:', action);
          // TODO: 实现AI功能
        }}
      />
    </div>
  );
}