# AI 配置页面测试清单

## 修复验证

- [ ] 页面加载时没有出现 "Maximum update depth exceeded" 错误
- [ ] 控制台没有无限循环相关的错误信息
- [ ] 页面渲染正常，组件显示完整

## 基本功能测试

### 1. 页面访问

- [ ] 访问 `http://localhost:4501/ai-config` 页面正常加载
- [ ] 页面标题和描述正确显示
- [ ] 如果没有配置，显示空状态提示

### 2. 添加配置功能

- [ ] 点击"添加配置"按钮，表单正常显示
- [ ] 提供商下拉菜单可以正常选择（OpenAI、Ollama、Gemini）
- [ ] 根据不同提供商，相关字段正确显示/隐藏：
  - OpenAI: 显示 API 密钥字段
  - Ollama: 显示端点字段，隐藏 API 密钥
  - Gemini: 显示 API 密钥字段
- [ ] 模型下拉菜单根据提供商正确更新
- [ ] 表单验证正常工作（必填字段、数值范围等）
- [ ] "测试连接"按钮可以点击（即使没有真实配置）
- [ ] "取消"按钮可以返回列表页面

### 3. 表单交互测试

- [ ] 切换提供商时，相关字段自动更新
- [ ] API 密钥字段的显示/隐藏切换正常
- [ ] 数值输入字段（最大令牌数、温度）验证正常
- [ ] 复选框（设为默认配置）可以正常切换

### 4. 错误处理

- [ ] 表单验证错误正确显示
- [ ] 网络错误时有适当的错误提示
- [ ] 错误状态可以正确清除

## 性能测试

- [ ] 页面加载速度正常（< 3秒）
- [ ] 表单交互响应及时
- [ ] 没有内存泄漏（长时间使用后页面仍然流畅）

## 浏览器兼容性

- [ ] Chrome 浏览器正常工作
- [ ] Firefox 浏览器正常工作
- [ ] Edge 浏览器正常工作

## 移动端测试

- [ ] 响应式设计在移动设备上正常显示
- [ ] 触摸交互正常工作

## 测试步骤

### 快速验证步骤：

1. 打开 `http://localhost:4501/ai-config`
2. 检查控制台是否有错误
3. 点击"添加配置"
4. 切换不同的提供商选项
5. 观察字段变化是否正常
6. 点击"取消"返回

### 详细测试步骤：

1. 清空浏览器缓存
2. 打开开发者工具的控制台
3. 访问 AI 配置页面
4. 执行上述所有功能测试
5. 记录任何异常行为

## 常见问题排查

### 如果仍然出现无限循环错误：

1. 检查 `useEffect` 依赖数组
2. 检查 `useCallback` 的使用
3. 检查状态更新是否导致组件重新渲染
4. 使用 React DevTools 分析组件渲染次数

### 如果表单行为异常：

1. 检查 `handleFieldChange` 函数
2. 检查状态更新逻辑
3. 验证 `useMemo` 的依赖项
4. 检查 Select 组件的 value 和 onValueChange 属性
