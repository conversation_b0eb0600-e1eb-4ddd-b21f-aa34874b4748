# Database Configuration
# SQLite for development
DATABASE_URL="file:./dev.db"

# For production with PostgreSQL, use:
# DATABASE_URL="postgresql://username:password@localhost:5432/nextjs_document_editor"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# OAuth Providers (Optional)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
GITHUB_ID=""
GITHUB_SECRET=""

# AI Services (Optional - users can configure these in the app)
OPENAI_API_KEY=""
OLLAMA_ENDPOINT="http://localhost:11434"
GEMINI_API_KEY=""

# App Configuration
NODE_ENV="development"