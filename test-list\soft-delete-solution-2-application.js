/**
 * 方案2：应用层处理软删除唯一性冲突
 *
 * 文件作用：
 * 1. 在应用层面检查文件夹名称的唯一性
 * 2. 提供灵活的业务逻辑处理
 * 3. 支持自定义错误消息和用户提示
 *
 * 适用场景：
 * - 需要复杂的业务逻辑判断
 * - 数据库不支持部分索引
 * - 需要提供详细的用户反馈
 *
 * 优点：
 * ✅ 灵活性高，可以实现复杂逻辑
 * ✅ 数据库无关，兼容性好
 * ✅ 易于调试和维护
 * ✅ 可以提供友好的错误消息
 *
 * 缺点：
 * ❌ 性能较差（额外查询）
 * ❌ 并发安全性问题
 * ❌ 容易遗漏检查
 * ❌ 维护成本高
 *
 * 当前问题：
 * - 文档创建时有重复检查，但错误提示可能不够友好
 * - 文件夹创建时部分 API 有检查，部分没有
 * - 数据库层面完全没有约束，存在数据一致性风险
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * 检查文件夹名称是否已存在（只检查活跃的文件夹）
 * @param {string} name - 文件夹名称
 * @param {string} userId - 用户ID
 * @param {string|null} parentId - 父文件夹ID
 * @param {string|null} excludeId - 排除的文件夹ID（用于更新时）
 * @returns {Promise<boolean>} 是否存在冲突
 */
async function checkFolderNameConflict(name, userId, parentId = null, excludeId = null) {
  console.log(`🔍 检查文件夹名称冲突: "${name}"`);

  const whereClause = {
    name: name,
    userId: userId,
    parentId: parentId,
    isDeleted: false, // 只检查活跃的文件夹
  };

  // 如果是更新操作，排除当前文件夹
  if (excludeId) {
    whereClause.id = { not: excludeId };
  }

  const existingFolder = await prisma.folder.findFirst({
    where: whereClause,
    select: { id: true, name: true, createdAt: true }
  });

  if (existingFolder) {
    console.log(`❌ 发现冲突: 文件夹 "${name}" 已存在`);
    console.log(`   - 现有文件夹ID: ${existingFolder.id}`);
    console.log(`   - 创建时间: ${existingFolder.createdAt}`);
    return true;
  }

  console.log(`✅ 无冲突: 文件夹名称 "${name}" 可用`);
  return false;
}

/**
 * 安全创建文件夹（带冲突检查）
 * @param {string} name - 文件夹名称
 * @param {string} userId - 用户ID
 * @param {string|null} parentId - 父文件夹ID
 * @returns {Promise<Object>} 创建结果
 */
async function safeCreateFolder(name, userId, parentId = null) {
  console.log(`📁 尝试创建文件夹: "${name}"`);

  try {
    // 步骤1: 检查名称冲突
    const hasConflict = await checkFolderNameConflict(name, userId, parentId);

    if (hasConflict) {
      // 检查是否有已删除的同名文件夹
      const deletedFolder = await prisma.folder.findFirst({
        where: {
          name: name,
          userId: userId,
          parentId: parentId,
          isDeleted: true
        },
        select: { id: true, name: true, updatedAt: true },
        orderBy: { updatedAt: 'desc' }
      });

      if (deletedFolder) {
        return {
          success: false,
          error: 'FOLDER_NAME_EXISTS',
          message: `文件夹名称 "${name}" 已存在`,
          suggestion: '您可以选择恢复之前删除的文件夹，或使用其他名称',
          deletedFolder: {
            id: deletedFolder.id,
            deletedAt: deletedFolder.updatedAt
          }
        };
      } else {
        return {
          success: false,
          error: 'FOLDER_NAME_EXISTS',
          message: `文件夹名称 "${name}" 已存在`,
          suggestion: '请使用其他名称'
        };
      }
    }

    // 步骤2: 创建文件夹
    const folder = await prisma.folder.create({
      data: {
        name: name,
        userId: userId,
        parentId: parentId,
        isDeleted: false
      }
    });

    console.log(`✅ 文件夹创建成功: ${folder.id}`);

    return {
      success: true,
      folder: folder,
      message: `文件夹 "${name}" 创建成功`
    };

  } catch (error) {
    console.error(`❌ 创建文件夹失败:`, error.message);

    return {
      success: false,
      error: 'CREATE_FAILED',
      message: '创建文件夹失败',
      details: error.message
    };
  }
}

/**
 * 恢复已删除的文件夹
 * @param {string} folderId - 文件夹ID
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 恢复结果
 */
async function restoreFolder(folderId, userId) {
  console.log(`🔄 尝试恢复文件夹: ${folderId}`);

  try {
    // 检查文件夹是否存在且已删除
    const folder = await prisma.folder.findFirst({
      where: {
        id: folderId,
        userId: userId,
        isDeleted: true
      }
    });

    if (!folder) {
      return {
        success: false,
        error: 'FOLDER_NOT_FOUND',
        message: '文件夹不存在或未被删除'
      };
    }

    // 检查恢复后是否会有名称冲突
    const hasConflict = await checkFolderNameConflict(
      folder.name,
      userId,
      folder.parentId,
      folderId
    );

    if (hasConflict) {
      return {
        success: false,
        error: 'RESTORE_CONFLICT',
        message: `无法恢复：文件夹名称 "${folder.name}" 已存在`,
        suggestion: '请先重命名现有文件夹，或为要恢复的文件夹选择新名称'
      };
    }

    // 恢复文件夹
    const restoredFolder = await prisma.folder.update({
      where: { id: folderId },
      data: {
        isDeleted: false,
        updatedAt: new Date()
      }
    });

    console.log(`✅ 文件夹恢复成功: ${restoredFolder.name}`);

    return {
      success: true,
      folder: restoredFolder,
      message: `文件夹 "${restoredFolder.name}" 恢复成功`
    };

  } catch (error) {
    console.error(`❌ 恢复文件夹失败:`, error.message);

    return {
      success: false,
      error: 'RESTORE_FAILED',
      message: '恢复文件夹失败',
      details: error.message
    };
  }
}

/**
 * 测试应用层处理方案
 */
async function testApplicationLayerSolution() {
  console.log('🚀 测试应用层软删除处理方案');
  console.log('=' .repeat(50));

  try {
    const user = await prisma.user.findFirst();
    if (!user) {
      console.log('❌ 没有测试用户');
      return;
    }

    const testName = 'AppLayerTest';

    // 测试1: 创建文件夹
    console.log('\n📝 测试1: 创建文件夹');
    const result1 = await safeCreateFolder(testName, user.id);
    console.log('结果:', result1.success ? '成功' : '失败');
    if (!result1.success) console.log('错误:', result1.message);

    // 测试2: 尝试创建同名文件夹
    console.log('\n📝 测试2: 尝试创建同名文件夹');
    const result2 = await safeCreateFolder(testName, user.id);
    console.log('结果:', result2.success ? '成功' : '失败');
    if (!result2.success) console.log('错误:', result2.message);

    if (result1.success) {
      // 测试3: 软删除文件夹
      console.log('\n📝 测试3: 软删除文件夹');
      await prisma.folder.update({
        where: { id: result1.folder.id },
        data: { isDeleted: true }
      });
      console.log('✅ 软删除完成');

      // 测试4: 删除后创建同名文件夹
      console.log('\n📝 测试4: 删除后创建同名文件夹');
      const result4 = await safeCreateFolder(testName, user.id);
      console.log('结果:', result4.success ? '成功' : '失败');
      if (result4.success) {
        console.log('✅ 可以创建同名文件夹');
      }

      // 清理测试数据
      await prisma.folder.deleteMany({
        where: { name: testName, userId: user.id }
      });
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testApplicationLayerSolution();
}

module.exports = {
  checkFolderNameConflict,
  safeCreateFolder,
  restoreFolder
};
