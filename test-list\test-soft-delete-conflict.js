/**
 * 软删除唯一性约束冲突测试用例
 *
 * 文件作用：
 * 1. 测试在软删除场景下，重新创建同名文件夹是否会产生唯一性约束冲突
 * 2. 演示当前数据库设计（无唯一约束）下的行为
 * 3. 为软删除功能的设计提供测试基础
 *
 * 测试场景：
 * - 创建文件夹 "test123"
 * - 软删除该文件夹（设置 isDeleted = true）
 * - 尝试重新创建同名文件夹 "test123"
 * - 验证是否会产生冲突
 *
 * 预期结果：
 * - 当前设计下不会有冲突（因为没有唯一约束）
 * - 数据库中会同时存在已删除和活跃的同名文件夹
 *
 * 注意事项：
 * - 这个测试会在数据库中创建和删除测试数据
 * - 测试完成后会自动清理数据
 * - 需要数据库中至少有一个用户记录
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * 主测试函数：测试软删除的唯一性约束问题
 *
 * 测试步骤：
 * 1. 获取测试用户
 * 2. 创建测试文件夹
 * 3. 执行软删除操作
 * 4. 查看数据库状态
 * 5. 尝试创建同名文件夹
 * 6. 清理测试数据
 */
async function testSoftDeleteConflict() {
  try {
    console.log('测试软删除的唯一性约束问题...\n');

    // 步骤1: 获取数据库中的第一个用户作为测试用户
    const user = await prisma.user.findFirst();
    if (!user) {
      console.log('❌ 没有用户，无法测试');
      console.log('提示: 请先运行 npm run db:seed 创建测试数据');
      return;
    }
    console.log(`📝 使用测试用户: ${user.email} (ID: ${user.id})`);

    const testName = 'test123'; // 测试文件夹名称

    // 步骤2: 创建测试文件夹
    console.log('\n📁 步骤1: 创建文件夹:', testName);
    const folder1 = await prisma.folder.create({
      data: {
        name: testName,
        userId: user.id,
        isDeleted: false, // 明确设置为未删除状态
      }
    });
    console.log('✅ 文件夹创建成功');
    console.log(`   - ID: ${folder1.id}`);
    console.log(`   - 名称: ${folder1.name}`);
    console.log(`   - 删除状态: ${folder1.isDeleted}`);

    // 步骤3: 执行软删除操作
    console.log('\n🗑️  步骤2: 执行软删除操作');
    await prisma.folder.update({
      where: { id: folder1.id },
      data: {
        isDeleted: true,
        updatedAt: new Date() // 更新修改时间
      }
    });
    console.log('✅ 软删除操作完成');
    console.log('   - 文件夹已标记为已删除，但数据仍在数据库中');

    // 步骤4: 查看数据库当前状态
    console.log('\n🔍 步骤3: 查看数据库当前状态');
    const allFolders = await prisma.folder.findMany({
      where: { name: testName, userId: user.id },
      select: { id: true, name: true, isDeleted: true, createdAt: true, updatedAt: true },
      orderBy: { createdAt: 'asc' }
    });
    console.log(`📊 数据库中共有 ${allFolders.length} 个名为 "${testName}" 的文件夹:`);
    allFolders.forEach((f, index) => {
      console.log(`   ${index + 1}. ID: ${f.id.slice(-8)}... | 名称: ${f.name} | 已删除: ${f.isDeleted ? '是' : '否'}`);
    });

    // 验证软删除的效果
    const activeFolders = allFolders.filter(f => !f.isDeleted);
    const deletedFolders = allFolders.filter(f => f.isDeleted);
    console.log(`   📈 活跃文件夹: ${activeFolders.length} 个`);
    console.log(`   🗑️  已删除文件夹: ${deletedFolders.length} 个`);

    // 步骤5: 尝试创建同名文件夹（关键测试点）
    console.log('\n🆕 步骤4: 尝试创建同名文件夹...');
    console.log('   这是测试的关键点：软删除后是否能创建同名文件夹');

    try {
      const folder2 = await prisma.folder.create({
        data: {
          name: testName,
          userId: user.id,
          isDeleted: false, // 新文件夹为活跃状态
        }
      });
      console.log('✅ 同名文件夹创建成功！');
      console.log(`   - 新文件夹 ID: ${folder2.id}`);
      console.log(`   - 名称: ${folder2.name}`);
      console.log(`   - 删除状态: ${folder2.isDeleted}`);

      // 步骤6: 验证最终状态
      console.log('\n📊 步骤5: 验证最终数据库状态');
      const finalFolders = await prisma.folder.findMany({
        where: { name: testName, userId: user.id },
        select: { id: true, name: true, isDeleted: true },
        orderBy: { createdAt: 'asc' }
      });
      console.log(`   现在数据库中有 ${finalFolders.length} 个名为 "${testName}" 的文件夹:`);
      finalFolders.forEach((f, index) => {
        const status = f.isDeleted ? '已删除' : '活跃';
        console.log(`   ${index + 1}. ${f.id.slice(-8)}... - ${status}`);
      });

      // 清理测试数据（硬删除，完全移除）
      console.log('\n🧹 清理测试数据...');
      await prisma.folder.deleteMany({
        where: {
          OR: [
            { id: folder1.id },
            { id: folder2.id }
          ]
        }
      });
      console.log('✅ 测试数据清理完成');

    } catch (error) {
      console.log('❌ 创建同名文件夹失败:', error.message);
      console.log('   错误类型:', error.constructor.name);
      if (error.code) {
        console.log('   错误代码:', error.code);
      }

      // 清理测试数据
      console.log('\n🧹 清理测试数据...');
      await prisma.folder.delete({ where: { id: folder1.id } });
      console.log('✅ 测试数据清理完成');
    }

    // 测试结论
    console.log('\n🎯 测试结论:');
    console.log('   ✅ 当前数据库设计允许创建同名文件夹');
    console.log('   📝 原因: folders 表没有设置唯一约束 UNIQUE(userId, name)');
    console.log('   💡 这意味着软删除不会产生唯一性冲突');
    console.log('   ⚠️  但可能导致用户界面显示多个同名文件夹');

  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
    console.error('   错误详情:', error);
  } finally {
    // 确保数据库连接正确关闭
    await prisma.$disconnect();
    console.log('\n🔌 数据库连接已关闭');
  }
}

// 执行测试
console.log('🚀 开始执行软删除唯一性约束测试...');
console.log('=' .repeat(60));
testSoftDeleteConflict();
