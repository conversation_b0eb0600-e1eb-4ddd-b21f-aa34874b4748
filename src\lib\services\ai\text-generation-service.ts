/**
 * AI 文本生成服务
 * 专门处理文本续写和生成功能
 */

import { IAIService } from './base-ai-service';
import { aiServiceManager } from './ai-service-factory';
import { AIRequest, AIResponse, AIServiceError, AIErrorType } from '@/types/ai.types';

/**
 * 文本生成请求参数
 */
export interface TextGenerationRequest {
  /** 当前文档内容（作为上下文） */
  context: string;
  /** 选中的文本（如果有） */
  selectedText?: string;
  /** 光标位置前的文本 */
  beforeCursor?: string;
  /** 光标位置后的文本 */
  afterCursor?: string;
  /** 生成类型 */
  type: 'continue' | 'complete' | 'expand';
  /** 生成长度偏好 */
  length?: 'short' | 'medium' | 'long';
  /** 写作风格 */
  style?: 'formal' | 'casual' | 'creative' | 'technical';
}

/**
 * 文本生成结果
 */
export interface TextGenerationResult {
  /** 生成的文本内容 */
  content: string;
  /** 生成的多个选项（如果有） */
  alternatives?: string[];
  /** 生成类型 */
  type: TextGenerationRequest['type'];
  /** 使用的令牌数 */
  tokensUsed: number;
  /** 响应时间 */
  responseTime: number;
  /** 生成的唯一ID */
  id: string;
}

/**
 * AI 文本生成服务类
 */
export class TextGenerationService {
  private aiService: IAIService;
  
  constructor(aiService?: IAIService) {
    this.aiService = aiService || aiServiceManager.getDefaultService();
  }
  
  /**
   * 生成续写内容
   * @param request 文本生成请求
   * @returns 生成结果
   */
  async generateText(request: TextGenerationRequest): Promise<TextGenerationResult> {
    try {
      const prompt = this.buildPrompt(request);
      const aiRequest: AIRequest = {
        prompt,
        context: request.context,
        maxTokens: this.getMaxTokensForLength(request.length),
        temperature: this.getTemperatureForStyle(request.style)
      };
      
      const startTime = Date.now();
      const response = await this.aiService.generateText(aiRequest);
      const responseTime = Date.now() - startTime;
      
      // 处理生成的内容
      const processedContent = this.processGeneratedContent(response.content, request);
      const alternatives = this.extractAlternatives(response.content);
      
      return {
        content: processedContent,
        alternatives,
        type: request.type,
        tokensUsed: response.tokensUsed,
        responseTime,
        id: this.generateId()
      };
    } catch (error) {
      if (error instanceof AIServiceError) {
        throw error;
      }
      throw new AIServiceError(
        AIErrorType.UNKNOWN_ERROR,
        `文本生成失败: ${error instanceof Error ? error.message : '未知错误'}`,
        this.aiService.provider,
        error instanceof Error ? error : undefined
      );
    }
  }
  
  /**
   * 生成多个续写选项
   * @param request 文本生成请求
   * @param count 生成数量
   * @returns 多个生成结果
   */
  async generateMultipleOptions(
    request: TextGenerationRequest, 
    count: number = 3
  ): Promise<TextGenerationResult[]> {
    const promises = Array.from({ length: count }, () => this.generateText(request));
    const results = await Promise.allSettled(promises);
    
    return results
      .filter((result): result is PromiseFulfilledResult<TextGenerationResult> => 
        result.status === 'fulfilled'
      )
      .map(result => result.value);
  }
  
  /**
   * 构建生成提示
   */
  private buildPrompt(request: TextGenerationRequest): string {
    const { type, selectedText, beforeCursor, afterCursor, style, length } = request;
    
    let prompt = '';
    
    switch (type) {
      case 'continue':
        prompt = this.buildContinuePrompt(beforeCursor || '', afterCursor, style, length);
        break;
      case 'complete':
        prompt = this.buildCompletePrompt(selectedText || '', beforeCursor, afterCursor, style);
        break;
      case 'expand':
        prompt = this.buildExpandPrompt(selectedText || '', style, length);
        break;
    }
    
    return prompt;
  }
  
  /**
   * 构建续写提示
   */
  private buildContinuePrompt(
    beforeText: string, 
    afterText?: string, 
    style?: string, 
    length?: string
  ): string {
    let prompt = '请基于以下文本内容继续写作：\n\n';
    prompt += `${beforeText}\n\n`;
    
    if (afterText) {
      prompt += `注意：后续内容为 "${afterText}"，请确保续写内容与之连贯。\n\n`;
    }
    
    prompt += '要求：\n';
    prompt += '- 保持与原文的语调和风格一致\n';
    prompt += '- 内容要自然流畅，逻辑连贯\n';
    prompt += '- 不要重复已有内容\n';
    
    if (style) {
      prompt += `- 写作风格：${this.getStyleDescription(style)}\n`;
    }
    
    if (length) {
      prompt += `- 内容长度：${this.getLengthDescription(length)}\n`;
    }
    
    prompt += '\n请直接输出续写内容，不需要其他说明：';
    
    return prompt;
  }
  
  /**
   * 构建补全提示
   */
  private buildCompletePrompt(
    selectedText: string,
    beforeText?: string,
    afterText?: string,
    style?: string
  ): string {
    let prompt = '请补全以下不完整的文本：\n\n';
    
    if (beforeText) {
      prompt += `前文：${beforeText}\n\n`;
    }
    
    prompt += `待补全文本：${selectedText}\n\n`;
    
    if (afterText) {
      prompt += `后文：${afterText}\n\n`;
    }
    
    prompt += '要求：\n';
    prompt += '- 补全内容要与上下文连贯\n';
    prompt += '- 保持原有的语调和风格\n';
    prompt += '- 使内容更加完整和清晰\n';
    
    if (style) {
      prompt += `- 写作风格：${this.getStyleDescription(style)}\n`;
    }
    
    prompt += '\n请直接输出补全后的完整文本：';
    
    return prompt;
  }
  
  /**
   * 构建扩展提示
   */
  private buildExpandPrompt(selectedText: string, style?: string, length?: string): string {
    let prompt = '请扩展以下文本内容，使其更加详细和丰富：\n\n';
    prompt += `${selectedText}\n\n`;
    
    prompt += '要求：\n';
    prompt += '- 保持原意不变\n';
    prompt += '- 添加更多细节和说明\n';
    prompt += '- 使内容更加生动和具体\n';
    prompt += '- 保持逻辑清晰\n';
    
    if (style) {
      prompt += `- 写作风格：${this.getStyleDescription(style)}\n`;
    }
    
    if (length) {
      prompt += `- 扩展程度：${this.getLengthDescription(length)}\n`;
    }
    
    prompt += '\n请直接输出扩展后的文本：';
    
    return prompt;
  }
  
  /**
   * 获取风格描述
   */
  private getStyleDescription(style: string): string {
    const descriptions = {
      formal: '正式、严谨',
      casual: '轻松、随意',
      creative: '创意、生动',
      technical: '技术、专业'
    };
    return descriptions[style as keyof typeof descriptions] || '自然';
  }
  
  /**
   * 获取长度描述
   */
  private getLengthDescription(length: string): string {
    const descriptions = {
      short: '简短（1-2句话）',
      medium: '中等（1-2段）',
      long: '详细（多段内容）'
    };
    return descriptions[length as keyof typeof descriptions] || '适中';
  }
  
  /**
   * 根据长度偏好获取最大令牌数
   */
  private getMaxTokensForLength(length?: string): number {
    switch (length) {
      case 'short':
        return 150;
      case 'medium':
        return 500;
      case 'long':
        return 1000;
      default:
        return 300;
    }
  }
  
  /**
   * 根据风格获取温度参数
   */
  private getTemperatureForStyle(style?: string): number {
    switch (style) {
      case 'creative':
        return 0.9;
      case 'casual':
        return 0.8;
      case 'formal':
        return 0.5;
      case 'technical':
        return 0.3;
      default:
        return 0.7;
    }
  }
  
  /**
   * 处理生成的内容
   */
  private processGeneratedContent(content: string, request: TextGenerationRequest): string {
    // 清理内容
    let processed = content.trim();
    
    // 移除可能的引号包围
    if ((processed.startsWith('"') && processed.endsWith('"')) ||
        (processed.startsWith("'") && processed.endsWith("'"))) {
      processed = processed.slice(1, -1);
    }
    
    // 移除可能的说明性前缀
    const prefixes = ['续写：', '补全：', '扩展：', '生成内容：'];
    for (const prefix of prefixes) {
      if (processed.startsWith(prefix)) {
        processed = processed.substring(prefix.length).trim();
        break;
      }
    }
    
    return processed;
  }
  
  /**
   * 提取备选方案
   */
  private extractAlternatives(content: string): string[] {
    // 尝试从内容中提取多个选项
    const alternatives: string[] = [];
    
    // 检查是否包含分隔符
    const separators = ['---', '选项1:', '选项2:', '选项3:', '方案一:', '方案二:', '方案三:'];
    
    for (const separator of separators) {
      if (content.includes(separator)) {
        const parts = content.split(separator)
          .map(part => part.trim())
          .filter(part => part.length > 0);
        
        if (parts.length > 1) {
          alternatives.push(...parts.slice(1)); // 跳过第一部分（通常是说明）
          break;
        }
      }
    }
    
    return alternatives;
  }
  
  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 创建文本生成服务实例
 */
export function createTextGenerationService(aiService?: IAIService): TextGenerationService {
  return new TextGenerationService(aiService);
}