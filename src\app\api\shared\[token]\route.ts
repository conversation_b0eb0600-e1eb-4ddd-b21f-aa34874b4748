import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db/prisma';

/**
 * GET /api/shared/[token] - 通过分享令牌获取公共文档
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const { token } = params;

    if (!token) {
      return NextResponse.json({ error: '分享令牌无效' }, { status: 400 });
    }

    // 查找具有该分享令牌的公共文档
    const document = await prisma.document.findFirst({
      where: {
        shareToken: token,
        isPublic: true,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
        folder: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!document) {
      return NextResponse.json(
        { error: '文档未找到或已停止分享' },
        { status: 404 }
      );
    }

    // 返回文档信息（不包含敏感信息）
    const sharedDocument = {
      id: document.id,
      title: document.title,
      content: document.content,
      wordCount: document.wordCount,
      charCount: document.charCount,
      createdAt: document.createdAt,
      updatedAt: document.updatedAt,
      author: {
        name: document.user.name,
      },
      folder: document.folder ? {
        name: document.folder.name,
      } : null,
    };

    return NextResponse.json({ document: sharedDocument });
  } catch (error) {
    console.error('获取分享文档失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}