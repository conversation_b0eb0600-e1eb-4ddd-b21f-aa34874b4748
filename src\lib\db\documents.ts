import { prisma } from './prisma';
import { Document, DocumentMetadata } from '@/types';
import { JSONContent } from '@tiptap/react';

export async function createDocument(data: {
  title: string;
  content: JSONContent;
  userId: string;
  folderId?: string;
}): Promise<Document> {
  const contentString = JSON.stringify(data.content);
  const wordCount = getWordCountFromContent(data.content);
  const charCount = getCharCountFromContent(data.content);

  const document = await prisma.document.create({
    data: {
      title: data.title,
      content: contentString,
      userId: data.userId,
      folderId: data.folderId,
      wordCount,
      charCount,
    },
  });

  return transformPrismaDocumentToDocument(document);
}

export async function getDocumentById(
  id: string,
  userId: string
): Promise<Document | null> {
  const document = await prisma.document.findFirst({
    where: {
      id,
      userId,
    },
  });

  if (!document) return null;

  return transformPrismaDocumentToDocument(document);
}

export async function getDocumentsByUserId(userId: string): Promise<Document[]> {
  const documents = await prisma.document.findMany({
    where: { userId },
    orderBy: { updatedAt: 'desc' },
  });

  return documents.map(transformPrismaDocumentToDocument);
}

export async function getDocumentsByFolderId(
  folderId: string,
  userId: string
): Promise<Document[]> {
  const documents = await prisma.document.findMany({
    where: {
      folderId,
      userId,
    },
    orderBy: { updatedAt: 'desc' },
  });

  return documents.map(transformPrismaDocumentToDocument);
}

export async function updateDocument(
  id: string,
  userId: string,
  data: Partial<{
    title: string;
    content: JSONContent;
    folderId: string;
  }>
): Promise<Document> {
  const updateData: any = {};

  if (data.title) updateData.title = data.title;
  if (data.folderId !== undefined) updateData.folderId = data.folderId;
  if (data.content) {
    updateData.content = JSON.stringify(data.content);
    updateData.wordCount = getWordCountFromContent(data.content);
    updateData.charCount = getCharCountFromContent(data.content);
  }

  const document = await prisma.document.update({
    where: {
      id,
      userId,
    },
    data: updateData,
  });

  return transformPrismaDocumentToDocument(document);
}

export async function deleteDocument(id: string, userId: string): Promise<void> {
  await prisma.document.delete({
    where: {
      id,
      userId,
    },
  });
}

export async function createDocumentHistory(data: {
  documentId: string;
  version: number;
  content: JSONContent;
  changeType: 'user' | 'ai';
}): Promise<void> {
  await prisma.documentHistory.create({
    data: {
      documentId: data.documentId,
      version: data.version,
      content: JSON.stringify(data.content),
      changeType: data.changeType,
    },
  });
}

// Helper functions
function transformPrismaDocumentToDocument(document: any): Document {
  return {
    ...document,
    folderId: document.folderId || undefined,
    lastSyncAt: document.lastSyncAt || undefined,
    content: JSON.parse(document.content),
    metadata: {
      wordCount: document.wordCount,
      characterCount: document.charCount,
      tags: [],
      isPublic: document.isPublic,
      shareToken: document.shareToken || undefined,
    },
  };
}

function getWordCountFromContent(content: JSONContent): number {
  const text = extractTextFromContent(content);
  return text.trim().split(/\s+/).filter((word) => word.length > 0).length;
}

function getCharCountFromContent(content: JSONContent): number {
  const text = extractTextFromContent(content);
  return text.length;
}

function extractTextFromContent(content: JSONContent): string {
  if (!content) return '';

  let text = '';

  if (content.text) {
    text += content.text;
  }

  if (content.content && Array.isArray(content.content)) {
    for (const child of content.content) {
      text += extractTextFromContent(child);
    }
  }

  return text;
}
