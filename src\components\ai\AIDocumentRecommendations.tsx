/**
 * AI 文档推荐组件
 * 提供基于内容相似性的相关文档推荐功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Search, 
  Clock, 
  Sparkles, 
  ExternalLink,
  Loader2,
  RefreshCw,
  TrendingUp,
  Tag,
  Calendar
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import type { RelatedDocumentRecommendation } from '@/types/ai-classification.types';

interface AIDocumentRecommendationsProps {
  /** 当前文档ID（用于获取相关推荐） */
  currentDocumentId?: string;
  /** 推荐点击回调 */
  onRecommendationClick?: (documentId: string) => void;
  /** 最大推荐数量 */
  maxRecommendations?: number;
}

/**
 * AI 文档推荐组件
 */
export function AIDocumentRecommendations({
  currentDocumentId,
  onRecommendationClick,
  maxRecommendations = 10
}: AIDocumentRecommendationsProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [relatedDocuments, setRelatedDocuments] = useState<RelatedDocumentRecommendation[]>([]);
  const [recentDocuments, setRecentDocuments] = useState<RelatedDocumentRecommendation[]>([]);
  const [smartRecommendations, setSmartRecommendations] = useState<RelatedDocumentRecommendation[]>([]);
  const [topicQuery, setTopicQuery] = useState('');
  const [topicResults, setTopicResults] = useState<RelatedDocumentRecommendation[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // 自动加载推荐
  useEffect(() => {
    if (currentDocumentId) {
      loadRelatedDocuments();
    }
    loadRecentDocuments();
    loadSmartRecommendations();
  }, [currentDocumentId]);

  /**
   * 加载相关文档推荐
   */
  const loadRelatedDocuments = async () => {
    if (!currentDocumentId) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/ai/document-recommendations/related', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentId: currentDocumentId,
          limit: maxRecommendations
        })
      });

      if (response.ok) {
        const data = await response.json();
        setRelatedDocuments(data.data || []);
      } else {
        throw new Error('加载相关文档失败');
      }
    } catch (error) {
      console.error('加载相关文档失败:', error);
      toast({
        title: '加载失败',
        description: '无法加载相关文档推荐',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 加载最近相关文档
   */
  const loadRecentDocuments = async () => {
    try {
      const response = await fetch('/api/ai/document-recommendations/recent', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          days: 7,
          limit: maxRecommendations
        })
      });

      if (response.ok) {
        const data = await response.json();
        setRecentDocuments(data.data || []);
      }
    } catch (error) {
      console.error('加载最近文档失败:', error);
    }
  };

  /**
   * 加载智能推荐
   */
  const loadSmartRecommendations = async () => {
    try {
      const response = await fetch('/api/ai/document-recommendations/smart', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contextDocumentId: currentDocumentId,
          limit: maxRecommendations
        })
      });

      if (response.ok) {
        const data = await response.json();
        setSmartRecommendations(data.data || []);
      }
    } catch (error) {
      console.error('加载智能推荐失败:', error);
    }
  };

  /**
   * 按主题搜索文档
   */
  const searchByTopic = async () => {
    if (!topicQuery.trim()) return;

    setIsSearching(true);
    try {
      const response = await fetch(
        `/api/ai/document-recommendations/by-topic?topic=${encodeURIComponent(topicQuery)}&excludeDocumentId=${currentDocumentId || ''}&limit=${maxRecommendations}`
      );

      if (response.ok) {
        const data = await response.json();
        setTopicResults(data.data || []);
      } else {
        throw new Error('主题搜索失败');
      }
    } catch (error) {
      console.error('主题搜索失败:', error);
      toast({
        title: '搜索失败',
        description: '无法搜索相关主题文档',
        variant: 'destructive'
      });
    } finally {
      setIsSearching(false);
    }
  };

  /**
   * 获取关联类型图标
   */
  const getRelationTypeIcon = (type: string) => {
    switch (type) {
      case 'similar_content':
        return <FileText className="h-4 w-4" />;
      case 'same_topic':
        return <Tag className="h-4 w-4" />;
      case 'complementary':
        return <TrendingUp className="h-4 w-4" />;
      case 'reference':
        return <ExternalLink className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  /**
   * 获取关联类型文本
   */
  const getRelationTypeText = (type: string) => {
    switch (type) {
      case 'similar_content':
        return '相似内容';
      case 'same_topic':
        return '相同主题';
      case 'complementary':
        return '互补内容';
      case 'reference':
        return '参考资料';
      default:
        return '相关文档';
    }
  };

  /**
   * 获取相关性颜色
   */
  const getRelevanceColor = (score: number) => {
    if (score >= 0.8) return 'bg-green-100 text-green-800';
    if (score >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  };

  /**
   * 渲染推荐列表
   */
  const renderRecommendationList = (recommendations: RelatedDocumentRecommendation[], emptyMessage: string) => {
    if (recommendations.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>{emptyMessage}</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {recommendations.map((recommendation, index) => (
          <Card 
            key={`${recommendation.relatedDocumentId}-${index}`}
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => onRecommendationClick?.(recommendation.relatedDocumentId)}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {getRelationTypeIcon(recommendation.relationType)}
                    <h3 className="font-medium text-sm line-clamp-1">
                      {recommendation.relatedDocumentTitle}
                    </h3>
                    <Badge className={getRelevanceColor(recommendation.relevanceScore)}>
                      {Math.round(recommendation.relevanceScore * 100)}%
                    </Badge>
                  </div>
                  
                  <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                    {recommendation.description}
                  </p>
                  
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span className="flex items-center gap-1">
                      <Tag className="h-3 w-3" />
                      {getRelationTypeText(recommendation.relationType)}
                    </span>
                  </div>
                  
                  {recommendation.reason && (
                    <p className="text-xs text-gray-400 mt-1 italic">
                      {recommendation.reason}
                    </p>
                  )}
                </div>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    onRecommendationClick?.(recommendation.relatedDocumentId);
                  }}
                  className="ml-2"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* 头部操作 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            AI 文档推荐
          </CardTitle>
          <CardDescription>
            基于内容分析的智能文档推荐和发现
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button
              onClick={() => {
                loadRelatedDocuments();
                loadRecentDocuments();
                loadSmartRecommendations();
              }}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              刷新推荐
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 推荐内容 */}
      <Tabs defaultValue="related" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="related">相关文档</TabsTrigger>
          <TabsTrigger value="recent">最近相关</TabsTrigger>
          <TabsTrigger value="smart">智能推荐</TabsTrigger>
          <TabsTrigger value="topic">主题搜索</TabsTrigger>
        </TabsList>

        <TabsContent value="related" className="space-y-4">
          {renderRecommendationList(
            relatedDocuments,
            currentDocumentId ? '暂无相关文档推荐' : '请选择文档以获取相关推荐'
          )}
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          {renderRecommendationList(
            recentDocuments,
            '暂无最近相关的文档'
          )}
        </TabsContent>

        <TabsContent value="smart" className="space-y-4">
          {renderRecommendationList(
            smartRecommendations,
            '暂无智能推荐，AI正在学习您的使用习惯'
          )}
        </TabsContent>

        <TabsContent value="topic" className="space-y-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Input
                  placeholder="输入主题关键词..."
                  value={topicQuery}
                  onChange={(e) => setTopicQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && searchByTopic()}
                  className="flex-1"
                />
                <Button
                  onClick={searchByTopic}
                  disabled={isSearching || !topicQuery.trim()}
                  className="flex items-center gap-2"
                >
                  {isSearching ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                  搜索
                </Button>
              </div>
            </CardContent>
          </Card>
          
          {renderRecommendationList(
            topicResults,
            '输入主题关键词开始搜索相关文档'
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}