'use client';

import { Button } from '@/components/ui/Button';
import Link from 'next/link';

interface QuickActionsProps {
  onCreateDocument: () => void;
  onRefresh: () => void;
}

export function QuickActions({ onCreateDocument, onRefresh }: QuickActionsProps) {
  const actions = [
    {
      title: '创建新文档',
      description: '开始一个全新的文档',
      icon: '📝',
      action: onCreateDocument,
      primary: true
    },
    {
      title: '文档管理',
      description: '管理您的所有文档',
      icon: '📁',
      href: '/document-manager'
    },
    {
      title: '编辑器',
      description: '直接进入编辑器',
      icon: '✏️',
      href: '/editor'
    },
    {
      title: '刷新数据',
      description: '更新最新信息',
      icon: '🔄',
      action: onRefresh
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow p-4 sm:p-6 mb-6 sm:mb-8">
      <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">快速操作</h2>
      
      <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        {actions.map((action, index) => (
          <div key={index}>
            {action.href ? (
              <Link href={action.href}>
                <div className="p-3 sm:p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all cursor-pointer group touch-manipulation active:bg-gray-50">
                  <div className="text-xl sm:text-2xl mb-2">{action.icon}</div>
                  <h3 className="font-medium text-sm sm:text-base text-gray-900 group-hover:text-blue-600 transition-colors">
                    {action.title}
                  </h3>
                  <p className="text-xs sm:text-sm text-gray-500 mt-1 hidden sm:block">
                    {action.description}
                  </p>
                </div>
              </Link>
            ) : (
              <div
                onClick={action.action}
                className={`p-3 sm:p-4 border rounded-lg hover:shadow-sm transition-all cursor-pointer group touch-manipulation ${
                  action.primary 
                    ? 'border-blue-300 bg-blue-50 hover:bg-blue-100 active:bg-blue-200' 
                    : 'border-gray-200 hover:border-gray-300 active:bg-gray-50'
                }`}
              >
                <div className="text-xl sm:text-2xl mb-2">{action.icon}</div>
                <h3 className={`font-medium text-sm sm:text-base transition-colors ${
                  action.primary 
                    ? 'text-blue-900 group-hover:text-blue-700' 
                    : 'text-gray-900 group-hover:text-blue-600'
                }`}>
                  {action.title}
                </h3>
                <p className="text-xs sm:text-sm text-gray-500 mt-1 hidden sm:block">
                  {action.description}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}