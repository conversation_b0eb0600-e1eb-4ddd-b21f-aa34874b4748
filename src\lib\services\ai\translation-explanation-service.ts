/**
 * AI 翻译和解释服务
 * 专门处理文本翻译、概念解释和自定义指令执行功能
 */

import { IAIService } from './base-ai-service';
import { aiServiceManager } from './ai-service-factory';
import { AIRequest, AIResponse, AIServiceError, AIErrorType } from '@/types/ai.types';

/**
 * 支持的语言列表
 */
export const SUPPORTED_LANGUAGES = {
  'zh-CN': '简体中文',
  'zh-TW': '繁体中文',
  'en': '英语',
  'ja': '日语',
  'ko': '韩语',
  'fr': '法语',
  'de': '德语',
  'es': '西班牙语',
  'it': '意大利语',
  'pt': '葡萄牙语',
  'ru': '俄语',
  'ar': '阿拉伯语',
  'hi': '印地语',
  'th': '泰语',
  'vi': '越南语'
} as const;

export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES;

/**
 * 翻译质量等级
 */
export type TranslationQuality = 'fast' | 'balanced' | 'accurate';

/**
 * 解释类型
 */
export type ExplanationType = 
  | 'concept'     // 概念解释
  | 'term'        // 术语解释
  | 'process'     // 流程解释
  | 'example'     // 举例说明
  | 'comparison'  // 对比解释
  | 'simple';     // 简化解释

/**
 * 内容格式类型
 */
export type ContentFormat = 
  | 'paragraph'   // 段落
  | 'list'        // 列表
  | 'table'       // 表格
  | 'code'        // 代码
  | 'outline'     // 大纲
  | 'steps'       // 步骤
  | 'qa'          // 问答
  | 'summary';    // 摘要

/**
 * 创意写作类型
 */
export type CreativeWritingType = 
  | 'story'       // 故事
  | 'poem'        // 诗歌
  | 'dialogue'    // 对话
  | 'description' // 描述
  | 'metaphor'    // 比喻
  | 'scenario';   // 场景

/**
 * 翻译请求参数
 */
export interface TranslationRequest {
  /** 原始文本 */
  text: string;
  /** 源语言（可选，AI 自动检测） */
  sourceLanguage?: SupportedLanguage;
  /** 目标语言 */
  targetLanguage: SupportedLanguage;
  /** 翻译质量 */
  quality?: TranslationQuality;
  /** 上下文信息 */
  context?: string;
  /** 专业领域 */
  domain?: string;
  /** 特殊要求 */
  instructions?: string;
}

/**
 * 翻译结果
 */
export interface TranslationResult {
  /** 翻译后的文本 */
  translatedText: string;
  /** 检测到的源语言 */
  detectedSourceLanguage: SupportedLanguage;
  /** 目标语言 */
  targetLanguage: SupportedLanguage;
  /** 翻译质量评分（0-100） */
  qualityScore: number;
  /** 备选翻译 */
  alternatives?: string[];
  /** 翻译说明 */
  notes?: string[];
  /** 使用的令牌数 */
  tokensUsed: number;
  /** 响应时间 */
  responseTime: number;
  /** 生成的唯一ID */
  id: string;
}

/**
 * 解释请求参数
 */
export interface ExplanationRequest {
  /** 需要解释的文本 */
  text: string;
  /** 解释类型 */
  type: ExplanationType;
  /** 目标受众水平 */
  audienceLevel?: 'beginner' | 'intermediate' | 'advanced';
  /** 上下文信息 */
  context?: string;
  /** 专业领域 */
  domain?: string;
  /** 特殊要求 */
  instructions?: string;
}

/**
 * 解释结果
 */
export interface ExplanationResult {
  /** 解释内容 */
  explanation: string;
  /** 解释类型 */
  type: ExplanationType;
  /** 关键要点 */
  keyPoints: string[];
  /** 相关概念 */
  relatedConcepts?: string[];
  /** 示例 */
  examples?: string[];
  /** 参考资料建议 */
  references?: string[];
  /** 使用的令牌数 */
  tokensUsed: number;
  /** 响应时间 */
  responseTime: number;
  /** 生成的唯一ID */
  id: string;
}

/**
 * 自定义指令请求参数
 */
export interface CustomInstructionRequest {
  /** 用户指令 */
  instruction: string;
  /** 输入文本（可选） */
  inputText?: string;
  /** 期望的输出格式 */
  outputFormat?: ContentFormat;
  /** 上下文信息 */
  context?: string;
  /** 特殊要求 */
  requirements?: string[];
}

/**
 * 自定义指令结果
 */
export interface CustomInstructionResult {
  /** 生成的内容 */
  content: string;
  /** 输出格式 */
  format: ContentFormat;
  /** 执行的指令 */
  instruction: string;
  /** 处理说明 */
  processingNotes?: string[];
  /** 使用的令牌数 */
  tokensUsed: number;
  /** 响应时间 */
  responseTime: number;
  /** 生成的唯一ID */
  id: string;
}

/**
 * 创意写作请求参数
 */
export interface CreativeWritingRequest {
  /** 主题或提示 */
  prompt: string;
  /** 写作类型 */
  type: CreativeWritingType;
  /** 目标长度 */
  length?: 'short' | 'medium' | 'long';
  /** 风格要求 */
  style?: string;
  /** 情感色调 */
  tone?: string;
  /** 特殊要求 */
  requirements?: string[];
}

/**
 * 创意写作结果
 */
export interface CreativeWritingResult {
  /** 创作内容 */
  content: string;
  /** 写作类型 */
  type: CreativeWritingType;
  /** 创作主题 */
  theme: string;
  /** 风格特点 */
  styleFeatures: string[];
  /** 创作说明 */
  creationNotes?: string[];
  /** 使用的令牌数 */
  tokensUsed: number;
  /** 响应时间 */
  responseTime: number;
  /** 生成的唯一ID */
  id: string;
}

/**
 * AI 翻译和解释服务类
 */
export class TranslationExplanationService {
  private aiService: IAIService;
  
  constructor(aiService?: IAIService) {
    this.aiService = aiService || aiServiceManager.getDefaultService();
  }
  
  /**
   * 翻译文本
   * @param request 翻译请求参数
   * @returns 翻译结果
   */
  async translateText(request: TranslationRequest): Promise<TranslationResult> {
    try {
      const prompt = this.buildTranslationPrompt(request);
      const aiRequest: AIRequest = {
        prompt,
        context: request.context,
        maxTokens: this.getMaxTokensForTranslation(request.text.length),
        temperature: this.getTemperatureForQuality(request.quality)
      };
      
      const startTime = Date.now();
      const response = await this.aiService.generateText(aiRequest);
      const responseTime = Date.now() - startTime;
      
      // 解析翻译结果
      const parsedResult = this.parseTranslationResponse(response.content, request);
      
      return {
        ...parsedResult,
        targetLanguage: request.targetLanguage,
        tokensUsed: response.tokensUsed,
        responseTime,
        id: this.generateId('translation')
      };
    } catch (error) {
      if (error instanceof AIServiceError) {
        throw error;
      }
      throw new AIServiceError(
        AIErrorType.UNKNOWN_ERROR,
        `翻译失败: ${error instanceof Error ? error.message : '未知错误'}`,
        this.aiService.provider,
        error instanceof Error ? error : undefined
      );
    }
  }
  
  /**
   * 解释文本或概念
   * @param request 解释请求参数
   * @returns 解释结果
   */
  async explainText(request: ExplanationRequest): Promise<ExplanationResult> {
    try {
      const prompt = this.buildExplanationPrompt(request);
      const aiRequest: AIRequest = {
        prompt,
        context: request.context,
        maxTokens: this.getMaxTokensForExplanation(request.type),
        temperature: 0.7
      };
      
      const startTime = Date.now();
      const response = await this.aiService.generateText(aiRequest);
      const responseTime = Date.now() - startTime;
      
      // 解析解释结果
      const parsedResult = this.parseExplanationResponse(response.content, request);
      
      return {
        ...parsedResult,
        type: request.type,
        tokensUsed: response.tokensUsed,
        responseTime,
        id: this.generateId('explanation')
      };
    } catch (error) {
      if (error instanceof AIServiceError) {
        throw error;
      }
      throw new AIServiceError(
        AIErrorType.UNKNOWN_ERROR,
        `解释失败: ${error instanceof Error ? error.message : '未知错误'}`,
        this.aiService.provider,
        error instanceof Error ? error : undefined
      );
    }
  }
  
  /**
   * 执行自定义指令
   * @param request 自定义指令请求参数
   * @returns 执行结果
   */
  async executeCustomInstruction(request: CustomInstructionRequest): Promise<CustomInstructionResult> {
    try {
      const prompt = this.buildCustomInstructionPrompt(request);
      const aiRequest: AIRequest = {
        prompt,
        context: request.context,
        maxTokens: this.getMaxTokensForInstruction(request.outputFormat),
        temperature: 0.8
      };
      
      const startTime = Date.now();
      const response = await this.aiService.generateText(aiRequest);
      const responseTime = Date.now() - startTime;
      
      // 解析执行结果
      const parsedResult = this.parseCustomInstructionResponse(response.content, request);
      
      return {
        ...parsedResult,
        instruction: request.instruction,
        format: request.outputFormat || 'paragraph',
        tokensUsed: response.tokensUsed,
        responseTime,
        id: this.generateId('instruction')
      };
    } catch (error) {
      if (error instanceof AIServiceError) {
        throw error;
      }
      throw new AIServiceError(
        AIErrorType.UNKNOWN_ERROR,
        `指令执行失败: ${error instanceof Error ? error.message : '未知错误'}`,
        this.aiService.provider,
        error instanceof Error ? error : undefined
      );
    }
  }
  
  /**
   * 创意写作
   * @param request 创意写作请求参数
   * @returns 创作结果
   */
  async createContent(request: CreativeWritingRequest): Promise<CreativeWritingResult> {
    try {
      const prompt = this.buildCreativeWritingPrompt(request);
      const aiRequest: AIRequest = {
        prompt,
        maxTokens: this.getMaxTokensForCreativeWriting(request.length),
        temperature: 0.9
      };
      
      const startTime = Date.now();
      const response = await this.aiService.generateText(aiRequest);
      const responseTime = Date.now() - startTime;
      
      // 解析创作结果
      const parsedResult = this.parseCreativeWritingResponse(response.content, request);
      
      return {
        ...parsedResult,
        type: request.type,
        theme: request.prompt,
        tokensUsed: response.tokensUsed,
        responseTime,
        id: this.generateId('creative')
      };
    } catch (error) {
      if (error instanceof AIServiceError) {
        throw error;
      }
      throw new AIServiceError(
        AIErrorType.UNKNOWN_ERROR,
        `创意写作失败: ${error instanceof Error ? error.message : '未知错误'}`,
        this.aiService.provider,
        error instanceof Error ? error : undefined
      );
    }
  }
  
  /**
   * 构建翻译提示
   */
  private buildTranslationPrompt(request: TranslationRequest): string {
    const { text, sourceLanguage, targetLanguage, quality, domain, instructions } = request;
    
    const sourceLangName = sourceLanguage ? SUPPORTED_LANGUAGES[sourceLanguage] : '自动检测';
    const targetLangName = SUPPORTED_LANGUAGES[targetLanguage];
    
    let prompt = `请将以下文本翻译为${targetLangName}：\n\n${text}\n\n要求：\n`;
    prompt += `- 源语言：${sourceLangName}\n`;
    prompt += `- 目标语言：${targetLangName}\n`;
    
    switch (quality) {
      case 'fast':
        prompt += '- 翻译质量：快速翻译，保持基本准确性\n';
        break;
      case 'accurate':
        prompt += '- 翻译质量：高精度翻译，注重准确性和地道性\n';
        break;
      default:
        prompt += '- 翻译质量：平衡速度和准确性\n';
    }
    
    if (domain) {
      prompt += `- 专业领域：${domain}\n`;
    }
    
    prompt += '- 保持原文的语调和风格\n';
    prompt += '- 确保翻译自然流畅\n';
    
    if (instructions) {
      prompt += `- 特殊要求：${instructions}\n`;
    }
    
    prompt += '\n请按以下格式输出：\n';
    prompt += '翻译结果：[翻译后的文本]\n';
    prompt += '检测语言：[检测到的源语言代码]\n';
    prompt += '质量评分：[0-100的评分]\n';
    prompt += '备选翻译：[如果有其他翻译选项]\n';
    prompt += '翻译说明：[翻译过程中的注意事项]';
    
    return prompt;
  }
  
  /**
   * 构建解释提示
   */
  private buildExplanationPrompt(request: ExplanationRequest): string {
    const { text, type, audienceLevel, domain, instructions } = request;
    
    let prompt = '';
    
    switch (type) {
      case 'concept':
        prompt = `请解释以下概念：\n\n${text}\n\n`;
        break;
      case 'term':
        prompt = `请解释以下术语的含义：\n\n${text}\n\n`;
        break;
      case 'process':
        prompt = `请解释以下流程或过程：\n\n${text}\n\n`;
        break;
      case 'example':
        prompt = `请通过具体例子来解释：\n\n${text}\n\n`;
        break;
      case 'comparison':
        prompt = `请通过对比的方式来解释：\n\n${text}\n\n`;
        break;
      case 'simple':
        prompt = `请用简单易懂的方式解释：\n\n${text}\n\n`;
        break;
    }
    
    prompt += '要求：\n';
    
    if (audienceLevel) {
      const levelMap = {
        beginner: '初学者水平，使用简单易懂的语言',
        intermediate: '中等水平，可以使用一些专业术语',
        advanced: '高级水平，可以使用专业术语和深入分析'
      };
      prompt += `- 目标受众：${levelMap[audienceLevel]}\n`;
    }
    
    if (domain) {
      prompt += `- 专业领域：${domain}\n`;
    }
    
    prompt += '- 解释要清晰、准确、易懂\n';
    prompt += '- 提供具体的例子或类比\n';
    prompt += '- 突出关键要点\n';
    
    if (instructions) {
      prompt += `- 特殊要求：${instructions}\n`;
    }
    
    prompt += '\n请按以下格式输出：\n';
    prompt += '解释内容：[详细解释]\n';
    prompt += '关键要点：[要点1, 要点2, ...]\n';
    prompt += '相关概念：[相关概念1, 相关概念2, ...]\n';
    prompt += '举例说明：[例子1, 例子2, ...]\n';
    prompt += '参考建议：[进一步学习的建议]';
    
    return prompt;
  }
  
  /**
   * 构建自定义指令提示
   */
  private buildCustomInstructionPrompt(request: CustomInstructionRequest): string {
    const { instruction, inputText, outputFormat, requirements } = request;
    
    let prompt = `请按照以下指令执行任务：\n\n${instruction}\n\n`;
    
    if (inputText) {
      prompt += `输入内容：\n${inputText}\n\n`;
    }
    
    prompt += '要求：\n';
    
    if (outputFormat) {
      const formatMap = {
        paragraph: '段落形式',
        list: '列表形式',
        table: '表格形式',
        code: '代码形式',
        outline: '大纲形式',
        steps: '步骤形式',
        qa: '问答形式',
        summary: '摘要形式'
      };
      prompt += `- 输出格式：${formatMap[outputFormat]}\n`;
    }
    
    if (requirements && requirements.length > 0) {
      prompt += '- 特殊要求：\n';
      requirements.forEach((req, index) => {
        prompt += `  ${index + 1}. ${req}\n`;
      });
    }
    
    prompt += '- 确保输出内容准确、完整\n';
    prompt += '- 保持逻辑清晰、结构合理\n';
    
    prompt += '\n请按以下格式输出：\n';
    prompt += '执行结果：[按要求生成的内容]\n';
    prompt += '处理说明：[执行过程中的说明]';
    
    return prompt;
  }
  
  /**
   * 构建创意写作提示
   */
  private buildCreativeWritingPrompt(request: CreativeWritingRequest): string {
    const { prompt: userPrompt, type, length, style, tone, requirements } = request;
    
    const typeMap = {
      story: '故事',
      poem: '诗歌',
      dialogue: '对话',
      description: '描述',
      metaphor: '比喻',
      scenario: '场景'
    };
    
    let prompt = `请创作一个${typeMap[type]}，主题是：${userPrompt}\n\n要求：\n`;
    
    if (length) {
      const lengthMap = {
        short: '简短（100-200字）',
        medium: '中等（300-500字）',
        long: '较长（600-1000字）'
      };
      prompt += `- 长度：${lengthMap[length]}\n`;
    }
    
    if (style) {
      prompt += `- 风格：${style}\n`;
    }
    
    if (tone) {
      prompt += `- 语调：${tone}\n`;
    }
    
    prompt += '- 内容要有创意和想象力\n';
    prompt += '- 语言要生动、形象\n';
    prompt += '- 结构要完整、合理\n';
    
    if (requirements && requirements.length > 0) {
      prompt += '- 特殊要求：\n';
      requirements.forEach((req, index) => {
        prompt += `  ${index + 1}. ${req}\n`;
      });
    }
    
    prompt += '\n请按以下格式输出：\n';
    prompt += '创作内容：[创作的内容]\n';
    prompt += '风格特点：[风格特点1, 风格特点2, ...]\n';
    prompt += '创作说明：[创作思路和特色说明]';
    
    return prompt;
  }
  
  /**
   * 解析翻译响应
   */
  private parseTranslationResponse(content: string, request: TranslationRequest): Omit<TranslationResult, 'targetLanguage' | 'tokensUsed' | 'responseTime' | 'id'> {
    const translationMatch = content.match(/翻译结果：\s*(.+?)(?=\n|$)/);
    const languageMatch = content.match(/检测语言：\s*(.+?)(?=\n|$)/);
    const qualityMatch = content.match(/质量评分：\s*(\d+)/);
    const alternativesMatch = content.match(/备选翻译：\s*(.+?)(?=\n翻译说明：|$)/);
    const notesMatch = content.match(/翻译说明：\s*(.+?)$/);
    
    const translatedText = translationMatch ? translationMatch[1].trim() : content.trim();
    const detectedLanguage = languageMatch ? languageMatch[1].trim() : request.sourceLanguage || 'zh-CN';
    const qualityScore = qualityMatch ? parseInt(qualityMatch[1]) : 85;
    
    const alternatives = alternativesMatch ? 
      alternativesMatch[1].split(/[,，]/).map(alt => alt.trim()).filter(alt => alt.length > 0) : [];
    
    const notes = notesMatch ? 
      notesMatch[1].split(/[,，。]/).map(note => note.trim()).filter(note => note.length > 0) : [];
    
    return {
      translatedText,
      detectedSourceLanguage: detectedLanguage as SupportedLanguage,
      qualityScore,
      alternatives: alternatives.length > 0 ? alternatives : undefined,
      notes: notes.length > 0 ? notes : undefined
    };
  }
  
  /**
   * 解析解释响应
   */
  private parseExplanationResponse(content: string, request: ExplanationRequest): Omit<ExplanationResult, 'type' | 'tokensUsed' | 'responseTime' | 'id'> {
    const explanationMatch = content.match(/解释内容：\s*(.+?)(?=\n关键要点：|$)/);
    const keyPointsMatch = content.match(/关键要点：\s*(.+?)(?=\n相关概念：|$)/);
    const conceptsMatch = content.match(/相关概念：\s*(.+?)(?=\n举例说明：|$)/);
    const examplesMatch = content.match(/举例说明：\s*(.+?)(?=\n参考建议：|$)/);
    const referencesMatch = content.match(/参考建议：\s*(.+?)$/);
    
    const explanation = explanationMatch ? explanationMatch[1].trim() : content.trim();
    
    const parseList = (text: string | undefined): string[] => {
      if (!text) return [];
      return text.split(/[,，、]/).map(item => item.trim()).filter(item => item.length > 0);
    };
    
    return {
      explanation,
      keyPoints: parseList(keyPointsMatch?.[1]),
      relatedConcepts: parseList(conceptsMatch?.[1]),
      examples: parseList(examplesMatch?.[1]),
      references: parseList(referencesMatch?.[1])
    };
  }
  
  /**
   * 解析自定义指令响应
   */
  private parseCustomInstructionResponse(content: string, request: CustomInstructionRequest): Omit<CustomInstructionResult, 'instruction' | 'format' | 'tokensUsed' | 'responseTime' | 'id'> {
    const resultMatch = content.match(/执行结果：\s*(.+?)(?=\n处理说明：|$)/);
    const notesMatch = content.match(/处理说明：\s*(.+?)$/);
    
    const resultContent = resultMatch ? resultMatch[1].trim() : content.trim();
    const processingNotes = notesMatch ? 
      notesMatch[1].split(/[,，。]/).map(note => note.trim()).filter(note => note.length > 0) : [];
    
    return {
      content: resultContent,
      processingNotes: processingNotes.length > 0 ? processingNotes : undefined
    };
  }
  
  /**
   * 解析创意写作响应
   */
  private parseCreativeWritingResponse(content: string, request: CreativeWritingRequest): Omit<CreativeWritingResult, 'type' | 'theme' | 'tokensUsed' | 'responseTime' | 'id'> {
    const contentMatch = content.match(/创作内容：\s*(.+?)(?=\n风格特点：|$)/);
    const featuresMatch = content.match(/风格特点：\s*(.+?)(?=\n创作说明：|$)/);
    const notesMatch = content.match(/创作说明：\s*(.+?)$/);
    
    const creativeContent = contentMatch ? contentMatch[1].trim() : content.trim();
    
    const parseList = (text: string | undefined): string[] => {
      if (!text) return [];
      return text.split(/[,，、]/).map(item => item.trim()).filter(item => item.length > 0);
    };
    
    const styleFeatures = parseList(featuresMatch?.[1]);
    const creationNotes = parseList(notesMatch?.[1]);
    
    return {
      content: creativeContent,
      styleFeatures: styleFeatures.length > 0 ? styleFeatures : ['创意', '生动'],
      creationNotes: creationNotes.length > 0 ? creationNotes : undefined
    };
  }
  
  /**
   * 根据文本长度获取翻译的最大令牌数
   */
  private getMaxTokensForTranslation(textLength: number): number {
    return Math.min(Math.max(textLength * 2, 200), 2000);
  }
  
  /**
   * 根据解释类型获取最大令牌数
   */
  private getMaxTokensForExplanation(type: ExplanationType): number {
    const tokenMap = {
      concept: 800,
      term: 600,
      process: 1000,
      example: 900,
      comparison: 1200,
      simple: 700
    };
    return tokenMap[type] || 800;
  }
  
  /**
   * 根据输出格式获取指令执行的最大令牌数
   */
  private getMaxTokensForInstruction(format?: ContentFormat): number {
    const tokenMap = {
      paragraph: 800,
      list: 600,
      table: 1000,
      code: 1200,
      outline: 800,
      steps: 900,
      qa: 1000,
      summary: 600
    };
    return tokenMap[format || 'paragraph'];
  }
  
  /**
   * 根据长度获取创意写作的最大令牌数
   */
  private getMaxTokensForCreativeWriting(length?: string): number {
    const tokenMap = {
      short: 400,
      medium: 800,
      long: 1500
    };
    return tokenMap[length as keyof typeof tokenMap] || 800;
  }
  
  /**
   * 根据翻译质量获取温度参数
   */
  private getTemperatureForQuality(quality?: TranslationQuality): number {
    const tempMap = {
      fast: 0.8,
      balanced: 0.5,
      accurate: 0.3
    };
    return tempMap[quality || 'balanced'];
  }
  
  /**
   * 生成唯一ID
   */
  private generateId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 创建翻译和解释服务实例
 */
export function createTranslationExplanationService(aiService?: IAIService): TranslationExplanationService {
  return new TranslationExplanationService(aiService);
}