/**
 * AI 文本改写服务
 * 专门处理文本改写和优化功能
 */

import { IAIService } from './base-ai-service';
import { aiServiceManager } from './ai-service-factory';
import { AIRequest, AIResponse, AIServiceError, AIErrorType, RewriteOptions } from '@/types/ai.types';

/**
 * 改写风格选项
 */
export type RewriteStyle = 
  | 'formal'      // 正式
  | 'informal'    // 非正式
  | 'academic'    // 学术
  | 'creative'    // 创意
  | 'concise'     // 简洁
  | 'detailed'    // 详细
  | 'professional'// 专业
  | 'casual';     // 随意

/**
 * 改写类型
 */
export type RewriteType = 
  | 'improve'     // 改进优化
  | 'grammar'     // 语法检查
  | 'style'       // 风格调整
  | 'simplify'    // 简化
  | 'expand'      // 扩展
  | 'tone';       // 语调调整

/**
 * 文本改写请求参数
 */
export interface TextRewriteRequest {
  /** 原始文本 */
  originalText: string;
  /** 改写类型 */
  type: RewriteType;
  /** 改写风格 */
  style?: RewriteStyle;
  /** 目标语调 */
  tone?: 'professional' | 'friendly' | 'neutral' | 'persuasive' | 'formal' | 'casual';
  /** 目标长度 */
  length?: 'shorter' | 'longer' | 'same';
  /** 上下文信息 */
  context?: string;
  /** 特殊要求 */
  instructions?: string;
  /** 生成多个版本 */
  generateMultiple?: boolean;
}

/**
 * 文本改写结果
 */
export interface TextRewriteResult {
  /** 改写后的文本 */
  rewrittenText: string;
  /** 多个改写版本 */
  alternatives: string[];
  /** 改写类型 */
  type: RewriteType;
  /** 使用的风格 */
  style?: RewriteStyle;
  /** 原始文本 */
  originalText: string;
  /** 改写说明 */
  explanation?: string;
  /** 主要改动点 */
  changes: TextChange[];
  /** 使用的令牌数 */
  tokensUsed: number;
  /** 响应时间 */
  responseTime: number;
  /** 生成的唯一ID */
  id: string;
}

/**
 * 文本改动信息
 */
export interface TextChange {
  /** 改动类型 */
  type: 'grammar' | 'style' | 'structure' | 'vocabulary' | 'tone';
  /** 改动描述 */
  description: string;
  /** 原始片段 */
  original?: string;
  /** 修改后片段 */
  modified?: string;
}

/**
 * AI 文本改写服务类
 */
export class TextRewriteService {
  private aiService: IAIService;
  
  constructor(aiService?: IAIService) {
    this.aiService = aiService || aiServiceManager.getDefaultService();
  }
  
  /**
   * 改写文本
   * @param request 改写请求参数
   * @returns 改写结果
   */
  async rewriteText(request: TextRewriteRequest): Promise<TextRewriteResult> {
    try {
      const prompt = this.buildRewritePrompt(request);
      const aiRequest: AIRequest = {
        prompt,
        context: request.context,
        maxTokens: this.getMaxTokensForRequest(request),
        temperature: this.getTemperatureForType(request.type)
      };
      
      const startTime = Date.now();
      const response = await this.aiService.generateText(aiRequest);
      const responseTime = Date.now() - startTime;
      
      // 解析改写结果
      const parsedResult = this.parseRewriteResponse(response.content, request);
      
      return {
        ...parsedResult,
        originalText: request.originalText,
        type: request.type,
        style: request.style,
        tokensUsed: response.tokensUsed,
        responseTime,
        id: this.generateId()
      };
    } catch (error) {
      if (error instanceof AIServiceError) {
        throw error;
      }
      throw new AIServiceError(
        AIErrorType.UNKNOWN_ERROR,
        `文本改写失败: ${error instanceof Error ? error.message : '未知错误'}`,
        this.aiService.provider,
        error instanceof Error ? error : undefined
      );
    }
  }
  
  /**
   * 批量改写（生成多个版本）
   * @param request 改写请求参数
   * @param count 生成版本数量
   * @returns 多个改写结果
   */
  async rewriteMultiple(
    request: TextRewriteRequest, 
    count: number = 3
  ): Promise<TextRewriteResult[]> {
    const modifiedRequest = { ...request, generateMultiple: true };
    const promises = Array.from({ length: count }, () => this.rewriteText(modifiedRequest));
    const results = await Promise.allSettled(promises);
    
    return results
      .filter((result): result is PromiseFulfilledResult<TextRewriteResult> => 
        result.status === 'fulfilled'
      )
      .map(result => result.value);
  }
  
  /**
   * 语法检查和修正
   * @param text 原始文本
   * @param context 上下文
   * @returns 语法修正结果
   */
  async checkGrammar(text: string, context?: string): Promise<TextRewriteResult> {
    return this.rewriteText({
      originalText: text,
      type: 'grammar',
      context,
      generateMultiple: false
    });
  }
  
  /**
   * 风格调整
   * @param text 原始文本
   * @param targetStyle 目标风格
   * @param context 上下文
   * @returns 风格调整结果
   */
  async adjustStyle(
    text: string, 
    targetStyle: RewriteStyle, 
    context?: string
  ): Promise<TextRewriteResult> {
    return this.rewriteText({
      originalText: text,
      type: 'style',
      style: targetStyle,
      context,
      generateMultiple: true
    });
  }
  
  /**
   * 文本简化
   * @param text 原始文本
   * @param context 上下文
   * @returns 简化结果
   */
  async simplifyText(text: string, context?: string): Promise<TextRewriteResult> {
    return this.rewriteText({
      originalText: text,
      type: 'simplify',
      length: 'shorter',
      context
    });
  }
  
  /**
   * 文本扩展
   * @param text 原始文本
   * @param context 上下文
   * @returns 扩展结果
   */
  async expandText(text: string, context?: string): Promise<TextRewriteResult> {
    return this.rewriteText({
      originalText: text,
      type: 'expand',
      length: 'longer',
      context
    });
  }
  
  /**
   * 构建改写提示
   */
  private buildRewritePrompt(request: TextRewriteRequest): string {
    const { originalText, type, style, tone, length, instructions, generateMultiple } = request;
    
    let prompt = '';
    
    switch (type) {
      case 'improve':
        prompt = this.buildImprovePrompt(originalText, style, tone, length);
        break;
      case 'grammar':
        prompt = this.buildGrammarPrompt(originalText);
        break;
      case 'style':
        prompt = this.buildStylePrompt(originalText, style, tone);
        break;
      case 'simplify':
        prompt = this.buildSimplifyPrompt(originalText);
        break;
      case 'expand':
        prompt = this.buildExpandPrompt(originalText);
        break;
      case 'tone':
        prompt = this.buildTonePrompt(originalText, tone);
        break;
    }
    
    // 添加特殊要求
    if (instructions) {
      prompt += `\n\n特殊要求：${instructions}`;
    }
    
    // 添加多版本生成要求
    if (generateMultiple) {
      prompt += '\n\n请提供2-3个不同的改写版本，用"---版本分隔---"分隔。';
    }
    
    // 添加输出格式要求
    prompt += '\n\n请按以下格式输出：';
    prompt += '\n改写结果：[改写后的文本]';
    prompt += '\n改写说明：[简要说明主要改动]';
    prompt += '\n主要改动：[列出具体改动点]';
    
    return prompt;
  }
  
  /**
   * 构建改进提示
   */
  private buildImprovePrompt(
    text: string, 
    style?: RewriteStyle, 
    tone?: string, 
    length?: string
  ): string {
    let prompt = `请改进以下文本，使其更加清晰、流畅和有效：\n\n${text}\n\n要求：`;
    prompt += '\n- 保持原意不变';
    prompt += '\n- 提高可读性和表达效果';
    prompt += '\n- 修正语法和用词问题';
    
    if (style) {
      prompt += `\n- 风格：${this.getStyleDescription(style)}`;
    }
    
    if (tone) {
      prompt += `\n- 语调：${this.getToneDescription(tone)}`;
    }
    
    if (length) {
      prompt += `\n- 长度：${this.getLengthDescription(length)}`;
    }
    
    return prompt;
  }
  
  /**
   * 构建语法检查提示
   */
  private buildGrammarPrompt(text: string): string {
    return `请检查并修正以下文本中的语法错误、标点符号问题和用词不当：\n\n${text}\n\n要求：
- 修正所有语法错误
- 改正标点符号使用
- 优化用词选择
- 保持原意不变
- 提高文本的准确性和专业性`;
  }
  
  /**
   * 构建风格调整提示
   */
  private buildStylePrompt(text: string, style?: RewriteStyle, tone?: string): string {
    let prompt = `请调整以下文本的写作风格：\n\n${text}\n\n要求：`;
    
    if (style) {
      prompt += `\n- 目标风格：${this.getStyleDescription(style)}`;
    }
    
    if (tone) {
      prompt += `\n- 目标语调：${this.getToneDescription(tone)}`;
    }
    
    prompt += '\n- 保持核心内容不变';
    prompt += '\n- 调整表达方式和用词';
    prompt += '\n- 确保风格一致性';
    
    return prompt;
  }
  
  /**
   * 构建简化提示
   */
  private buildSimplifyPrompt(text: string): string {
    return `请简化以下文本，使其更加简洁明了：\n\n${text}\n\n要求：
- 去除冗余内容
- 使用简单直接的表达
- 保留核心信息
- 提高可读性
- 适合更广泛的读者群体`;
  }
  
  /**
   * 构建扩展提示
   */
  private buildExpandPrompt(text: string): string {
    return `请扩展以下文本，使其更加详细和丰富：\n\n${text}\n\n要求：
- 添加更多细节和说明
- 提供具体例子或论证
- 使内容更加完整
- 保持逻辑清晰
- 增强说服力和可信度`;
  }
  
  /**
   * 构建语调调整提示
   */
  private buildTonePrompt(text: string, tone?: string): string {
    let prompt = `请调整以下文本的语调：\n\n${text}\n\n要求：`;
    
    if (tone) {
      prompt += `\n- 目标语调：${this.getToneDescription(tone)}`;
    }
    
    prompt += '\n- 保持内容不变';
    prompt += '\n- 调整表达方式';
    prompt += '\n- 确保语调一致';
    
    return prompt;
  }
  
  /**
   * 解析改写响应
   */
  private parseRewriteResponse(content: string, request: TextRewriteRequest): Omit<TextRewriteResult, 'originalText' | 'type' | 'style' | 'tokensUsed' | 'responseTime' | 'id'> {
    // 分割多个版本
    const versions = content.split('---版本分隔---').map(v => v.trim()).filter(v => v.length > 0);
    
    let mainResult = versions[0] || content;
    const alternatives = versions.slice(1);
    
    // 解析主结果
    const rewrittenText = this.extractRewrittenText(mainResult);
    const explanation = this.extractExplanation(mainResult);
    const changes = this.extractChanges(mainResult, request.type);
    
    return {
      rewrittenText,
      alternatives: alternatives.map(alt => this.extractRewrittenText(alt)),
      explanation,
      changes
    };
  }
  
  /**
   * 提取改写后的文本
   */
  private extractRewrittenText(content: string): string {
    const match = content.match(/改写结果：\s*([\s\S]+?)(?=\n改写说明：|\n主要改动：|$)/);
    if (match) {
      return match[1].trim();
    }
    
    // 如果没有找到标记，返回整个内容的第一段
    const lines = content.split('\n').filter(line => line.trim());
    return lines[0] || content.trim();
  }
  
  /**
   * 提取改写说明
   */
  private extractExplanation(content: string): string {
    const match = content.match(/改写说明：\s*([\s\S]+?)(?=\n主要改动：|$)/);
    return match ? match[1].trim() : '';
  }
  
  /**
   * 提取改动信息
   */
  private extractChanges(content: string, type: RewriteType): TextChange[] {
    const match = content.match(/主要改动：\s*([\s\S]+?)$/);
    if (!match) return [];
    
    const changesText = match[1].trim();
    const changeLines = changesText.split('\n').filter(line => line.trim());
    
    return changeLines.map(line => ({
      type: this.inferChangeType(line, type),
      description: line.replace(/^[-•]\s*/, '').trim()
    }));
  }
  
  /**
   * 推断改动类型
   */
  private inferChangeType(changeDescription: string, requestType: RewriteType): TextChange['type'] {
    const desc = changeDescription.toLowerCase();
    
    if (desc.includes('语法') || desc.includes('grammar')) return 'grammar';
    if (desc.includes('风格') || desc.includes('style')) return 'style';
    if (desc.includes('结构') || desc.includes('structure')) return 'structure';
    if (desc.includes('用词') || desc.includes('vocabulary')) return 'vocabulary';
    if (desc.includes('语调') || desc.includes('tone')) return 'tone';
    
    // 根据请求类型推断
    switch (requestType) {
      case 'grammar': return 'grammar';
      case 'style': return 'style';
      case 'tone': return 'tone';
      default: return 'style';
    }
  }
  
  /**
   * 获取风格描述
   */
  private getStyleDescription(style: RewriteStyle): string {
    const descriptions = {
      formal: '正式、严谨',
      informal: '非正式、轻松',
      academic: '学术、专业',
      creative: '创意、生动',
      concise: '简洁、精炼',
      detailed: '详细、全面',
      professional: '专业、商务',
      casual: '随意、口语化'
    };
    return descriptions[style] || style;
  }
  
  /**
   * 获取语调描述
   */
  private getToneDescription(tone: string): string {
    const descriptions = {
      professional: '专业、正式',
      friendly: '友好、亲切',
      neutral: '中性、客观',
      persuasive: '说服性、有力',
      formal: '正式、庄重',
      casual: '随意、轻松'
    };
    return descriptions[tone as keyof typeof descriptions] || tone;
  }
  
  /**
   * 获取长度描述
   */
  private getLengthDescription(length: string): string {
    const descriptions = {
      shorter: '更简洁',
      longer: '更详细',
      same: '保持相似长度'
    };
    return descriptions[length as keyof typeof descriptions] || length;
  }
  
  /**
   * 根据请求类型获取最大令牌数
   */
  private getMaxTokensForRequest(request: TextRewriteRequest): number {
    const baseTokens = Math.max(request.originalText.length * 2, 200);
    
    switch (request.type) {
      case 'expand':
        return Math.min(baseTokens * 2, 1500);
      case 'simplify':
        return Math.min(baseTokens * 0.8, 800);
      case 'grammar':
        return Math.min(baseTokens * 1.2, 1000);
      default:
        return Math.min(baseTokens * 1.5, 1200);
    }
  }
  
  /**
   * 根据改写类型获取温度参数
   */
  private getTemperatureForType(type: RewriteType): number {
    switch (type) {
      case 'style':
        return 0.9;
      case 'grammar':
        return 0.3;
      case 'style':
        return 0.7;
      case 'improve':
        return 0.6;
      default:
        return 0.7;
    }
  }
  
  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `rewrite_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 创建文本改写服务实例
 */
export function createTextRewriteService(aiService?: IAIService): TextRewriteService {
  return new TextRewriteService(aiService);
}