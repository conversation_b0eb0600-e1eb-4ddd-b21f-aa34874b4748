/**
 * AI 文件夹建议 API 路由
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { aiFolderOrganizer } from '@/lib/services/ai-folder-organizer';
import { z } from 'zod';

// 请求参数验证模式
const applySuggestionSchema = z.object({
  suggestionId: z.string().min(1, '建议ID不能为空')
});

/**
 * GET /api/ai/folder-suggestions
 * 获取文件夹结构建议
 */
export async function GET() {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 分析文件夹结构并获取建议
    const suggestions = await aiFolderOrganizer.analyzeFolderStructure(session.user.id);

    return NextResponse.json({
      success: true,
      data: suggestions
    });

  } catch (error) {
    console.error('获取文件夹建议失败:', error);
    
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '获取建议失败' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/ai/folder-suggestions/smart
 * 获取智能整理建议
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取智能整理建议
    const suggestions = await aiFolderOrganizer.generateSmartOrganizationSuggestions(
      session.user.id
    );

    return NextResponse.json({
      success: true,
      data: suggestions
    });

  } catch (error) {
    console.error('获取智能整理建议失败:', error);
    
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '获取建议失败' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/ai/folder-suggestions/apply
 * 应用整理建议
 */
export async function PUT(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { suggestionId } = applySuggestionSchema.parse(body);

    // 应用建议
    const result = await aiFolderOrganizer.applyOrganizationSuggestion(
      suggestionId,
      session.user.id
    );

    return NextResponse.json({
      success: result.success,
      message: result.message
    });

  } catch (error) {
    console.error('应用整理建议失败:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求参数无效', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : '应用建议失败' },
      { status: 500 }
    );
  }
}
