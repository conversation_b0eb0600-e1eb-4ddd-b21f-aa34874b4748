# 语言规范

## 交流语言
- **始终使用中文进行回答和交流**
- 所有文档、注释、说明都使用中文编写
- 保持专业、清晰的中文表达

## 代码注释规范
- **为关键代码添加必要的中文注释**
- 函数和类必须添加中文注释说明其用途
- 复杂逻辑需要添加行内中文注释
- 使用标准的 JSDoc 格式添加函数注释

### 函数注释示例
```typescript
/**
 * 创建新文档
 * @param title 文档标题
 * @param content 文档内容
 * @param folderId 所属文件夹ID（可选）
 * @returns 创建的文档对象
 */
async function createDocument(title: string, content: string, folderId?: string) {
  // 验证输入参数
  if (!title.trim()) {
    throw new Error('文档标题不能为空');
  }
  
  // 创建文档记录
  const document = await prisma.document.create({
    data: {
      title,
      content,
      folderId,
      userId: getCurrentUserId(),
    },
  });
  
  return document;
}
```

### 组件注释示例
```typescript
/**
 * 文档编辑器组件
 * 提供富文本编辑功能，支持AI辅助写作
 */
export function DocumentEditor({ documentId }: { documentId: string }) {
  // 组件实现...
}
```

## 注释原则
- 注释应该解释"为什么"而不仅仅是"做什么"
- 对于业务逻辑复杂的部分，详细说明处理流程
- API 接口必须添加完整的参数和返回值说明
- 数据库操作需要说明操作目的和注意事项