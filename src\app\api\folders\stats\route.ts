import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';

/**
 * GET /api/folders/stats - 获取用户文件夹统计信息
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const folderId = searchParams.get('folderId');

    // 构建查询条件
    const whereClause: any = {
      userId: session.user.id,
    };

    if (folderId) {
      whereClause.parentId = folderId;
    }

    // 并行执行多个统计查询
    const [
      totalFolders,
      rootFolders,
      totalDocuments,
      documentsInFolders,
      documentsInRoot,
      foldersByDepth,
      recentFolders,
      foldersCreatedThisWeek,
      foldersCreatedThisMonth,
      largestFolders,
    ] = await Promise.all([
      // 文件夹总数
      prisma.folder.count({
        where: { userId: session.user.id },
      }),

      // 根级文件夹数量
      prisma.folder.count({
        where: {
          userId: session.user.id,
          parentId: null,
        },
      }),

      // 文档总数
      prisma.document.count({
        where: { userId: session.user.id },
      }),

      // 文件夹中的文档数量
      prisma.document.count({
        where: {
          userId: session.user.id,
          folderId: { not: null },
        },
      }),

      // 根目录中的文档数量
      prisma.document.count({
        where: {
          userId: session.user.id,
          folderId: null,
        },
      }),

      // 按层级统计文件夹（简化版本，只统计到3层）
      Promise.all([
        prisma.folder.count({
          where: {
            userId: session.user.id,
            parentId: null,
          },
        }),
        prisma.folder.count({
          where: {
            userId: session.user.id,
            parent: { parentId: null },
          },
        }),
        prisma.folder.count({
          where: {
            userId: session.user.id,
            parent: { parent: { parentId: null } },
          },
        }),
      ]),

      // 最近创建的文件夹
      prisma.folder.findMany({
        where: { userId: session.user.id },
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          id: true,
          name: true,
          createdAt: true,
          _count: {
            select: {
              documents: true,
              children: true,
            },
          },
        },
      }),

      // 本周创建的文件夹数量
      prisma.folder.count({
        where: {
          userId: session.user.id,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
        },
      }),

      // 本月创建的文件夹数量
      prisma.folder.count({
        where: {
          userId: session.user.id,
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          },
        },
      }),

      // 包含最多文档的文件夹
      prisma.folder.findMany({
        where: { userId: session.user.id },
        include: {
          _count: {
            select: {
              documents: true,
              children: true,
            },
          },
        },
        orderBy: {
          documents: {
            _count: 'desc',
          },
        },
        take: 5,
      }),
    ]);

    // 计算文件夹使用率
    const folderUsageRate = totalDocuments > 0
      ? Math.round((documentsInFolders / totalDocuments) * 100)
      : 0;

    // 计算平均每个文件夹的文档数量
    const avgDocumentsPerFolder = totalFolders > 0
      ? Math.round(documentsInFolders / totalFolders)
      : 0;

    const stats = {
      overview: {
        totalFolders,
        rootFolders,
        totalDocuments,
        documentsInFolders,
        documentsInRoot,
        folderUsageRate,
        avgDocumentsPerFolder,
        foldersCreatedThisWeek,
        foldersCreatedThisMonth,
      },
      hierarchy: {
        depth1: foldersByDepth[0], // 根级文件夹
        depth2: foldersByDepth[1], // 二级文件夹
        depth3: foldersByDepth[2], // 三级文件夹
      },
      recentFolders,
      largestFolders: largestFolders.map(folder => ({
        id: folder.id,
        name: folder.name,
        documentsCount: folder._count.documents,
        childrenCount: folder._count.children,
        createdAt: folder.createdAt,
      })),
    };

    return NextResponse.json({ stats });
  } catch (error) {
    console.error('获取文件夹统计信息失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
