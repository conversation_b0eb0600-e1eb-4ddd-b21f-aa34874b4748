/**
 * Gemini 服务实现
 * 使用官方 Google Generative AI SDK 提供 Gemini API 的集成服务
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { BaseAIService } from './base-ai-service';
import { aiEnvConfig } from '@/lib/config/env';
import {
  AIServiceConfig,
  AIRequest,
  AIResponse,
  AIServiceError,
  AIErrorType,
  AIProvider
} from '@/types/ai.types';

/**
 * Gemini 服务实现
 */
export class GeminiService extends BaseAIService {
  readonly provider: AIProvider = 'gemini';

  private genAI: GoogleGenerativeAI;
  private proxyUrl: string = '';

  constructor(config: AIServiceConfig) {
    super(config);

    if (!config.apiKey) {
      throw new Error('Gemini API 密钥是必需的');
    }

    // 确定代理地址：配置 > 环境变量 > 无代理
    if (config.endpoint && config.endpoint.trim()) {
      // 配置的代理优先
      this.proxyUrl = config.endpoint;
    } else if (typeof window === 'undefined') {
      // 服务器端使用环境变量
      this.proxyUrl = aiEnvConfig.getProxyUrl();
    }

    // 创建 Gemini 客户端
    this.genAI = this.createGeminiClient();
  }

  /**
   * 创建 Gemini 客户端
   */
  private createGeminiClient(): GoogleGenerativeAI {
    // 如果有代理配置，设置环境变量
    if (this.proxyUrl && typeof window === 'undefined') {
      console.log('Gemini 客户端使用代理:', this.proxyUrl);

      // 设置环境变量代理
      process.env.HTTP_PROXY = this.proxyUrl;
      process.env.HTTPS_PROXY = this.proxyUrl;
    }

    return new GoogleGenerativeAI(this.config.apiKey!);
  }

  /**
   * 创建代理 fetch 函数
   */
  private async createProxyFetch() {
    if (!this.proxyUrl || typeof window !== 'undefined') {
      return undefined;
    }

    try {
      const agent = new HttpsProxyAgent(this.proxyUrl);
      const nodeFetch = (await import('node-fetch')).default;

      return async (url: string, init: any = {}) => {
        return nodeFetch(url, {
          ...init,
          agent: agent
        }) as any;
      };
    } catch (error) {
      console.warn('创建代理 fetch 失败:', error);
      return undefined;
    }
  }

  /**
   * 生成文本内容
   */
  async _generateText(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    return this.executeWithRetry(async () => {
      try {
        // 如果有代理，临时替换全局 fetch
        const proxyFetch = await this.createProxyFetch();
        const originalFetch = globalThis.fetch;

        if (proxyFetch) {
          globalThis.fetch = proxyFetch as any;
        }

        try {
          const model = this.genAI.getGenerativeModel({
            model: this.config.model || 'gemini-pro'
          });

          // 构建提示内容
          const prompt = this.buildPrompt(request);

          const result = await model.generateContent(prompt);
          const response = await result.response;

          if (!response.text()) {
            throw new AIServiceError(
              AIErrorType.INVALID_REQUEST,
              'Gemini 返回了空的响应',
              this.provider
            );
          }

          // 检查安全过滤
          if (response.candidates && response.candidates[0]?.finishReason === 'SAFETY') {
            throw new AIServiceError(
              AIErrorType.CONTENT_FILTERED,
              '内容被 Gemini 安全过滤器拦截',
              this.provider
            );
          }

          return {
            content: response.text().trim(),
            tokensUsed: this.estimateTokens(response.text()),
            responseTime: Date.now() - startTime,
            model: this.config.model || 'gemini-pro',
            provider: this.provider
          };
        } finally {
          // 恢复原始 fetch
          if (proxyFetch && originalFetch) {
            globalThis.fetch = originalFetch;
          }
        }
      } catch (error) {
        throw this.handleGeminiError(error);
      }
    }, 'Gemini text generation');
  }

  /**
   * 获取可用模型列表
   */
  async getAvailableModels(): Promise<string[]> {
    try {
      // Gemini 目前支持的模型
      return [
        'gemini-1.5-pro',
        'gemini-1.5-flash',
        'gemini-pro',
        'gemini-pro-vision',
        'gemini-2.5-flash-lite'
      ];
    } catch (error) {
      console.warn('获取 Gemini 模型列表失败:', error);
      return ['gemini-pro'];
    }
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('开始 Gemini 连接测试...');

      // 如果有代理，临时替换全局 fetch
      const proxyFetch = await this.createProxyFetch();
      const originalFetch = globalThis.fetch;

      if (proxyFetch) {
        globalThis.fetch = proxyFetch as any;
      }

      try {
        const model = this.genAI.getGenerativeModel({
          model: this.config.model || 'gemini-pro'
        });

        // 增加超时控制
        const result = await Promise.race([
          model.generateContent('测试'),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('连接测试超时')), 45000) // 45秒超时
          )
        ]);

        const response = await (result as any).response;
        const success = !!(response.text() && response.text().length > 0);

        console.log('Gemini 连接测试结果:', success);
        return success;
      } finally {
        // 恢复原始 fetch
        if (proxyFetch && originalFetch) {
          globalThis.fetch = originalFetch;
        }
      }
    } catch (error) {
      console.error('Gemini 连接测试失败:', error);

      // 提供更详细的错误信息
      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('ETIMEDOUT')) {
          console.error('连接超时，可能是代理配置问题或网络问题');
        } else if (error.message.includes('ECONNREFUSED')) {
          console.error('连接被拒绝，请检查代理服务器是否运行');
        } else if (error.message.includes('fetch failed')) {
          console.error('网络请求失败，可能需要配置代理');
        }
      }

      return false;
    }
  }

  /**
   * 构建提示内容
   */
  private buildPrompt(request: AIRequest): string {
    let prompt = '';

    // 添加系统提示
    if (request.systemPrompt) {
      prompt += `系统提示: ${request.systemPrompt}\n\n`;
    }

    // 添加上下文
    if (request.context) {
      prompt += `上下文信息: ${request.context}\n\n`;
    }

    // 添加用户提示
    prompt += request.prompt;

    return prompt;
  }

  /**
   * 估算令牌数量
   */
  private estimateTokens(text: string): number {
    // 简单的令牌估算：中文字符按1:1，英文单词按1:1.3
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishText = text.replace(/[\u4e00-\u9fff]/g, '');
    const englishWords = englishText.split(/\s+/).filter(word => word.length > 0).length;

    return Math.ceil(chineseChars + englishWords * 1.3);
  }

  /**
   * 处理 Gemini 错误
   */
  private handleGeminiError(error: any): AIServiceError {
    if (error.message) {
      const message = error.message.toLowerCase();

      if (message.includes('api key')) {
        return new AIServiceError(
          AIErrorType.INVALID_API_KEY,
          'Gemini API 密钥无效或已过期',
          this.provider,
          error
        );
      }

      if (message.includes('quota') || message.includes('limit')) {
        return new AIServiceError(
          AIErrorType.QUOTA_EXCEEDED,
          '请求配额已用完或超出限制',
          this.provider,
          error
        );
      }

      if (message.includes('safety') || message.includes('blocked')) {
        return new AIServiceError(
          AIErrorType.CONTENT_FILTERED,
          '内容被安全过滤器拦截',
          this.provider,
          error
        );
      }

      if (message.includes('timeout')) {
        return new AIServiceError(
          AIErrorType.TIMEOUT,
          '请求超时，请稍后再试',
          this.provider,
          error
        );
      }

      if (message.includes('network') || message.includes('connection') || message.includes('fetch failed')) {
        return new AIServiceError(
          AIErrorType.NETWORK_ERROR,
          '网络连接失败，请检查网络设置或代理配置',
          this.provider,
          error
        );
      }
    }

    // 处理其他错误
    return this.handleHttpError(error, 'Gemini API request');
  }
}