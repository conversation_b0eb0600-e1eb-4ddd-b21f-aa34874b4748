import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db/prisma';
import { z } from 'zod';

// 文件夹更新的验证模式
const updateFolderSchema = z.object({
  name: z.string().min(1, '文件夹名称不能为空').max(255, '文件夹名称长度不能超过255个字符').optional(),
  parentId: z.string().nullable().optional(),
});

/**
 * GET /api/folders/[id] - 获取指定文件夹
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const includeDocuments = searchParams.get('includeDocuments') !== 'false';
    const includeChildren = searchParams.get('includeChildren') !== 'false';
    const depth = parseInt(searchParams.get('depth') || '2');

    const includeOptions: any = {
      parent: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          documents: true,
          children: true,
        },
      },
    };

    // 包含子文件夹
    if (includeChildren) {
      includeOptions.children = {
        include: {
          _count: {
            select: {
              documents: true,
              children: true,
            },
          },
        },
      };

      // 递归包含子文件夹（根据深度）
      if (depth > 1) {
        includeOptions.children.include.children = {
          include: {
            _count: {
              select: {
                documents: true,
                children: true,
              },
            },
          },
        };
      }
    }

    // 包含文档
    if (includeDocuments) {
      includeOptions.documents = {
        where: {
          isDeleted: false, // 只包含未删除的文档
        },
        select: {
          id: true,
          title: true,
          createdAt: true,
          updatedAt: true,
          wordCount: true,
          charCount: true,
          isPublic: true,
          shareToken: true,
        },
        orderBy: {
          updatedAt: 'desc',
        },
      };
    }

    const folder = await prisma.folder.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
        isDeleted: false, // 不返回已删除的文件夹
      },
      include: includeOptions,
    });

    if (!folder) {
      return NextResponse.json({ error: '文件夹未找到' }, { status: 404 });
    }

    return NextResponse.json({ folder });
  } catch (error) {
    console.error('获取文件夹失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/folders/[id] - 更新文件夹（重命名或移动）
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = updateFolderSchema.parse(body);

    // 检查文件夹是否存在且属于当前用户
    const existingFolder = await prisma.folder.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!existingFolder) {
      return NextResponse.json({ error: '文件夹未找到' }, { status: 404 });
    }

    // 如果要移动到新的父文件夹，验证父文件夹存在且防止循环引用
    if (validatedData.parentId !== undefined) {
      if (validatedData.parentId) {
        // 检查父文件夹是否存在且属于当前用户
        const parentFolder = await prisma.folder.findFirst({
          where: {
            id: validatedData.parentId,
            userId: session.user.id,
          },
        });

        if (!parentFolder) {
          return NextResponse.json(
            { error: '父文件夹未找到' },
            { status: 404 }
          );
        }

        // 防止将文件夹移动到自身或其子文件夹中
        const isCircular = await checkCircularReference(
          params.id,
          validatedData.parentId
        );

        if (isCircular) {
          return NextResponse.json(
            { error: '不能将文件夹移动到自身或其子文件夹中' },
            { status: 400 }
          );
        }
      }
    }

    // 如果重命名，检查同一位置是否已存在同名文件夹
    if (validatedData.name && validatedData.name !== existingFolder.name) {
      const parentId = validatedData.parentId !== undefined
        ? validatedData.parentId
        : existingFolder.parentId;

      const duplicateFolder = await prisma.folder.findFirst({
        where: {
          name: validatedData.name,
          parentId: parentId,
          userId: session.user.id,
          id: { not: params.id },
        },
      });

      if (duplicateFolder) {
        return NextResponse.json(
          { error: '同一位置已存在同名文件夹' },
          { status: 409 }
        );
      }
    }

    const updatedFolder = await prisma.folder.update({
      where: {
        id: params.id,
      },
      data: {
        ...(validatedData.name && { name: validatedData.name }),
        ...(validatedData.parentId !== undefined && { parentId: validatedData.parentId }),
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
          },
        },
        children: {
          include: {
            _count: {
              select: {
                documents: true,
                children: true,
              },
            },
          },
        },
        documents: {
          select: {
            id: true,
            title: true,
            createdAt: true,
            updatedAt: true,
            wordCount: true,
            charCount: true,
          },
        },
        _count: {
          select: {
            documents: true,
            children: true,
          },
        },
      },
    });

    console.log(`用户 ${session.user.id} 更新了文件夹: ${updatedFolder.name}`);

    return NextResponse.json({ folder: updatedFolder });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '输入参数无效', details: error.issues },
        { status: 400 }
      );
    }

    console.error('更新文件夹失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/folders/[id] - 删除文件夹
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const force = searchParams.get('force') === 'true';

    // 检查文件夹是否存在且属于当前用户
    const folder = await prisma.folder.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
      include: {
        children: true,
        documents: true,
      },
    });

    if (!folder) {
      return NextResponse.json({ error: '文件夹未找到' }, { status: 404 });
    }

    // 如果不是强制删除，检查文件夹是否包含子文件夹或文档
    if (!force && (folder.children.length > 0 || folder.documents.length > 0)) {
      return NextResponse.json(
        {
          error: '不能删除包含子文件夹或文档的文件夹',
          hasChildren: folder.children.length > 0,
          hasDocuments: folder.documents.length > 0,
          childrenCount: folder.children.length,
          documentsCount: folder.documents.length,
        },
        { status: 400 }
      );
    }

    // 如果是强制删除，使用事务删除所有相关数据
    if (force) {
      await prisma.$transaction(async (tx) => {
        // 递归删除所有子文件夹和文档
        await deleteFolder(tx, params.id);
      });
    } else {
      // 普通删除（只删除空文件夹）
      await prisma.folder.delete({
        where: {
          id: params.id,
        },
      });
    }

    console.log(`用户 ${session.user.id} 删除了文件夹: ${folder.name}${force ? ' (强制删除)' : ''}`);

    return NextResponse.json({
      message: '文件夹删除成功',
      deletedFolder: {
        id: folder.id,
        name: folder.name,
      },
    });
  } catch (error) {
    console.error('删除文件夹失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 检查循环引用的辅助函数
 */
async function checkCircularReference(
  folderId: string,
  targetParentId: string
): Promise<boolean> {
  if (folderId === targetParentId) {
    return true;
  }

  const descendants = await getDescendantIds(folderId);
  return descendants.includes(targetParentId);
}

/**
 * 获取所有子文件夹ID的辅助函数
 */
async function getDescendantIds(folderId: string): Promise<string[]> {
  const descendants: string[] = [];

  const children = await prisma.folder.findMany({
    where: { parentId: folderId },
    select: { id: true },
  });

  for (const child of children) {
    descendants.push(child.id);
    const childDescendants = await getDescendantIds(child.id);
    descendants.push(...childDescendants);
  }

  return descendants;
}

/**
 * 递归删除文件夹及其所有内容的辅助函数
 */
async function deleteFolder(tx: any, folderId: string): Promise<void> {
  // 获取所有子文件夹
  const children = await tx.folder.findMany({
    where: { parentId: folderId },
    select: { id: true },
  });

  // 递归删除所有子文件夹
  for (const child of children) {
    await deleteFolder(tx, child.id);
  }

  // 删除文件夹中的所有文档的相关数据
  const documents = await tx.document.findMany({
    where: { folderId },
    select: { id: true },
  });

  for (const document of documents) {
    // 删除文档历史记录
    await tx.documentHistory.deleteMany({
      where: { documentId: document.id },
    });

    // 删除AI交互记录
    await tx.aIInteraction.deleteMany({
      where: { documentId: document.id },
    });
  }

  // 删除文件夹中的所有文档
  await tx.document.deleteMany({
    where: { folderId },
  });

  // 最后删除文件夹本身
  await tx.folder.delete({
    where: { id: folderId },
  });
}