'use client';

import React, { useState } from 'react';
import { Editor } from '@/components/editor/Editor';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';

/**
 * Chat 和 Edit 功能演示页面
 */
export default function ChatEditDemoPage() {
  const [content, setContent] = useState(`
    <h1>Chat 和 Edit 功能演示</h1>
    
    <p>这个页面演示了文本选择菜单的 Chat 和 Edit 功能。请尝试以下操作：</p>
    
    <h2>Chat 功能测试</h2>
    <p>选择这段文本，然后点击 "Chat" 按钮。这将打开一个 AI 对话界面，您可以与 AI 讨论选中的内容，包括解释、翻译、总结等功能。</p>
    
    <h2>Edit 功能测试</h2>
    <p>选择这段文本，然后点击 "Edit" 按钮。这将打开一个 AI 编辑界面，可以对选中的文本进行改进、重写、正式化、简化等操作，并直接替换原文。</p>
    
    <h2>格式化快捷操作</h2>
    <p>选择文本后，您还可以使用快速格式化功能，如加粗、斜体、复制、添加链接等。这些操作可以快速应用到选中的文本上。</p>
    
    <h2>复杂文本测试</h2>
    <p>这是一段包含<strong>粗体</strong>、<em>斜体</em>和<a href="#">链接</a>的复杂文本。选择包含格式的文本时，菜单应该能够正确处理，并提供相应的编辑选项。AI 功能也应该能够理解和处理这些格式化的内容。</p>
    
    <blockquote>
      <p>这是一个引用块。选择引用块中的文本来测试 Chat 和 Edit 功能在不同元素类型中的表现。AI 应该能够理解上下文并提供合适的响应。</p>
    </blockquote>
    
    <ul>
      <li>列表项目 1 - 测试在列表中选择文本的功能</li>
      <li>列表项目 2 - Chat 功能应该能够理解列表结构</li>
      <li>列表项目 3 - Edit 功能应该保持适当的格式</li>
    </ul>
    
    <h2>长文本测试</h2>
    <p>这是一段较长的文本，用于测试 Chat 和 Edit 功能在处理长文本时的表现。当选择的文本很长时，AI 应该能够有效地处理和响应。编辑功能也应该能够处理长文本的改写和优化，同时保持内容的连贯性和准确性。用户界面应该能够清晰地显示原文和编辑结果的对比，让用户能够轻松地查看和确认更改。</p>
  `);

  const [logs, setLogs] = useState<string[]>([]);

  /**
   * 添加日志
   */
  const addLog = (message: string) => {
    setLogs(prev => [...prev.slice(-9), `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  /**
   * 清除日志
   */
  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Chat 和 Edit 功能演示
          </h1>
          <p className="text-gray-600 mb-6">
            这个页面演示了文本选择菜单的 Chat 和 Edit 功能。选择编辑器中的任意文本来体验完整的 AI 交互功能。
          </p>
          
          {/* 使用说明 */}
          <Card className="p-4 mb-6 bg-blue-50 border-blue-200">
            <h3 className="font-semibold text-blue-900 mb-2">使用说明：</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
              <div>
                <h4 className="font-medium mb-1">Chat 功能：</h4>
                <ul className="space-y-1">
                  <li>• 选择文本后点击 "Chat" 按钮</li>
                  <li>• 支持解释、翻译、总结、讨论等模式</li>
                  <li>• 提供完整的对话界面</li>
                  <li>• 可以继续与 AI 深入交流</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-1">Edit 功能：</h4>
                <ul className="space-y-1">
                  <li>• 选择文本后点击 "Edit" 按钮</li>
                  <li>• 支持改进、重写、正式化等模式</li>
                  <li>• 提供原文和编辑结果对比</li>
                  <li>• 可以直接应用编辑结果</li>
                </ul>
              </div>
            </div>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 编辑器区域 */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">编辑器</h2>
              <div className="border rounded-lg">
                <Editor
                  content={content}
                  onChange={setContent}
                  placeholder="开始编辑文档..."
                  enableAI={true}
                  className="min-h-[600px]"
                />
              </div>
            </Card>
          </div>

          {/* 信息面板 */}
          <div className="lg:col-span-1 space-y-6">
            {/* 功能状态 */}
            <Card className="p-6">
              <h3 className="font-semibold mb-3">功能状态</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span>文本选择检测</span>
                  <span className="text-green-600 font-medium">✓ 启用</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Chat 对话功能</span>
                  <span className="text-green-600 font-medium">✓ 启用</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Edit 编辑功能</span>
                  <span className="text-green-600 font-medium">✓ 启用</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>格式化工具</span>
                  <span className="text-green-600 font-medium">✓ 启用</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>AI 服务</span>
                  <span className="text-yellow-600 font-medium">⚠ 模拟</span>
                </div>
              </div>
            </Card>

            {/* 测试指南 */}
            <Card className="p-6">
              <h3 className="font-semibold mb-3">测试指南</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <h4 className="font-medium text-blue-700 mb-1">Chat 测试：</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>1. 选择一段文本</li>
                    <li>2. 点击 "Chat" 按钮</li>
                    <li>3. 尝试不同的对话模式</li>
                    <li>4. 与 AI 进行多轮对话</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-green-700 mb-1">Edit 测试：</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>1. 选择需要编辑的文本</li>
                    <li>2. 点击 "Edit" 按钮</li>
                    <li>3. 尝试不同的编辑模式</li>
                    <li>4. 应用编辑结果</li>
                  </ul>
                </div>
              </div>
            </Card>

            {/* 快捷操作 */}
            <Card className="p-6">
              <h3 className="font-semibold mb-3">快捷操作</h3>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearLogs}
                  className="w-full"
                >
                  清除日志
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setContent('')}
                  className="w-full"
                >
                  清空编辑器
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.location.reload()}
                  className="w-full"
                >
                  重新加载
                </Button>
              </div>
            </Card>
          </div>
        </div>

        {/* 特性说明 */}
        <Card className="p-6 mt-8">
          <h2 className="text-xl font-semibold mb-4">功能特性</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h3 className="font-semibold mb-2 text-blue-700">Chat 功能</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• 多种对话模式（解释、翻译、总结、讨论）</li>
                <li>• 完整的对话界面和历史记录</li>
                <li>• 支持多轮对话和上下文理解</li>
                <li>• 响应结果可复制和重新生成</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-green-700">Edit 功能</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• 多种编辑模式（改进、重写、正式化等）</li>
                <li>• 原文和编辑结果对比显示</li>
                <li>• 一键应用编辑结果到文档</li>
                <li>• 支持重新生成和调整</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-purple-700">交互体验</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• 智能菜单定位和边界检测</li>
                <li>• 流畅的动画和视觉反馈</li>
                <li>• 键盘快捷键支持</li>
                <li>• 响应式设计适配各种屏幕</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}