'use client';

import { User } from 'next-auth';
import { UserAvatar } from '@/components/auth/UserAvatar';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';

interface DashboardHeaderProps {
  user: User;
  onShowProfile: () => void;
}

export function DashboardHeader({ user, onShowProfile }: DashboardHeaderProps) {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-14 sm:h-16">
          <div className="flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1">
            <Link href="/dashboard" className="flex items-center min-w-0">
              <h1 className="text-lg sm:text-xl font-semibold text-gray-900 truncate">
                AI 文档编辑器
              </h1>
            </Link>
            
            <nav className="hidden md:flex space-x-4">
              <Link 
                href="/dashboard" 
                className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium touch-manipulation"
              >
                仪表板
              </Link>
              <Link 
                href="/document-manager" 
                className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium touch-manipulation"
              >
                文档管理
              </Link>
              <Link 
                href="/editor" 
                className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium touch-manipulation"
              >
                编辑器
              </Link>
            </nav>
          </div>
          
          <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
            <Button
              variant="outline"
              size="sm"
              onClick={onShowProfile}
              className="hidden sm:inline-flex touch-manipulation"
            >
              个人设置
            </Button>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700 hidden sm:block truncate max-w-32">
                {user.name || user.email}
              </span>
              <button 
                onClick={onShowProfile}
                className="touch-manipulation"
                title="个人设置"
              >
                <UserAvatar size="md" />
              </button>
            </div>
          </div>
        </div>
        
        {/* 移动端导航菜单 */}
        <div className="md:hidden border-t border-gray-200 py-2">
          <nav className="flex space-x-1 overflow-x-auto scrollbar-hide">
            <Link 
              href="/dashboard" 
              className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap touch-manipulation"
            >
              仪表板
            </Link>
            <Link 
              href="/document-manager" 
              className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap touch-manipulation"
            >
              文档管理
            </Link>
            <Link 
              href="/editor" 
              className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap touch-manipulation"
            >
              编辑器
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
}