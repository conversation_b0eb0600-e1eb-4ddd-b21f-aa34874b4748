import { prisma } from './prisma';
import { Folder, Document } from '@/types';

// Helper functions
function transformPrismaFolderToFolder(folder: any): Folder {
  return {
    ...folder,
    parentId: folder.parentId || undefined,
    children: [],
  };
}

function transformPrismaDocumentToDocument(document: any): Document {
  return {
    ...document,
    folderId: document.folderId || undefined,
    lastSyncAt: document.lastSyncAt || undefined,
    content: JSON.parse(document.content),
    metadata: {
      wordCount: document.wordCount,
      characterCount: document.charCount,
      tags: [],
      isPublic: document.isPublic,
      shareToken: document.shareToken || undefined,
    },
  };
}

export async function createFolder(data: {
  name: string;
  userId: string;
  parentId?: string;
}): Promise<Folder> {
  const folder = await prisma.folder.create({
    data: {
      name: data.name,
      userId: data.userId,
      parentId: data.parentId,
    },
  });

  return transformPrismaFolderToFolder(folder);
}

export async function getFolderById(
  id: string,
  userId: string
): Promise<Folder | null> {
  const folder = await prisma.folder.findFirst({
    where: {
      id,
      userId,
    },
    include: {
      children: true,
      documents: true,
    },
  });

  if (!folder) return null;

  const transformedFolder = transformPrismaFolderToFolder(folder);
  transformedFolder.children = [
    ...folder.children.map(transformPrismaFolderToFolder),
    ...folder.documents.map(transformPrismaDocumentToDocument),
  ];

  return transformedFolder;
}

export async function getFoldersByUserId(userId: string): Promise<Folder[]> {
  const folders = await prisma.folder.findMany({
    where: {
      userId,
      parentId: null, // Only root folders
    },
    include: {
      children: {
        include: {
          children: true,
          documents: true,
        },
      },
      documents: true,
    },
    orderBy: { createdAt: 'asc' },
  });

  return folders.map((folder) => {
    const transformedFolder = transformPrismaFolderToFolder(folder);
    transformedFolder.children = [
      ...folder.children.map((child) => {
        const transformedChild = transformPrismaFolderToFolder(child);
        transformedChild.children = [
          ...child.children.map(transformPrismaFolderToFolder),
          ...child.documents.map(transformPrismaDocumentToDocument),
        ];
        return transformedChild;
      }),
      ...folder.documents.map(transformPrismaDocumentToDocument),
    ];
    return transformedFolder;
  });
}

export async function getFoldersByParentId(
  parentId: string,
  userId: string
): Promise<Folder[]> {
  const folders = await prisma.folder.findMany({
    where: {
      parentId,
      userId,
    },
    include: {
      children: true,
      documents: true,
    },
    orderBy: { createdAt: 'asc' },
  });

  return folders.map((folder) => {
    const transformedFolder = transformPrismaFolderToFolder(folder);
    transformedFolder.children = [
      ...folder.children.map(transformPrismaFolderToFolder),
      ...folder.documents.map(transformPrismaDocumentToDocument),
    ];
    return transformedFolder;
  });
}

export async function updateFolder(
  id: string,
  userId: string,
  data: Partial<{
    name: string;
    parentId: string;
  }>
): Promise<Folder> {
  const folder = await prisma.folder.update({
    where: {
      id,
      userId,
    },
    data,
  });

  return transformPrismaFolderToFolder(folder);
}

export async function deleteFolder(id: string, userId: string): Promise<void> {
  // First, move all documents in this folder to root (no folder)
  await prisma.document.updateMany({
    where: {
      folderId: id,
      userId,
    },
    data: {
      folderId: null,
    },
  });

  // Then delete the folder (this will cascade to child folders)
  await prisma.folder.delete({
    where: {
      id,
      userId,
    },
  });
}

export async function moveFolderToParent(
  id: string,
  userId: string,
  newParentId?: string
): Promise<Folder> {
  const folder = await prisma.folder.update({
    where: {
      id,
      userId,
    },
    data: {
      parentId: newParentId,
    },
  });

  return transformPrismaFolderToFolder(folder);
}
