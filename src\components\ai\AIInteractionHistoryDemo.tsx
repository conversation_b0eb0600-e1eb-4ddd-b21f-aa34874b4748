/**
 * AI 交互历史记录演示组件
 * 用于展示和测试历史记录功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import { AIInteractionHistory } from './AIInteractionHistory';
import { AIHistoryPanel } from './AIHistoryPanel';
import { 
  Play, 
  Database, 
  Trash2, 
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';

/**
 * 演示状态
 */
interface DemoState {
  loading: boolean;
  message: string;
  type: 'info' | 'success' | 'error';
}

/**
 * AI 交互历史记录演示组件
 */
export function AIInteractionHistoryDemo() {
  const [demoState, setDemoState] = useState<DemoState>({
    loading: false,
    message: '',
    type: 'info',
  });

  /**
   * 创建演示数据
   */
  const createDemoData = async () => {
    setDemoState({ loading: true, message: '正在创建演示数据...', type: 'info' });

    try {
      // 模拟创建多条交互记录
      const demoInteractions = [
        {
          documentId: 'demo-doc-1',
          type: 'generate',
          input: '请帮我写一段关于人工智能发展历程的介绍',
          output: '人工智能的发展可以追溯到20世纪50年代，经历了多个重要阶段。从早期的符号主义方法到现代的深度学习，AI技术不断演进，如今已在各个领域展现出强大的应用潜力。',
          provider: 'openai',
          model: 'gpt-4',
          tokens: 180,
        },
        {
          documentId: 'demo-doc-1',
          type: 'rewrite',
          input: '这段文字太学术化了，请帮我改写得更通俗易懂',
          output: '人工智能就像是让计算机变得更聪明的技术。从几十年前开始，科学家们就在研究如何让机器像人一样思考和学习。现在，AI已经能够帮助我们做很多事情，比如语音识别、图像处理等。',
          provider: 'openai',
          model: 'gpt-3.5-turbo',
          tokens: 120,
        },
        {
          documentId: 'demo-doc-2',
          type: 'summarize',
          input: '请总结这篇关于机器学习的长文章',
          output: '文章主要介绍了机器学习的基本概念、主要算法类型（监督学习、无监督学习、强化学习）以及在实际应用中的案例。强调了数据质量对模型性能的重要性。',
          provider: 'gemini',
          model: 'gemini-pro',
          tokens: 95,
        },
        {
          documentId: 'demo-doc-2',
          type: 'translate',
          input: '请将这段中文翻译成英文：机器学习是人工智能的核心技术之一',
          output: 'Machine learning is one of the core technologies of artificial intelligence.',
          provider: 'openai',
          model: 'gpt-4',
          tokens: 45,
        },
        {
          documentId: 'demo-doc-3',
          type: 'explain',
          input: '请解释什么是神经网络',
          output: '神经网络是一种模仿人脑神经元结构的计算模型。它由多个相互连接的节点（神经元）组成，通过调整连接权重来学习数据中的模式。深度神经网络具有多个隐藏层，能够学习复杂的特征表示。',
          provider: 'ollama',
          model: 'llama2',
          tokens: 150,
        },
        {
          documentId: 'demo-doc-3',
          type: 'analyze',
          input: '请分析这篇技术文档的写作风格和目标读者',
          output: '这篇文档采用了技术性较强的写作风格，使用了专业术语和详细的技术描述。目标读者主要是具有一定技术背景的开发者和研究人员。建议在面向普通用户时增加更多解释性内容。',
          provider: 'gemini',
          model: 'gemini-pro',
          tokens: 110,
        },
      ];

      // 模拟 API 调用延迟
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 这里应该调用实际的 API 来创建记录
      // 由于演示环境可能没有完整的认证，我们只是模拟成功
      
      setDemoState({ 
        loading: false, 
        message: `成功创建了 ${demoInteractions.length} 条演示交互记录！`, 
        type: 'success' 
      });

      // 3秒后清除消息
      setTimeout(() => {
        setDemoState({ loading: false, message: '', type: 'info' });
      }, 3000);

    } catch (error) {
      setDemoState({ 
        loading: false, 
        message: '创建演示数据失败，请检查网络连接和认证状态', 
        type: 'error' 
      });
    }
  };

  /**
   * 清除演示数据
   */
  const clearDemoData = async () => {
    setDemoState({ loading: true, message: '正在清除演示数据...', type: 'info' });

    try {
      // 模拟 API 调用延迟
      await new Promise(resolve => setTimeout(resolve, 1500));

      setDemoState({ 
        loading: false, 
        message: '演示数据已清除！', 
        type: 'success' 
      });

      // 3秒后清除消息
      setTimeout(() => {
        setDemoState({ loading: false, message: '', type: 'info' });
      }, 3000);

    } catch (error) {
      setDemoState({ 
        loading: false, 
        message: '清除演示数据失败', 
        type: 'error' 
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* 演示控制面板 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          演示控制面板
        </h3>
        
        <div className="flex items-center space-x-4 mb-4">
          <button
            onClick={createDemoData}
            disabled={demoState.loading}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {demoState.loading ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Play className="w-4 h-4 mr-2" />
            )}
            创建演示数据
          </button>

          <button
            onClick={clearDemoData}
            disabled={demoState.loading}
            className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            清除演示数据
          </button>

          <div className="flex items-center text-sm text-gray-600">
            <Database className="w-4 h-4 mr-1" />
            <span>演示环境</span>
          </div>
        </div>

        {/* 状态消息 */}
        {demoState.message && (
          <div className={`flex items-center p-3 rounded-lg ${
            demoState.type === 'success' ? 'bg-green-50 text-green-700' :
            demoState.type === 'error' ? 'bg-red-50 text-red-700' :
            'bg-blue-50 text-blue-700'
          }`}>
            {demoState.type === 'success' && <CheckCircle className="w-4 h-4 mr-2" />}
            {demoState.type === 'error' && <AlertCircle className="w-4 h-4 mr-2" />}
            {demoState.type === 'info' && <Info className="w-4 h-4 mr-2" />}
            {demoState.message}
          </div>
        )}

        {/* 使用说明 */}
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">使用说明：</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 点击"创建演示数据"按钮生成测试用的 AI 交互记录</li>
            <li>• 在下方的历史记录界面中测试搜索、过滤、分页等功能</li>
            <li>• 可以选择记录进行批量删除操作</li>
            <li>• 点击"清除演示数据"可以清空所有测试数据</li>
            <li>• 在实际使用中，交互记录会在 AI 功能使用时自动创建</li>
          </ul>
        </div>
      </div>

      {/* 历史记录组件 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-[700px]">
        <AIInteractionHistory />
      </div>
    </div>
  );
}