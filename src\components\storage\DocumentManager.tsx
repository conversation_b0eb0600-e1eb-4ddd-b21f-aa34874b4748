'use client';

import { useState } from 'react';
import { useDocuments } from '@/hooks/useDocuments';
import { DocumentEditor } from '@/components/editor/DocumentEditor';
import { Button } from '@/components/ui/Button';
import { LocalDocument } from '@/lib/storage/database';
import { createEmptyContent } from '@/lib/storage/document-utils';

interface DocumentManagerProps {
  userId: string;
}

export function DocumentManager({ userId }: DocumentManagerProps) {
  const {
    documents,
    loading,
    error,
    stats,
    createDocument,
    deleteDocument,
    duplicateDocument,
    searchDocuments,
    refreshDocuments,
    clearError
  } = useDocuments({ userId, autoLoad: true });

  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<LocalDocument[]>([]);
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateDocument = async () => {
    setIsCreating(true);
    try {
      const newDoc = await createDocument({
        title: `新文档 ${new Date().toLocaleString()}`,
        content: createEmptyContent(),
        userId,
        metadata: {
          wordCount: 0,
          characterCount: 0,
          tags: [],
          isPublic: false
        }
      });
      
      if (newDoc) {
        setSelectedDocumentId(newDoc.id);
      }
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteDocument = async (id: string) => {
    if (confirm('确定要删除这个文档吗？')) {
      const success = await deleteDocument(id);
      if (success && selectedDocumentId === id) {
        setSelectedDocumentId(null);
      }
    }
  };

  const handleDuplicateDocument = async (id: string) => {
    const duplicated = await duplicateDocument(id);
    if (duplicated) {
      setSelectedDocumentId(duplicated.id);
    }
  };

  const handleSearch = async () => {
    if (searchQuery.trim()) {
      const results = await searchDocuments(searchQuery);
      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  };

  const selectedDocument = documents.find(doc => doc.id === selectedDocumentId);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">我的文档</h2>
            <Button
              onClick={handleCreateDocument}
              disabled={isCreating}
              size="sm"
            >
              {isCreating ? '创建中...' : '新建'}
            </Button>
          </div>
          
          {/* Search */}
          <div className="flex gap-2">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索文档..."
              className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <Button onClick={handleSearch} size="sm" variant="outline">
              搜索
            </Button>
          </div>
        </div>

        {/* Stats */}
        {stats && (
          <div className="p-4 bg-blue-50 border-b border-gray-200">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-blue-600">文档数量</div>
                <div className="font-medium">{stats.totalDocuments}</div>
              </div>
              <div>
                <div className="text-blue-600">总字数</div>
                <div className="font-medium">{stats.totalWords.toLocaleString()}</div>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="p-4 bg-red-50 border-b border-red-200">
            <div className="flex justify-between items-start">
              <div className="text-red-800 text-sm">{error.message}</div>
              <Button
                onClick={clearError}
                variant="ghost"
                size="sm"
                className="text-red-600 hover:text-red-800"
              >
                ×
              </Button>
            </div>
          </div>
        )}

        {/* Document List */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="p-4 text-center text-gray-500">加载中...</div>
          ) : (
            <div className="p-2">
              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2 px-2">
                    搜索结果 ({searchResults.length})
                  </h3>
                  {searchResults.map((doc) => (
                    <DocumentItem
                      key={`search-${doc.id}`}
                      document={doc}
                      isSelected={selectedDocumentId === doc.id}
                      onSelect={() => setSelectedDocumentId(doc.id)}
                      onDelete={() => handleDeleteDocument(doc.id)}
                      onDuplicate={() => handleDuplicateDocument(doc.id)}
                    />
                  ))}
                </div>
              )}

              {/* All Documents */}
              <div>
                {searchResults.length === 0 && (
                  <h3 className="text-sm font-medium text-gray-700 mb-2 px-2">
                    所有文档 ({documents.length})
                  </h3>
                )}
                {documents.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    还没有文档，点击"新建"创建第一个文档
                  </div>
                ) : (
                  documents.map((doc) => (
                    <DocumentItem
                      key={doc.id}
                      document={doc}
                      isSelected={selectedDocumentId === doc.id}
                      onSelect={() => setSelectedDocumentId(doc.id)}
                      onDelete={() => handleDeleteDocument(doc.id)}
                      onDuplicate={() => handleDuplicateDocument(doc.id)}
                    />
                  ))
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <Button
            onClick={refreshDocuments}
            variant="outline"
            size="sm"
            className="w-full"
          >
            刷新列表
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {selectedDocumentId ? (
          <DocumentEditor
            documentId={selectedDocumentId}
            userId={userId}
            placeholder="开始写作..."
            autoSave={true}
            autoSaveDelay={2000}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="text-gray-500 mb-4">选择一个文档开始编辑</div>
              <Button onClick={handleCreateDocument} disabled={isCreating}>
                {isCreating ? '创建中...' : '创建新文档'}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

interface DocumentItemProps {
  document: LocalDocument;
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
}

function DocumentItem({ document, isSelected, onSelect, onDelete, onDuplicate }: DocumentItemProps) {
  return (
    <div
      className={`p-3 mb-2 rounded-md cursor-pointer transition-colors ${
        isSelected
          ? 'bg-blue-100 border border-blue-300'
          : 'hover:bg-gray-100 border border-transparent'
      }`}
      onClick={onSelect}
    >
      <div className="flex justify-between items-start mb-2">
        <h4 className="font-medium text-sm truncate flex-1 mr-2">
          {document.title}
        </h4>
        <div className="flex gap-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDuplicate();
            }}
            className="text-gray-400 hover:text-gray-600 text-xs p-1"
            title="复制"
          >
            📋
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="text-gray-400 hover:text-red-600 text-xs p-1"
            title="删除"
          >
            🗑️
          </button>
        </div>
      </div>
      
      <div className="text-xs text-gray-500 space-y-1">
        <div className="flex justify-between">
          <span>{document.metadata.wordCount} 词</span>
          <span>{document.metadata.characterCount} 字符</span>
        </div>
        <div>
          更新于 {document.updatedAt.toLocaleDateString()}
        </div>
        {document.isDirty && (
          <div className="text-orange-600">未同步</div>
        )}
      </div>
      
      {document.metadata.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {document.metadata.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-gray-200 text-gray-700 text-xs rounded-full"
            >
              {tag}
            </span>
          ))}
          {document.metadata.tags.length > 3 && (
            <span className="text-xs text-gray-500">
              +{document.metadata.tags.length - 3}
            </span>
          )}
        </div>
      )}
    </div>
  );
}