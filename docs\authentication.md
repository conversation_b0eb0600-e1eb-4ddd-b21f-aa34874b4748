# 用户认证系统文档

## 概述

本项目实现了完整的用户认证系统，基于 NextAuth.js 构建，支持邮箱密码登录和第三方 OAuth 登录。

## 功能特性

### 已实现功能

1. **用户注册和登录**
   - 邮箱密码注册
   - 邮箱密码登录
   - 密码加密存储（bcrypt）
   - 输入验证和错误处理

2. **会话管理**
   - JWT 令牌认证
   - 会话持久化
   - 自动会话刷新
   - 安全的会话存储

3. **用户界面**
   - 登录表单组件
   - 注册表单组件
   - 用户头像组件
   - 认证守卫组件

4. **API 路由**
   - NextAuth.js 认证端点
   - 用户注册 API
   - 认证测试 API
   - 中间件保护

5. **数据库集成**
   - Prisma ORM 集成
   - 用户数据模型
   - 会话和账户管理
   - 数据库迁移

## 技术架构

### 核心组件

```
src/
├── lib/auth/
│   ├── config.ts          # NextAuth 配置
│   ├── utils.ts           # 认证工具函数
│   ├── session.ts         # 会话管理
│   ├── jwt.ts             # JWT 令牌处理
│   └── password.ts        # 密码处理
├── components/auth/
│   ├── SessionProvider.tsx # 会话提供者
│   ├── LoginForm.tsx      # 登录表单
│   ├── RegisterForm.tsx   # 注册表单
│   ├── UserAvatar.tsx     # 用户头像
│   └── AuthGuard.tsx      # 认证守卫
├── hooks/
│   └── useAuth.ts         # 认证 Hook
├── app/api/auth/
│   ├── [...nextauth]/     # NextAuth 路由
│   ├── register/          # 注册 API
│   └── test/              # 测试 API
└── app/auth/
    ├── signin/            # 登录页面
    └── signup/            # 注册页面
```

### 数据库模型

```prisma
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  emailVerified DateTime?
  name          String?
  image         String?
  password      String?
  subscription  String    @default("free")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts       Account[]
  sessions       Session[]
  folders        Folder[]
  documents      Document[]
  aiConfigs      AIConfiguration[]
  aiInteractions AIInteraction[]
}

model Account {
  // NextAuth 账户模型
}

model Session {
  // NextAuth 会话模型
}

model VerificationToken {
  // NextAuth 验证令牌模型
}
```

## 使用指南

### 1. 环境配置

在 `.env` 文件中配置必要的环境变量：

```env
# NextAuth 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# 可选的 OAuth 提供商
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
GITHUB_ID=""
GITHUB_SECRET=""
```

### 2. 在组件中使用认证

```tsx
import { useAuth } from "@/hooks/useAuth";

function MyComponent() {
  const { user, isAuthenticated, login, logout, register } = useAuth();

  if (!isAuthenticated) {
    return <div>请先登录</div>;
  }

  return (
    <div>
      <p>欢迎，{user.name}！</p>
      <button onClick={logout}>退出登录</button>
    </div>
  );
}
```

### 3. 保护页面和路由

```tsx
import { requireAuth } from "@/lib/auth/utils";

export default async function ProtectedPage() {
  // 服务端认证检查
  const user = await requireAuth();

  return <div>受保护的页面内容</div>;
}
```

### 4. 客户端认证守卫

```tsx
import { AuthGuard } from "@/components/auth/AuthGuard";

function App() {
  return (
    <AuthGuard requireAuth={true}>
      <ProtectedContent />
    </AuthGuard>
  );
}
```

## API 端点

### 认证相关 API

- `POST /api/auth/register` - 用户注册
- `POST /api/auth/signin` - 用户登录（NextAuth）
- `POST /api/auth/signout` - 用户退出（NextAuth）
- `GET /api/auth/session` - 获取当前会话（NextAuth）
- `GET /api/auth/test` - 认证系统测试

### 请求示例

```javascript
// 用户注册
const response = await fetch('/api/auth/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: '张三',
    email: '<EMAIL>',
    password: 'password123'
  })
});
```

## 安全特性

1. **密码安全**
   - bcrypt 哈希加密
   - 盐值随机生成
   - 密码强度验证

2. **会话安全**
   - JWT 令牌签名
   - 会话过期管理
   - CSRF 保护

3. **输入验证**
   - Zod 模式验证
   - 邮箱格式检查
   - 密码复杂度要求

4. **中间件保护**
   - 路由级别保护
   - 自动重定向
   - 权限检查

## 扩展功能

### 计划中的功能

1. **第三方登录**
   - Google OAuth
   - GitHub OAuth
   - 微信登录

2. **邮箱验证**
   - 注册邮箱验证
   - 密码重置
   - 邮箱变更验证

3. **多因素认证**
   - TOTP 支持
   - SMS 验证
   - 备用码

4. **用户管理**
   - 个人资料编辑
   - 头像上传
   - 账户设置

## 故障排除

### 常见问题

1. **NextAuth 配置错误**
   - 检查 `NEXTAUTH_SECRET` 是否设置
   - 确认 `NEXTAUTH_URL` 正确

2. **数据库连接问题**
   - 运行 `npm run db:push` 同步数据库
   - 检查 `DATABASE_URL` 配置

3. **会话问题**
   - 清除浏览器 Cookie
   - 重启开发服务器

### 调试工具

- 访问 `/api/auth/test` 检查认证状态
- 查看浏览器开发者工具的网络请求
- 检查服务器控制台日志

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础认证系统实现
- ✅ 用户注册和登录
- ✅ 会话管理
- ✅ 密码加密
- ✅ 输入验证
- ✅ 用户界面组件
- ✅ API 路由
- ✅ 数据库集成
- ✅ 中间件保护