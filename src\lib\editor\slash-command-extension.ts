import { Extension, Editor } from '@tiptap/core';
import { PluginKey, Plugin, Transaction, EditorState } from '@tiptap/pm/state';
import { Decoration, DecorationSet, EditorView } from '@tiptap/pm/view';
import { SlashCommandPluginOptions } from '../../types/slash-command.types';

export interface SlashCommandStorage {
  isOpen: boolean;
  range: { from: number; to: number } | null;
  query: string;
  decorationId: string | null;
}

interface PluginState {
  isOpen: boolean;
  range: { from: number; to: number } | null;
  query: string;
  decorations: DecorationSet;
}

/**
 * 斜杠命令扩展
 * 负责检测斜杠字符并触发命令菜单
 */
export const SlashCommandExtension = Extension.create<SlashCommandPluginOptions, SlashCommandStorage>({
  name: 'slashCommand',

  addOptions() {
    return {
      commands: [],
      trigger: '/',
      allowSpaces: false,
      startOfLine: false,
      char: '/',
    };
  },

  addStorage() {
    return {
      isOpen: false,
      range: null,
      query: '',
      decorationId: null,
    };
  },

  addProseMirrorPlugins() {
    const extension = this;
    
    return [
      new Plugin<PluginState>({
        key: new PluginKey('slashCommand'),

        state: {
          init(): PluginState {
            return {
              isOpen: false,
              range: null,
              query: '',
              decorations: DecorationSet.empty,
            };
          },

          apply(tr: Transaction, prev: PluginState, _oldState: EditorState, newState: EditorState): PluginState {
            const { selection, doc } = newState;
            const { from, to } = selection;

            // 检查是否是文本选择且光标在同一位置
            if (from !== to) {
              return {
                ...prev,
                isOpen: false,
                range: null,
                query: '',
                decorations: DecorationSet.empty,
              };
            }

            // 获取光标前的文本
            const $from = selection.$from;
            const textBefore = $from.nodeBefore?.textContent || '';

            // 检查是否输入了触发字符
            const triggerIndex = textBefore.lastIndexOf(extension.options.trigger);

            if (triggerIndex === -1) {
              return {
                ...prev,
                isOpen: false,
                range: null,
                query: '',
                decorations: DecorationSet.empty,
              };
            }

            // 获取触发字符后的查询文本
            const query = textBefore.slice(triggerIndex + 1);

            // 检查查询是否有效
            const isValidQuery = extension.options.allowSpaces || !query.includes(' ');
            const isAtStartOfLine = extension.options.startOfLine ? triggerIndex === 0 : true;

            if (!isValidQuery || !isAtStartOfLine) {
              return {
                ...prev,
                isOpen: false,
                range: null,
                query: '',
                decorations: DecorationSet.empty,
              };
            }

            // 计算范围
            const range = {
              from: $from.pos - query.length - 1,
              to: $from.pos,
            };

            // 创建装饰
            const decoration = Decoration.inline(range.from, range.to, {
              class: 'slash-command-query',
              'data-query': query,
            });

            const decorations = DecorationSet.create(doc, [decoration]);

            // 更新存储
            extension.storage.isOpen = true;
            extension.storage.range = range;
            extension.storage.query = query;

            return {
              isOpen: true,
              range,
              query,
              decorations,
            };
          },
        },

        props: {
          decorations(state: EditorState) {
            return (this as Plugin<PluginState>).getState(state)?.decorations;
          },

          handleKeyDown(view: EditorView, event: KeyboardEvent) {
            const { state } = view;
            const pluginState = (this as Plugin<PluginState>).getState(state);

            if (!pluginState?.isOpen) {
              return false;
            }

            // 处理特殊按键
            if (event.key === 'Escape') {
              // 关闭命令菜单
              extension.storage.isOpen = false;
              extension.storage.range = null;
              extension.storage.query = '';

              // 触发自定义事件
              const customEvent = new CustomEvent('slash-command-close');
              view.dom.dispatchEvent(customEvent);

              return true;
            }

            if (event.key === 'Enter') {
              // 阻止默认的回车行为，让菜单组件处理
              event.preventDefault();

              // 触发自定义事件
              const customEvent = new CustomEvent('slash-command-enter', {
                detail: { query: pluginState.query, range: pluginState.range }
              });
              view.dom.dispatchEvent(customEvent);

              return true;
            }

            if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
              // 阻止默认的箭头键行为，让菜单组件处理
              event.preventDefault();

              // 触发自定义事件
              const customEvent = new CustomEvent('slash-command-navigate', {
                detail: {
                  direction: event.key === 'ArrowUp' ? 'up' : 'down',
                  query: pluginState.query,
                  range: pluginState.range
                }
              });
              view.dom.dispatchEvent(customEvent);

              return true;
            }

            return false;
          },

          handleTextInput(view: EditorView, _from: number, _to: number, _text: string) {
            const { state } = view;
            const pluginState = (this as Plugin<PluginState>).getState(state);

            // 如果菜单已打开，触发查询更新事件
            if (pluginState?.isOpen) {
              setTimeout(() => {
                const newPluginState = (this as Plugin<PluginState>).getState(view.state);
                if (newPluginState?.isOpen) {
                  const customEvent = new CustomEvent('slash-command-query-update', {
                    detail: {
                      query: newPluginState.query,
                      range: newPluginState.range
                    }
                  });
                  view.dom.dispatchEvent(customEvent);
                }
              }, 0);
            }

            return false;
          },
        },
      }),
    ];
  },

  addCommands() {
    return {
      /**
       * 关闭斜杠命令菜单
       */
      closeSlashCommand: () => () => {
        this.storage.isOpen = false;
        this.storage.range = null;
        this.storage.query = '';
        return true;
      },

      /**
       * 执行斜杠命令
       */
      executeSlashCommand: (commandId: string) => ({ editor }: { editor: Editor }) => {
        const { range } = this.storage;

        if (!range) {
          return false;
        }

        // 删除触发文本
        const transaction = editor.state.tr.delete(range.from, range.to);
        editor.view.dispatch(transaction);

        // 关闭菜单
        this.storage.isOpen = false;
        this.storage.range = null;
        this.storage.query = '';

        // 触发命令执行事件
        const customEvent = new CustomEvent('slash-command-execute', {
          detail: {
            commandId,
            range: { from: range.from, to: range.from }
          }
        });

        setTimeout(() => {
          editor.view.dom.dispatchEvent(customEvent);
        }, 0);

        return true;
      },
    } as any; // 临时使用 any 来避免复杂的类型问题
  },
});