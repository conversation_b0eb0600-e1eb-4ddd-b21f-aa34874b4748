{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "disabled": false, "autoApprove": ["browser_click", "browser_close", "browser_console_messages", "browser_drag", "browser_evaluate", "browser_file_upload", "browser_handle_dialog", "browser_hover", "browser_navigate", "browser_navigate_back", "browser_navigate_forward", "browser_network_requests", "browser_press_key", "browser_resize", "browser_select_option", "browser_snapshot", "browser_take_screenshot", "browser_type", "browser_wait_for", "browser_tab_close", "browser_tab_list", "browser_tab_new", "browser_tab_select", "browser_install", "browser_mouse_click_xy", "browser_mouse_drag_xy", "browser_mouse_move_xy", "browser_pdf_save", "browser_console_messages"]}}}