'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Editor } from '@tiptap/react';
import { AIDocumentAnalysisPanel, AIDocumentAnalysisLoading } from './AIDocumentAnalysisPanel';
import { 
  DocumentAnalysisService, 
  DocumentAnalysisResult,
  DocumentAnalysisType,
  SummaryLength
} from '@/lib/services/ai/document-analysis-service';
import { aiServiceManager } from '@/lib/services/ai/ai-service-factory';
import { AIServiceError } from '@/types/ai.types';

/**
 * AI 文档分析管理器的属性
 */
interface AIDocumentAnalysisManagerProps {
  /** TipTap 编辑器实例 */
  editor: Editor;
  /** 是否启用 AI 功能 */
  enabled?: boolean;
}

/**
 * 分析状态
 */
interface AnalysisState {
  /** 是否正在分析 */
  isAnalyzing: boolean;
  /** 是否显示结果 */
  showResult: boolean;
  /** 分析结果 */
  result?: DocumentAnalysisResult;
  /** 错误信息 */
  error?: string;
  /** 当前分析类型 */
  currentType?: DocumentAnalysisType;
}

/**
 * 快速分析选项
 */
interface QuickAnalysisOption {
  id: DocumentAnalysisType;
  label: string;
  description: string;
  icon: string;
  shortcut?: string;
}

/**
 * 预定义的快速分析选项
 */
const QUICK_ANALYSIS_OPTIONS: QuickAnalysisOption[] = [
  {
    id: 'summary',
    label: '生成摘要',
    description: '生成文档的简洁摘要',
    icon: '📄',
    shortcut: '⌘⇧S'
  },
  {
    id: 'keywords',
    label: '提取关键词',
    description: '识别文档中的重要概念',
    icon: '🏷️',
    shortcut: '⌘⇧K'
  },
  {
    id: 'outline',
    label: '生成大纲',
    description: '创建文档的结构化大纲',
    icon: '📋',
    shortcut: '⌘⇧O'
  },
  {
    id: 'analysis',
    label: '内容分析',
    description: '分析文档的主题、语调和质量',
    icon: '📊',
    shortcut: '⌘⇧A'
  }
];

/**
 * AI 文档分析管理器组件
 * 负责管理 AI 文档分析的整个流程
 */
export function AIDocumentAnalysisManager({ 
  editor, 
  enabled = true 
}: AIDocumentAnalysisManagerProps) {
  const [state, setState] = useState<AnalysisState>({
    isAnalyzing: false,
    showResult: false
  });
  
  const documentAnalysisService = useRef<DocumentAnalysisService>();
  const abortController = useRef<AbortController>();

  // 初始化文档分析服务
  useEffect(() => {
    try {
      const aiService = aiServiceManager.getDefaultService();
      documentAnalysisService.current = new DocumentAnalysisService(aiService);
    } catch (error) {
      console.warn('AI 服务未配置，AI 文档分析功能将不可用');
    }
  }, []);

  /**
   * 开始文档分析
   */
  const startAnalysis = useCallback(async (
    type: DocumentAnalysisType,
    options: {
      summaryLength?: SummaryLength;
      keywordCount?: number;
      outlineDepth?: number;
    } = {}
  ) => {
    if (!enabled || !documentAnalysisService.current || state.isAnalyzing) {
      return;
    }

    try {
      // 获取文档内容
      const content = editor.getText();
      
      if (!content.trim()) {
        setState(prev => ({ 
          ...prev, 
          error: '文档内容为空，无法进行分析' 
        }));
        return;
      }

      // 获取文档标题（如果有）
      const firstHeading = editor.getJSON().content?.find(
        (node: any) => node.type === 'heading' && node.attrs?.level === 1
      );
      const title = firstHeading?.content?.[0]?.text;

      // 创建取消控制器
      abortController.current = new AbortController();

      // 更新状态为分析中
      setState({
        isAnalyzing: true,
        showResult: false,
        error: undefined,
        currentType: type
      });

      // 执行分析
      const result = await documentAnalysisService.current.analyzeDocument({
        content,
        type,
        title,
        ...options
      });

      // 更新状态为显示结果
      setState(prev => ({
        ...prev,
        isAnalyzing: false,
        showResult: true,
        result,
        error: undefined
      }));

    } catch (error) {
      console.error('AI 文档分析失败:', error);
      
      let errorMessage = '分析失败，请稍后重试';
      if (error instanceof AIServiceError) {
        errorMessage = error.message;
      }

      setState(prev => ({
        ...prev,
        isAnalyzing: false,
        showResult: false,
        error: errorMessage
      }));
    }
  }, [editor, enabled, state.isAnalyzing]);

  /**
   * 关闭分析面板
   */
  const closeAnalysis = useCallback(() => {
    setState({
      isAnalyzing: false,
      showResult: false
    });
  }, []);

  /**
   * 重新分析
   */
  const reanalyze = useCallback(async (type: DocumentAnalysisType) => {
    await startAnalysis(type);
  }, [startAnalysis]);

  /**
   * 取消分析
   */
  const cancelAnalysis = useCallback(() => {
    if (abortController.current) {
      abortController.current.abort();
    }
    
    setState({
      isAnalyzing: false,
      showResult: false
    });
  }, []);

  /**
   * 快速分析方法
   */
  const quickAnalysis = useCallback((type: DocumentAnalysisType) => {
    const defaultOptions = {
      summary: { summaryLength: 'medium' as SummaryLength },
      keywords: { keywordCount: 10 },
      outline: { outlineDepth: 3 },
      analysis: {},
      topics: {}
    };
    
    return startAnalysis(type, defaultOptions[type] || {});
  }, [startAnalysis]);

  // 暴露方法给父组件
  useEffect(() => {
    if (editor) {
      // 将方法添加到编辑器实例上，方便其他组件调用
      (editor as any).aiDocumentAnalysis = {
        // 快速分析方法
        generateSummary: (length?: SummaryLength) => startAnalysis('summary', { summaryLength: length }),
        extractKeywords: (count?: number) => startAnalysis('keywords', { keywordCount: count }),
        generateOutline: (depth?: number) => startAnalysis('outline', { outlineDepth: depth }),
        analyzeContent: () => startAnalysis('analysis'),
        
        // 通用分析方法
        analyze: (type: DocumentAnalysisType, options?: any) => startAnalysis(type, options),
        
        // 状态
        isAnalyzing: state.isAnalyzing,
        
        // 快速选项
        quickOptions: QUICK_ANALYSIS_OPTIONS
      };
    }
  }, [editor, startAnalysis, state.isAnalyzing]);

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + S: 生成摘要
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'S') {
        event.preventDefault();
        quickAnalysis('summary');
      }
      
      // Ctrl/Cmd + Shift + K: 提取关键词
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'K') {
        event.preventDefault();
        quickAnalysis('keywords');
      }
      
      // Ctrl/Cmd + Shift + O: 生成大纲
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'O') {
        event.preventDefault();
        quickAnalysis('outline');
      }
      
      // Ctrl/Cmd + Shift + A: 内容分析
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'A') {
        event.preventDefault();
        quickAnalysis('analysis');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [quickAnalysis]);

  if (!enabled || !documentAnalysisService.current) {
    return null;
  }

  return (
    <>
      {/* 分析中的加载状态 */}
      <AIDocumentAnalysisLoading
        visible={state.isAnalyzing}
        onCancel={cancelAnalysis}
        message="正在分析文档内容..."
        type={state.currentType}
      />

      {/* 分析结果显示 */}
      {state.result && (
        <AIDocumentAnalysisPanel
          result={state.result}
          visible={state.showResult}
          onClose={closeAnalysis}
          onReanalyze={reanalyze}
          isAnalyzing={state.isAnalyzing}
        />
      )}

      {/* 错误提示 */}
      {state.error && (
        <div className="fixed top-4 right-4 z-50">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-w-md shadow-lg">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className="text-red-600 mt-0.5">⚠️</div>
                <div>
                  <div className="font-medium text-red-800">分析失败</div>
                  <div className="text-sm text-red-700 mt-1">{state.error}</div>
                </div>
              </div>
              <button
                onClick={() => setState(prev => ({ ...prev, error: undefined }))}
                className="text-red-500 hover:text-red-700 ml-4"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

/**
 * 扩展编辑器类型定义
 */
declare module '@tiptap/react' {
  interface Editor {
    aiDocumentAnalysis?: {
      // 快速分析方法
      generateSummary: (length?: SummaryLength) => Promise<void>;
      extractKeywords: (count?: number) => Promise<void>;
      generateOutline: (depth?: number) => Promise<void>;
      analyzeContent: () => Promise<void>;
      
      // 通用分析方法
      analyze: (type: DocumentAnalysisType, options?: any) => Promise<void>;
      
      // 状态
      isAnalyzing: boolean;
      
      // 快速选项
      quickOptions: QuickAnalysisOption[];
    };
  }
}